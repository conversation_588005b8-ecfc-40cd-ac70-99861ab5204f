# 打包输出目录
target
dist
node_modules
build
*/build/

# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.tar
*.tgz
*.rar

# virtual machine crash logs
hs_err_pid*

# IDE
.idea/
*.iml
.DS_Store
.metadata/
.classpath
.project
.settings/
*.bak
.gradle/
classes/
bin/
*/bin/
/logs/
/fohowe-urm-app/src/main/java/com/hisun/lemon/urm/config/pengyuxintestConfig.java
