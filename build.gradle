buildscript {
    repositories {
        maven { url "http://*************:8089/repository/maven-public/"
            credentials {
                username 'dev'
                password 'fohow123!@#'
            }
            authentication {
                basic(BasicAuthentication)
            }
        }
    }
    ext {
        springBootVersion = '1.5.3.RELEASE'
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("org.springframework.boot:spring-boot-starter-thymeleaf:${springBootVersion}")
        classpath("io.spring.gradle:dependency-management-plugin:1.0.1.RELEASE")
    }
}

subprojects{
    apply plugin: 'java'
    apply plugin: 'eclipse'
    apply plugin: 'maven'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'

    version = '1.0.0-SNAPSHOT'
    group = 'com.hisun'
    
    sourceCompatibility = 1.8
    targetCompatibility = 1.8

    configurations.all {
        // check for updates every build
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
        resolutionStrategy.force 'xml-apis:xml-apis:1.0.b2'
    }

    bootRepackage {
        enabled = false
        //mainClass=
    }
    

    repositories {
        maven { url "http://*************:8089/repository/maven-public/"
            credentials {
                username 'dev'
                password 'fohow123!@#'
            }
            authentication {
                basic(BasicAuthentication)
            }
        }
    }

    dependencyManagement {
       // imports {
       //       mavenBom 'org.springframework.cloud:spring-cloud-dependencies:Dalston.SR1'
      //    }
        dependencies {
            dependency 'mysql:mysql-connector-java:8.0.32'
            dependency 'com.hisun:lemon-framework:1.0.1-SNAPSHOT'
            dependency 'com.hisun:lemon-framework-lock:1.0.1-SNAPSHOT'
            dependency 'com.hisun:lemon-interface:1.0.1-SNAPSHOT'
            dependency 'com.hisun:lemon-swagger:1.0.1-SNAPSHOT'
            dependency 'com.hisun:lemon-gateway-dto:1.0.1-SNAPSHOT'
            dependency 'com.hisun:fohowe-common-api:1.0.0-SNAPSHOT'
            dependency 'com.hisun:bns-interface:1.0.0-SNAPSHOT'
            dependency 'com.hisun:fohowe-api-ec:1.0.0-SNAPSHOT'
            dependency 'com.hisun:fohowe-fin-interface:1.0.0-SNAPSHOT' 
            //dependency 'com.hisun:lemon-fohow-authority-validate:1.0.1-SNAPSHOT'
            dependency 'com.hisun:lemon-framework-accdate-bind:1.0.2-SNAPSHOT'
            dependency 'com.alibaba:easyexcel:2.2.4'  
           // dependency 'xom:xom:1.2.5'
           // dependency 'xml-apis:xml-apis:2.0.2'
           // dependency 'junit:junit:4.11'
         }
    }
    
    dependencies {
        testCompile("org.springframework.boot:spring-boot-starter-test")
       // testCompile "junit:junit:4.12"
    }
    
    jar {
        from('src/main/resources') {
            include '**/*.*'
        }
    }

    //打包源代码
    task sourcesJar(type: Jar) {
        classifier = 'sources'
        from sourceSets.main.allSource
    }
    artifacts {
        archives jar
        archives sourcesJar
    }
    
    
    uploadArchives {
        repositories {
            mavenDeployer {
                snapshotRepository(url: "http://*************:8089/repository/maven-snapshots/") {
                    authentication(userName:'dev', password:'fohow123!@#')
                }
                repository(url: "http://*************:8089/repository/maven-releases/") {
                    authentication(userName:'dev', password:'fohow123!@#')
                }
            }
        }
    }
}

description = 'fohowe-urm'
