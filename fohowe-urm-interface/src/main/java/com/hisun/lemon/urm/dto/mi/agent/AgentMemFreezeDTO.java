package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
import com.hisun.lemon.urm.enums.mi.FreezeStatusEnums;
import com.hisun.lemon.urm.enums.mi.FreezeTypeEnums;
/**
 * Agent 会员冻结传输对象
 * <AUTHOR>
 * @date 2017年11月06日
 * @time 下午14:27:30
 */
@ClientValidated
@ApiModel(value="AgentMemFreezeDTO", description="会员冻结传输对象")
public class AgentMemFreezeDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="记录ID",required=false)
    private Long id;
	
	@ApiModelProperty(value="批量解冻idArr",required=false)
    private List<String> idArr;
	
    @ApiModelProperty(value="经销商编号", required= false,dataType="String")
    private String memberNo;
    
    @ApiModelProperty(value="代办处编号", required= false,dataType="String")
    private String agentNo;
    
    @ApiModelProperty(value="冻结类型1代办处及以下经销商 2经销商", required= false,dataType="String")
    private String freezeType;
    
    @ApiModelProperty(value="状态1生效  2取消", required= false,dataType="String")
    private String status;
    
    @ApiModelProperty(value="操作人",required=false,dataType="String")
    private String operCode;
    
    @ApiModelProperty(value="开始操作日期",dataType="String")
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minOperDate;
    
    @ApiModelProperty(value="结束操作日期",dataType="String")
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxOperDate;
    
    @ApiModelProperty(value="取消人",required=false,dataType="String")
    private String cancelCode;
    
    @ApiModelProperty(value="开始取消日期")
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minCancelDate;
    
    @ApiModelProperty(value="结束取消日期")
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxCancelDate;

	private String companyCode;

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getOperCode() {
		return operCode;
	}

	public void setOperCode(String operCode) {
		this.operCode = operCode;
	}

	public String getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}

	public LocalDateTime getMinOperDate() {
		return minOperDate;
	}

	public void setMinOperDate(LocalDateTime minOperDate) {
		this.minOperDate = minOperDate;
	}

	public LocalDateTime getMaxOperDate() {
		return maxOperDate;
	}

	public void setMaxOperDate(LocalDateTime maxOperDate) {
		this.maxOperDate = maxOperDate;
	}

	public LocalDateTime getMinCancelDate() {
		return minCancelDate;
	}

	public void setMinCancelDate(LocalDateTime minCancelDate) {
		this.minCancelDate = minCancelDate;
	}

	public LocalDateTime getMaxCancelDate() {
		return maxCancelDate;
	}

	public void setMaxCancelDate(LocalDateTime maxCancelDate) {
		this.maxCancelDate = maxCancelDate;
	}

	public List<String> getIdArr() {
		return idArr;
	}

	public void setIdArr(List<String> idArr) {
		this.idArr = idArr;
	}

	public String getFreezeType() {
		return freezeType;
	}

	public void setFreezeType(String freezeType) {
		this.freezeType = freezeType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
}
