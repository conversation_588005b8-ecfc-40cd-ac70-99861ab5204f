package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.hisun.lemon.framework.validation.ClientValidated;

/**
 * AgentQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年10月30日
 * @time 上午9:27:30
 *
 */

@ClientValidated
@ApiModel(value="AgentQueryBean", description="代办处传输对象")
public class AgentQueryBean{
	/*=============base data============ */
	@ApiModelProperty(value="奖金制度",dataType="String")
    private String bonusType;
	
    @ApiModelProperty(value="区域",dataType="String")
    private String areaCode;
    
    @ApiModelProperty(value="所属分公司",dataType="String")
    private String companyCode;
    
    @ApiModelProperty(value="代办处",dataType="String")
    private String agentNo;
    
    /*=============show data============ */
    @ApiModelProperty(value="记录编号")
    private long id;
    
    @ApiModelProperty(value="代办处名称",dataType="String")
    private String name;
    
    @ApiModelProperty(value="加入期数",required=false,dataType="String")
    private String startWeek;
    
    @ApiModelProperty(value="登录状态",required=false,dataType="String")
	private String loginStatus;
    
    /*=========购货结算参数设置==========*/
    @ApiModelProperty(value="特批货物额度",required=false,dataType="String")
    private BigDecimal promAmt;
    
    @ApiModelProperty(value="已批货物额度",required=false,dataType="String")
    private BigDecimal promTotalAmt;
   
    @ApiModelProperty(value="FV允许的最低额度",required=false,dataType="String")
    private BigDecimal fvLimit;
   
    @ApiModelProperty(value="F$允许的最低额度",required=false,dataType="String")
    private BigDecimal fpLimit;
    
    @ApiModelProperty(value="F000允许的最低额度",required=false,dataType="String")
    private BigDecimal f0Limit;
    
    @ApiModelProperty(value="奖金发放方式0=发放到代办处，1=发放到经销商",required=false,dataType="String")
    private String bonusSendType;
   
    @ApiModelProperty(value="奖金发放到会员模式代办费计提：0 否，1 计提经销商报单 2计提代办处报单",required=false,dataType="String")
    private String isBonus;

    @ApiModelProperty(value="币种",required=false,dataType="String")
    private String currencyCode;
    
    @ApiModelProperty(value="奖金发放币种",required=false,dataType="String")
    private String balanceType;
    
    @ApiModelProperty(value="上级代办处",required=false,dataType="String")
    private String parentNo;
    
    @ApiModelProperty(value="代办处级别",required=false,dataType="Integer")
    private Integer levelType;
    
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getStartWeek() {
		return startWeek;
	}
	public void setStartWeek(String startWeek) {
		this.startWeek = startWeek;
	}
	public BigDecimal getPromAmt() {
		return promAmt;
	}
	public void setPromAmt(BigDecimal promAmt) {
		this.promAmt = promAmt;
	}
	public BigDecimal getPromTotalAmt() {
		return promTotalAmt;
	}
	public void setPromTotalAmt(BigDecimal promTotalAmt) {
		this.promTotalAmt = promTotalAmt;
	}
	public BigDecimal getFvLimit() {
		return fvLimit;
	}
	public void setFvLimit(BigDecimal fvLimit) {
		this.fvLimit = fvLimit;
	}
	public BigDecimal getFpLimit() {
		return fpLimit;
	}
	public void setFpLimit(BigDecimal fpLimit) {
		this.fpLimit = fpLimit;
	}
	public BigDecimal getF0Limit() {
		return f0Limit;
	}
	public void setF0Limit(BigDecimal f0Limit) {
		this.f0Limit = f0Limit;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getLoginStatus() {
		return loginStatus;
	}
	public void setLoginStatus(String loginStatus) {
		this.loginStatus = loginStatus;
	}
	public String getBonusSendType() {
		return bonusSendType;
	}
	public void setBonusSendType(String bonusSendType) {
		this.bonusSendType = bonusSendType;
	}
	public String getIsBonus() {
		return isBonus;
	}
	public void setIsBonus(String isBonus) {
		this.isBonus = isBonus;
	}
	public String getBalanceType() {
		return balanceType;
	}
	public void setBalanceType(String balanceType) {
		this.balanceType = balanceType;
	}
	public String getParentNo() {
		return parentNo;
	}
	public void setParentNo(String parentNo) {
		this.parentNo = parentNo;
	}
	public Integer getLevelType() {
		return levelType;
	}
	public void setLevelType(Integer levelType) {
		this.levelType = levelType;
	}
	
}
