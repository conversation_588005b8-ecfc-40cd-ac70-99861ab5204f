 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * ShareBonusAccountRsp 传输对象
 * <AUTHOR>
 * @date 2018年09月10号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="ShareBonusAccountRsp", description="账户传输对象")
public class ShareBonusAccountRsp{
    
    @ApiModelProperty(value="全球领袖分红余额")
    private BigDecimal globalShare;
    
    @ApiModelProperty(value="凤凰大使分红余额")
    private BigDecimal ambassadorShare;

	public BigDecimal getGlobalShare() {
		return globalShare;
	}

	public void setGlobalShare(BigDecimal globalShare) {
		this.globalShare = globalShare;
	}

	public BigDecimal getAmbassadorShare() {
		return ambassadorShare;
	}

	public void setAmbassadorShare(BigDecimal ambassadorShare) {
		this.ambassadorShare = ambassadorShare;
	}
    
}
