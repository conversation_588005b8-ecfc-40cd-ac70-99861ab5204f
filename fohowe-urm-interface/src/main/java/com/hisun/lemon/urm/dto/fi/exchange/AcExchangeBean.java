 package com.hisun.lemon.urm.dto.fi.exchange;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcExchangeBean 传输对象
 * <AUTHOR>
 * @date 2017年11月21号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcExchangeBean", description="兑换对象")
public class AcExchangeBean{
	@ApiModelProperty(name="id",value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(name="exchangeNo",value="编号",required=false)
    private String exchangeNo;
	@ApiModelProperty(name="acType",value="账户类型",required=false)
	private String acType;
	@ApiModelProperty(name="companyCode",value="公司编号",required=false,dataType="String")
    private String companyCode;
	@ApiModelProperty(name="companyName",value="公司名称",required=false,dataType="String")
    private String companyName;
	@ApiModelProperty(name="userCode",value="用户编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(name="money",value="兑换金额",required=false,dataType="BigDecimal")
    private BigDecimal money;
	@ApiModelProperty(name="moneyFv",value="兑换获得FV",required=false,dataType="BigDecimal")
	private BigDecimal moneyFv;
	@ApiModelProperty(name="moneyH0",value="兑换获得H0",required=false,dataType="BigDecimal")
	private BigDecimal moneyH0;
	@ApiModelProperty(name="handlingFee",value="手续费",required=false,dataType="BigDecimal")
    private BigDecimal handlingFee;
	@ApiModelProperty(name="status",value="状态1：新增",required=false,dataType="String")
    private String status;
	@ApiModelProperty(name="creatorCode",value="创建人编号",required=false,dataType="String")
    private String creatorCode;
	@ApiModelProperty(name="checkerCode",value="确认人编号",required=false,dataType="String")
    private String checkerCode;
	@ApiModelProperty(name="checkTime",value="确认时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkTime;
	@ApiModelProperty(name="createTime",value="创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
	@ApiModelProperty(name="memo",value="备注(会员)",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(name="remark",value="摘要(公司)",required=false,dataType="String")
    private String remark;
	@ApiModelProperty(name="periodWeek",value="兑换申请期数",required=false,dataType="String")
    private String periodWeek;
	@ApiModelProperty(name="agentNo",value="代办处编号",required=false,dataType="String")
    private String agentNo;
	
	@ApiModelProperty(value="总计F$",required=false,dataType="BigDecimal")
	private BigDecimal totalF$;
	
	@ApiModelProperty(value="兑换类型",required=false)
	private Integer exType;
	
	@ApiModelProperty(value="用户名称",required=false)
	private String userName;
	@ApiModelProperty(value="加赠金额",required=false)
    private BigDecimal attrValue;
	@ApiModelProperty(value="活动编号",required=false)
    private Long eventAttrId;
	@ApiModelProperty(value="加赠状态",required=false)
    private Integer promotionStatus;
	
	
	public BigDecimal getTotalF$() {
		return totalF$;
	}
	public void setTotalF$(BigDecimal totalF$) {
		this.totalF$ = totalF$;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	
	public BigDecimal getMoneyFv() {
		return moneyFv;
	}
	public void setMoneyFv(BigDecimal moneyFv) {
		this.moneyFv = moneyFv;
	}
	public BigDecimal getMoneyH0() {
		return moneyH0;
	}
	public void setMoneyH0(BigDecimal moneyH0) {
		this.moneyH0 = moneyH0;
	}
	public BigDecimal getHandlingFee() {
		return handlingFee;
	}
	public void setHandlingFee(BigDecimal handlingFee) {
		this.handlingFee = handlingFee;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCreatorCode() {
		return creatorCode;
	}
	public void setCreatorCode(String creatorCode) {
		this.creatorCode = creatorCode;
	}
	public String getCheckerCode() {
		return checkerCode;
	}
	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}
	public LocalDateTime getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(LocalDateTime checkTime) {
		this.checkTime = checkTime;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public Integer getExType() {
		return exType;
	}
	public void setExType(Integer exType) {
		this.exType = exType;
	}
	public String getExchangeNo() {
		return exchangeNo;
	}
	public void setExchangeNo(String exchangeNo) {
		this.exchangeNo = exchangeNo;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public BigDecimal getAttrValue() {
		return attrValue;
	}
	public void setAttrValue(BigDecimal attrValue) {
		this.attrValue = attrValue;
	}
	public Long getEventAttrId() {
		return eventAttrId;
	}
	public void setEventAttrId(Long eventAttrId) {
		this.eventAttrId = eventAttrId;
	}
	public Integer getPromotionStatus() {
		return promotionStatus;
	}
	public void setPromotionStatus(Integer promotionStatus) {
		this.promotionStatus = promotionStatus;
	}
	
}
