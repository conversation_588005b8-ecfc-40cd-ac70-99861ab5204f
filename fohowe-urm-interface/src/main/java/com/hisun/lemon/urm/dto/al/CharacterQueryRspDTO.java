package com.hisun.lemon.urm.dto.al;

public class CharacterQueryRspDTO {

    /**
     * 字符键ID值
     */
    private String id;
    /**
     * 字符键值
     */
    private String characterKey;
    /**
     * 字符键值描述
     */
    private String keyDesc;
    /**
     * @Fields langId 语言id
     */
    private String langId;
    /**
     * @Fields langCode 语言编码
     */
    private String langCode;
    /**
     * @Fields langName 语言名称
     */
    private String langName;
    /**
     * @Fields displayTest 显示文字
     */
    private String characterValue;
    /**
     * @Fields groupId 群组编号
     */
    private String groupId;

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getLangName() {
        return langName;
    }

    public void setLangName(String langName) {
        this.langName = langName;
    }

    public String getCharacterValue() {
        return characterValue;
    }

    public void setCharacterValue(String characterValue) {
        this.characterValue = characterValue;
    }

    public String getCharacterKey() {
        return characterKey;
    }

    public void setCharacterKey(String characterKey) {
        this.characterKey = characterKey;
    }

    public String getKeyDesc() {
        return keyDesc;
    }

    public void setKeyDesc(String keyDesc) {
        this.keyDesc = keyDesc;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLangId() {
        return langId;
    }

    public void setLangId(String langId) {
        this.langId = langId;
    }

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
    
}
