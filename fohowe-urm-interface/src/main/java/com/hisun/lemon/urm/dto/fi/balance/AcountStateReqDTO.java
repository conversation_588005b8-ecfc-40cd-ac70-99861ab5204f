package com.hisun.lemon.urm.dto.fi.balance;

import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceItem 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcountStateReqDTO", description="账户统计报表传输对象")
public class AcountStateReqDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="奖金制度")
    private String bonusType;
    @ApiModelProperty(value="区域")
    private String areaCode;
    @ApiModelProperty(value="所属分公司")
    private String companyCode;
    @ApiModelProperty(value="代办处")
    private String agentNo;
	
	@ApiModelProperty(value="汇总类型:1总公司 2区域 3分公司 4代办处 5用户",required=false,dataType="String")
	private String type;
	
	@ApiModelProperty(value="用户编号",required=false,dataType="String")
	private String usercode;
	
	@ApiModelProperty(name="acType",value="账户类型:f$＝f$,fv＝fv,f0=f000,h0=h0000,fb=fb,pv=活跃pv\r\n" + 
			"p$=活跃p$,b1=重消分配,b2=旅游基金,b3=名车基金,b4=游艇基金,b5=住宅基金,b6=市场发展基金",required=false)
	private String acType;
	
	@ApiModelProperty(value="开始创建日期")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minCreateDate;
	
	@ApiModelProperty(value="结束创建日期")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxCreateDate;
	
	@ApiModelProperty(value="开始期数",required=false,dataType="String")
	private String minPeriod;
	
	@ApiModelProperty(value="结束期数",required=false,dataType="String")
	private String maxPeriod;
	
	@ApiModelProperty(value="交易类别，a01:充值;a02:提现;a03:兑换;a04:报单a05:提货;\r\n" + 
			"a06:奖金发放;a07:借款;a08:还款;a09转账;a10活跃扣款 \r\n" + 
			"a11点位合并转移 1x之后的界面定义扣补\r\n" + 
			"a21:代办处存款",required=false)
	private String orderType;
	
	@ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    private int pageSize;

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getUsercode() {
		return usercode;
	}

	public void setUsercode(String usercode) {
		this.usercode = usercode;
	}

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public LocalDateTime getMinCreateDate() {
		return minCreateDate;
	}

	public void setMinCreateDate(LocalDateTime minCreateDate) {
		this.minCreateDate = minCreateDate;
	}

	public LocalDateTime getMaxCreateDate() {
		return maxCreateDate;
	}

	public void setMaxCreateDate(LocalDateTime maxCreateDate) {
		this.maxCreateDate = maxCreateDate;
	}

	public String getMinPeriod() {
		return minPeriod;
	}

	public void setMinPeriod(String minPeriod) {
		this.minPeriod = minPeriod;
	}

	public String getMaxPeriod() {
		return maxPeriod;
	}

	public void setMaxPeriod(String maxPeriod) {
		this.maxPeriod = maxPeriod;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
    
}
