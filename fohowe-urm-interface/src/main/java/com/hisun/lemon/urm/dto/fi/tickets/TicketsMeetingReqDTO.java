package com.hisun.lemon.urm.dto.fi.tickets;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

public class TicketsMeetingReqDTO {
    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private int pageNum=1;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    private int pageSize=20;

    private String meetingNo;
    private String meetingTitle;
    private String companyCode;
    private String city;
    @ApiModelProperty(value = "预计开始时间")
    private String meetingDateStart;
    @ApiModelProperty(value = "预计结束时间")
    private String meetingDateEnd;
    @ApiModelProperty(value = "预计会议类型")
    private String planMeetingType;
    @ApiModelProperty(value = "计划人数")
    private Integer planNum;
    @ApiModelProperty(value = "预计参与经理")
    private String planManager;
    @ApiModelProperty(value = "收费标准F$")
    private BigDecimal planAmount;
    @ApiModelProperty(value = "预计收费币种")
    private String planCurrency;
    @ApiModelProperty(value = "预计主讲老师")
    private String planLecturer;
    @ApiModelProperty(value = "预计参会老师")
    private String planTeacher;
    @ApiModelProperty(value = "预计翻译")
    private String planTranslator;
    
    private Integer vcStatus;
    
    
    private Integer planYearMonth;
    private Integer actalYearMonth;
    
    private List<String> teachers;
    
    public int getPageNum() {
        return pageNum;
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo;
    }

    public String getMeetingTitle() {
        return meetingTitle;
    }

    public void setMeetingTitle(String meetingTitle) {
        this.meetingTitle = meetingTitle;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getMeetingDateStart() {
        return meetingDateStart;
    }

    public void setMeetingDateStart(String meetingDateStart) {
        this.meetingDateStart = meetingDateStart;
    }

    public String getMeetingDateEnd() {
        return meetingDateEnd;
    }

    public void setMeetingDateEnd(String meetingDateEnd) {
        this.meetingDateEnd = meetingDateEnd;
    }

    public String getPlanMeetingType() {
        return planMeetingType;
    }

    public void setPlanMeetingType(String planMeetingType) {
        this.planMeetingType = planMeetingType;
    }

    public Integer getPlanNum() {
        return planNum;
    }

    public void setPlanNum(Integer planNum) {
        this.planNum = planNum;
    }

    public String getPlanManager() {
        return planManager;
    }

    public void setPlanManager(String planManager) {
        this.planManager = planManager;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public String getPlanCurrency() {
        return planCurrency;
    }

    public void setPlanCurrency(String planCurrency) {
        this.planCurrency = planCurrency;
    }

    public String getPlanLecturer() {
        return planLecturer;
    }

    public void setPlanLecturer(String planLecturer) {
        this.planLecturer = planLecturer;
    }

    public String getPlanTeacher() {
        return planTeacher;
    }

    public void setPlanTeacher(String planTeacher) {
        this.planTeacher = planTeacher;
    }

    public String getPlanTranslator() {
        return planTranslator;
    }

    public void setPlanTranslator(String planTranslator) {
        this.planTranslator = planTranslator;
    }

	public Integer getPlanYearMonth() {
		return planYearMonth;
	}

	public void setPlanYearMonth(Integer planYearMonth) {
		this.planYearMonth = planYearMonth;
	}

	public Integer getActalYearMonth() {
		return actalYearMonth;
	}

	public void setActalYearMonth(Integer actalYearMonth) {
		this.actalYearMonth = actalYearMonth;
	}

	public List<String> getTeachers() {
		return teachers;
	}

	public void setTeachers(List<String> teachers) {
		this.teachers = teachers;
	}

	public Integer getVcStatus() {
		return vcStatus;
	}

	public void setVcStatus(Integer vcStatus) {
		this.vcStatus = vcStatus;
	}
    
}
