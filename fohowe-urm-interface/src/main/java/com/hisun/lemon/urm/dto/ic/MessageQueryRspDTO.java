package com.hisun.lemon.urm.dto.ic;

import java.time.LocalDateTime;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-陌生人留言管理查询返回
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 下午3:58:04
 *
 */
public class MessageQueryRspDTO {
    @ApiModelProperty(name = "messageId", value = "留言编号,以','分隔")
    @Length(max = 1000, message = "URM10001")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class })
    private String messageId;

    @ApiModelProperty(name = "firstName", value = "名")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String firstName;

    @ApiModelProperty(name = "lastName", value = "姓")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String lastName;

    @ApiModelProperty(name = "sex", value = "性别 man-男 woman-女")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    @Pattern(regexp = "man|woman", message = "URM10007")
    private String sex;

    @ApiModelProperty(name = "phoneNumber", value = "联系电话")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String phoneNumber;

    @ApiModelProperty(name = "eMail", value = "E-mail")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String eMail;

    @ApiModelProperty(name = "skype", value = "SKYPE")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String skype;

    @ApiModelProperty(name = "viber", value = "VIBER")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String viber;

    @ApiModelProperty(name = "facebook", value = "FACEBOOK")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String facebook;

    @ApiModelProperty(name = "address", value = "居住地址")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String address;

    @ApiModelProperty(name = "job", value = "职业")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String job;

    @ApiModelProperty(name = "refereeCode", value = "推荐人编号")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String refereeCode;

    @ApiModelProperty(name = "messageContent", value = "留言咨询")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String messageContent;

    @ApiModelProperty(name = "status", value = "状态 0-未读 1-已读")
    private String status;
    
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private LocalDateTime createTime;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String geteMail() {
        return eMail;
    }

    public void seteMail(String eMail) {
        this.eMail = eMail;
    }

    public String getSkype() {
        return skype;
    }

    public void setSkype(String skype) {
        this.skype = skype;
    }

    public String getViber() {
        return viber;
    }

    public void setViber(String viber) {
        this.viber = viber;
    }

    public String getFacebook() {
        return facebook;
    }

    public void setFacebook(String facebook) {
        this.facebook = facebook;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getRefereeCode() {
        return refereeCode;
    }

    public void setRefereeCode(String refereeCode) {
        this.refereeCode = refereeCode;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

}
