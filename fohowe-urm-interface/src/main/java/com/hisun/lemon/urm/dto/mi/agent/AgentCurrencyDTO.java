package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * 
 * <AUTHOR>
 * @date 2018-02-03 10:45
 */
@ApiModel(value="MemCurrencyDTO", description="代办处经销商货币对应DTO")
public class AgentCurrencyDTO  extends GenericDTO<NoBody>{
	
    @ApiModelProperty(value="代办处编号")
    private String agentNo;
    
    @ApiModelProperty(value="奖金制度")
    private String bonusType;

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
    
}