package com.hisun.lemon.urm.dto.ic;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.urm.enums.ic.AgentValid;
import com.hisun.lemon.urm.enums.ic.MemberValid;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-文件管理信息修改
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 上午10:44:41
 *
 */
public class FileModifyReqDTO {

    @ApiModelProperty(name = "fileId", value = "文件编号,以','分隔")
    @Length(max = 1000, message = "URM10001")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class })
    private String fileId;

    @ApiModelProperty(name = "dfileId", value = "文件下载编号")
    @Length(max = 1000, message = "URM10001")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.third.class })
    private String dfileId;

    @ApiModelProperty(name = "companyCode", value = "公司编号")
    @Length(max = 1000, message = "URM10001")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String companyCode;

    @ApiModelProperty(name = "fileName", value = "文件名")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    @Length(max = 1000, message = "URM10002")
    private String fileName;

    @ApiModelProperty(name = "fileDesc", value = "文件标题")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    @Length(max = 1000, message = "URM10002")
    private String fileDesc;

    @ApiModelProperty(name = "agnetValid", value = "0不允许代办处使用 1允许代办处使用")
    @JsonSerialize(using=CustomValueEnumSerializer.class)
    @JsonDeserialize(using=CustomValueEnumDeserializer.class)
    private AgentValid agnetValid;

    @ApiModelProperty(name = "memberValid", value = "0不允许经销商使用 1允许经销商使用")
    @JsonSerialize(using=CustomValueEnumSerializer.class)
    @JsonDeserialize(using=CustomValueEnumDeserializer.class)
    private MemberValid memberValid;

    @ApiModelProperty(name = "langId", value = "语言编码ID")
    private String langId;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getDfileId() {
        return dfileId;
    }

    public void setDfileId(String dfileId) {
        this.dfileId = dfileId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public AgentValid getAgnetValid() {
        return agnetValid;
    }

    public void setAgnetValid(AgentValid agnetValid) {
        this.agnetValid = agnetValid;
    }

    public MemberValid getMemberValid() {
        return memberValid;
    }

    public void setMemberValid(MemberValid memberValid) {
        this.memberValid = memberValid;
    }

    public String getFileDesc() {
        return fileDesc;
    }

    public void setFileDesc(String fileDesc) {
        this.fileDesc = fileDesc;
    }

    public String getLangId() {
        return langId;
    }

    public void setLangId(String langId) {
        this.langId = langId;
    }

}
