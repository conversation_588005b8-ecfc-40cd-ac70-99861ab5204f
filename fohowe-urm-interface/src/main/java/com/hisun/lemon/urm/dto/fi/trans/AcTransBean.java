 package com.hisun.lemon.urm.dto.fi.trans;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcTransBean 传输对象
 * <AUTHOR>
 * @date 2017年11月10号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcTransBean", description="转账对象")
public class AcTransBean{
	@ApiModelProperty(name="id",value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(name="acType",value="账户类型",required=false)
    private String acType;
	@ApiModelProperty(name="outCompanyCode",value="付款方公司编号",required=false,dataType="String")
    private String outCompanyCode;
	@ApiModelProperty(name="outUserCode",value="付款方用户编号",required=false,dataType="String")
    private String outUserCode;
	@ApiModelProperty(name="outUserName",value="付款方用户名称",required=false,dataType="String")
	private String outUserName;
	@ApiModelProperty(name="outCurrency",value="付款方货币",required=false,dataType="String")
    private String outCurrency;
	@ApiModelProperty(name="outRate",value="付款方汇率(美元)",required=false,dataType="BigDecimal")
    private BigDecimal outRate;
	@ApiModelProperty(name="outMoney",value="付款金额",required=false,dataType="BigDecimal")
    private BigDecimal outMoney;
	@ApiModelProperty(name="handlingFee",value="手续费",required=false,dataType="BigDecimal")
    private BigDecimal handlingFee;
	@ApiModelProperty(name="inCompanyCode",value="收款方公司编号",required=false,dataType="String")
    private String inCompanyCode;
	@ApiModelProperty(name="inUserCode",value="收款方用户编号",required=false,dataType="String")
    private String inUserCode;
	@ApiModelProperty(name="inUserName",value="收款方用户名称",required=false,dataType="String")
	private String inUserName;
	@ApiModelProperty(name="inCurrency",value="收款方货币",required=false,dataType="String")
    private String inCurrency;
	@ApiModelProperty(name="inRate",value="收款方汇率(美元)",required=false,dataType="BigDecimal")
    private BigDecimal inRate;
	@ApiModelProperty(name="inMoney",value="收款金额",required=false,dataType="BigDecimal")
    private BigDecimal inMoney;
	@ApiModelProperty(name="status",value="状态，1:新建 ",required=false,dataType="String")
    private String status;
	@ApiModelProperty(name="creatorCode",value="创建人编号",required=false,dataType="String")
    private String creatorCode;
	@ApiModelProperty(name="checkerCode",value="确认人编号",required=false,dataType="String")
    private String checkerCode;
	@ApiModelProperty(name="checkTime",value="确认时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkTime;
	@ApiModelProperty(name="memo",value="备注(会员)",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(name="remark",value="摘要(公司)",required=false,dataType="String")
    private String remark;
	@ApiModelProperty(name="transType",value="转账类型:1会员向代办处申购2会员向代办处申领3会员向会员转账4任意",required=false,dataType="String")
    private String transType;
	@ApiModelProperty(name="cancelCode",value="取消人",required=false,dataType="String")
    private String cancelCode;
	@ApiModelProperty(name="cancelTime",value="取消时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime cancelTime;
	@ApiModelProperty(name="periodWeek",value="转账申请期数",required=false,dataType="String")
    private String periodWeek;
	@ApiModelProperty(name="transNo",value="转账单号",required=false,dataType="Long")
    private String transNo;
    @ApiModelProperty(name="createTime",value="创建时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
    @ApiModelProperty(name="outAgentNo",value="转出代办处",required=false,dataType="String")
    private String outAgentNo;
    @ApiModelProperty(name="inAgentNo",value="转入代办处",required=false,dataType="String")
    private String inAgentNo; 
    @ApiModelProperty(name="agentMemo",value="代办处备注",required=false,dataType="String")
    private String agentMemo; 
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getOutCompanyCode() {
		return outCompanyCode;
	}
	public void setOutCompanyCode(String outCompanyCode) {
		this.outCompanyCode = outCompanyCode;
	}
	public String getOutUserCode() {
		return outUserCode;
	}
	public void setOutUserCode(String outUserCode) {
		this.outUserCode = outUserCode;
	}
	public String getOutCurrency() {
		return outCurrency;
	}
	public void setOutCurrency(String outCurrency) {
		this.outCurrency = outCurrency;
	}
	public BigDecimal getOutRate() {
		return outRate;
	}
	public void setOutRate(BigDecimal outRate) {
		this.outRate = outRate;
	}
	public BigDecimal getOutMoney() {
		return outMoney;
	}
	public void setOutMoney(BigDecimal outMoney) {
		this.outMoney = outMoney;
	}
	public BigDecimal getHandlingFee() {
		return handlingFee;
	}
	public void setHandlingFee(BigDecimal handlingFee) {
		this.handlingFee = handlingFee;
	}
	public String getInCompanyCode() {
		return inCompanyCode;
	}
	public void setInCompanyCode(String inCompanyCode) {
		this.inCompanyCode = inCompanyCode;
	}
	public String getInUserCode() {
		return inUserCode;
	}
	public void setInUserCode(String inUserCode) {
		this.inUserCode = inUserCode;
	}
	public String getInCurrency() {
		return inCurrency;
	}
	public void setInCurrency(String inCurrency) {
		this.inCurrency = inCurrency;
	}
	public BigDecimal getInRate() {
		return inRate;
	}
	public void setInRate(BigDecimal inRate) {
		this.inRate = inRate;
	}
	public BigDecimal getInMoney() {
		return inMoney;
	}
	public void setInMoney(BigDecimal inMoney) {
		this.inMoney = inMoney;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCreatorCode() {
		return creatorCode;
	}
	public void setCreatorCode(String creatorCode) {
		this.creatorCode = creatorCode;
	}
	public String getCheckerCode() {
		return checkerCode;
	}
	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}
	public LocalDateTime getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(LocalDateTime checkTime) {
		this.checkTime = checkTime;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getTransType() {
		return transType;
	}
	public void setTransType(String transType) {
		this.transType = transType;
	}
	public String getCancelCode() {
		return cancelCode;
	}
	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}
	public LocalDateTime getCancelTime() {
		return cancelTime;
	}
	public void setCancelTime(LocalDateTime cancelTime) {
		this.cancelTime = cancelTime;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getTransNo() {
		return transNo;
	}
	public void setTransNo(String transNo) {
		this.transNo = transNo;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getOutAgentNo() {
		return outAgentNo;
	}
	public void setOutAgentNo(String outAgentNo) {
		this.outAgentNo = outAgentNo;
	}
	public String getInAgentNo() {
		return inAgentNo;
	}
	public void setInAgentNo(String inAgentNo) {
		this.inAgentNo = inAgentNo;
	}
	public String getAgentMemo() {
		return agentMemo;
	}
	public void setAgentMemo(String agentMemo) {
		this.agentMemo = agentMemo;
	}
	public String getOutUserName() {
		return outUserName;
	}
	public void setOutUserName(String outUserName) {
		this.outUserName = outUserName;
	}
	public String getInUserName() {
		return inUserName;
	}
	public void setInUserName(String inUserName) {
		this.inUserName = inUserName;
	}
}
