package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月06日
 * @time 下午14:27:30
 */

@ClientValidated
@ApiModel(value="AgentMemUpdateDTO", description="代办处传输对象")
public class AgentMemUpdateDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	
	@ApiModelProperty(value="记录ID",required=false)
	@NotNull(groups = ValidateGroup.first.class)
    private Long id;
	
    @ApiModelProperty(value="区域/分公司", required= false,dataType="String")
    private String companycode;
    
    @ApiModelProperty(value="原代办处编号", required= false,dataType="String")
    private String agentNo;
    
    @ApiModelProperty(value="经销商编号（需要的经销商网络的根节点）", required= false)
    private String[] memberNo;
    
    @ApiModelProperty(value="目标代办处编号", required= false,dataType="String")
    private String toAgentNo;
    
    @ApiModelProperty(value="单据状态:0=新增1=审核",required=false,dataType="String")
    private String receiptstatus;
    
    @ApiModelProperty(value="单据类型:0.经销商所属代办处更改1.代办处下属经销商转移2.mi.chAgent.linkNet",required=false)
    private String receipttype;
    
    @ApiModelProperty(value="备注",required=false,dataType="String")
    private String remark;
    
    @ApiModelProperty(value="开始创建日期",required=false,dataType="String")
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minOrderdate;
    
    @ApiModelProperty(value="结束创建日期",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxOrderdate;
    
    @ApiModelProperty(value="开始审核日期",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minOrdercheckdate;
    
    @ApiModelProperty(value="结束审核日期",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxOrdercheckdate;
    
    @ApiModelProperty(value="创建人",required=false,dataType="String")
    private String orderUser;
    
    @ApiModelProperty(value="审核人",required=false,dataType="String")
    private String orderChecker;
    
    @ApiModelProperty(value="批量审核、删除id", required= false)
    private List<Long> idList;
    
    @ApiModelProperty(value="删除角色",required=false,dataType="String")
    private String delRoles;
    
    @ApiModelProperty(value="添加角色",required=false,dataType="String")
    private String addRoles;

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCompanycode() {
		return companycode;
	}

	public void setCompanycode(String companycode) {
		this.companycode = companycode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String[] getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String[] memberNo) {
		this.memberNo = memberNo;
	}

	public String getToAgentNo() {
		return toAgentNo;
	}

	public void setToAgentNo(String toAgentNo) {
		this.toAgentNo = toAgentNo;
	}

	public String getReceiptstatus() {
		return receiptstatus;
	}

	public void setReceiptstatus(String receiptstatus) {
		this.receiptstatus = receiptstatus;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public LocalDateTime getMinOrderdate() {
		return minOrderdate;
	}

	public void setMinOrderdate(LocalDateTime minOrderdate) {
		this.minOrderdate = minOrderdate;
	}

	public LocalDateTime getMaxOrderdate() {
		return maxOrderdate;
	}

	public void setMaxOrderdate(LocalDateTime maxOrderdate) {
		this.maxOrderdate = maxOrderdate;
	}

	public LocalDateTime getMinOrdercheckdate() {
		return minOrdercheckdate;
	}

	public void setMinOrdercheckdate(LocalDateTime minOrdercheckdate) {
		this.minOrdercheckdate = minOrdercheckdate;
	}

	public LocalDateTime getMaxOrdercheckdate() {
		return maxOrdercheckdate;
	}

	public void setMaxOrdercheckdate(LocalDateTime maxOrdercheckdate) {
		this.maxOrdercheckdate = maxOrdercheckdate;
	}

	public String getOrderUser() {
		return orderUser;
	}

	public void setOrderUser(String orderUser) {
		this.orderUser = orderUser;
	}

	public String getOrderChecker() {
		return orderChecker;
	}

	public void setOrderChecker(String orderChecker) {
		this.orderChecker = orderChecker;
	}

	public List<Long> getIdList() {
		return idList;
	}

	public void setIdList(List<Long> idList) {
		this.idList = idList;
	}

	public String getReceipttype() {
		return receipttype;
	}

	public void setReceipttype(String receipttype) {
		this.receipttype = receipttype;
	}

	public String getDelRoles() {
		return delRoles;
	}

	public void setDelRoles(String delRoles) {
		this.delRoles = delRoles;
	}

	public String getAddRoles() {
		return addRoles;
	}

	public void setAddRoles(String addRoles) {
		this.addRoles = addRoles;
	}
	
}
