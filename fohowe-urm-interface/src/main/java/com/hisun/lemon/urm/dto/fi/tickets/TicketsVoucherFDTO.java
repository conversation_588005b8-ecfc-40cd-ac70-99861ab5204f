package com.hisun.lemon.urm.dto.fi.tickets;

import java.util.List;

public class TicketsVoucherFDTO {
    private Integer pageNum = 1;
    private Integer pageSize = 20;
    private String teacherNo;
    private String companyCode;
    private String ticketsNo;
    private Integer vcStatus;
    private String meetingNo;
    private String ficheckeStatus;
    private String ticketsStatus;
    private Integer startSn;
    private Integer endSn;
    private List<String> teachers;
    
    public String getTeacherNo() {
        return teacherNo;
    }

    public void setTeacherNo(String teacherNo) {
        this.teacherNo = teacherNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getTicketsNo() {
        return ticketsNo;
    }

    public void setTicketsNo(String ticketsNo) {
        this.ticketsNo = ticketsNo;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo;
    }

    public String getFicheckeStatus() {
        return ficheckeStatus;
    }

    public void setFicheckeStatus(String ficheckeStatus) {
        this.ficheckeStatus = ficheckeStatus;
    }

    public String getTicketsStatus() {
		return ticketsStatus;
	}

	public void setTicketsStatus(String ticketsStatus) {
		this.ticketsStatus = ticketsStatus;
	}

	public Integer getStartSn() {
		return startSn;
	}

	public void setStartSn(Integer startSn) {
		this.startSn = startSn;
	}

	public Integer getEndSn() {
		return endSn;
	}

	public void setEndSn(Integer endSn) {
		this.endSn = endSn;
	}

	public List<String> getTeachers() {
		return teachers;
	}

	public void setTeachers(List<String> teachers) {
		this.teachers = teachers;
	}

	public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
