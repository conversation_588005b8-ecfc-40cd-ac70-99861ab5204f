package com.hisun.lemon.urm.dto.mi.member;

import java.math.BigDecimal;

public class RightPvBean{
		private BigDecimal pv;
		private BigDecimal pvLimit;
		private BigDecimal regPv;
		private String left;
		private String right;
		private String parent;
		private String index;
		private String rightNo;
		
		public BigDecimal getPv() {
			return pv;
		}
		public void setPv(BigDecimal pv) {
			this.pv = pv;
		}
		public BigDecimal getPvLimit() {
			return pvLimit;
		}
		public void setPvLimit(BigDecimal pvLimit) {
			this.pvLimit = pvLimit;
		}
		public BigDecimal getRegPv() {
			return regPv;
		}
		public void setRegPv(BigDecimal regPv) {
			this.regPv = regPv;
		}
		public String getLeft() {
			return left;
		}
		public void setLeft(String left) {
			this.left = left;
		}
		public String getRight() {
			return right;
		}
		public void setRight(String right) {
			this.right = right;
		}
		public String getParent() {
			return parent;
		}
		public void setParent(String parent) {
			this.parent = parent;
		}
		public String getIndex() {
			return index;
		}
		public void setIndex(String index) {
			this.index = index;
		}
		public String getRightNo() {
			return rightNo;
		}
		public void setRightNo(String rightNo) {
			this.rightNo = rightNo;
		}
		
		
	}