package com.hisun.lemon.urm.dto.fi.tickets;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ClientValidated
@ApiModel(value="TicketsMeetingDTO", description="转账对象")
public class TicketsMeetingDTO extends GenericDTO<NoBody> {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private TicketsMeetingBean mainInfo = new TicketsMeetingBean();
	
	private Integer planYear;
	private Integer actalYear;
	private Integer planYearMonth;
	private Integer actalYearMonth;
	
	private String[] idArr;
	
	private Integer[] vcStatuArr;
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public TicketsMeetingBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(TicketsMeetingBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	
	public String[] getIdArr() {
		return idArr;
	}
	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}
	
	public Integer[] getVcStatuArr() {
		return vcStatuArr;
	}
	public void setVcStatuArr(Integer[] vcStatuArr) {
		this.vcStatuArr = vcStatuArr;
	}
	public Integer getPlanYearMonth() {
		return planYearMonth;
	}
	public void setPlanYearMonth(Integer planYearMonth) {
		this.planYearMonth = planYearMonth;
	}
	public Integer getActalYearMonth() {
		return actalYearMonth;
	}
	public void setActalYearMonth(Integer actalYearMonth) {
		this.actalYearMonth = actalYearMonth;
	}
	public Integer getPlanYear() {
		return planYear;
	}
	public void setPlanYear(Integer planYear) {
		this.planYear = planYear;
	}
	public Integer getActalYear() {
		return actalYear;
	}
	public void setActalYear(Integer actalYear) {
		this.actalYear = actalYear;
	}
 
}
