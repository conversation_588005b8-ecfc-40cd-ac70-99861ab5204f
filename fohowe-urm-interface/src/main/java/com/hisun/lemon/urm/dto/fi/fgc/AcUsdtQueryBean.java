 package com.hisun.lemon.urm.dto.fi.fgc;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
/**
 * AcInputQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月14号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcUsdtQueryBean", description="账户传输对象")
public class AcUsdtQueryBean{
	
	private Long id;

	private String periodWeek;

	private String orderNo;

	private String companyCode;

	private String agentNo;

	private String userCode;

	private String serial;

	private String usdtCode;

	private String coinType;

	private String transferType;

	private BigDecimal transferAmount;

	private String localCurrency;

	private BigDecimal localMoney;

	private BigDecimal rate;

	private BigDecimal fdMoney;

	private BigDecimal rewardRate;

	private String comments;

	private String transferStatus;
	
	private String status;

	private LocalDateTime transferTime;

	private BigDecimal transactionFee;

	private String payee;

	private String dataType;

	private String address;

	private String stockType;

	private String createrCode;

	private String createrName;

	private LocalDateTime createTime;

	private String checkerCode;

	private String checkerName;

	private LocalDateTime checkeTime;

	private String checkType;

	private LocalDateTime dealDate;

	private String remark;

	private LocalDateTime cancelTime;

	private String cancelCode;

	private String requestId;
 
    private String fiCheckStatus;
   
    private LocalDateTime ficheckeTime;
  
    private String ficheckerCode;
 
    private String memo;
    
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getPeriodWeek() {
		return periodWeek;
	}

	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getSerial() {
		return serial;
	}

	public void setSerial(String serial) {
		this.serial = serial;
	}

	public String getUsdtCode() {
		return usdtCode;
	}

	public void setUsdtCode(String usdtCode) {
		this.usdtCode = usdtCode;
	}

	public String getCoinType() {
		return coinType;
	}

	public void setCoinType(String coinType) {
		this.coinType = coinType;
	}

	public String getTransferType() {
		return transferType;
	}

	public void setTransferType(String transferType) {
		this.transferType = transferType;
	}

	public BigDecimal getTransferAmount() {
		return transferAmount;
	}

	public void setTransferAmount(BigDecimal transferAmount) {
		this.transferAmount = transferAmount;
	}

	public String getLocalCurrency() {
		return localCurrency;
	}

	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}

	public BigDecimal getLocalMoney() {
		return localMoney;
	}

	public void setLocalMoney(BigDecimal localMoney) {
		this.localMoney = localMoney;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public BigDecimal getFdMoney() {
		return fdMoney;
	}

	public void setFdMoney(BigDecimal fdMoney) {
		this.fdMoney = fdMoney;
	}

	public BigDecimal getRewardRate() {
		return rewardRate;
	}

	public void setRewardRate(BigDecimal rewardRate) {
		this.rewardRate = rewardRate;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getTransferStatus() {
		return transferStatus;
	}

	public void setTransferStatus(String transferStatus) {
		this.transferStatus = transferStatus;
	}

	public LocalDateTime getTransferTime() {
		return transferTime;
	}

	public void setTransferTime(LocalDateTime transferTime) {
		this.transferTime = transferTime;
	}

	public BigDecimal getTransactionFee() {
		return transactionFee;
	}

	public void setTransactionFee(BigDecimal transactionFee) {
		this.transactionFee = transactionFee;
	}

	public String getPayee() {
		return payee;
	}

	public void setPayee(String payee) {
		this.payee = payee;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getStockType() {
		return stockType;
	}

	public void setStockType(String stockType) {
		this.stockType = stockType;
	}

	public String getCreaterCode() {
		return createrCode;
	}

	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getCheckerCode() {
		return checkerCode;
	}

	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}

	public String getCheckerName() {
		return checkerName;
	}

	public void setCheckerName(String checkerName) {
		this.checkerName = checkerName;
	}

	public LocalDateTime getCheckeTime() {
		return checkeTime;
	}

	public void setCheckeTime(LocalDateTime checkeTime) {
		this.checkeTime = checkeTime;
	}

	public String getCheckType() {
		return checkType;
	}

	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}

	public LocalDateTime getDealDate() {
		return dealDate;
	}

	public void setDealDate(LocalDateTime dealDate) {
		this.dealDate = dealDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public LocalDateTime getCancelTime() {
		return cancelTime;
	}

	public void setCancelTime(LocalDateTime cancelTime) {
		this.cancelTime = cancelTime;
	}

	public String getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getFiCheckStatus() {
		return fiCheckStatus;
	}

	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}

	public LocalDateTime getFicheckeTime() {
		return ficheckeTime;
	}

	public void setFicheckeTime(LocalDateTime ficheckeTime) {
		this.ficheckeTime = ficheckeTime;
	}

	public String getFicheckerCode() {
		return ficheckerCode;
	}

	public void setFicheckerCode(String ficheckerCode) {
		this.ficheckerCode = ficheckerCode;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}
}
