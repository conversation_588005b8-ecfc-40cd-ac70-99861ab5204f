package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.format.annotation.DateTimeFormat;

import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.DateConstant;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月02日
 * @time 下午14:27:30
 */

@ClientValidated
@ApiModel(value="AgentBean", description="代办处传输对象")
public class AgentBean{
	/*==========基本信息============*/
	@ApiModelProperty(value="记录ID",required=false)
	@NotNull(message="URM10005",groups = { ValidateGroup.second.class })
    private Long id;
	@ApiModelProperty(value="分公司编号",required=false,dataType="String")
	@NotBlank(message="URM10005",groups = { ValidateGroup.first.class })
    private String companyCode;
	@ApiModelProperty(value="代办处编号",required=false,dataType="String")
	@NotBlank(message="URM10005",groups = { ValidateGroup.first.class })
    private String agentNo;
	@ApiModelProperty(value="负责人编号",required=false,dataType="String")
    private String recommendNo;
	@ApiModelProperty(value="姓名",required=false,dataType="String")
	@NotBlank(message="URM10005",groups = { ValidateGroup.first.class })
    private String name;
	@ApiModelProperty(value="昵称",required=false,dataType="String")
    private String petName;
	@ApiModelProperty(value="性别：M男W女",required=false)
    private String sex;
	@ApiModelProperty(value="生日(yyyy-MM-dd)",required=false)
	@DateTimeFormat(pattern=DateConstant.STR1_YMD)
    private Date birthday;
	@ApiModelProperty(value="证件类型:1身份证2护照",required=false)
    private String paperType;
	@ApiModelProperty(value="证件号",required=false,dataType="String")
    private String paperNo;
    /*==========联系信息============*/
	@ApiModelProperty(value="店铺名称",required=false,dataType="String")
    private String storeName;
	@ApiModelProperty(value="店铺地址",required=false,dataType="String")
	@NotEmpty(message="URM10005",groups = { ValidateGroup.first.class })
    private String storeAddr;
	@ApiModelProperty(value="邮编",required=false,dataType="String")
    private String storePost;
	@ApiModelProperty(value="办公电话",required=false,dataType="String")
    private String officeTel;
	@ApiModelProperty(value="手机",required=false,dataType="String")
	@NotEmpty(message="URM10005",groups = { ValidateGroup.first.class })
    private String mobile;
	@ApiModelProperty(value="传真",required=false,dataType="String")
    private String fax;
	@ApiModelProperty(value="电子邮箱",required=false,dataType="String")
    private String email;
    /*==========银行账户信息============*/
	@ApiModelProperty(value="开户银行",required=false,dataType="String")
    private String accountBank;
	@ApiModelProperty(value="银行帐号",required=false,dataType="String")
    private String accountCode;
	@ApiModelProperty(value="开户名",required=false,dataType="String")
    private String accountName;
    /*==========权限信息============*/
	@ApiModelProperty(value="登录状态(1已限制0未限制)",required=false)
	private String loginStatus;
    /*==========密码设置============*/
	@ApiModelProperty(value="登录名",required=false,dataType="String")
	@NotEmpty(message="URM10005",groups = { ValidateGroup.first.class })
    private String loginName;
	@ApiModelProperty(value="设置密码",required=false,dataType="String")
	@NotEmpty(message="URM10005",groups = { ValidateGroup.first.class })
    private String password;
	@ApiModelProperty(value="重复密码",required=false,dataType="String")
	@NotEmpty(message="URM10005",groups = { ValidateGroup.first.class })
    private String repeatPassword;
	@ApiModelProperty(value="默认语言",required=false,dataType="String")
	@NotBlank(message="URM10005",groups = { ValidateGroup.first.class })
    private String defaultlanuage;
    /*==========其他信息============*/ 
	@ApiModelProperty(value="加入期数",required=false,dataType="String")
    private String startWeek;
	
	@ApiModelProperty(value="备注",required=false,dataType="String")
    private String remark;
	
	@ApiModelProperty(value="附件",required=false,dataType="String")
	private String attachmentURL;
	
	@ApiModelProperty(value="上级代办处",required=false,dataType="String")
    private String parentNo;
    
    @ApiModelProperty(value="代办处级别",required=false,dataType="Integer")
    private Integer levelType;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getRecommendNo() {
		return recommendNo;
	}
	public void setRecommendNo(String recommendNo) {
		this.recommendNo = recommendNo;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getPetName() {
		return petName;
	}
	public void setPetName(String petName) {
		this.petName = petName;
	}
	public Date getBirthday() {
		return birthday;
	}
	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}
	public String getPaperNo() {
		return paperNo;
	}
	public void setPaperNo(String paperNo) {
		this.paperNo = paperNo;
	}
	public String getStoreName() {
		return storeName;
	}
	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}
	public String getStoreAddr() {
		return storeAddr;
	}
	public void setStoreAddr(String storeAddr) {
		this.storeAddr = storeAddr;
	}
	public String getStorePost() {
		return storePost;
	}
	public void setStorePost(String storePost) {
		this.storePost = storePost;
	}
	public String getOfficeTel() {
		return officeTel;
	}
	public void setOfficeTel(String officeTel) {
		this.officeTel = officeTel;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getFax() {
		return fax;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getAccountBank() {
		return accountBank;
	}
	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}
	public String getAccountCode() {
		return accountCode;
	}
	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getRepeatPassword() {
		return repeatPassword;
	}
	public void setRepeatPassword(String repeatPassword) {
		this.repeatPassword = repeatPassword;
	}
	public String getDefaultlanuage() {
		return defaultlanuage;
	}
	public void setDefaultlanuage(String defaultlanuage) {
		this.defaultlanuage = defaultlanuage;
	}
	public String getStartWeek() {
		return startWeek;
	}
	public void setStartWeek(String startWeek) {
		this.startWeek = startWeek;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getAttachmentURL() {
		return attachmentURL;
	}
	public void setAttachmentURL(String attachmentURL) {
		this.attachmentURL = attachmentURL;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getPaperType() {
		return paperType;
	}
	public void setPaperType(String paperType) {
		this.paperType = paperType;
	}
	public String getLoginStatus() {
		return loginStatus;
	}
	public void setLoginStatus(String loginStatus) {
		this.loginStatus = loginStatus;
	}
	public String getParentNo() {
		return parentNo;
	}
	public void setParentNo(String parentNo) {
		this.parentNo = parentNo;
	}
	public Integer getLevelType() {
		return levelType;
	}
	public void setLevelType(Integer levelType) {
		this.levelType = levelType;
	}
	
}
