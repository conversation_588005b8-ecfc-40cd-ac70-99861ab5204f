package com.hisun.lemon.urm.dto.al;

import java.util.List;

import com.hisun.lemon.urm.dto.mi.agent.AgentCompany;

/**
 * 
 * @ClassName: CurrencyCompanyQueryListRspDTO 
 * @Description: 基本资料管理-货币管理查询返回分公司和可用货币列表
 * @author: tian
 * @date: 2017年11月8日 下午4:39:53
 */
public class CurrencyCompanyQueryListRspDTO {
    private String currencyCode;
    private String currencyName;
    private List<CurrencyCompanyQueryRspDTO> currencyList;
    private List<AgentCompany> agentList;

    public List<CurrencyCompanyQueryRspDTO> getCurrencyList() {
        return currencyList;
    }

    public void setCurrencyList(List<CurrencyCompanyQueryRspDTO> currencyList) {
        this.currencyList = currencyList;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public List<AgentCompany> getAgentList() {
        return agentList;
    }

    public void setAgentList(List<AgentCompany> agentList) {
        this.agentList = agentList;
    }
}
