package com.hisun.lemon.urm.dto.al;

import java.util.List;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 语言管理-语言维护
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午2:21:33
 *
 */
public class RateAdjustModifyReqDTO {

    @ApiModelProperty(name = "rateCode", value = "调整单编号", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class })
    private String rateCode;
    
    @ApiModelProperty(name = "isBalance", value = "是否更新账户余额(0-否，1-是)", required = false, dataType = "String")
    private String isBalance;

    @ApiModelProperty(name = "rateAdjustList", value = "汇率调整清单", required = false, dataType = "list")
    private List<CurrencyRateDTO> rateAdjustList;

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public String getIsBalance() {
        return isBalance;
    }

    public void setIsBalance(String isBalance) {
        this.isBalance = isBalance;
    }

    public List<CurrencyRateDTO> getRateAdjustList() {
        return rateAdjustList;
    }

    public void setRateAdjustList(List<CurrencyRateDTO> rateAdjustList) {
        this.rateAdjustList = rateAdjustList;
    }

}
