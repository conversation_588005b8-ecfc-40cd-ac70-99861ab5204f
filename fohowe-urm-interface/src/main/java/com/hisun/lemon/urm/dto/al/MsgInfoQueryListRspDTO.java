package com.hisun.lemon.urm.dto.al;

import java.util.List;

import com.hisun.lemon.urm.common.PageInfo;

/**
 * 语言管理-语言维护查询返回list
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午4:36:43
 *
 */
public class MsgInfoQueryListRspDTO extends PageInfo{
    /**
     * 语言编码列表
     */
    private List<MsgInfoQueryRspDTO> charValueList;
    private boolean isExists;

    public List<MsgInfoQueryRspDTO> getCharValueList() {
        return charValueList;
    }

    public void setCharValueList(List<MsgInfoQueryRspDTO> charValueList) {
        this.charValueList = charValueList;
    }

    public boolean getIsExists() {
        return isExists;
    }

    public void setIsExists(boolean isExists) {
        this.isExists = isExists;
    }
}
