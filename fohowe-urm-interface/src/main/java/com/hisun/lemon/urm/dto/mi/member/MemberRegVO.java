package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

/**
 * MemberRegVO 传输对象
 * <AUTHOR>
 * @date 2017年11月1日
 * @time 上午9:27:30
 *
 */

@ClientValidated
@ApiModel(value="MemberRegVO", description="经销商注册返回对象")
public class MemberRegVO{
	@ApiModelProperty(value="查询结果集")
	List<RightSimpleBean> rightList;

	@ApiModelProperty(value = "经销商编号", dataType = "String")
	private String memberNo;
	
	public List<RightSimpleBean> getRightList() {
		return rightList;
	}

	public void setRightList(List<RightSimpleBean> rightList) {
		this.rightList = rightList;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}
	
}
