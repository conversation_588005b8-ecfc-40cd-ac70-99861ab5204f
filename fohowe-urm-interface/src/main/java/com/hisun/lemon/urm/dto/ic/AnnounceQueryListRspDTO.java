package com.hisun.lemon.urm.dto.ic;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.urm.common.PageInfo;

/**
 * 信息中心-公告查询返回DTO
 * 
 * <AUTHOR>
 * @date 2017年11月1日
 * @time 下午4:52:37
 *
 */
public class AnnounceQueryListRspDTO extends PageInfo {
    /**
     * 公告列表
     */
    private List<AnnounceQueryRspDTO> announceList;
    private Map<String, String> typeEnum;

    private Map<Object, String> userTypeEnum;

    private Map<Object, String> targetTerminalEnum;

    public List<AnnounceQueryRspDTO> getAnnounceList() {
        return announceList;
    }

    public void setAnnounceList(List<AnnounceQueryRspDTO> announceList) {
        this.announceList = announceList;
    }

    public Map<String, String> getTypeEnum() {
        return typeEnum;
    }

    public void setTypeEnum(Map<String, String> typeEnum) {
        this.typeEnum = typeEnum;
    }

    public Map<Object, String> getUserTypeEnum() {
        return userTypeEnum;
    }

    public void setUserTypeEnum(Map<Object, String> userTypeEnum) {
        this.userTypeEnum = userTypeEnum;
    }

    public Map<Object, String> getTargetTerminalEnum() {
        return targetTerminalEnum;
    }

    public void setTargetTerminalEnum(Map<Object, String> targetTerminalEnum) {
        this.targetTerminalEnum = targetTerminalEnum;
    }

}
