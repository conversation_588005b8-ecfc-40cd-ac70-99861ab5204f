package com.hisun.lemon.urm.dto.ic;

import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-文件管理查询
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 上午11:36:16
 *
 */
public class FileQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(name = "companyCode", value = "公司编号", required = false, dataType = "String")
    private String companyCode;

    @ApiModelProperty(name = "fileDesc", value = "文件标题", required = false, dataType = "String")
    private String fileDesc;

    @ApiModelProperty(name = "langId", value = "语言编码ID", required = false, dataType = "String")
    private String langId;
    
    @ApiModelProperty(name = "beginDate", value = "查询开始日期", required = false, dataType = "LocalDateTime")
    private LocalDateTime beginDate;

    @ApiModelProperty(name = "endDate", value = "查询结束日期", required = false, dataType = "LocalDateTime")
    private LocalDateTime endDate;

    @ApiModelProperty(name = "pageNum", value = "页码", required = true, dataType = "Integer")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量", required = true, dataType = "Integer")
    private int pageSize;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getFileDesc() {
        return fileDesc;
    }

    public void setFileDesc(String fileDesc) {
        this.fileDesc = fileDesc;
    }

    public String getLangId() {
        return langId;
    }

    public void setLangId(String langId) {
        this.langId = langId;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
