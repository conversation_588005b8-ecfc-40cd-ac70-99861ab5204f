package com.hisun.lemon.urm.dto.fi.fgc;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ClientValidated
@ApiModel(value = "FgcOrderReqDTO", description = "fgc订单传输对象")
public class FgcReportReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "经销商编号")
    private String memberNo;

    @ApiModelProperty(value = "奖金制度")
    private String bonusType;

    @ApiModelProperty(value = "代办处编号")
    private String agentNo;
    
    @ApiModelProperty(value = "所属分公司")
    private String companyCode;

    @ApiModelProperty(value = "汇总类型 5:经销商 4:代办处，3 分公司")
    private String queryType;

    @ApiModelProperty(value = "提交起始期数", required = false)
    private Integer startWeek;

    @ApiModelProperty(value = "提交结束期数", required = false)
    private Integer endWeek;

    @ApiModelProperty(value = "分页查询-第几页", required = true)
    private Integer pageNum = 1;

    @ApiModelProperty(value = "分页查询-每页数据条数", required = true)
    private Integer pageSize = 20;

    @ApiModelProperty(value = "提交起始时间(yyyy-MM-dd HH:mm:ss)")
    private String startTime;
    
    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public Integer getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(Integer startWeek) {
        this.startWeek = startWeek;
    }

    public Integer getEndWeek() {
        return endWeek;
    }

    public void setEndWeek(Integer endWeek) {
        this.endWeek = endWeek;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	
	public String getQueryType() {
		return queryType;
	}

	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
    
}