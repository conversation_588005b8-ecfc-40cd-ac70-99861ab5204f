 package com.hisun.lemon.urm.dto.fi.balance;

import java.util.List;

import org.hibernate.validator.constraints.NotBlank;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="CheckAccountDTO", description="账户传输对象")
public class CheckAccountDTO extends GenericDTO<NoBody>{

	@ApiModelProperty(value="账户开通集合",required=false)
    private List<CheckAccountBean> openAccountList;

	public List<CheckAccountBean> getOpenAccountList() {
		return openAccountList;
	}

	public void setOpenAccountList(List<CheckAccountBean> openAccountList) {
		this.openAccountList = openAccountList;
	}
	
	
}
