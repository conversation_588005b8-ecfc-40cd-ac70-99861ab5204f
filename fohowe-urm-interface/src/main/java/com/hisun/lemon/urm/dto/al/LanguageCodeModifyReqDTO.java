package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 语言管理-语言编码管理
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午2:21:33
 *
 */
public class LanguageCodeModifyReqDTO {

    @ApiModelProperty(name = "id", value = "id")
    @NotNull(message = "URM10005", groups = { ValidateGroup.second.class, ValidateGroup.third.class })
    @Min(value = 0, message = "URM10006", groups = { ValidateGroup.second.class, ValidateGroup.third.class })
    private Long id;

    @ApiModelProperty(name = "langCode", value = "语言编码")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String langCode;

    @ApiModelProperty(name = "langName", value = "语言名称")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String langName;

    @ApiModelProperty(name = "allowedUser", value = "管理人员")
    @Length(max = 1000, message = "URM10001")
    private String allowedUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getLangName() {
        return langName;
    }

    public void setLangName(String langName) {
        this.langName = langName;
    }

    public String getAllowedUser() {
        return allowedUser;
    }

    public void setAllowedUser(String allowedUser) {
        this.allowedUser = allowedUser;
    }

}
