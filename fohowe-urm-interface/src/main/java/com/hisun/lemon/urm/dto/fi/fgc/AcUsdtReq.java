package com.hisun.lemon.urm.dto.fi.fgc;

public class AcUsdtReq {
	// c2c
	private String asset;//转账资产代币 默认USDT
	private String dataType;//回调数据类型 c2c是c2c转账记录，stock是现货转账记录
	private String comments;//备注
	private String payee;//接收方，收款账号
	private String payer;//发送方，付款账号
	private String transactionFee;///转账手续费
	private String transferAmount;//转账数量(金额)
	private String transferNo;//转账编号
	private String transferStatus;//转账状态 默认4 
	private String transferTime;//转账时间 长整数
	private String transferType;//转账类型 
	// 现货转账
	private String bnId;//转账编号
	private String status;//转账状态
	private String coin;//转账资产代币
	private String address;//发送或接收地址
	private String type;//1为充值，2为提现
	private String txId;//哈希值
	private String insertTime;//转账时间
	
	private String sign;//MD5签名
	
	
	public String getAsset() {
		return asset;
	}
	public void setAsset(String asset) {
		this.asset = asset;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getPayee() {
		return payee;
	}
	public void setPayee(String payee) {
		this.payee = payee;
	}
	public String getPayer() {
		return payer;
	}
	public void setPayer(String payer) {
		this.payer = payer;
	}
	public String getTransactionFee() {
		return transactionFee;
	}
	public void setTransactionFee(String transactionFee) {
		this.transactionFee = transactionFee;
	}
	public String getTransferAmount() {
		return transferAmount;
	}
	public void setTransferAmount(String transferAmount) {
		this.transferAmount = transferAmount;
	}
	public String getTransferNo() {
		return transferNo;
	}
	public void setTransferNo(String transferNo) {
		this.transferNo = transferNo;
	}
	public String getTransferStatus() {
		return transferStatus;
	}
	public void setTransferStatus(String transferStatus) {
		this.transferStatus = transferStatus;
	}
	public String getTransferTime() {
		return transferTime;
	}
	public void setTransferTime(String transferTime) {
		this.transferTime = transferTime;
	}
	public String getTransferType() {
		return transferType;
	}
	public void setTransferType(String transferType) {
		this.transferType = transferType;
	}
	public String getSign() {
		return sign;
	}
	public void setSign(String sign) {
		this.sign = sign;
	}
	
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	public String getBnId() {
		return bnId;
	}
	public void setBnId(String bnId) {
		this.bnId = bnId;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCoin() {
		return coin;
	}
	public void setCoin(String coin) {
		this.coin = coin;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getTxId() {
		return txId;
	}
	public void setTxId(String txId) {
		this.txId = txId;
	}
	public String getInsertTime() {
		return insertTime;
	}
	public void setInsertTime(String insertTime) {
		this.insertTime = insertTime;
	}
	@Override
	public String toString() {
		return "AcUsdtReq [asset=" + asset + ", dataType=" + dataType + ", comments=" + comments + ", payee=" + payee
				+ ", payer=" + payer + ", transactionFee=" + transactionFee + ", transferAmount=" + transferAmount
				+ ", transferNo=" + transferNo + ", transferStatus=" + transferStatus + ", transferTime=" + transferTime
				+ ", transferType=" + transferType + ", bnId=" + bnId + ", status=" + status + ", coin=" + coin
				+ ", address=" + address + ", type=" + type + ", txId=" + txId + ", insertTime=" + insertTime
				+ ", sign=" + sign + "]";
	}
 
}
