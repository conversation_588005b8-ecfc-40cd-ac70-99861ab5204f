package com.hisun.lemon.urm.dto.fi.balance;

import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceItem 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceItemDTO", description="账户明细传输对象")
public class AcBalanceItemDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private AcBalanceItemBean mainInfo = new AcBalanceItemBean();
	
	@ApiModelProperty(name="acType",value="账户类型:f$＝f$,fv＝fv,f0=f000,h0=h0000,fb=fb,pv=活跃pv\r\n" + 
			"p$=活跃p$,b1=重消分配,b2=旅游基金,b3=名车基金,b4=游艇基金,b5=住宅基金,b6=市场发展基金",required=false)
	private String acType;
	
	private String[] acTypes;
	
	@ApiModelProperty(value="经销商/代办处编号",required=false,dataType="String")
	private String userCode;
	
	@ApiModelProperty(value="交易类别，a01:充值;a02:提现;a03:兑换;a04:报单a05:提货;\r\n" + 
			"a06:奖金发放;a07:借款;a08:还款;a09转账;a10活跃扣款 \r\n" + 
			"a11点位合并转移 1x之后的界面定义扣补\r\n" + 
			"a21:代办处存款",required=false)
	private String orderType;
	
	@ApiModelProperty(value="开始创建日期")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minDealDate;
	
	@ApiModelProperty(value="结束创建日期")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxDealDate;
	
	@ApiModelProperty(value="备注",required=false,dataType="String")
	private String memo;
	
	@ApiModelProperty(value="关联单号",required=false,dataType="String")
    private String orderNo;
	
	@ApiModelProperty(value="代办处(用于查询活跃业绩)")
    private String agentNo;
	
	@ApiModelProperty(value = "起始期数", required = false)
    private Integer startWeek;
    
    @ApiModelProperty(value = "结束期数", required = false)
    private Integer endWeek;
    
    @ApiModelProperty(value = "年份", required = false)
    private Integer year;
    
    private String history;

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public AcBalanceItemBean getMainInfo() {
		return mainInfo;
	}

	public void setMainInfo(AcBalanceItemBean mainInfo) {
		this.mainInfo = mainInfo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public LocalDateTime getMinDealDate() {
		return minDealDate;
	}

	public void setMinDealDate(LocalDateTime minDealDate) {
		this.minDealDate = minDealDate;
	}

	public LocalDateTime getMaxDealDate() {
		return maxDealDate;
	}

	public void setMaxDealDate(LocalDateTime maxDealDate) {
		this.maxDealDate = maxDealDate;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public Integer getStartWeek() {
		return startWeek;
	}

	public void setStartWeek(Integer startWeek) {
		this.startWeek = startWeek;
	}

	public Integer getEndWeek() {
		return endWeek;
	}

	public void setEndWeek(Integer endWeek) {
		this.endWeek = endWeek;
	}

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	public String[] getAcTypes() {
		return acTypes;
	}

	public void setAcTypes(String[] acTypes) {
		this.acTypes = acTypes;
	}

	public String getHistory() {
		return history;
	}

	public void setHistory(String history) {
		this.history = history;
	}
	
}
