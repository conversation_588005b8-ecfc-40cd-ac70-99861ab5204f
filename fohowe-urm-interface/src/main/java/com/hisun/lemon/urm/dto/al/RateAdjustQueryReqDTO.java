package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @ClassName: RateAdjustQueryReqDTO 
 * @Description: 汇率调整查询请求DTO
 * @author: tian
 * @date: 2017年11月9日 下午4:18:32
 */
public class RateAdjustQueryReqDTO {

    @ApiModelProperty(name = "rateCode", value = "调整单编号", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.fourth.class })
    private String rateCode;

    @ApiModelProperty(name = "status", value = "审核状态 0-未审核 1-已审核", required = false, dataType = "String")
    private String status;

    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.third.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.third.class })
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.third.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.third.class })
    private int pageSize;

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
