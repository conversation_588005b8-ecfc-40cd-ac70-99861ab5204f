package com.hisun.lemon.urm.dto.fi.fgc;

import java.time.LocalDateTime;
import java.util.Arrays;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ClientValidated
@ApiModel(value="AcUsdtDTO", description="账户传输对象")
public class AcUsdtDTO extends GenericDTO<NoBody> {
	@ApiModelProperty(value="分页相关-页码")
	private int pageNum = 1;
	@ApiModelProperty(value="分页相关-每页数量")
	private int pageSize = 20;
	@ApiModelProperty(value="分页相关-总记录数",hidden=true)
	private int totalCount = 0;
    @ApiModelProperty(value="奖金制度")
    private String bonusType;
    @ApiModelProperty(value="区域")
    private String areaCode;
    @ApiModelProperty(value="所属分公司")
    private String companyCode;
    @ApiModelProperty(value="奖金制度数组")
    private String[] bonusTypes;
    @ApiModelProperty(value="区域数组")
    private String[] areaCodes;
    @ApiModelProperty(value="所属分公司数组")
    private String[] companyCodes;
    @ApiModelProperty(value="代办处")
    private String agentNo;
    @ApiModelProperty(value="转账单号")
    private String orderNo;
    @ApiModelProperty(value="财务确认 0 未确认，1 已确认")
    private String fiCheckStatus;
    
    @ApiModelProperty(value="奖金制度")
    private String[] bonusTypeArr;
    @ApiModelProperty(value="区域")
    private String[] areaCodeArr;
    @ApiModelProperty(value="所属分公司")
    private String[] companyCodeArr;
    @ApiModelProperty(value="状态:1:新单 2:分公司已审核 3:总公司已审核  8：已作废  6:已取消",required=false,dataType="String")
   	private String[] statusArr;
    
    @ApiModelProperty(value="USDT账号",required=false,dataType="String")
	private String usdtCode;
    
    @ApiModelProperty(value="经销商/代办处编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(value="账户类型",required=false,dataType="String")
	private String acType;
	@ApiModelProperty(value="方式,0=转入，1=手工，2=POS",required=false,dataType="String")
	private String tranType;
	@ApiModelProperty(value="c2c，stock",required=false,dataType="String")
	private String dataType;
	@ApiModelProperty(value="状态:0 待处理,1:已充值 2:退回 3:已退回",required=false,dataType="String")
	private String status;
	@ApiModelProperty(value="充值申请期数",required=false,dataType="String")
	private String periodWeek;
	
	@ApiModelProperty(value="开始加入期数（用于匹配条件查询）")
    private String minPeriodWeek;
    
    @ApiModelProperty(value="结束加入期数（用于匹配条件查询）")
    private String maxPeriodWeek;
    
    @ApiModelProperty(value="开始创建日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minCreateTime;
	@ApiModelProperty(value="结束创建日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxCreateTime;
	@ApiModelProperty(value="批量审核ID",required=false)
	private String[] idArr;
	
	
	public int getPageNum() {
		return pageNum;
	}
	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	public int getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String[] getBonusTypes() {
		return bonusTypes;
	}
	public void setBonusTypes(String[] bonusTypes) {
		this.bonusTypes = bonusTypes;
	}
	public String[] getAreaCodes() {
		return areaCodes;
	}
	public void setAreaCodes(String[] areaCodes) {
		this.areaCodes = areaCodes;
	}
	public String[] getCompanyCodes() {
		return companyCodes;
	}
	public void setCompanyCodes(String[] companyCodes) {
		this.companyCodes = companyCodes;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getUsdtCode() {
		return usdtCode;
	}
	public void setUsdtCode(String usdtCode) {
		this.usdtCode = usdtCode;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getTranType() {
		return tranType;
	}
	public void setTranType(String tranType) {
		this.tranType = tranType;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getMinPeriodWeek() {
		return minPeriodWeek;
	}
	public void setMinPeriodWeek(String minPeriodWeek) {
		this.minPeriodWeek = minPeriodWeek;
	}
	public String getMaxPeriodWeek() {
		return maxPeriodWeek;
	}
	public void setMaxPeriodWeek(String maxPeriodWeek) {
		this.maxPeriodWeek = maxPeriodWeek;
	}
	public LocalDateTime getMinCreateTime() {
		return minCreateTime;
	}
	public void setMinCreateTime(LocalDateTime minCreateTime) {
		this.minCreateTime = minCreateTime;
	}
	public LocalDateTime getMaxCreateTime() {
		return maxCreateTime;
	}
	public void setMaxCreateTime(LocalDateTime maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
	public String[] getIdArr() {
		return idArr;
	}
	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}
	
	public String[] getBonusTypeArr() {
		return bonusTypeArr;
	}
	public void setBonusTypeArr(String[] bonusTypeArr) {
		this.bonusTypeArr = bonusTypeArr;
	}
	public String[] getAreaCodeArr() {
		return areaCodeArr;
	}
	public void setAreaCodeArr(String[] areaCodeArr) {
		this.areaCodeArr = areaCodeArr;
	}
	public String[] getCompanyCodeArr() {
		return companyCodeArr;
	}
	public void setCompanyCodeArr(String[] companyCodeArr) {
		this.companyCodeArr = companyCodeArr;
	}
	public String[] getStatusArr() {
		return statusArr;
	}
	public void setStatusArr(String[] statusArr) {
		this.statusArr = statusArr;
	}
	
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	
	public String getFiCheckStatus() {
		return fiCheckStatus;
	}
	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}
	@Override
	public String toString() {
		return "AcUsdtDTO [pageNum=" + pageNum + ", pageSize=" + pageSize + ", totalCount=" + totalCount
				+ ", bonusType=" + bonusType + ", areaCode=" + areaCode + ", companyCode=" + companyCode
				+ ", bonusTypes=" + Arrays.toString(bonusTypes) + ", areaCodes=" + Arrays.toString(areaCodes)
				+ ", companyCodes=" + Arrays.toString(companyCodes) + ", agentNo=" + agentNo + ", usdtCode=" + usdtCode
				+ ", userCode=" + userCode + ", acType=" + acType + ", tranType=" + tranType + ", status=" + status
				+ ", periodWeek=" + periodWeek + ", minPeriodWeek=" + minPeriodWeek + ", maxPeriodWeek=" + maxPeriodWeek
				+ ", minCreateTime=" + minCreateTime + ", maxCreateTime=" + maxCreateTime + ", idArr="
				+ Arrays.toString(idArr) + "]";
	}
	
}
