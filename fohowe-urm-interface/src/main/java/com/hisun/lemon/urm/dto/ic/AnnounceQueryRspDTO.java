package com.hisun.lemon.urm.dto.ic;

import java.time.LocalDateTime;

import lombok.ToString;

@ToString
public class AnnounceQueryRspDTO {
    /**
     * @Fields announceId 公告ID
     */
    private String announceId;
    /**
     * @Fields title 公告标题
     */
    private String title;
    /**
     * @Fields creatorCode 创建人
     */
    private String creatorCode;
    /**
     * @Fields targetTerminal 目标终端 0-所有 1-网站 2-设备
     */
    private String targetTerminal;
    /**
     * @Fields type 类型TYPE 1公告 2新闻 3通知 4其他
     */
    private String type;
    /**
     * @Fields userType 发布对象 0公司用户 1经销商 2代办处
     */
    private String userType;
    /**
     * @Fields companyCode 公司编号，以','分隔
     */
    private String companyCode;
    /**
     * @Fields fromCompany 来源
     */
    private String fromCompany;
    /**
     * @Fields content 公告内容
     */
    private String content;
    private String langName;
    private String langId;
    private String langCode;
    private LocalDateTime createTime;
    private String hasLook;
    private String groupId;

    public String getAnnounceId() {
        return announceId;
    }

    public void setAnnounceId(String announceId) {
        this.announceId = announceId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getFromCompany() {
        return fromCompany;
    }

    public void setFromCompany(String fromCompany) {
        this.fromCompany = fromCompany;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getLangName() {
        return langName;
    }

    public void setLangName(String langName) {
        this.langName = langName;
    }

    public String getLangId() {
        return langId;
    }

    public void setLangId(String langId) {
        this.langId = langId;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getTargetTerminal() {
        return targetTerminal;
    }

    public void setTargetTerminal(String targetTerminal) {
        this.targetTerminal = targetTerminal;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getHasLook() {
        return hasLook;
    }

    public void setHasLook(String hasLook) {
        this.hasLook = hasLook;
    }

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
    
    
}
