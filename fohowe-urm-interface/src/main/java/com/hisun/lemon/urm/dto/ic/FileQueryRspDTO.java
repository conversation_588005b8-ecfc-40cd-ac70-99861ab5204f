package com.hisun.lemon.urm.dto.ic;

import java.time.LocalDateTime;

public class FileQueryRspDTO {
    /**
     * @Fields fileId 文件ID
     */
    private String fileId;
    /**
     * @Fields companyCode 公司编号 COMPANY_CODE
     */
    private String companyCode;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields fileName 文件名称 FILE_NAME
     */
    private String fileName;
    /**
     * @Fields downloadedTimes 下载次数 DOWNLOADED_TIMES
     */
    private Integer downloadedTimes;
    /**
     * @Fields agnetValid 0 不允许代办处使用 1允许代办处使用
     */
    private String agnetValid;
    /**
     * @Fields memberValid 0 不允许会员使用 1允许会员使用
     */
    private String memberValid;
    /**
     * @Fields tmSmp
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields fileDesc 文件标题
     */
    private String fileDesc;
    
    private String dfileId;
    
    private String id;
    
    private String url;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getDownloadedTimes() {
        return downloadedTimes;
    }

    public void setDownloadedTimes(Integer downloadedTimes) {
        this.downloadedTimes = downloadedTimes;
    }

    public String getAgnetValid() {
        return agnetValid;
    }

    public void setAgnetValid(String agnetValid) {
        this.agnetValid = agnetValid;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getMemberValid() {
        return memberValid;
    }

    public void setMemberValid(String memberValid) {
        this.memberValid = memberValid;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getFileDesc() {
        return fileDesc;
    }

    public void setFileDesc(String fileDesc) {
        this.fileDesc = fileDesc;
    }

    public String getDfileId() {
        return dfileId;
    }

    public void setDfileId(String dfileId) {
        this.dfileId = dfileId;
    }

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}
}
