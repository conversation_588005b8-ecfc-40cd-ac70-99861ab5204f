package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月2日
 * @time 下午14:27:30
 */

@ClientValidated
@ApiModel(value="IsAgentOrMember", description="代办处查询传输对象")
public class IsAgentOrMember{
	@ApiModelProperty(value="用户编号")
	private String userCode;
	@ApiModelProperty(value="用户类型")
	private String userType;
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
}
