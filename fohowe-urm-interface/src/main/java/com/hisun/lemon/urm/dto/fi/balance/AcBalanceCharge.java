package com.hisun.lemon.urm.dto.fi.balance;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;

@ApiModel(value="AcBalanceCharge", description="账户传输对象")
public class AcBalanceCharge {
	private String userCode;
	private String acType;
	private BigDecimal balance;
	private boolean isCanReduce;
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public boolean isCanReduce() {
		return isCanReduce;
	}
	public void setCanReduce(boolean isCanReduce) {
		this.isCanReduce = isCanReduce;
	}
	public BigDecimal getBalance() {
		return balance;
	}
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}
}
