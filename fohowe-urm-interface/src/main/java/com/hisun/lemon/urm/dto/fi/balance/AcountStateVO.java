package com.hisun.lemon.urm.dto.fi.balance;


import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.PageInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcountStateVO 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcountStateVO", description="账户统计报表传输对象")
public class AcountStateVO extends PageInfo{
	@ApiModelProperty(value="账户统计列表" )
    private List<AccountStateBean> acountStateBeanList;
	
	private String type;
	private String orderType;
	private String thePeriod;
	
	@ApiModelProperty(value="账户类型")
	Map acTypeKV=null;
	
	public List<AccountStateBean> getAcountStateBeanList() {
		return acountStateBeanList;
	}
	public void setAcountStateBeanList(List<AccountStateBean> acountStateBeanList) {
		this.acountStateBeanList = acountStateBeanList;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getThePeriod() {
		return thePeriod;
	}
	public void setThePeriod(String thePeriod) {
		this.thePeriod = thePeriod;
	}
	public Map getAcTypeKV() {
		return acTypeKV;
	}
	public void setAcTypeKV(Map acTypeKV) {
		this.acTypeKV = acTypeKV;
	}
	
}
