package com.hisun.lemon.urm.dto.bd;
import com.hisun.lemon.urm.common.PageInfo;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
public class BdTourismPromotionDTO extends PageInfo {
    @ApiModelProperty(name = "pageNum",value = "当前页数")
    private int pageNum = 1;
    @ApiModelProperty(name = "PageSize",value = "分页条数")
    private int pageSize = 20;
    //1是范围内，2是范围外
    private List<BdTourismPromotionEndBean> dtoList = new ArrayList<>();

    @Override
    public int getPageNum() {
        return pageNum;
    }

    @Override
    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    @Override
    public int getPageSize() {
        return pageSize;
    }

    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<BdTourismPromotionEndBean> getDtoList() {
        return dtoList;
    }

    public void setDtoList(List<BdTourismPromotionEndBean> dtoList) {
        this.dtoList = dtoList;
    }
}