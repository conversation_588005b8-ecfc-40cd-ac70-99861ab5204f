 package com.hisun.lemon.urm.dto.fi.change;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcChangeQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcChangeQueryBean", description="账户传输对象")
public class AcChangeQueryBean{
	/*=============base data============ */
	@ApiModelProperty(value="奖金制度",dataType="String")
    private String bonusType;
    @ApiModelProperty(value="区域",dataType="String")
    private String areaCode;
    @ApiModelProperty(value="所属分公司",dataType="String")
    private String companyCode;
    /*=============show data============ */
    @ApiModelProperty(value="记录编号")
    private long id;
    @ApiModelProperty(value="账户类型")
    private String acType;
    @ApiModelProperty(value="用户编号",dataType="String")
    private String userCode;
    @ApiModelProperty(value="代办处",dataType="String")
    private String agentNo;
    @ApiModelProperty(value="交易类别",required=false)
	private String orderType;
    @ApiModelProperty(value="余额变化",required=false,dataType="BigDecimal")
	private BigDecimal money;
    @ApiModelProperty(value="备注",required=false,dataType="String")
	private String memo;
    @ApiModelProperty(value="操作类型：1=存入(不判断余额),2=提取(判断余额)",required=false,dataType="String")
	private String changeType;
    @ApiModelProperty(value="状态，0新增，1审核  2发放  3 删除",required=false,dataType="String")
	private String status;
    @ApiModelProperty(value="审核时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkTime;
    @ApiModelProperty(value="创建时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getChangeType() {
		return changeType;
	}
	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public LocalDateTime getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(LocalDateTime checkTime) {
		this.checkTime = checkTime;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
}
