package com.hisun.lemon.urm.dto.fi.tickets;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

public class TicketsFinishMeetingReqDTO {
    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private int pageNum=1;
    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    private int pageSize=20;
    @ApiModelProperty(value = "会议编号")
    private String meetingNo;
    @ApiModelProperty(value = "会议名称")
    private String meetingTitle;
    @ApiModelProperty(value = "区域编码")
    private String areaCode;
    @ApiModelProperty(value = "参加的分公司")
    private String companyCode;
    @ApiModelProperty(value = "城市/代办处")
    private String city;
    @ApiModelProperty(value = "一级会议类型")
    private String actalFirstType;
    @ApiModelProperty(value = "预计的会议类型")
    private String planMeetingType;
    @ApiModelProperty(value = "会议类型")
    private String actalMeetingType;
    @ApiModelProperty(value = "现场成交量")
    private Integer dealQty;
    @ApiModelProperty(value = "定金")
    private BigDecimal deposit;
    @ApiModelProperty(value = "备注")
    private String memo;
    private String actualLecturer;
    
    private String actualTranslator;
    
    @ApiModelProperty(value = "审核时间")
    private String auditTime;
    
    @ApiModelProperty(value = "开始时间")
    private String heldDateStart;
    @ApiModelProperty(value = "结束时间")
    private String heldDateEnd;
    
    private Integer planYearMonth;
    private Integer actalYearMonth;

    private List<String> teachers;
    
    public String getActualLecturer() {
        return actualLecturer;
    }

    public void setActualLecturer(String actualLecturer) {
        this.actualLecturer = actualLecturer;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo;
    }

    public String getMeetingTitle() {
        return meetingTitle;
    }

    public void setMeetingTitle(String meetingTitle) {
        this.meetingTitle = meetingTitle;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPlanMeetingType() {
        return planMeetingType;
    }

    public void setPlanMeetingType(String planMeetingType) {
        this.planMeetingType = planMeetingType;
    }

    public Integer getDealQty() {
        return dealQty;
    }

    public void setDealQty(Integer dealQty) {
        this.dealQty = dealQty;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

	public String getActalFirstType() {
		return actalFirstType;
	}

	public void setActalFirstType(String actalFirstType) {
		this.actalFirstType = actalFirstType;
	}

	public Integer getPlanYearMonth() {
		return planYearMonth;
	}

	public void setPlanYearMonth(Integer planYearMonth) {
		this.planYearMonth = planYearMonth;
	}

	public Integer getActalYearMonth() {
		return actalYearMonth;
	}

	public void setActalYearMonth(Integer actalYearMonth) {
		this.actalYearMonth = actalYearMonth;
	}

	public String getActualTranslator() {
		return actualTranslator;
	}

	public void setActualTranslator(String actualTranslator) {
		this.actualTranslator = actualTranslator;
	}

	public List<String> getTeachers() {
		return teachers;
	}

	public void setTeachers(List<String> teachers) {
		this.teachers = teachers;
	}

	public String getHeldDateStart() {
		return heldDateStart;
	}

	public void setHeldDateStart(String heldDateStart) {
		this.heldDateStart = heldDateStart;
	}

	public String getHeldDateEnd() {
		return heldDateEnd;
	}

	public void setHeldDateEnd(String heldDateEnd) {
		this.heldDateEnd = heldDateEnd;
	}

	public String getActalMeetingType() {
		return actalMeetingType;
	}

	public void setActalMeetingType(String actalMeetingType) {
		this.actalMeetingType = actalMeetingType;
	}
    
}
