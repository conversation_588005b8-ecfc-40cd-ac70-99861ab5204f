package com.hisun.lemon.urm.dto.al;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * 语言管理-语言维护
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午2:21:33
 *
 */
public class MsgInfoModifyReqDTO {

    @ApiModelProperty(name = "msgCd", value = "字符键值（多条记录时以','分隔）")
    private String msgCd;

    @ApiModelProperty(name = "language", value = "语言编码")
    private String language;
    
    @ApiModelProperty(name = "msgInfo", value = "字符键值描述")
    private String msgInfo;

    @ApiModelProperty(name = "charValueList", value = "语言编码与显示文字list")
    private List<MsgInfoQueryRspDTO> charValueList;

	public String getMsgCd() {
		return msgCd;
	}

	public void setMsgCd(String msgCd) {
		this.msgCd = msgCd;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getMsgInfo() {
		return msgInfo;
	}

	public void setMsgInfo(String msgInfo) {
		this.msgInfo = msgInfo;
	}

	public List<MsgInfoQueryRspDTO> getCharValueList() {
		return charValueList;
	}

	public void setCharValueList(List<MsgInfoQueryRspDTO> charValueList) {
		this.charValueList = charValueList;
	}
     
}
