 package com.hisun.lemon.urm.dto.fi.appl;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcApplQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月16号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcApplQueryBean", description="账户传输对象")
public class AcApplQueryBean{
	/*=============base data============ */
	@ApiModelProperty(value="奖金制度",dataType="String")
    private String bonusType;
    @ApiModelProperty(value="区域",dataType="String")
    private String areaCode;
    @ApiModelProperty(value="所属分公司",dataType="String")
    private String companyCode;
    /*=============show data============ */
    @ApiModelProperty(name="id",value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(value="申请单号",required=false,dataType="String")
    private String applNo;//AL2017070700002
	@ApiModelProperty(value="用户编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(value="申领金额",required=false,dataType="BigDecimal")
    private BigDecimal amount;
	@ApiModelProperty(value="申领金额(适应千分位)",required=false,dataType="String")
    private String amountStr;
	@ApiModelProperty(value="实发金额",required=false,dataType="BigDecimal")
    private BigDecimal sendAmt;
	@ApiModelProperty(value="实发金额(适应千分位)",required=false,dataType="String")
    private String sendAmtStr;
	@ApiModelProperty(value="本地货币代码",required=false,dataType="String")
    private String localCurrency;
	@ApiModelProperty(value="本地货币金额",required=false,dataType="BigDecimal")
    private BigDecimal localMoney;
	@ApiModelProperty(value="本地货币金额(适应千分位)",required=false,dataType="String")
    private String localMoneyStr;
	@ApiModelProperty(value="汇率",required=false,dataType="BigDecimal")
    private BigDecimal rate;
	@ApiModelProperty(value="手续费",required=false,dataType="BigDecimal")
    private BigDecimal fees;
	@ApiModelProperty(value="状态：1新建6已取消7已退回2分公司已审核3总公司已审核5发放成功4财务已审核",required=false,dataType="String")
    private String status;
	@ApiModelProperty(value="状态：0:未通知 1：通知",required=false,dataType="String")
    private String easStatus;
	@ApiModelProperty(value="申请时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
	@ApiModelProperty(value="审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkTime;
	@ApiModelProperty(value="审核人编号",required=false,dataType="String")
    private String checkerCode;
	@ApiModelProperty(value="审核人名称",required=false,dataType="String")
    private String checkerName;
	@ApiModelProperty(value="总公司审核编号",required=false,dataType="String")
    private String reCheckerCode;
	@ApiModelProperty(value="总公司审核人名称",required=false,dataType="String")
    private String reCheckerName;
	@ApiModelProperty(value="总公司审核时间（入账时间）",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime reCheckTime;
	@ApiModelProperty(value="财务确认状态 0:未确认;1:部分确认;2:已确认",required=false,dataType="String")
    private String fiCheckStatus;
	@ApiModelProperty(value="财务确认时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime fiCheckTime;
	@ApiModelProperty(value="财务确认人编号",required=false,dataType="String")
    private String fiCheckerCode;
	@ApiModelProperty(value="财务确认人名称 ",required=false,dataType="String")
    private String fiCheckerName;
    @ApiModelProperty(value="已财务确认金额",required=false)
    private BigDecimal recAmount;
    @ApiModelProperty(value="已财务确认金额(适应千分位)",required=false)
    private String recAmountStr;
    @ApiModelProperty(value="提现申请期数",required=false,dataType="String")
    private String periodWeek;
    @ApiModelProperty(value="波兰商城类别 1：自然人  2：个体户",required=false,dataType="String")
    private String malltype;
    @ApiModelProperty(value="波兰商城的税金",required=false,dataType="String")
    private String taxmoney;
    @ApiModelProperty(value="总计F$",required=false,dataType="BigDecimal")
	private BigDecimal totalF$;
    @ApiModelProperty(value="财务确认备注",required=false,dataType="String")
    private String fiCheckMemo;
    
    private String agentNo;
    
    private Integer finStatus;
    
    private Integer finNum;
	
	private BigDecimal localFinMoney;
    
	private BigDecimal localTotalF$;
    private BigDecimal finTotalF$;
	private String name;
	private String mobile;
	private String accountBank;
	private String accountName;
	private String accountCode;

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getAccountBank() {
		return accountBank;
	}

	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public BigDecimal getTotalF$() {
		return totalF$;
	}
	public void setTotalF$(BigDecimal totalF$) {
		this.totalF$ = totalF$;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getApplNo() {
		return applNo;
	}
	public void setApplNo(String applNo) {
		this.applNo = applNo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public BigDecimal getSendAmt() {
		return sendAmt;
	}
	public void setSendAmt(BigDecimal sendAmt) {
		this.sendAmt = sendAmt;
	}
	public String getLocalCurrency() {
		return localCurrency;
	}
	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}
	public BigDecimal getLocalMoney() {
		return localMoney;
	}
	public void setLocalMoney(BigDecimal localMoney) {
		this.localMoney = localMoney;
	}
	public BigDecimal getRate() {
		return rate;
	}
	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}
	public BigDecimal getFees() {
		return fees;
	}
	public void setFees(BigDecimal fees) {
		this.fees = fees;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public LocalDateTime getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(LocalDateTime checkTime) {
		this.checkTime = checkTime;
	}
	public String getCheckerCode() {
		return checkerCode;
	}
	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}
	public String getCheckerName() {
		return checkerName;
	}
	public void setCheckerName(String checkerName) {
		this.checkerName = checkerName;
	}
	public String getReCheckerCode() {
		return reCheckerCode;
	}
	public void setReCheckerCode(String reCheckerCode) {
		this.reCheckerCode = reCheckerCode;
	}
	public String getReCheckerName() {
		return reCheckerName;
	}
	public void setReCheckerName(String reCheckerName) {
		this.reCheckerName = reCheckerName;
	}
	public LocalDateTime getReCheckTime() {
		return reCheckTime;
	}
	public void setReCheckTime(LocalDateTime reCheckTime) {
		this.reCheckTime = reCheckTime;
	}
	public String getFiCheckStatus() {
		return fiCheckStatus;
	}
	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}
	public LocalDateTime getFiCheckTime() {
		return fiCheckTime;
	}
	public void setFiCheckTime(LocalDateTime fiCheckTime) {
		this.fiCheckTime = fiCheckTime;
	}
	public String getFiCheckerCode() {
		return fiCheckerCode;
	}
	public void setFiCheckerCode(String fiCheckerCode) {
		this.fiCheckerCode = fiCheckerCode;
	}
	public String getFiCheckerName() {
		return fiCheckerName;
	}
	public void setFiCheckerName(String fiCheckerName) {
		this.fiCheckerName = fiCheckerName;
	}
	public BigDecimal getRecAmount() {
		return recAmount;
	}
	public void setRecAmount(BigDecimal recAmount) {
		this.recAmount = recAmount;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getMalltype() {
		return malltype;
	}
	public void setMalltype(String malltype) {
		this.malltype = malltype;
	}
	public String getTaxmoney() {
		return taxmoney;
	}
	public void setTaxmoney(String taxmoney) {
		this.taxmoney = taxmoney;
	}
	public String getAmountStr() {
		return amountStr;
	}
	public void setAmountStr(String amountStr) {
		this.amountStr = amountStr;
	}
	public String getSendAmtStr() {
		return sendAmtStr;
	}
	public void setSendAmtStr(String sendAmtStr) {
		this.sendAmtStr = sendAmtStr;
	}
	public String getLocalMoneyStr() {
		return localMoneyStr;
	}
	public void setLocalMoneyStr(String localMoneyStr) {
		this.localMoneyStr = localMoneyStr;
	}
	public String getRecAmountStr() {
		return recAmountStr;
	}
	public void setRecAmountStr(String recAmountStr) {
		this.recAmountStr = recAmountStr;
	}
	public String getFiCheckMemo() {
		return fiCheckMemo;
	}
	public void setFiCheckMemo(String fiCheckMemo) {
		this.fiCheckMemo = fiCheckMemo;
	}
	public String getEasStatus() {
		return easStatus;
	}
	public void setEasStatus(String easStatus) {
		this.easStatus = easStatus;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public Integer getFinStatus() {
		return finStatus;
	}
	public void setFinStatus(Integer finStatus) {
		this.finStatus = finStatus;
	}
	public Integer getFinNum() {
		return finNum;
	}
	public void setFinNum(Integer finNum) {
		this.finNum = finNum;
	}
	public BigDecimal getLocalFinMoney() {
		return localFinMoney;
	}
	public void setLocalFinMoney(BigDecimal localFinMoney) {
		this.localFinMoney = localFinMoney;
	}
	public BigDecimal getLocalTotalF$() {
		return localTotalF$;
	}
	public void setLocalTotalF$(BigDecimal localTotalF$) {
		this.localTotalF$ = localTotalF$;
	}
	public BigDecimal getFinTotalF$() {
		return finTotalF$;
	}
	public void setFinTotalF$(BigDecimal finTotalF$) {
		this.finTotalF$ = finTotalF$;
	}
	
}
