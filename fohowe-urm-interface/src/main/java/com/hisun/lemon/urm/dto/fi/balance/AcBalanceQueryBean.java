 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceQueryBean", description="账户传输对象")
public class AcBalanceQueryBean{
	/*=============base data============ */
	@ApiModelProperty(value="奖金制度",dataType="String")
    private String bonusType;
    @ApiModelProperty(value="区域",dataType="String")
    private String areaCode;
    @ApiModelProperty(value="所属分公司",dataType="String")
    private String companyCode;
    @ApiModelProperty(value="代办处",dataType="String")
    private String agentNo;
    /*=============show data============ */
    @ApiModelProperty(value="记录编号")
    private long id;
    @ApiModelProperty(value="账户类型",dataType="String")
    private String acType;
    @ApiModelProperty(value="用户编号",dataType="String")
    private String userCode;
    @ApiModelProperty(value="账户余额",dataType="BigDecimal")
    private BigDecimal balance;
    @ApiModelProperty(value="可用余额",dataType="String")
    private BigDecimal validBalance;
    @ApiModelProperty(value="借款余额",dataType="String")
    private BigDecimal oweAmt;
    @ApiModelProperty(value="状态",dataType="String")
    private String status;
    @ApiModelProperty(value="用户姓名",dataType="String")
    private String userName;
    
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getBalance() {
		return balance;
	}
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}
	public BigDecimal getValidBalance() {
		return validBalance;
	}
	public void setValidBalance(BigDecimal validBalance) {
		this.validBalance = validBalance;
	}
	public BigDecimal getOweAmt() {
		return oweAmt;
	}
	public void setOweAmt(BigDecimal oweAmt) {
		this.oweAmt = oweAmt;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

}
