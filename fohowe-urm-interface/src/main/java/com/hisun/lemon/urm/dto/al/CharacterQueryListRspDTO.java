package com.hisun.lemon.urm.dto.al;

import java.util.List;

import com.hisun.lemon.urm.common.PageInfo;

/**
 * 语言管理-语言维护查询返回list
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午4:36:43
 *
 */
public class CharacterQueryListRspDTO extends PageInfo{
    /**
     * 语言编码列表
     */
    private List<CharacterQueryRspDTO> charValueList;
    private boolean isExists;

    public List<CharacterQueryRspDTO> getCharValueList() {
        return charValueList;
    }

    public void setCharValueList(List<CharacterQueryRspDTO> charValueList) {
        this.charValueList = charValueList;
    }

    public boolean getIsExists() {
        return isExists;
    }

    public void setIsExists(boolean isExists) {
        this.isExists = isExists;
    }
    
	public void setExists(boolean isExists) {
		this.isExists = isExists;
	}
}
