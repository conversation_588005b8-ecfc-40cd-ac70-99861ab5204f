package com.hisun.lemon.urm.dto.fi.tickets;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TicketsRegistrationMemberBean {
    private Long id;

    private String meetingNo;

    private String memberNo;

    private String memberName;

    private String agentNo;

    private String companyCode;

    private Integer status;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fiCheckTime;


    private String meetingTitle;

    private String planMeetingType;

    private Integer vcStatus;

    private String city;

    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime meetingDateStart;

    private BigDecimal planAmount;

    private String orderNo;


    private String goodsCode;

    private String meetCompanyCode;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getFiCheckTime() {
        return fiCheckTime;
    }

    public void setFiCheckTime(LocalDateTime fiCheckTime) {
        this.fiCheckTime = fiCheckTime;
    }

    public String getMeetingTitle() {
        return meetingTitle;
    }

    public void setMeetingTitle(String meetingTitle) {
        this.meetingTitle = meetingTitle;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public LocalDateTime getMeetingDateStart() {
        return meetingDateStart;
    }

    public void setMeetingDateStart(LocalDateTime meetingDateStart) {
        this.meetingDateStart = meetingDateStart;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }


    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getPlanMeetingType() {
        return planMeetingType;
    }

    public void setPlanMeetingType(String planMeetingType) {
        this.planMeetingType = planMeetingType;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public String getMeetCompanyCode() {
        return meetCompanyCode;
    }

    public void setMeetCompanyCode(String meetCompanyCode) {
        this.meetCompanyCode = meetCompanyCode;
    }
}
