 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 汇率调整传输对象
 * <AUTHOR>
 * @date 2017年12月7号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="RateChangeReqDTO", description="汇率调整传输对象")
public class RateChangeReqDTO{
	@ApiModelProperty(value="货币编码",required=false)
	private String currencyCode;
	
	@ApiModelProperty(value="货币名称",required=false)
	private String currencyName;
	
	@ApiModelProperty(value="对美元汇率",required=false,dataType="BigDecimal")
    private BigDecimal rateToUsd;
	
	@ApiModelProperty(value="对欧元汇率",required=false,dataType="BigDecimal")
    private BigDecimal rateToEur;
	
	@ApiModelProperty(value="原来对美元汇率",required=false,dataType="BigDecimal")
    private BigDecimal oldRateToUsd;
	
	@ApiModelProperty(value="原来对欧元汇率",required=false,dataType="BigDecimal")
    private BigDecimal oldRateToEur;
	
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public BigDecimal getRateToUsd() {
		return rateToUsd;
	}
	public void setRateToUsd(BigDecimal rateToUsd) {
		this.rateToUsd = rateToUsd;
	}
	public BigDecimal getRateToEur() {
		return rateToEur;
	}
	public void setRateToEur(BigDecimal rateToEur) {
		this.rateToEur = rateToEur;
	}
	public BigDecimal getOldRateToUsd() {
		return oldRateToUsd;
	}
	public void setOldRateToUsd(BigDecimal oldRateToUsd) {
		this.oldRateToUsd = oldRateToUsd;
	}
	public BigDecimal getOldRateToEur() {
		return oldRateToEur;
	}
	public void setOldRateToEur(BigDecimal oldRateToEur) {
		this.oldRateToEur = oldRateToEur;
	}
	public String getCurrencyName() {
		return currencyName;
	}
	public void setCurrencyName(String currencyName) {
		this.currencyName = currencyName;
	}
    
}
