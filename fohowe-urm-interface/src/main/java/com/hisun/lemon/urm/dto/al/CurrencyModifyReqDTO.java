package com.hisun.lemon.urm.dto.al;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 基本资料管理-货币管理修改
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 下午6:24:08
 *
 */
public class CurrencyModifyReqDTO {

    @ApiModelProperty(name = "currencyCode", value = "货币编码", required = true, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class, ValidateGroup.second.class,
            ValidateGroup.third.class })
    private String currencyCode;

    @ApiModelProperty(name = "currencyName", value = "货币名称", required = true, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String currencyName;

    @ApiModelProperty(name = "companyCode", value = "公司编号,以','分隔", required = false, dataType = "String")
    @Length(max = 1000, message = "URM10001")
    private String companyCode;

    @ApiModelProperty(name = "rateToGold", value = "兑黄金美元汇率", required = false, dataType = "BigDecimal")
    private BigDecimal rateToGold;
    
    @ApiModelProperty(name = "rateToUsdt", value = "USDT购买汇率", required = false, dataType = "BigDecimal")
    private BigDecimal rateToUsdt;

    private String currencyUsdt;
    
    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public BigDecimal getRateToGold() {
        return rateToGold;
    }

    public void setRateToGold(BigDecimal rateToGold) {
        this.rateToGold = rateToGold;
    }

	public BigDecimal getRateToUsdt() {
		return rateToUsdt;
	}

	public void setRateToUsdt(BigDecimal rateToUsdt) {
		this.rateToUsdt = rateToUsdt;
	}

	public String getCurrencyUsdt() {
		return currencyUsdt;
	}

	public void setCurrencyUsdt(String currencyUsdt) {
		this.currencyUsdt = currencyUsdt;
	}

}
