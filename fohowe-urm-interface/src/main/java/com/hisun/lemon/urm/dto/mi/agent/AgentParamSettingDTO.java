package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.enums.mi.BonusSendTypeEnums;
import com.hisun.lemon.urm.enums.mi.IsBonusEnums;
/**
 * AgentParamSettingDTO 代办处购物结算参数设置
 * <AUTHOR>
 * @date 2017年11月2日
 * @time 下午14:27:30
 */

@ClientValidated
@ApiModel(value="AgentParamSettingDTO", description="代办处传输对象")
public class AgentParamSettingDTO{
	@ApiModelProperty(value="特批货物额度",required=false,dataType="String")
    private BigDecimal promAmt;
    
    @ApiModelProperty(value="FV允许的最低额度",required=false,dataType="String")
    private BigDecimal fvLimit;
   
    @ApiModelProperty(value="F$允许的最低额度",required=false,dataType="String")
    private BigDecimal fpLimit;
    
    @ApiModelProperty(value="F000允许的最低额度",required=false,dataType="String")
    private BigDecimal f0Limit;
    
    @ApiModelProperty(value="奖金发放方式0=发放到代办处，1=发放到经销商",required=false,dataType="String")
    private String bonusSendType;
   
    @ApiModelProperty(value="奖金发放到会员模式代办费计提：0 否，1 计提经销商报单 2计提代办处报单",required=false,dataType="String")
    private String isBonus;
    
    @ApiModelProperty(value="奖金发放币种",required=false,dataType="String")
    private String balanceType;

    @ApiModelProperty(value="币种",required=false,dataType="String")
    private String currencyCode;
    
    @ApiModelProperty(value="需要变动的代办处编号（用于批量更改，以及单条记录修改）",dataType="String")
    private List<String>  agentNoList;

	public BigDecimal getPromAmt() {
		return promAmt;
	}

	public void setPromAmt(BigDecimal promAmt) {
		this.promAmt = promAmt;
	}

	public BigDecimal getFvLimit() {
		return fvLimit;
	}

	public void setFvLimit(BigDecimal fvLimit) {
		this.fvLimit = fvLimit;
	}

	public BigDecimal getFpLimit() {
		return fpLimit;
	}

	public void setFpLimit(BigDecimal fpLimit) {
		this.fpLimit = fpLimit;
	}

	public BigDecimal getF0Limit() {
		return f0Limit;
	}

	public void setF0Limit(BigDecimal f0Limit) {
		this.f0Limit = f0Limit;
	}

	public String getBonusSendType() {
		return bonusSendType;
	}

	public void setBonusSendType(String bonusSendType) {
		this.bonusSendType = bonusSendType;
	}

	public String getIsBonus() {
		return isBonus;
	}

	public void setIsBonus(String isBonus) {
		this.isBonus = isBonus;
	}

	public String getBalanceType() {
		return balanceType;
	}

	public void setBalanceType(String balanceType) {
		this.balanceType = balanceType;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public List<String> getAgentNoList() {
		return agentNoList;
	}

	public void setAgentNoList(List<String> agentNoList) {
		this.agentNoList = agentNoList;
	}
}
