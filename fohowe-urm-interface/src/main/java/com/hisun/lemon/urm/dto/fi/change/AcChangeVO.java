 package com.hisun.lemon.urm.dto.fi.change;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceVO", description="账户传输对象")
public class AcChangeVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
	@ApiModelProperty(value="查询结果集")
	List<AcChangeQueryBean> dataList;
	
	@ApiModelProperty(value="账户类型")
	Map<String, String> acTypeKV=null;
	
	@ApiModelProperty(value="交易类型")
	Map<String, String> orderTypeKV=null;
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcChangeQueryBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AcChangeQueryBean> dataList) {
		this.dataList = dataList;
	}

	public Map<String, String> getAcTypeKV() {
		return acTypeKV;
	}

	public void setAcTypeKV(Map<String, String> acTypeKV) {
		this.acTypeKV = acTypeKV;
	}

	public Map<String, String> getOrderTypeKV() {
		return orderTypeKV;
	}

	public void setOrderTypeKV(Map<String, String> orderTypeKV) {
		this.orderTypeKV = orderTypeKV;
	}
}
