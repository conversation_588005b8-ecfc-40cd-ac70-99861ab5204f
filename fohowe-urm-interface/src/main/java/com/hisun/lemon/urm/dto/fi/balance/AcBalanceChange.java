package com.hisun.lemon.urm.dto.fi.balance;
/**
 * 账户变动
 * <AUTHOR>
 * @time 2017.11.22 下午19:35:22
 */

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ApiModel(value="AcBalanceChange", description="账户传输对象")
public class AcBalanceChange implements Comparable<AcBalanceChange>{
	@ApiModelProperty(value="变动金额（正负表示扣减）",required=true,dataType="BigDecimal")
	private BigDecimal money;
	@ApiModelProperty(value="账户类型",required=true)
	private String acType;
	@ApiModelProperty(value="用户编号",required=true,dataType="String")
	private String userCode;
	@ApiModelProperty(value="备注",required=true,dataType="String")
	private String memo;
	@ApiModelProperty(value="关联单号",required=true,dataType="String")
	private String orderNo;
	@ApiModelProperty(value="交易类别",required=true)
	private String orderType;
	@ApiModelProperty(value="转出银行帐号/发票号",required=false,dataType="String")
    private String outCode;
	@ApiModelProperty(value="用户代办处",required=false,dataType="String")
	private String userAgentNo;
	
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public String getOutCode() {
		return outCode;
	}
	public void setOutCode(String outCode) {
		this.outCode = outCode;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getUserAgentNo() {
		return userAgentNo;
	}
	public void setUserAgentNo(String userAgentNo) {
		this.userAgentNo = userAgentNo;
	}
	@Override
	public int compareTo(AcBalanceChange o) {
		if(this.getUserCode().compareTo(o.getUserCode())>0) {
			return 1;
		}else if(this.getUserCode().compareTo(o.getUserCode())<0){
			return -1;
		}else {
			return 0;
		}
	}
	
}
