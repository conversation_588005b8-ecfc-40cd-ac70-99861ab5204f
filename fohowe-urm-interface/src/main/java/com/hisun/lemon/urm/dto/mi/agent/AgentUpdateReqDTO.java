package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceChange;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月02日
 * @time 下午14:27:30
 */

@ClientValidated
@ApiModel(value="AgentUpdateReqDTO", description="代办处传输对象")
public class AgentUpdateReqDTO{
	/*==========基本信息============*/
	@ApiModelProperty(value="代办处编号",required=false,dataType="String")
    private String agentNo;
	
	@ApiModelProperty(value="特批货物额度变动值",required=false,dataType="String")
    private BigDecimal thePromAmt;
	
	@ApiModelProperty(value="扣减资金集合")
	private List<AcBalanceChange> acBalanceChangeList;

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public BigDecimal getThePromAmt() {
		return thePromAmt;
	}

	public void setThePromAmt(BigDecimal thePromAmt) {
		this.thePromAmt = thePromAmt;
	}

	public List<AcBalanceChange> getAcBalanceChangeList() {
		return acBalanceChangeList;
	}

	public void setAcBalanceChangeList(List<AcBalanceChange> acBalanceChangeList) {
		this.acBalanceChangeList = acBalanceChangeList;
	}
	
}
