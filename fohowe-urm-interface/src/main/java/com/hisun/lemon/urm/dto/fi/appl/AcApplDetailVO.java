 package com.hisun.lemon.urm.dto.fi.appl;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcApplDetailVO 传输对象
 * <AUTHOR>
 * @date 2017年11月16号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcApplDetailVO", description="账户传输对象")
public class AcApplDetailVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
    @ApiModelProperty(value="账户列表" )
    private List<AcApplDetailBean> dataList;
    
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcApplDetailBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AcApplDetailBean> dataList) {
		this.dataList = dataList;
	}
}
