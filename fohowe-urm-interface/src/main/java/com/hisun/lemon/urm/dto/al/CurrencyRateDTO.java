package com.hisun.lemon.urm.dto.al;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class CurrencyRateDTO {
    /**
     * @Fields currencyCode 货币编码
     */
    private String currencyCode;
    /**
     * @Fields currencyName 货币名称
     */
    private String currencyName;
    /**
     * @Fields lastUpdateTime 最后调整时间
     */
    private LocalDateTime lastUpdateTime;
    /**
     * @Fields rateToUsd 对美元汇率
     */
    private BigDecimal rateToUsd;
    /**
     * @Fields rateToEur 对欧元汇率
     */
    private BigDecimal rateToEur;
    /**
     * @Fields rateToInput 对F$充值汇率
     */
    private BigDecimal rateToInput;
    /**
     * @Fields rateToApple 对F$提现汇率
     */
    private BigDecimal rateToApple;
    /**
     * @Fields rateToApple 兑黄金美元汇率
     */
    private BigDecimal rateToGold;
    /**
     * USDT币种
     */
    private String currencyUsdt;
    /**
     * USDT汇率
     */
    private BigDecimal rateToUsdt;
    /**
     * 奖励比例
     */
    private BigDecimal usdtRate;

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public BigDecimal getRateToUsd() {
        return rateToUsd;
    }

    public void setRateToUsd(BigDecimal rateToUsd) {
        this.rateToUsd = rateToUsd;
    }

    public BigDecimal getRateToEur() {
        return rateToEur;
    }

    public void setRateToEur(BigDecimal rateToEur) {
        this.rateToEur = rateToEur;
    }

    public BigDecimal getRateToInput() {
        return rateToInput;
    }

    public void setRateToInput(BigDecimal rateToInput) {
        this.rateToInput = rateToInput;
    }

    public BigDecimal getRateToApple() {
        return rateToApple;
    }

    public void setRateToApple(BigDecimal rateToApple) {
        this.rateToApple = rateToApple;
    }

    public BigDecimal getRateToGold() {
        return rateToGold;
    }

    public void setRateToGold(BigDecimal rateToGold) {
        this.rateToGold = rateToGold;
    }

	public String getCurrencyUsdt() {
		return currencyUsdt;
	}

	public void setCurrencyUsdt(String currencyUsdt) {
		this.currencyUsdt = currencyUsdt;
	}

	public BigDecimal getRateToUsdt() {
		return rateToUsdt;
	}

	public void setRateToUsdt(BigDecimal rateToUsdt) {
		this.rateToUsdt = rateToUsdt;
	}

	public BigDecimal getUsdtRate() {
		return usdtRate;
	}

	public void setUsdtRate(BigDecimal usdtRate) {
		this.usdtRate = usdtRate;
	}
	
}
