package com.hisun.lemon.urm.dto.mi.member;

import java.time.LocalDateTime;
import java.util.Date;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.DateConstant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * MemberBean 传输对象
 * 
 * <AUTHOR>
 * @date 2017年10月30日
 * @time 上午9:27:30
 *
 */

@ClientValidated
@ApiModel(value = "MemberBean", description = "经销商传输对象")
public class MemberBean {
	/* ==========基本信息============ */
	@ApiModelProperty(value = "记录ID")
	private Long id;

	@ApiModelProperty(value = "经销商编号", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.second.class,ValidateGroup.fourth.class})
	private String memberNo;

	@ApiModelProperty(value = "推荐人", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String recommendNo;

	@ApiModelProperty(value = "代办处编号", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String agentNo;

	@ApiModelProperty(value = "所属分公司", dataType = "String")
	private String companyCode;

	@ApiModelProperty(value = "姓名", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String name;

	@ApiModelProperty(value = "昵称", dataType = "String")
	private String petName;

	@ApiModelProperty(value = "性别：M男W女")
	private String sex;

	@ApiModelProperty(value = "生日")
	@DateTimeFormat(pattern=DateConstant.STR1_YMD)
    private Date birthday;

	@ApiModelProperty(value = "证件类型:1身份证2护照")
	@NotNull(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String paperType;

	@ApiModelProperty(value = "证件号", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String paperNo;

	@ApiModelProperty(value = "健康状态，1:良;2:一般;3:差")
	private String healthy;

	@ApiModelProperty(value = "知晓方式，1：朋友；2：广告；3：网络；")
	private String knowWay;
	@ApiModelProperty(value = "种类，1—最多只能发展2个经营权,2—可发展无限经营权", dataType = "String")
	private String memType;

	@ApiModelProperty(value = "注册时布局类型，1=直线，2=三角，3=双直线(购买经营权时必须)")
	@NotNull(message = "URM10005",groups = {ValidateGroup.first.class, ValidateGroup.second.class})
	private String disType;

	/* ==========联系信息============ */
	@ApiModelProperty(value = "地址", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String storeAddr;
	
	@ApiModelProperty(value = "邮编", dataType = "String")
	private String storePost;
	
	@ApiModelProperty(value = "家庭电话", dataType = "String")
	private String homeTel;
	
	@ApiModelProperty(value = "办公电话", dataType = "String")
	private String officeTel;
	
	@ApiModelProperty(value = "手机", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String mobile;
	
	@ApiModelProperty(value = "传真", dataType = "String")
	private String fax;
	
	@ApiModelProperty(value = "电子邮箱", dataType = "String")
	private String email;
	
	@ApiModelProperty(value = "网址", dataType = "String")
	private String webAddr;
	
	/* ==========银行账户信息============ */
	@ApiModelProperty(value = "开户银行", dataType = "String")
	private String accountBank;
	
	@ApiModelProperty(value = "银行帐号", dataType = "String")
	private String accountCode;
	
	@ApiModelProperty(value = "开户名", dataType = "String")
	private String accountName;
	
	/* ==========权限信息============ */
	@ApiModelProperty(value = "是否购买了经营权：1是0否", dataType = "String")
	@NotBlank(message = "URM10005",groups = ValidateGroup.first.class)
	private String isPurchRight;
	
	@ApiModelProperty(value = "是否限制网络接线：0否1是", dataType = "String")
	private String isFree;
	
	@ApiModelProperty(value = "经销商状态", dataType = "String")
	private String status;
	
	@ApiModelProperty(value = "登录状态:1限制0未限制", dataType = "String")
	private String loginStatus;
	
	private Integer promQual;
	
	/* ==========密码设置============ */
	@ApiModelProperty(value = "登录名", dataType = "String")
	private String loginName;
	
	@ApiModelProperty(value = "密码", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String password;
	
	@ApiModelProperty(value = "重复密码", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String repeatPassword;
	
	@ApiModelProperty(value = "默认语言", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String defaultlanuage;

	/* ==========其他信息============ */
	@ApiModelProperty(value = "注册日期", dataType = "LocalDateTime")
	@JsonSerialize(using = DateJsonSerializer.class)
	@JsonDeserialize(using = DateJsonDeserializer.class)
	private LocalDateTime registerDate;
	
	@ApiModelProperty(value = "激活时间", dataType = "LocalDateTime")
	@JsonSerialize(using = DateJsonSerializer.class)
	@JsonDeserialize(using = DateJsonDeserializer.class)
	private LocalDateTime activeTime;
	
	@ApiModelProperty(value = "备注", dataType = "String")
	private String remark;
	
	@ApiModelProperty(value = "注销时间", dataType = "LocalDateTime")
	@JsonSerialize(using = DateJsonSerializer.class)
	@JsonDeserialize(using = DateJsonDeserializer.class)
	private LocalDateTime exitTime;
	
	@ApiModelProperty(value = "注销状态，1=注销", dataType = "String")
	private String exitStatus;
	
	@ApiModelProperty(value = "注销操作人", dataType = "String")
	private String exitUser;
	
	@ApiModelProperty(value = "最后活跃期数", dataType = "String")
	private String activeWeek;
	
	@ApiModelProperty(value = "加入期数", dataType = "String")
	private String periodWeek;
	
	@ApiModelProperty(value = "接点权编号(购买经营权时必须)", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.second.class})
	private String linkNo;
	
	@ApiModelProperty(value = "经营权编号", dataType = "String")
	private String rightNo;
	
	@ApiModelProperty(value = "购买经营权数量(购买经营权时必须)", dataType = "String")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class, ValidateGroup.second.class})
	private String buyNum;
	
	@ApiModelProperty(value = "参加促销类型0：不参加，1：480PV(1+6)，2：750(2+5)，3：1020(3+4)，4：1+5，5：180促销，6：美国制度30pv(购买经营权时必须)")
	@NotNull(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.second.class})
	private String isPromType;
	
	@ApiModelProperty(value = "加入PV(购买经营权时必须)", dataType = "String")
	@NotBlank(message = "URM10005",groups = ValidateGroup.second.class)
	private String addPv;
	
	@ApiModelProperty(value = "奖金制度(购买经营权时必须)1:欧洲2：东非3：西非4：美国")
	@NotNull(message = "URM10005",groups = ValidateGroup.second.class)
	private String bonusType;
	
	@ApiModelProperty(value = "注册渠道：波兰PLW 立陶宛LTW 跨境电商CNK 美国USA")
	@NotBlank(message = "URM10005",groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.fourth.class})
	private String channelType;
	
	@ApiModelProperty(value="东西非接线限制")
    private String africaConnLimit;
	
	@ApiModelProperty(value="税号")
    private String taxId;
	
	@ApiModelProperty(value="是否推广注册")
	private Integer promoteFlag;
	
	@ApiModelProperty(value="是否积分会员")
	private Integer integralFlag;
	
	@ApiModelProperty(value="是否可以超过PV限制")
	private Integer isPvLimit;
	
	private Integer isAgentOrder;

	private String shopFlag;

	public String getShopFlag() {
		return shopFlag;
	}

	public void setShopFlag(String shopFlag) {
		this.shopFlag = shopFlag;
	}

	//是否LUCKLIFE
    private Integer isVipLine;
    private Integer vipLineWeek;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getRecommendNo() {
		return recommendNo;
	}

	public void setRecommendNo(String recommendNo) {
		this.recommendNo = recommendNo;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPetName() {
		return petName;
	}

	public void setPetName(String petName) {
		this.petName = petName;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public String getPaperNo() {
		return paperNo;
	}

	public void setPaperNo(String paperNo) {
		this.paperNo = paperNo;
	}

	public String getMemType() {
		return memType;
	}

	public void setMemType(String memType) {
		this.memType = memType;
	}

	public String getStoreAddr() {
		return storeAddr;
	}

	public void setStoreAddr(String storeAddr) {
		this.storeAddr = storeAddr;
	}

	public String getStorePost() {
		return storePost;
	}

	public void setStorePost(String storePost) {
		this.storePost = storePost;
	}

	public String getHomeTel() {
		return homeTel;
	}

	public void setHomeTel(String homeTel) {
		this.homeTel = homeTel;
	}

	public String getOfficeTel() {
		return officeTel;
	}

	public void setOfficeTel(String officeTel) {
		this.officeTel = officeTel;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getWebAddr() {
		return webAddr;
	}

	public void setWebAddr(String webAddr) {
		this.webAddr = webAddr;
	}

	public String getAccountBank() {
		return accountBank;
	}

	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getIsPurchRight() {
		return isPurchRight;
	}

	public void setIsPurchRight(String isPurchRight) {
		this.isPurchRight = isPurchRight;
	}

	public String getIsFree() {
		return isFree;
	}

	public void setIsFree(String isFree) {
		this.isFree = isFree;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getRepeatPassword() {
		return repeatPassword;
	}

	public void setRepeatPassword(String repeatPassword) {
		this.repeatPassword = repeatPassword;
	}

	public String getDefaultlanuage() {
		return defaultlanuage;
	}

	public void setDefaultlanuage(String defaultlanuage) {
		this.defaultlanuage = defaultlanuage;
	}

	public LocalDateTime getRegisterDate() {
		return registerDate;
	}

	public void setRegisterDate(LocalDateTime registerDate) {
		this.registerDate = registerDate;
	}

	public LocalDateTime getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(LocalDateTime activeTime) {
		this.activeTime = activeTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public LocalDateTime getExitTime() {
		return exitTime;
	}

	public void setExitTime(LocalDateTime exitTime) {
		this.exitTime = exitTime;
	}

	public String getExitStatus() {
		return exitStatus;
	}

	public void setExitStatus(String exitStatus) {
		this.exitStatus = exitStatus;
	}

	public String getExitUser() {
		return exitUser;
	}

	public void setExitUser(String exitUser) {
		this.exitUser = exitUser;
	}

	public String getActiveWeek() {
		return activeWeek;
	}

	public void setActiveWeek(String activeWeek) {
		this.activeWeek = activeWeek;
	}

	public String getPeriodWeek() {
		return periodWeek;
	}

	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}

	public String getLinkNo() {
		return linkNo;
	}

	public void setLinkNo(String linkNo) {
		this.linkNo = linkNo;
	}

	public String getBuyNum() {
		return buyNum;
	}

	public void setBuyNum(String buyNum) {
		this.buyNum = buyNum;
	}

	public String getAddPv() {
		return addPv;
	}

	public void setAddPv(String addPv) {
		this.addPv = addPv;
	}

	public String getKnowWay() {
		return knowWay;
	}

	public void setKnowWay(String knowWay) {
		this.knowWay = knowWay;
	}

	public String getRightNo() {
		return rightNo;
	}

	public void setRightNo(String rightNo) {
		this.rightNo = rightNo;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getPaperType() {
		return paperType;
	}

	public void setPaperType(String paperType) {
		this.paperType = paperType;
	}

	public String getHealthy() {
		return healthy;
	}

	public void setHealthy(String healthy) {
		this.healthy = healthy;
	}

	public String getDisType() {
		return disType;
	}

	public void setDisType(String disType) {
		this.disType = disType;
	}

	public String getIsPromType() {
		return isPromType;
	}

	public void setIsPromType(String isPromType) {
		this.isPromType = isPromType;
	}

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getLoginStatus() {
		return loginStatus;
	}

	public void setLoginStatus(String loginStatus) {
		this.loginStatus = loginStatus;
	}

	public String getAfricaConnLimit() {
		return africaConnLimit;
	}

	public void setAfricaConnLimit(String africaConnLimit) {
		this.africaConnLimit = africaConnLimit;
	}

	public String getTaxId() {
		return taxId;
	}

	public void setTaxId(String taxId) {
		this.taxId = taxId;
	}

	public Integer getPromoteFlag() {
		return promoteFlag;
	}

	public void setPromoteFlag(Integer promoteFlag) {
		this.promoteFlag = promoteFlag;
	}

	public Integer getIsPvLimit() {
		return isPvLimit;
	}

	public void setIsPvLimit(Integer isPvLimit) {
		this.isPvLimit = isPvLimit;
	}

	public Integer getIntegralFlag() {
		return integralFlag;
	}

	public void setIntegralFlag(Integer integralFlag) {
		this.integralFlag = integralFlag;
	}
	
	public Integer getIsAgentOrder() {
		return isAgentOrder;
	}
	public void setIsAgentOrder(Integer isAgentOrder) {
		this.isAgentOrder = isAgentOrder;
	}

	public Integer getPromQual() {
		return promQual;
	}

	public void setPromQual(Integer promQual) {
		this.promQual = promQual;
	}

	public Integer getIsVipLine() {
		return isVipLine;
	}

	public void setIsVipLine(Integer isVipLine) {
		this.isVipLine = isVipLine;
	}

	public Integer getVipLineWeek() {
		return vipLineWeek;
	}

	public void setVipLineWeek(Integer vipLineWeek) {
		this.vipLineWeek = vipLineWeek;
	}
 
}
