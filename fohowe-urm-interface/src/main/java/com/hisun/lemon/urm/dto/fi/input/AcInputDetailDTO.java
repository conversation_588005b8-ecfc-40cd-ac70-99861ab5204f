 package com.hisun.lemon.urm.dto.fi.input;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputDetailDTO 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputDetailDTO", description="账户传输对象")
public class AcInputDetailDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(name="id",value="记录ID",required=false)
    private Long id;
	
	@ApiModelProperty(value="批量审核ID",required=false)
	private String[] idArr;
	
	@ApiModelProperty(value="金额",required=false,dataType="BigDecimal")
	private BigDecimal money;
	@ApiModelProperty(value="充值单id",required=false,dataType="String")
	private Long inputId;
	@ApiModelProperty(value="本地货币代码",required=false,dataType="String")
	private String localCurrency;
	@ApiModelProperty(value="本地货币金额",required=false,dataType="BigDecimal")
	private BigDecimal localMoney;
	@ApiModelProperty(value="汇率",required=false,dataType="BigDecimal")
	private BigDecimal rate;
	@ApiModelProperty(value="备注(申请时填写)",required=false,dataType="String")
	private String memo;
	@ApiModelProperty(value="状态:2:核准  6 删除",required=false,dataType="String")
	private String status;
	@ApiModelProperty(value="建档人",required=false,dataType="String")
	private String createrCode;
	@ApiModelProperty(value="建档人名称",required=false,dataType="String")
	private String createrName;
	@ApiModelProperty(value="建档时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime createTime;
	@ApiModelProperty(value="审核人",required=false,dataType="String")
	private String cancelCode;
	@ApiModelProperty(value="审核人名称",required=false,dataType="String")
	private String cancelName;
	@ApiModelProperty(value="审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime cancelTime;

	@ApiModelProperty(value="分页查询-第几页", required= true)
	private int pageNum;
	    
	@ApiModelProperty(value="分页查询-每页数据条数", required= true)
	private int pageSize;
	private String accountCash;
	private String subjectNo;

	public String getSubjectNo() {
		return subjectNo;
	}

	public void setSubjectNo(String subjectNo) {
		this.subjectNo = subjectNo;
	}

	public String getAccountCash() {
		return accountCash;
	}

	public void setAccountCash(String accountCash) {
		this.accountCash = accountCash;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public BigDecimal getMoney() {
		return money;
	}

	public void setMoney(BigDecimal money) {
		this.money = money;
	}

	public Long getInputId() {
		return inputId;
	}

	public void setInputId(Long inputId) {
		this.inputId = inputId;
	}

	public String getLocalCurrency() {
		return localCurrency;
	}

	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}

	public BigDecimal getLocalMoney() {
		return localMoney;
	}

	public void setLocalMoney(BigDecimal localMoney) {
		this.localMoney = localMoney;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getCreaterCode() {
		return createrCode;
	}

	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}

	public String getCancelName() {
		return cancelName;
	}

	public void setCancelName(String cancelName) {
		this.cancelName = cancelName;
	}

	public LocalDateTime getCancelTime() {
		return cancelTime;
	}

	public void setCancelTime(LocalDateTime cancelTime) {
		this.cancelTime = cancelTime;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String[] getIdArr() {
		return idArr;
	}

	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
}
