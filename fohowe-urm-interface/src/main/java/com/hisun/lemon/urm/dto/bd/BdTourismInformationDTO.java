package com.hisun.lemon.urm.dto.bd;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
public class BdTourismInformationDTO extends com.hisun.lemon.urm.common.PageInfo {
    @ApiModelProperty(name = "pageNum",value = "当前页数",dataType = "int")
    private int pageNum;
    @ApiModelProperty(name = "PageSize",value = "分页条数",dataType = "int")
    private int pageSize;
    //1是范围内，2是范围外
  
    @ApiModelProperty(name = "selectmod",value = "判断是否接收两个DTO参数类,0是不接收,非2是范围内,2是范围外",dataType = "int")
    private int selectmod = 0;

 
    @ApiModelProperty(name = "addANDdelNumber",value = "添加指定条添加或删除多少次",dataType = "int")
    private int addNumber=1;

 
    public List<BdTourismInformationBean> list = new ArrayList<>();
 
    @ApiModelProperty(name = "starDTO",value = "单查询数值或第一个查询条件开始")
    private BdTourismInformationBean startDTO=new BdTourismInformationBean();

 
    @ApiModelProperty(name = "endDTO",value = "单查询数值或第一个查询条件结束")
    private BdTourismInformationEndBean endDTO=new BdTourismInformationEndBean();

    public int getSelectmod() {
        return selectmod;
    }

    public void setSelectmod(int selectmod) {
        this.selectmod = selectmod;
    }

    public int getAddNumber() {
        return addNumber;
    }

    public void setAddNumber(int addNumber) {
        this.addNumber = addNumber;
    }

    public List<BdTourismInformationBean> getList() {
        return list;
    }

    public void setList(List<BdTourismInformationBean> list) {
        this.list = list;
    }

    public BdTourismInformationBean getStartDTO() {
        return startDTO;
    }

    public void setStartDTO(BdTourismInformationBean startDTO) {
        this.startDTO = startDTO;
    }

    public BdTourismInformationEndBean getEndDTO() {
        return endDTO;
    }

    public void setEndDTO(BdTourismInformationEndBean endDTO) {
        this.endDTO = endDTO;
    }

    public List<BdTourismInformationBean> getDTOList() {
        return DTOList;
    }

    public void setDTOList(List<BdTourismInformationBean> DTOList) {
        this.DTOList = DTOList;
    }

    @ApiModelProperty(name = "DTOList",value = "单查询数值或第一个查询条件结束",dataType = "List<BdTourismInformationBean>")
    private List<BdTourismInformationBean> DTOList=new ArrayList<BdTourismInformationBean>();

    @Override
    public int getPageNum() {
        return pageNum;
    }

    @Override
    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    @Override
    public int getPageSize() {
        return pageSize;
    }

    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    
}
