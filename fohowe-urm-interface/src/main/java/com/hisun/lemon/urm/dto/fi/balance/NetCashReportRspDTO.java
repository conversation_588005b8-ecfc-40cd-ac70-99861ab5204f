package com.hisun.lemon.urm.dto.fi.balance;

import java.util.List;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
/**
 * AcBalanceItem 传输对象
 * <AUTHOR>
 * @date 2018年09月12号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="NetCashReportRspDTO", description="账户统计报表传输对象")
public class NetCashReportRspDTO extends GenericDTO<NoBody>{
    private List<NetCashReportRspBean> list;
    
    private long total;

	public List<NetCashReportRspBean> getList() {
		return list;
	}

	public void setList(List<NetCashReportRspBean> list) {
		this.list = list;
	}

	public long getTotal() {
		return total;
	}

	public void setTotal(long total) {
		this.total = total;
	}
    
}
