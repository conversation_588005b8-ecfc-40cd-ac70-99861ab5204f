package com.hisun.lemon.urm.dto.al;

import org.hibernate.validator.constraints.NotBlank;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;

/**
 * 基本资料管理-货币管理修改
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 下午6:24:08
 *
 */
public class CurrencyQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(name = "companyCode", value = "公司编码", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.third.class })
    private String companyCode;

    @ApiModelProperty(name = "currencyCode", value = "货币编码", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class, ValidateGroup.fourth.class })
    private String currencyCode;

    @ApiModelProperty(name = "currencyName", value = "货币名称", required = false, dataType = "String")
    private String currencyName;

    @ApiModelProperty(value="开始创建日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private String minCreateTime;
    
	@ApiModelProperty(value="结束创建日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private String maxCreateTime;
    
    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量", required = false, dataType = "Integer")
    private int pageSize;

    @ApiModelProperty(name = "isAgent", value = "是否需要查询代办处列表(默认不需要) 1-需要")
    private String isAgent;
    //多选
    private String companyCodes;
    
    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getIsAgent() {
        return isAgent;
    }

    public void setIsAgent(String isAgent) {
        this.isAgent = isAgent;
    }

	public String getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(String minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public String getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(String maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getCompanyCodes() {
		return companyCodes;
	}

	public void setCompanyCodes(String companyCodes) {
		this.companyCodes = companyCodes;
	}

}
