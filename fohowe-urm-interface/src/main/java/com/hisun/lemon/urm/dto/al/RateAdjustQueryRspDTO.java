package com.hisun.lemon.urm.dto.al;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 * @ClassName: RateAdjustQueryRspDTO 
 * @Description: 汇率调整查询返回DTO
 * @author: tian
 * @date: 2017年11月9日 下午4:18:52
 */
public class RateAdjustQueryRspDTO {

    private List<CurrencyRateDTO> rateAdjustList;
    /**
     * @Fields adjustCode 调整单号 ADJUST_CODE
     */
    private String adjustCode;

    private String isBalance;
    /**
     * @Fields status 0:新建 1:审批
     */
    private String status;
    /**
     * @Fields creatorCode 创建人编号 CREATOR_CODE
     */
    private String creatorCode;
    /**
     * @Fields checkerCode 审核人编号
     */
    private String checkerCode;
    /**
     * @Fields checkTime 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public List<CurrencyRateDTO> getRateAdjustList() {
        return rateAdjustList;
    }

    public void setRateAdjustList(List<CurrencyRateDTO> rateAdjustList) {
        this.rateAdjustList = rateAdjustList;
    }

    public String getAdjustCode() {
        return adjustCode;
    }

    public void setAdjustCode(String adjustCode) {
        this.adjustCode = adjustCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getIsBalance() {
        return isBalance;
    }

    public void setIsBalance(String isBalance) {
        this.isBalance = isBalance;
    }

}
