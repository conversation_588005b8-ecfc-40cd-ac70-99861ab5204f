package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.dto.mi.group.ValidGroup1;


/**
 * 网络关系对象 
 * 
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ClientValidated
@ApiModel(value="NetWorkDTO", description=" 网络关系对象 ")
public class NetWorkDTO  extends GenericDTO<NoBody>{
    @ApiModelProperty(value="查询的编号",required= true)
    @NotEmpty(message="URM10001"/*,groups= {ValidGroup1.class}*/)  //参数不能为空
    private String searchNo;
    
    @ApiModelProperty(value="个人点位缩图功能，点击网络图向下查询时，将经营权编号传到这个字段")
    private String rightNo;
    
    @ApiModelProperty(value="查询总层级")
    private String totalLevel;

/**
 * 有对应枚举类 TreeDirectionEnums
 */
    @ApiModelProperty(value="查询方式【UP:向上查询  DOWN:向下查询,默认】")
    private String direction;

    @ApiModelProperty(value="查询模式【1=图形模式  (默认 查询3层)  2:树形模式】")
    private String showWay;
    

	public String getShowWay() {
		return showWay;
	}

	public void setShowWay(String showWay) {
		this.showWay = showWay;
	}

	public String getSearchNo() {
	return searchNo;
}

public void setSearchNo(String searchNo) {
	this.searchNo = searchNo;
}

	public String getDirection() {
		return direction;
	}

	public void setDirection(String direction) {
		this.direction = direction;
	}

	public String getTotalLevel() {
		return totalLevel;
	}

	public void setTotalLevel(String totalLevel) {
		this.totalLevel = totalLevel;
	}

	public String getRightNo() {
		return rightNo;
	}

	public void setRightNo(String rightNo) {
		this.rightNo = rightNo;
	}
	

 
    
    
    
    
}
