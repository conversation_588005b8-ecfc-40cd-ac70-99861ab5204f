 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.BaseDO;

/**
 * 
 * @ClassName: AcHistoryBalanceItemDO
 * @Description: TODO
 * @author: fy
 * @date: 2018年4月23日 上午9:40:26
 */
public class AcHistoryBalanceItemDO extends BaseDO{
	/**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields bonusType 奖金制度
     */
    private String bonusType;
    /**
     * @Fields companyCode 分公司编号
     */
    private String companyCode;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields userCode 经销商/代办处编号
     */
    private String userCode;
    /**
     * @Fields money 余额变化 money
     */
    private BigDecimal money;
    /**
     * @Fields balance 余额 balance
     */
    private BigDecimal balance;
    /**
     * @Fields acType  账户类别
     */
    private String acType;
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public BigDecimal getBalance() {
		return balance;
	}
	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
    
    
}
