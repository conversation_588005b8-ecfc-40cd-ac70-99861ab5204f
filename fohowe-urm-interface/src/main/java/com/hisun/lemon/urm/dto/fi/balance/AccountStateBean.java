 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AccountStateBean", description="账户传输对象")
public class AccountStateBean{
    @ApiModelProperty(value="区域",dataType="String")
    private String areaCode;
    
    @ApiModelProperty(value="分公司",dataType="String")
    private String companyCode;
    
    @ApiModelProperty(value="代办处",dataType="String")
    private String agentNo;
    
    @ApiModelProperty(value="经销商",dataType="String")
    private String memberNo;
    
    @ApiModelProperty(value="账户类型",dataType="String")
    private String acType;
    
    @ApiModelProperty(value="存入")
    private BigDecimal inMoney;
    
    @ApiModelProperty(value="取出")
    private BigDecimal outMoney;
    
    @ApiModelProperty(value="结余金额")
    private BigDecimal outFactMoney;

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public BigDecimal getInMoney() {
		return inMoney;
	}

	public void setInMoney(BigDecimal inMoney) {
		this.inMoney = inMoney;
	}

	public BigDecimal getOutMoney() {
		return outMoney;
	}

	public void setOutMoney(BigDecimal outMoney) {
		this.outMoney = outMoney;
	}

	public BigDecimal getOutFactMoney() {
		return outFactMoney;
	}

	public void setOutFactMoney(BigDecimal outFactMoney) {
		this.outFactMoney = outFactMoney;
	}
    
}
