package com.hisun.lemon.urm.dto.mi.member;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.urm.common.EnumDemoType;

public class TestDTO {
	//@JsonSerialize(using=CustomValueEnumSerializer.class)
	//@JsonDeserialize(using=CustomValueEnumDeserializer.class)
	private  EnumDemoType enums;

	public EnumDemoType getEnums() {
		return enums;
	}

	public void setEnums(EnumDemoType enums) {
		this.enums = enums;
	}

	
	
	
}
