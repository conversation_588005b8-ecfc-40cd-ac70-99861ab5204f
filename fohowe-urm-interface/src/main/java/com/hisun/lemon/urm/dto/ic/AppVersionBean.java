package com.hisun.lemon.urm.dto.ic;

public class AppVersionBean {
 
	private Integer id;

    private String appName;

    private String version;

    private String platform;

    private String appUrl;

    private Integer orderSn;
    
    private Long dfileId;

	public Long getDfileId() {
		return dfileId;
	}

	public void setDfileId(Long dfileId) {
		this.dfileId = dfileId;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform == null ? null : platform.trim();
    }

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl == null ? null : appUrl.trim();
    }

    public Integer getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(Integer orderSn) {
        this.orderSn = orderSn;
    }
}
