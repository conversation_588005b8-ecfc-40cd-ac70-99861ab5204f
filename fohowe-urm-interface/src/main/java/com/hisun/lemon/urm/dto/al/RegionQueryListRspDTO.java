/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: RegionQueryListRspDTO.java 
 * @Prject: fohowe-urm-interface
 * @Package: com.hisun.lemon.urm.dto.al 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月7日 下午5:29:14 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.dto.al;

import java.util.List;

import com.hisun.lemon.urm.common.PageInfo;

/** 
 * @ClassName: RegionQueryListRspDTO 
 * @Description: 基本资料管理-地区资料查询返回list
 * @author: tian
 * @date: 2017年11月7日 下午5:29:14  
 */
public class RegionQueryListRspDTO extends PageInfo {
    private List<RegionQueryRspDTO> regionList;

    public List<RegionQueryRspDTO> getRegionList() {
        return regionList;
    }

    public void setRegionList(List<RegionQueryRspDTO> regionList) {
        this.regionList = regionList;
    }
}
