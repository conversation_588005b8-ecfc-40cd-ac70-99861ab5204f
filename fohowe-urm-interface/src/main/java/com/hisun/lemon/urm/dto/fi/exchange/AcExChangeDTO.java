package com.hisun.lemon.urm.dto.fi.exchange;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ClientValidated
@ApiModel(value="AcExChangeDTO", description="兑换对象")
public class AcExChangeDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private AcExchangeBean mainInfo = new AcExchangeBean();
	
	@ApiModelProperty(name="exchangeNo",value="编号",required=false,dataType="String")
    private String exchangeNo;
	
	@ApiModelProperty(name="userCode",value="用户编号(付款方、收款方)",required=false,dataType="String")
	private String userCode;
	@ApiModelProperty(name="minCreateTime",value="开始创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minCreateTime;
	@ApiModelProperty(name="maxCreateTime",value="结束创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxCreateTime;
	@ApiModelProperty(name="",value="开始期数",required=false,dataType="String")
    private String minPeriodWeek;
	@ApiModelProperty(name="",value="结束期数",required=false,dataType="String")
    private String maxPeriodWeek;
	
	@ApiModelProperty(value="兑换类型",required=false)
	private Integer exType;
	
	@ApiModelProperty(value="兑换类型",required=false)
	private Integer promotionStatus;
	
	@ApiModelProperty(name="exchangeBeans",value="批量兑换",required=false)
	List<AcExchangeBean> exchangeBeans = new ArrayList<AcExchangeBean>();
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public AcExchangeBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(AcExchangeBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public LocalDateTime getMinCreateTime() {
		return minCreateTime;
	}
	public void setMinCreateTime(LocalDateTime minCreateTime) {
		this.minCreateTime = minCreateTime;
	}
	public LocalDateTime getMaxCreateTime() {
		return maxCreateTime;
	}
	public void setMaxCreateTime(LocalDateTime maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
	public String getMinPeriodWeek() {
		return minPeriodWeek;
	}
	public void setMinPeriodWeek(String minPeriodWeek) {
		this.minPeriodWeek = minPeriodWeek;
	}
	public String getMaxPeriodWeek() {
		return maxPeriodWeek;
	}
	public void setMaxPeriodWeek(String maxPeriodWeek) {
		this.maxPeriodWeek = maxPeriodWeek;
	}
	
	public Integer getExType() {
		return exType;
	}
	public void setExType(Integer exType) {
		this.exType = exType;
	}
	public List<AcExchangeBean> getExchangeBeans() {
		return exchangeBeans;
	}
	public void setExchangeBeans(List<AcExchangeBean> exchangeBeans) {
		this.exchangeBeans = exchangeBeans;
	}
	public String getExchangeNo() {
		return exchangeNo;
	}
	public void setExchangeNo(String exchangeNo) {
		this.exchangeNo = exchangeNo;
	}
	public Integer getPromotionStatus() {
		return promotionStatus;
	}
	public void setPromotionStatus(Integer promotionStatus) {
		this.promotionStatus = promotionStatus;
	}
	
}
