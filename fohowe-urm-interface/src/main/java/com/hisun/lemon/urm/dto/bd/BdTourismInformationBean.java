package com.hisun.lemon.urm.dto.bd;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
public class BdTourismInformationBean extends BaseDO {
    /***/
    @ApiModelProperty(name="id",value="*")
    private Long id;


    /**名额确认 【1】参与旅游 【2】 保留资格【3】领取产品*/
    @ApiModelProperty(name="vcStatus",value="名额确认*")
    private Integer vcStatus;


    /**旅游人员确认 【1】本人使用 【2】转让经销商 【3】转让非经销商*/
    @ApiModelProperty(name="fiCheckeCode",value="旅游人员确认")
    private Integer fiCheckeCode;


    /**个人电脑的编号*/
    @ApiModelProperty(name="memberNo",value="个人电脑的编号")
    private String memberNo;


    /**代办处*/
    @ApiModelProperty(name="agentNo",value="代办处")
    private String agentNo;


    /**分公司*/
    @ApiModelProperty(name="companyCode",value="分公司")
    private String companyCode;


    /**个人级别*/
    @ApiModelProperty(name="memberType",value="个人级别")
    private String memberType;


    /**经销商姓氏（母语）*/
    @ApiModelProperty(name="memberLastName",value="经销商姓氏（母语）")
    private String memberLastName;


    /**经销商名字（母语）*/
    @ApiModelProperty(name="menberFirstName",value="经销商名字（母语）")
    private String menberFirstName;


    /**经销商姓氏（英语）*/
    @ApiModelProperty(name="passportsLastName",value="经销商姓氏（英语）")
    private String passportsLastName;


    /**经销商名字（英语）*/
    @ApiModelProperty(name="passportsFirstName",value="经销商名字（英语）")
    private String passportsFirstName;


    /**性别*/
    @ApiModelProperty(name="sex",value="性别")
    private String sex;


    /**国籍*/
    @ApiModelProperty(name="menberNationality",value="国籍")
    private String menberNationality;


    /**出生日期*/
    @ApiModelProperty(name="menberBirthday",value="出生日期")
    private String menberBirthday;


    /**护照号码*/
    @ApiModelProperty(name="passportsNo",value="护照号码")
    private String passportsNo;


    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "")
	/**护照发放日期*/
    @ApiModelProperty(name="passportsData",value="护照发放日期")
    private LocalDate passportsData;


    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "")
	/**护照有效日期*/
    @ApiModelProperty(name="passportsEffectiveData",value="护照有效日期")
    private LocalDate passportsEffectiveData;


    /**是否穆斯林*/
    @ApiModelProperty(name="fiVcStatus",value="是否穆斯林")
    private Integer fiVcStatus;


    /**是否夫妻同行*/
    @ApiModelProperty(name="fiHusbandPeer",value="是否夫妻同行")
    private Integer fiHusbandPeer;


    /**妻子护照*/
    @ApiModelProperty(name="wifePassport",value="妻子护照")
    private String wifePassport;


    /**是否儿童同行*/
    @ApiModelProperty(name="fiChildren",value="是否儿童同行")
    private Integer fiChildren;


    /**儿童同行*/
    @ApiModelProperty(name="childrenPassportNo",value="儿童同行")
    private String childrenPassportNo;


    /**上传护照首页*/
    @ApiModelProperty(name="prssportPicPath",value="上传护照首页")
    private String prssportPicPath;


    /**上传护照签证页*/
    @ApiModelProperty(name="visaPrssportPicPath",value="上传护照签证页")
    private String visaPrssportPicPath;

    private Integer finishVcStatus;

    public Integer getFinishVcStatus() {
        return finishVcStatus;
    }

    public void setFinishVcStatus(Integer finishVcStatus) {
        this.finishVcStatus = finishVcStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public Integer getFiCheckeCode() {
        return fiCheckeCode;
    }

    public void setFiCheckeCode(Integer fiCheckeCode) {
        this.fiCheckeCode = fiCheckeCode;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getMemberType() {
        return memberType;
    }

    public void setMemberType(String memberType) {
        this.memberType = memberType;
    }

    public String getMemberLastName() {
        return memberLastName;
    }

    public void setMemberLastName(String memberLastName) {
        this.memberLastName = memberLastName;
    }

    public String getMenberFirstName() {
        return menberFirstName;
    }

    public void setMenberFirstName(String menberFirstName) {
        this.menberFirstName = menberFirstName;
    }

    public String getPassportsLastName() {
        return passportsLastName;
    }

    public void setPassportsLastName(String passportsLastName) {
        this.passportsLastName = passportsLastName;
    }

    public String getPassportsFirstName() {
        return passportsFirstName;
    }

    public void setPassportsFirstName(String passportsFirstName) {
        this.passportsFirstName = passportsFirstName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getMenberNationality() {
        return menberNationality;
    }

    public void setMenberNationality(String menberNationality) {
        this.menberNationality = menberNationality;
    }

    public String getMenberBirthday() {
        return menberBirthday;
    }

    public void setMenberBirthday(String menberBirthday) {
        this.menberBirthday = menberBirthday;
    }

    public String getPassportsNo() {
        return passportsNo;
    }

    public void setPassportsNo(String passportsNo) {
        this.passportsNo = passportsNo;
    }

    public LocalDate getPassportsData() {
        return passportsData;
    }

    public void setPassportsData(LocalDate passportsData) {
        this.passportsData = passportsData;
    }

    public LocalDate getPassportsEffectiveData() {
        return passportsEffectiveData;
    }

    public void setPassportsEffectiveData(LocalDate passportsEffectiveData) {
        this.passportsEffectiveData = passportsEffectiveData;
    }

    public Integer getFiVcStatus() {
        return fiVcStatus;
    }

    public void setFiVcStatus(Integer fiVcStatus) {
        this.fiVcStatus = fiVcStatus;
    }

    public Integer getFiHusbandPeer() {
        return fiHusbandPeer;
    }

    public void setFiHusbandPeer(Integer fiHusbandPeer) {
        this.fiHusbandPeer = fiHusbandPeer;
    }

    public String getWifePassport() {
        return wifePassport;
    }

    public void setWifePassport(String wifePassport) {
        this.wifePassport = wifePassport;
    }

    public Integer getFiChildren() {
        return fiChildren;
    }

    public void setFiChildren(Integer fiChildren) {
        this.fiChildren = fiChildren;
    }

    public String getChildrenPassportNo() {
        return childrenPassportNo;
    }

    public void setChildrenPassportNo(String childrenPassportNo) {
        this.childrenPassportNo = childrenPassportNo;
    }

    public String getPrssportPicPath() {
        return prssportPicPath;
    }

    public void setPrssportPicPath(String prssportPicPath) {
        this.prssportPicPath = prssportPicPath;
    }

    public String getVisaPrssportPicPath() {
        return visaPrssportPicPath;
    }

    public void setVisaPrssportPicPath(String visaPrssportPicPath) {
        this.visaPrssportPicPath = visaPrssportPicPath;
    }
}