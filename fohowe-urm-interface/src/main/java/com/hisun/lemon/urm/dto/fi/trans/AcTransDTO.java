package com.hisun.lemon.urm.dto.fi.trans;

import java.time.LocalDateTime;
import java.util.Arrays;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ClientValidated
@ApiModel(value="AcTransDTO", description="转账对象")
public class AcTransDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private AcTransBean mainInfo = new AcTransBean();
	@ApiModelProperty(name="userCode",value="用户编号(付款方、收款方)",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(name="inUserCode",value="用户编号(收款方)",required=false,dataType="String")
    private String inUserCode;
	@ApiModelProperty(name="outUserCode",value="用户编号(付款方)",required=false,dataType="String")
    private String outUserCode;
	@ApiModelProperty(name="companyCode",value="分公司编号",required=false,dataType="String")
	private String companyCode; 
	@ApiModelProperty(name="outCompanyCode",value="转出分公司编号",required=false,dataType="String")
    private String outCompanyCode;
	@ApiModelProperty(name="inCompanyCode",value="转入分公司编号",required=false,dataType="String")
	private String inCompanyCode;
	@ApiModelProperty(name="minCreateTime",value="开始创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minCreateTime;
	@ApiModelProperty(name="maxCreateTime",value="结束创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxCreateTime;
	@ApiModelProperty(name="acType",value="账户类型",required=false)
    private String acType;
	@ApiModelProperty(name="outAgentNo",value="转出代办处",required=false,dataType="String")
    private String outAgentNo;
    @ApiModelProperty(name="inAgentNo",value="转入代办处",required=false,dataType="String")
    private String inAgentNo; 
    @ApiModelProperty(name="startWeek",value="开始期次",required=false,dataType="String")
    private Integer startWeek; 
    @ApiModelProperty(name="endWeek",value="结束期次",required=false,dataType="String")
    private Integer endWeek; 
    @ApiModelProperty(name="transNo",value="单据编号",required=false,dataType="String")
    private String transNo; 
    @ApiModelProperty(name="agentMemo",value="代办处备注",required=false,dataType="String")
    private String agentMemo; 
    @ApiModelProperty(value="批量审核ID",required=false)
	private String[] idArr;
    @ApiModelProperty(name="status",value="状态，1:新建 ",required=false,dataType="String")
    private String status;
	@ApiModelProperty(name="minCheckTime",value="开始确认时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minCheckTime;
	@ApiModelProperty(name="maxCheckTime",value="结束确认时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxCheckTime;
	 @ApiModelProperty(name="memberName",value="经销商名称",required=false,dataType="String")
	 private String memberName; 
	 
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public AcTransBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(AcTransBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getInUserCode() {
		return inUserCode;
	}
	public void setInUserCode(String inUserCode) {
		this.inUserCode = inUserCode;
	}
	public String getOutUserCode() {
		return outUserCode;
	}
	public void setOutUserCode(String outUserCode) {
		this.outUserCode = outUserCode;
	}
	public String getOutCompanyCode() {
		return outCompanyCode;
	}
	public void setOutCompanyCode(String outCompanyCode) {
		this.outCompanyCode = outCompanyCode;
	}
	public String getInCompanyCode() {
		return inCompanyCode;
	}
	public void setInCompanyCode(String inCompanyCode) {
		this.inCompanyCode = inCompanyCode;
	}
	public LocalDateTime getMinCreateTime() {
		return minCreateTime;
	}
	public void setMinCreateTime(LocalDateTime minCreateTime) {
		this.minCreateTime = minCreateTime;
	}
	public LocalDateTime getMaxCreateTime() {
		return maxCreateTime;
	}
	public void setMaxCreateTime(LocalDateTime maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getOutAgentNo() {
		return outAgentNo;
	}
	public void setOutAgentNo(String outAgentNo) {
		this.outAgentNo = outAgentNo;
	}
	public String getInAgentNo() {
		return inAgentNo;
	}
	public void setInAgentNo(String inAgentNo) {
		this.inAgentNo = inAgentNo;
	}
	public Integer getStartWeek() {
		return startWeek;
	}
	public void setStartWeek(Integer startWeek) {
		this.startWeek = startWeek;
	}
	public Integer getEndWeek() {
		return endWeek;
	}
	public void setEndWeek(Integer endWeek) {
		this.endWeek = endWeek;
	}
	public String getTransNo() {
		return transNo;
	}
	public void setTransNo(String transNo) {
		this.transNo = transNo;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentMemo() {
		return agentMemo;
	}
	public void setAgentMemo(String agentMemo) {
		this.agentMemo = agentMemo;
	}
	public String[] getIdArr() {
		return idArr;
	}
	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public LocalDateTime getMinCheckTime() {
		return minCheckTime;
	}
	public void setMinCheckTime(LocalDateTime minCheckTime) {
		this.minCheckTime = minCheckTime;
	}
	public LocalDateTime getMaxCheckTime() {
		return maxCheckTime;
	}
	public void setMaxCheckTime(LocalDateTime maxCheckTime) {
		this.maxCheckTime = maxCheckTime;
	}
	public String getMemberName() {
		return memberName;
	}
	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	@Override
	public String toString() {
		return "AcTransDTO [baseInfo=" + baseInfo + ", mainInfo=" + mainInfo + ", userCode=" + userCode
				+ ", inUserCode=" + inUserCode + ", outUserCode=" + outUserCode + ", companyCode=" + companyCode
				+ ", outCompanyCode=" + outCompanyCode + ", inCompanyCode=" + inCompanyCode + ", minCreateTime="
				+ minCreateTime + ", maxCreateTime=" + maxCreateTime + ", acType=" + acType + ", outAgentNo="
				+ outAgentNo + ", inAgentNo=" + inAgentNo + ", startWeek=" + startWeek + ", endWeek=" + endWeek
				+ ", transNo=" + transNo + ", agentMemo=" + agentMemo + ", idArr=" + Arrays.toString(idArr)
				+ ", status=" + status + ", minCheckTime=" + minCheckTime + ", maxCheckTime=" + maxCheckTime
				+ ", memberName=" + memberName + "]";
	}
}
