package com.hisun.lemon.urm.dto.fi.fgc;

import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@ClientValidated
@ApiModel(value = "PersonalFgcRspDTO", description = "个人fgc信息传输对象")
public class PersonalFgcRspDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "经销商编号")
    private String memberNo;

    @ApiModelProperty(value = "fgc单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "黄金单价")
    private BigDecimal goldPrive;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "个人账户fgc余额")
    private BigDecimal fgcBalance;

    @ApiModelProperty(value = "fgc可购数量")
    private BigDecimal preorderAmount;

    @ApiModelProperty(value = "起存最小值")
    private String minSaveFgc;

    @ApiModelProperty(value = "十年起存最小值")
    private String tenMinSaveFgc;


    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getFgcBalance() {
        return fgcBalance;
    }

    public void setFgcBalance(BigDecimal fgcBalance) {
        this.fgcBalance = fgcBalance;
    }

    public BigDecimal getPreorderAmount() {
        return preorderAmount;
    }

    public void setPreorderAmount(BigDecimal preorderAmount) {
        this.preorderAmount = preorderAmount;
    }

    public String getMinSaveFgc() {
        return minSaveFgc;
    }

    public void setMinSaveFgc(String minSaveFgc) {
        this.minSaveFgc = minSaveFgc;
    }

    public String getTenMinSaveFgc() {
        return tenMinSaveFgc;
    }

    public void setTenMinSaveFgc(String tenMinSaveFgc) {
        this.tenMinSaveFgc = tenMinSaveFgc;
    }

    public BigDecimal getGoldPrive() {
        return goldPrive;
    }

    public void setGoldPrive(BigDecimal goldPrive) {
        this.goldPrive = goldPrive;
    }
}
