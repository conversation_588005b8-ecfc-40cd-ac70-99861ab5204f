package com.hisun.lemon.urm.dto.fi.tickets;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

public class TicketsManagerVO {
	@ApiModelProperty(value="分页相关-页码")
	private int pageNum = 1;
	@ApiModelProperty(value="分页相关-每页数量")
	private int pageSize = 20;
	@ApiModelProperty(value="分页相关-总记录数",hidden=true)
	private int totalCount = 0;
	
	List<TicketsManager> dataList;

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}

	public List<TicketsManager> getDataList() {
		return dataList;
	}

	public void setDataList(List<TicketsManager> dataList) {
		this.dataList = dataList;
	}
}
