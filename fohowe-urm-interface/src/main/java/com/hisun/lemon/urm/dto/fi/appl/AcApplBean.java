 package com.hisun.lemon.urm.dto.fi.appl;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcApplBean 传输对象
 * <AUTHOR>
 * @date 2017年11月10号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcApplBean", description="账户申购对象")
public class AcApplBean{
	@ApiModelProperty(name="id",value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(value="申请单号",required=false,dataType="String")
    private String applNo;//AL2017070700002
	@ApiModelProperty(value="用户编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(value="交易类型：提现",required=false,dataType="String")
    private String applType;
	/*=========申领新增信息==========*/
	@ApiModelProperty(value="账户类型",required=false,dataType="String")
    private String acType;
	@ApiModelProperty(value="申领金额",required=false,dataType="BigDecimal")
    private BigDecimal amount;
	@ApiModelProperty(value="实发金额",required=false,dataType="BigDecimal")
    private BigDecimal sendAmt;
	@ApiModelProperty(value="手续费",required=false,dataType="BigDecimal")
    private BigDecimal fees;
	@ApiModelProperty(value="摘要(公司)",required=false,dataType="String")
    private String remark;
	@ApiModelProperty(value="备注(会员)",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(value="本地货币代码",required=false,dataType="String")
    private String localCurrency;
	@ApiModelProperty(value="本地货币金额",required=false,dataType="BigDecimal")
    private BigDecimal localMoney;
	@ApiModelProperty(value="汇率",required=false,dataType="BigDecimal")
    private BigDecimal rate;
	
	@ApiModelProperty(value="申请人编号",required=false,dataType="String")
    private String createrCode;
	@ApiModelProperty(value="申请人名称",required=false,dataType="String")
    private String createrName;
	@ApiModelProperty(value="申请时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
	@ApiModelProperty(value="状态：1新建6已取消7已退回2分公司已审核3总公司已审核5发放成功4财务已审核",required=false,dataType="String")
    private String status;
	@ApiModelProperty(value="审核人编号",required=false,dataType="String")
    private String checkerCode;
	@ApiModelProperty(value="审核人名称",required=false,dataType="String")
    private String checkerName;
	@ApiModelProperty(value="审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkTime;
	@ApiModelProperty(value="失败原因",required=false,dataType="String")
    private String falseReason;
	
	@ApiModelProperty(value="总公司审核编号",required=false,dataType="String")
    private String reCheckerCode;
	@ApiModelProperty(value="总公司审核人名称",required=false,dataType="String")
    private String reCheckerName;
	@ApiModelProperty(value="总公司审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime reCheckTime;
	
	@ApiModelProperty(value="提现取消单号",required=false,dataType="String")
    private String cancelApplNo;
	@ApiModelProperty(value="作废人",required=false,dataType="String")
    private String cancelCode;
	@ApiModelProperty(value="作废时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime cancelTime;
	
	@ApiModelProperty(value="发放人",required=false,dataType="String")
    private String senderCode;
    /**
     * @Fields sendTime 发放时间
     */
	@ApiModelProperty(value="发放时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime sendTime;
	@ApiModelProperty(value="发放人姓名",required=false,dataType="String")
    private String senderName;
	@ApiModelProperty(value="发放计划编号",required=false,dataType="String")
    private String operNo;
	
	@ApiModelProperty(value="所属分公司",required=false,dataType="String")
    private String companyCode;
    
	@ApiModelProperty(value="财务确认状态 0:未确认;1:部分确认;2:已确认",required=false,dataType="String")
    private String fiCheckStatus;
	@ApiModelProperty(value="财务确认人编号",required=false,dataType="String")
    private String fiCheckerCode;
	@ApiModelProperty(value="财务确认人名称 ",required=false,dataType="String")
    private String fiCheckerName;
    @ApiModelProperty(value="财务确认时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime fiCheckTime;
    @ApiModelProperty(value="已财务确认金额",required=false,dataType="BigDecimal")
    private BigDecimal recAmount;

    @ApiModelProperty(value="提现申请期数",required=false,dataType="String")
    private String periodWeek;
    @ApiModelProperty(value="代办处编号",required=false,dataType="String")
    private String agentNo;
    @ApiModelProperty(value="波兰商城类别 1：自然人  2：个体户",required=false,dataType="String")
    private String malltype;
    @ApiModelProperty(value="波兰商城的税金",required=false,dataType="String")
    private String taxmoney;
    
    private Integer finStatus;
    
	private Integer finNum;
	
	private Integer accountId;
	
	private String accountName;
	
	private String subjectNo;
	
	private BigDecimal localFinMoney;
	private String userType;
	private String configType;

	public String getConfigType() {
		return configType;
	}

	public void setConfigType(String configType) {
		this.configType = configType;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getApplNo() {
		return applNo;
	}
	public void setApplNo(String applNo) {
		this.applNo = applNo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getApplType() {
		return applType;
	}
	public void setApplType(String applType) {
		this.applType = applType;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public BigDecimal getSendAmt() {
		return sendAmt;
	}
	public void setSendAmt(BigDecimal sendAmt) {
		this.sendAmt = sendAmt;
	}
	public BigDecimal getFees() {
		return fees;
	}
	public void setFees(BigDecimal fees) {
		this.fees = fees;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getLocalCurrency() {
		return localCurrency;
	}
	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}
	public BigDecimal getLocalMoney() {
		return localMoney;
	}
	public void setLocalMoney(BigDecimal localMoney) {
		this.localMoney = localMoney;
	}
	public BigDecimal getRate() {
		return rate;
	}
	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}
	public String getCreaterCode() {
		return createrCode;
	}
	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}
	public String getCreaterName() {
		return createrName;
	}
	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCheckerCode() {
		return checkerCode;
	}
	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}
	public String getCheckerName() {
		return checkerName;
	}
	public void setCheckerName(String checkerName) {
		this.checkerName = checkerName;
	}
	public LocalDateTime getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(LocalDateTime checkTime) {
		this.checkTime = checkTime;
	}
	public String getFalseReason() {
		return falseReason;
	}
	public void setFalseReason(String falseReason) {
		this.falseReason = falseReason;
	}
	public String getReCheckerCode() {
		return reCheckerCode;
	}
	public void setReCheckerCode(String reCheckerCode) {
		this.reCheckerCode = reCheckerCode;
	}
	public String getReCheckerName() {
		return reCheckerName;
	}
	public void setReCheckerName(String reCheckerName) {
		this.reCheckerName = reCheckerName;
	}
	public LocalDateTime getReCheckTime() {
		return reCheckTime;
	}
	public void setReCheckTime(LocalDateTime reCheckTime) {
		this.reCheckTime = reCheckTime;
	}
	public String getCancelApplNo() {
		return cancelApplNo;
	}
	public void setCancelApplNo(String cancelApplNo) {
		this.cancelApplNo = cancelApplNo;
	}
	public String getCancelCode() {
		return cancelCode;
	}
	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}
	public LocalDateTime getCancelTime() {
		return cancelTime;
	}
	public void setCancelTime(LocalDateTime cancelTime) {
		this.cancelTime = cancelTime;
	}
	public String getSenderCode() {
		return senderCode;
	}
	public void setSenderCode(String senderCode) {
		this.senderCode = senderCode;
	}
	public LocalDateTime getSendTime() {
		return sendTime;
	}
	public void setSendTime(LocalDateTime sendTime) {
		this.sendTime = sendTime;
	}
	public String getSenderName() {
		return senderName;
	}
	public void setSenderName(String senderName) {
		this.senderName = senderName;
	}
	public String getOperNo() {
		return operNo;
	}
	public void setOperNo(String operNo) {
		this.operNo = operNo;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getFiCheckStatus() {
		return fiCheckStatus;
	}
	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}
	public String getFiCheckerCode() {
		return fiCheckerCode;
	}
	public void setFiCheckerCode(String fiCheckerCode) {
		this.fiCheckerCode = fiCheckerCode;
	}
	public String getFiCheckerName() {
		return fiCheckerName;
	}
	public void setFiCheckerName(String fiCheckerName) {
		this.fiCheckerName = fiCheckerName;
	}
	public LocalDateTime getFiCheckTime() {
		return fiCheckTime;
	}
	public void setFiCheckTime(LocalDateTime fiCheckTime) {
		this.fiCheckTime = fiCheckTime;
	}
	public BigDecimal getRecAmount() {
		return recAmount;
	}
	public void setRecAmount(BigDecimal recAmount) {
		this.recAmount = recAmount;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getMalltype() {
		return malltype;
	}
	public void setMalltype(String malltype) {
		this.malltype = malltype;
	}
	public String getTaxmoney() {
		return taxmoney;
	}
	public void setTaxmoney(String taxmoney) {
		this.taxmoney = taxmoney;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public Integer getFinStatus() {
		return finStatus;
	}
	public void setFinStatus(Integer finStatus) {
		this.finStatus = finStatus;
	}
	public Integer getFinNum() {
		return finNum;
	}
	public void setFinNum(Integer finNum) {
		this.finNum = finNum;
	}
	public BigDecimal getLocalFinMoney() {
		return localFinMoney;
	}
	public void setLocalFinMoney(BigDecimal localFinMoney) {
		this.localFinMoney = localFinMoney;
	}

	public Integer getAccountId() {
		return accountId;
	}

	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getSubjectNo() {
		return subjectNo;
	}

	public void setSubjectNo(String subjectNo) {
		this.subjectNo = subjectNo;
	}
}
