 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceDTO", description="账户传输对象")
public class AcBalanceDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value = "基本查询信息")
	private MIBaseQueryInfo baseInfo = new MIBaseQueryInfo();
	@ApiModelProperty(value = "基本信息")
	private AcBalanceBean mainInfo = new AcBalanceBean();

	@ApiModelProperty(value = "用户编号", required = false, dataType = "String")
	@NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
	private String userCode;
	
	@ApiModelProperty(value = "账户类型", required = false)
	private String acType;
	
	@ApiModelProperty(value = "状态", required = false, dataType = "String")
	private String status;
	
	@ApiModelProperty(value = "用户类型", required = false)
	private String userType;
	
	@ApiModelProperty(value = "大于", required = false)
	private String bonusGreate;

	@ApiModelProperty(value = "等于", required = false)
	private String bonusEqual;

	@ApiModelProperty(value = "小于", required = false)
	private String bonusLeast;

	@ApiModelProperty(value = "大于等于", required = false)
	private String bonusGreateEq;

	@ApiModelProperty(value = "小于等于", required = false)
	private String bonusLeastEq;

	@ApiModelProperty(value = "查询范围", required = false)
	private BigDecimal bonusValue;
    
	@ApiModelProperty(value = "大于", required = false)
	private String oweGreate;

	@ApiModelProperty(value = "等于", required = false)
	private String oweEqual;

	@ApiModelProperty(value = "小于", required = false)
	private String oweLeast;

	@ApiModelProperty(value = "大于等于", required = false)
	private String oweGreateEq;

	@ApiModelProperty(value = "小于等于", required = false)
	private String oweLeastEq;
	
	private BigDecimal oweValue;
	
	
	
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public AcBalanceBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(AcBalanceBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getBonusGreate() {
		return bonusGreate;
	}
	public void setBonusGreate(String bonusGreate) {
		this.bonusGreate = bonusGreate;
	}
	public String getBonusEqual() {
		return bonusEqual;
	}
	public void setBonusEqual(String bonusEqual) {
		this.bonusEqual = bonusEqual;
	}
	public String getBonusLeast() {
		return bonusLeast;
	}
	public void setBonusLeast(String bonusLeast) {
		this.bonusLeast = bonusLeast;
	}
	public String getBonusGreateEq() {
		return bonusGreateEq;
	}
	public void setBonusGreateEq(String bonusGreateEq) {
		this.bonusGreateEq = bonusGreateEq;
	}
	public String getBonusLeastEq() {
		return bonusLeastEq;
	}
	public void setBonusLeastEq(String bonusLeastEq) {
		this.bonusLeastEq = bonusLeastEq;
	}
	public BigDecimal getBonusValue() {
		return bonusValue;
	}
	public void setBonusValue(BigDecimal bonusValue) {
		this.bonusValue = bonusValue;
	}
	public String getOweGreate() {
		return oweGreate;
	}
	public void setOweGreate(String oweGreate) {
		this.oweGreate = oweGreate;
	}
	public String getOweEqual() {
		return oweEqual;
	}
	public void setOweEqual(String oweEqual) {
		this.oweEqual = oweEqual;
	}
	public String getOweLeast() {
		return oweLeast;
	}
	public void setOweLeast(String oweLeast) {
		this.oweLeast = oweLeast;
	}
	public String getOweGreateEq() {
		return oweGreateEq;
	}
	public void setOweGreateEq(String oweGreateEq) {
		this.oweGreateEq = oweGreateEq;
	}
	public String getOweLeastEq() {
		return oweLeastEq;
	}
	public void setOweLeastEq(String oweLeastEq) {
		this.oweLeastEq = oweLeastEq;
	}
	public BigDecimal getOweValue() {
		return oweValue;
	}
	public void setOweValue(BigDecimal oweValue) {
		this.oweValue = oweValue;
	}
	
}
