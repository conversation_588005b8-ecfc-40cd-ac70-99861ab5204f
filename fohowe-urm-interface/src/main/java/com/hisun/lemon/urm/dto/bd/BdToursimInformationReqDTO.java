package com.hisun.lemon.urm.dto.bd;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;

public class BdToursimInformationReqDTO {

    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private int pageNum=1;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    private int pageSize=20;
    
    private Long id;

    private Integer vcStatus;

    private Integer fiCheckeCode;

    private String memberNo;

    private String agentNo;

    private String companyCode;

    private String memberType;

    private String memberLastName;

    private String menberFirstName;

    private String passportsLastName;

    private String passportsFirstName;

    private String sex;

    private String menberNationality;

    private String menberBirthday;

    private String passportsNo;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate passportsData;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate passportsEffectiveData;

    private Integer fiVcStatus;

    private Integer fiHusbandPeer;

    private String wifePassport;

    private Integer fiChildren;

    private String childrenPassportNo;

    private String prssportPicPath;

    private String visaPrssportPicPath;

    private Long turId;
    private Integer finishVcStatus;
    private String lineStatus;

    public String getLineStatus() {
        return lineStatus;
    }

    public void setLineStatus(String lineStatus) {
        this.lineStatus = lineStatus;
    }

    public Integer getFinishVcStatus() {
        return finishVcStatus;
    }

    public void setFinishVcStatus(Integer finishVcStatus) {
        this.finishVcStatus = finishVcStatus;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public Integer getFiCheckeCode() {
        return fiCheckeCode;
    }

    public void setFiCheckeCode(Integer fiCheckeCode) {
        this.fiCheckeCode = fiCheckeCode;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getMemberType() {
        return memberType;
    }

    public void setMemberType(String memberType) {
        this.memberType = memberType;
    }

    public String getMemberLastName() {
        return memberLastName;
    }

    public void setMemberLastName(String memberLastName) {
        this.memberLastName = memberLastName;
    }

    public String getMenberFirstName() {
        return menberFirstName;
    }

    public void setMenberFirstName(String menberFirstName) {
        this.menberFirstName = menberFirstName;
    }

    public String getPassportsLastName() {
        return passportsLastName;
    }

    public void setPassportsLastName(String passportsLastName) {
        this.passportsLastName = passportsLastName;
    }

    public String getPassportsFirstName() {
        return passportsFirstName;
    }

    public void setPassportsFirstName(String passportsFirstName) {
        this.passportsFirstName = passportsFirstName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getMenberNationality() {
        return menberNationality;
    }

    public void setMenberNationality(String menberNationality) {
        this.menberNationality = menberNationality;
    }

    public String getMenberBirthday() {
        return menberBirthday;
    }

    public void setMenberBirthday(String menberBirthday) {
        this.menberBirthday = menberBirthday;
    }

    public String getPassportsNo() {
        return passportsNo;
    }

    public void setPassportsNo(String passportsNo) {
        this.passportsNo = passportsNo;
    }
    
    public LocalDate getPassportsData() {
		return passportsData;
	}

	public void setPassportsData(LocalDate passportsData) {
		this.passportsData = passportsData;
	}

	public LocalDate getPassportsEffectiveData() {
		return passportsEffectiveData;
	}

	public void setPassportsEffectiveData(LocalDate passportsEffectiveData) {
		this.passportsEffectiveData = passportsEffectiveData;
	}

	public Integer getFiVcStatus() {
        return fiVcStatus;
    }

    public void setFiVcStatus(Integer fiVcStatus) {
        this.fiVcStatus = fiVcStatus;
    }

    public Integer getFiHusbandPeer() {
        return fiHusbandPeer;
    }

    public void setFiHusbandPeer(Integer fiHusbandPeer) {
        this.fiHusbandPeer = fiHusbandPeer;
    }

    public String getWifePassport() {
        return wifePassport;
    }

    public void setWifePassport(String wifePassport) {
        this.wifePassport = wifePassport;
    }

    public Integer getFiChildren() {
        return fiChildren;
    }

    public void setFiChildren(Integer fiChildren) {
        this.fiChildren = fiChildren;
    }

    public String getChildrenPassportNo() {
        return childrenPassportNo;
    }

    public void setChildrenPassportNo(String childrenPassportNo) {
        this.childrenPassportNo = childrenPassportNo;
    }

    public String getPrssportPicPath() {
        return prssportPicPath;
    }

    public void setPrssportPicPath(String prssportPicPath) {
        this.prssportPicPath = prssportPicPath;
    }

    public String getVisaPrssportPicPath() {
        return visaPrssportPicPath;
    }

    public void setVisaPrssportPicPath(String visaPrssportPicPath) {
        this.visaPrssportPicPath = visaPrssportPicPath;
    }

	public Long getTurId() {
		return turId;
	}

	public void setTurId(Long turId) {
		this.turId = turId;
	}
    
}
