 package com.hisun.lemon.urm.dto.fi.appl;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotEmpty;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.dto.mi.group.ValidGroup1;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 账户申领发放传输对象
 * <AUTHOR>
 * @date 2017年11月16号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcApplSendDTO", description="账户申领发放传输对象")
public class AcApplSendDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="申请编号",required=false)
	private String applNo;
	
	@ApiModelProperty(value="操作日期",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime operDate;
	
	@ApiModelProperty(value="建立者帐号",required=false)
	private String operCode;
	
	@ApiModelProperty(value="建立者名称",required=false)
	private String operName;
	
	@ApiModelProperty(value="状态，0新增，1已扣奖金钱包,2正在发放，3发放错误 4发放完成",required=false)
	private String status;
	
	@ApiModelProperty(value="汇出银行",required=false)
	private String sendBank;
	
	@ApiModelProperty(value="汇款银行 ",required=false)
	private String receiveBank;
	
	
	@ApiModelProperty(value="金额下限",required=false)
	private BigDecimal amountLow;
	
	@ApiModelProperty(value="金额上限",required=false)
	private BigDecimal amountUp;
	
	@ApiModelProperty(value="国别",required=false)
	private String companyCode;
	
	@ApiModelProperty(value="账户类型，f$＝f$，fv＝fv, f0=f000，pv=活跃pv，b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，s1=全球分红，s2=凤凰大使分红",required=false)
	private String acType;
	
	@ApiModelProperty(value="备注(会员)",required=false)
	private String memo;
	
	@ApiModelProperty(value="摘要(公司)",required=false)
	private String remark;

	public String getApplNo() {
		return applNo;
	}

	public void setApplNo(String applNo) {
		this.applNo = applNo;
	}

	public LocalDateTime getOperDate() {
		return operDate;
	}

	public void setOperDate(LocalDateTime operDate) {
		this.operDate = operDate;
	}

	public String getOperCode() {
		return operCode;
	}

	public void setOperCode(String operCode) {
		this.operCode = operCode;
	}

	public String getOperName() {
		return operName;
	}

	public void setOperName(String operName) {
		this.operName = operName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getSendBank() {
		return sendBank;
	}

	public void setSendBank(String sendBank) {
		this.sendBank = sendBank;
	}

	public String getReceiveBank() {
		return receiveBank;
	}

	public void setReceiveBank(String receiveBank) {
		this.receiveBank = receiveBank;
	}

	public BigDecimal getAmountLow() {
		return amountLow;
	}

	public void setAmountLow(BigDecimal amountLow) {
		this.amountLow = amountLow;
	}

	public BigDecimal getAmountUp() {
		return amountUp;
	}

	public void setAmountUp(BigDecimal amountUp) {
		this.amountUp = amountUp;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
