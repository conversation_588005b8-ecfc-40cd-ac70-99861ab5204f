package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.DateConstant;
import com.hisun.lemon.urm.enums.mi.FreezeStatusEnums;
import com.hisun.lemon.urm.enums.mi.FreezeTypeEnums;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月02日
 * @time 下午14:27:30
 *
 */

@ClientValidated
@ApiModel(value="AgentMemFreezeQueryBean", description="代办处传输对象")
public class AgentMemFreezeQueryBean{
	@ApiModelProperty(value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(value="经销商编号",required=false,dataType="String")
    private String memberNo;
	@ApiModelProperty(value="代办处编号",required=false,dataType="String")
    private String agentNo;
	@ApiModelProperty(value="冻结类型 1 代办处及以下经销商 2 经销商",required=false)
    private String freezeType;
	@ApiModelProperty(value="状态：1 生效 2 取消",required=false,dataType="String")
    private String status;
	@ApiModelProperty(value="操作人",required=false,dataType="String")
    private String operCode;
	@ApiModelProperty(value="操作时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime operDate;
	@ApiModelProperty(value="取消人",required=false,dataType="String")
    private String cancelCode;
	@ApiModelProperty(value="取消时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime cancelDate;
	@ApiModelProperty(value="经销商姓名",required=false,dataType="String")
    private String memberName;
	@ApiModelProperty(value="代办处姓名",required=false,dataType="String")
    private String agentName;
	@ApiModelProperty(value="分公司名称",required=false,dataType="String")
    private String companyName;
	@ApiModelProperty(value="分公司编号",required=false,dataType="String")
    private String companyCode;
	@ApiModelProperty(value="注册时间",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime registerDate;
	@ApiModelProperty(value="经销商级别",required=false,dataType="String")
    private String cardType;
	@ApiModelProperty(value="加入期数",required=false,dataType="String")
    private String startWeek;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getMemberNo() {
		return memberNo;
	}
	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getOperCode() {
		return operCode;
	}
	public void setOperCode(String operCode) {
		this.operCode = operCode;
	}
	public LocalDateTime getOperDate() {
		return operDate;
	}
	public void setOperDate(LocalDateTime operDate) {
		this.operDate = operDate;
	}
	public String getCancelCode() {
		return cancelCode;
	}
	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}
	public LocalDateTime getCancelDate() {
		return cancelDate;
	}
	public void setCancelDate(LocalDateTime cancelDate) {
		this.cancelDate = cancelDate;
	}
	public String getMemberName() {
		return memberName;
	}
	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	public String getAgentName() {
		return agentName;
	}
	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getCardType() {
		return cardType;
	}
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}
	public String getStartWeek() {
		return startWeek;
	}
	public void setStartWeek(String startWeek) {
		this.startWeek = startWeek;
	}
	public LocalDateTime getRegisterDate() {
		return registerDate;
	}
	public void setRegisterDate(LocalDateTime registerDate) {
		this.registerDate = registerDate;
	}
	public String getFreezeType() {
		return freezeType;
	}
	public void setFreezeType(String freezeType) {
		this.freezeType = freezeType;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
}
