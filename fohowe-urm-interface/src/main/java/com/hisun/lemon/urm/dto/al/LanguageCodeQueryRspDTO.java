package com.hisun.lemon.urm.dto.al;

public class LanguageCodeQueryRspDTO {
    /**
     * @Fields id 
     */
    private String id;
    /**
     * @Fields langCode 语言编码
     */
    private String langCode;
    /**
     * @Fields langName 语言名称
     */
    private String langName;
    /**
     * @Fields allowedUser 管理人员
     */
    private String allowedUser;

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getLangName() {
        return langName;
    }

    public void setLangName(String langName) {
        this.langName = langName;
    }

    public String getAllowedUser() {
        return allowedUser;
    }

    public void setAllowedUser(String allowedUser) {
        this.allowedUser = allowedUser;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
