 package com.hisun.lemon.urm.dto.fi.appl;

import java.math.BigDecimal;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcApplBean 传输对象
 * <AUTHOR>
 * @date 2017年11月10号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="MaxApplRspDTO", description="账户申购对象")
public class MaxApplRspDTO{
	@ApiModelProperty(value="用户编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(value="最大申请额度",required=false)
	private BigDecimal maxAppl;
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getMaxAppl() {
		return maxAppl;
	}
	public void setMaxAppl(BigDecimal maxAppl) {
		this.maxAppl = maxAppl;
	}
	
	
}
