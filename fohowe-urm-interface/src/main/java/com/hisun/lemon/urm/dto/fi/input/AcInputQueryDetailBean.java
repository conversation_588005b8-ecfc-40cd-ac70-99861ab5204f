package com.hisun.lemon.urm.dto.fi.input;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月14号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputQueryBean", description="账户传输对象")
public class AcInputQueryDetailBean{
    /*=============base data============ */
    @ApiModelProperty(value="所属分公司",required=false,dataType="String")
    private String companyCode;
    @ApiModelProperty(value="代办处",required=false,dataType="String")
    private String agentNo;
    /*=============show data============ */
    @ApiModelProperty(value="记录编号",required=false)
    private long id;
    @ApiModelProperty(value="用户编号",required=false,dataType="String")
    private String userCode;
    @ApiModelProperty(value="期次",required=false,dataType="String")
    private String periodWeek;
    @ApiModelProperty(value="订单号",required=false,dataType="String")
    private String inputNo;
    @ApiModelProperty(value="金额",required=false,dataType="BigDecimal")
    private BigDecimal money;
    @ApiModelProperty(value="金额(适应千分位)",required=false,dataType="String")
    private String moneyStr;
    @ApiModelProperty(value="本地货币代码",required=false,dataType="String")
    private String localCurrency;
    @ApiModelProperty(value="本地货币金额",required=false,dataType="BigDecimal")
    private BigDecimal localMoney;
    @ApiModelProperty(value="本地货币金额(适应千分位)",required=false,dataType="String")
    private String localMoneyStr;
    @ApiModelProperty(value="汇率",required=false,dataType="BigDecimal")
    private BigDecimal rate;
    @ApiModelProperty(value="备注",required=false,dataType="String")
    private String memo;
    @ApiModelProperty(value="摘要",required=false,dataType="String")
    private String remark;
    @ApiModelProperty(value="状态",required=false,dataType="String")
    private String status;
    @ApiModelProperty(value="状态",required=false,dataType="String")
    private String statuse;
    @ApiModelProperty(value="财务确认状态 a:未确认;b:部分确认;c:已确认",required=false,dataType="String")
    private String fiCheckStatus;
    @ApiModelProperty(value="审核时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
    @JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkeTime;
    @ApiModelProperty(value="创建时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
    @JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
    @ApiModelProperty(value="复核时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
    @JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime recheckeTime;
    @ApiModelProperty(value="财务确认备注",required=false,dataType="String")
    private String fiCheckMemo;
    
    private Integer finStatus;

    private Integer finNum;

    @ApiModelProperty(value="本地货币已入账金额",required=false,dataType="BigDecimal")
    private BigDecimal localFinMoney;
    
    private String accountCash;
    private String accountCompany;
    private String accountName;

    private long detailId;
    
    private String createrCode;
    
    private String createrName;
    
    private String cancelCode;
   
    private String cancelName;
    
    private LocalDateTime cancelTime;
 
    private String accountType;
    @ApiModelProperty(value="总计F$",required=false,dataType="BigDecimal")
    private BigDecimal totalF$;
    private BigDecimal recTotalF$;
    private BigDecimal localTotalF$;

    private BigDecimal finTotalF$;

    public BigDecimal getTotalF$() {
        return totalF$;
    }

    public void setTotalF$(BigDecimal totalF$) {
        this.totalF$ = totalF$;
    }

    public BigDecimal getRecTotalF$() {
        return recTotalF$;
    }

    public void setRecTotalF$(BigDecimal recTotalF$) {
        this.recTotalF$ = recTotalF$;
    }

    public BigDecimal getLocalTotalF$() {
        return localTotalF$;
    }

    public void setLocalTotalF$(BigDecimal localTotalF$) {
        this.localTotalF$ = localTotalF$;
    }

    public BigDecimal getFinTotalF$() {
        return finTotalF$;
    }

    public void setFinTotalF$(BigDecimal finTotalF$) {
        this.finTotalF$ = finTotalF$;
    }

    public String getStatuse() {
        return statuse;
    }

    public void setStatuse(String statuse) {
        this.statuse = statuse;
    }

    public String getAccountCash() {
        return accountCash;
    }

    public void setAccountCash(String accountCash) {
        this.accountCash = accountCash;
    }

    public String getAccountCompany() {
        return accountCompany;
    }

    public void setAccountCompany(String accountCompany) {
        this.accountCompany = accountCompany;
    }
    public String getCompanyCode() {
        return companyCode;
    }
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
    public long getId() {
        return id;
    }
    public void setId(long id) {
        this.id = id;
    }
    public String getUserCode() {
        return userCode;
    }
    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }
    public String getPeriodWeek() {
        return periodWeek;
    }
    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }
    public String getInputNo() {
        return inputNo;
    }
    public void setInputNo(String inputNo) {
        this.inputNo = inputNo;
    }
 
    public BigDecimal getMoney() {
        return money;
    }
    public void setMoney(BigDecimal money) {
        this.money = money;
    }
    public String getLocalCurrency() {
        return localCurrency;
    }
    public void setLocalCurrency(String localCurrency) {
        this.localCurrency = localCurrency;
    }
    public BigDecimal getLocalMoney() {
        return localMoney;
    }
    public void setLocalMoney(BigDecimal localMoney) {
        this.localMoney = localMoney;
    }
    public BigDecimal getRate() {
        return rate;
    }
    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }
    public String getMemo() {
        return memo;
    }
    public void setMemo(String memo) {
        this.memo = memo;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public LocalDateTime getCheckeTime() {
        return checkeTime;
    }
    public void setCheckeTime(LocalDateTime checkeTime) {
        this.checkeTime = checkeTime;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getRecheckeTime() {
        return recheckeTime;
    }
    public void setRecheckeTime(LocalDateTime recheckeTime) {
        this.recheckeTime = recheckeTime;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getFiCheckStatus() {
        return fiCheckStatus;
    }
    public void setFiCheckStatus(String fiCheckStatus) {
        this.fiCheckStatus = fiCheckStatus;
    }
    public String getAgentNo() {
        return agentNo;
    }
    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }
    public String getMoneyStr() {
        return moneyStr;
    }
    public void setMoneyStr(String moneyStr) {
        this.moneyStr = moneyStr;
    }
    public String getLocalMoneyStr() {
        return localMoneyStr;
    }
    public void setLocalMoneyStr(String localMoneyStr) {
        this.localMoneyStr = localMoneyStr;
    }
    
    public String getFiCheckMemo() {
        return fiCheckMemo;
    }
    public void setFiCheckMemo(String fiCheckMemo) {
        this.fiCheckMemo = fiCheckMemo;
    }
    public Integer getFinStatus() {
        return finStatus;
    }
    public void setFinStatus(Integer finStatus) {
        this.finStatus = finStatus;
    }
    public BigDecimal getLocalFinMoney() {
        return localFinMoney;
    }
    public void setLocalFinMoney(BigDecimal localFinMoney) {
        this.localFinMoney = localFinMoney;
    }
    public Integer getFinNum() {
        return finNum;
    }
    public void setFinNum(Integer finNum) {
        this.finNum = finNum;
    }
	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public long getDetailId() {
		return detailId;
	}

	public void setDetailId(long detailId) {
		this.detailId = detailId;
	}

	public String getCreaterCode() {
		return createrCode;
	}

	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public String getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}

	public String getCancelName() {
		return cancelName;
	}

	public void setCancelName(String cancelName) {
		this.cancelName = cancelName;
	}

	public LocalDateTime getCancelTime() {
		return cancelTime;
	}

	public void setCancelTime(LocalDateTime cancelTime) {
		this.cancelTime = cancelTime;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

}
