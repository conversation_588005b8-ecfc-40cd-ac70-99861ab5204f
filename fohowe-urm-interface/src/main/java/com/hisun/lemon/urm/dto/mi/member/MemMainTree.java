package com.hisun.lemon.urm.dto.mi.member;


import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * 骨干网络图
 * <AUTHOR>
 * @date 2017-12-18 19:19
 */
public class MemMainTree {
	
	
	@ApiModelProperty(value="用户编号",required= true)
    private String memNo;
    
    @ApiModelProperty(value="推荐编号",hidden= true)
    private String parentMemNo;
    
    @ApiModelProperty(value="用户名称",required= true)
    private String memName;
    
    @ApiModelProperty(value="图片地址Key",required= true)
    private String picUrlKey;
    
    @ApiModelProperty(value="级别名称Key",required= true)
    private String levelKey;
    
    @ApiModelProperty(value="是否有父节点",hidden= true)
    private boolean hasParent;
    
    @ApiModelProperty(value="代办处")
    private String agentNo;
    @ApiModelProperty(value="是否显示信息，true时隐藏，不显示信息")
    private boolean isHidden;
    
    
    @ApiModelProperty(value="左区编号",hidden= true)
    private String leftMem;
    @ApiModelProperty(value="右编号",hidden= true)
    private String rightMem;
    @ApiModelProperty(value="经营权编号",hidden= true)
    private String rightNo;
    @ApiModelProperty(value="是否主经营权",hidden= true)
    private String primRight;
    
    /**
     * 节点的子节点
     */
    private List<MemMainTree> children = new ArrayList<MemMainTree>();

	public boolean isHidden() {
		return isHidden;
	}

	public void setHidden(boolean isHidden) {
		this.isHidden = isHidden;
	}

	public List<MemMainTree> getChildren() {
		return children;
	}

	public void setChildren(List<MemMainTree> children) {
		this.children = children;
	}

	public String getMemNo() {
		return memNo;
	}

	public void setMemNo(String memNo) {
		this.memNo = memNo;
	}

	public String getParentMemNo() {
		return parentMemNo;
	}

	public void setParentMemNo(String parentMemNo) {
		this.parentMemNo = parentMemNo;
	}

	public String getMemName() {
		return memName;
	}

	public void setMemName(String memName) {
		this.memName = memName;
	}

	public String getPicUrlKey() {
		return picUrlKey;
	}

	public void setPicUrlKey(String picUrlKey) {
		this.picUrlKey = picUrlKey;
	}

	public String getLevelKey() {
		return levelKey;
	}

	public void setLevelKey(String levelKey) {
		this.levelKey = levelKey;
	}

	public boolean isHasParent() {
		return hasParent;
	}

	public void setHasParent(boolean hasParent) {
		this.hasParent = hasParent;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getLeftMem() {
		return leftMem;
	}

	public void setLeftMem(String leftMem) {
		this.leftMem = leftMem;
	}

	public String getRightMem() {
		return rightMem;
	}

	public void setRightMem(String rightMem) {
		this.rightMem = rightMem;
	}

	public String getRightNo() {
		return rightNo;
	}

	public void setRightNo(String rightNo) {
		this.rightNo = rightNo;
	}

	public String getPrimRight() {
		return primRight;
	}

	public void setPrimRight(String primRight) {
		this.primRight = primRight;
	}
 





}