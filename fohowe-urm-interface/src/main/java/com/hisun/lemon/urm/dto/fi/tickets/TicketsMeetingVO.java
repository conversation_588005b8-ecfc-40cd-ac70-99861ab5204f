package com.hisun.lemon.urm.dto.fi.tickets;

import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ClientValidated
@ApiModel(value="TicketsMeetingVO", description="转账对象")
public class TicketsMeetingVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
	@ApiModelProperty(value="查询结果集")
	List<TicketsMeetingBean> dataList;
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public List<TicketsMeetingBean> getDataList() {
		return dataList;
	}
	public void setDataList(List<TicketsMeetingBean> dataList) {
		this.dataList = dataList;
	}
}
