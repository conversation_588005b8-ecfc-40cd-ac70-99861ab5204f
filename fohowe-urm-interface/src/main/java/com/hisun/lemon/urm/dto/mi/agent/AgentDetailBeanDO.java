package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.enums.PaperTypeEnums;
import com.hisun.lemon.fohowe.common.enums.Sex;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.DateConstant;
import com.hisun.lemon.urm.enums.mi.BonusSendTypeEnums;
import com.hisun.lemon.urm.enums.mi.IsBonusEnums;
import com.hisun.lemon.urm.enums.sys.SuspendStatus;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月02日
 * @time 下午14:27:30
 *
 */

@ClientValidated
@ApiModel(value="AgentDetailBeanDO", description="代办处传输对象")
public class AgentDetailBeanDO{
	/*==========基本信息============*/
	@ApiModelProperty(value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(value="分公司编号",required=false,dataType="String")
    private String companyCode;
	@ApiModelProperty(value="地区编号",required=false,dataType="String")
	private String regionCode;
	@ApiModelProperty(value="代办处编号",required=false,dataType="String")
    private String agentNo;
	@ApiModelProperty(value="负责人编号",required=false,dataType="String")
    private String recommendNo;
	@ApiModelProperty(value="姓名",required=false,dataType="String")
    private String name;
	@ApiModelProperty(value="昵称",required=false,dataType="String")
    private String petName;
	@ApiModelProperty(value="性别：M男W女",required=false)
	@JsonDeserialize(using = CustomValueEnumDeserializer.class)
	@JsonSerialize(using = CustomValueEnumSerializer.class)
    private Sex sex;
	@ApiModelProperty(value="生日",required=false)
	@JsonFormat(pattern=DateConstant.STR1_YMD)
    private LocalDateTime birthday;
	@ApiModelProperty(value="证件类型:1身份证2护照",required=false)
	@JsonDeserialize(using = CustomValueEnumDeserializer.class)
	@JsonSerialize(using = CustomValueEnumSerializer.class)
    private PaperTypeEnums paperType;
	@ApiModelProperty(value="证件号",required=false,dataType="String")
    private String paperNo;
    /*==========联系信息============*/
	@ApiModelProperty(value="店铺名称",required=false,dataType="String")
    private String storeName;
	@ApiModelProperty(value="店铺地址",required=false,dataType="String")
    private String storeAddr;
	@ApiModelProperty(value="邮编",required=false,dataType="String")
    private String storePost;
	@ApiModelProperty(value="办公电话",required=false,dataType="String")
    private String officeTel;
	@ApiModelProperty(value="手机",required=false,dataType="String")
    private String mobile;
	@ApiModelProperty(value="传真",required=false,dataType="String")
    private String fax;
	@ApiModelProperty(value="电子邮箱",required=false,dataType="String")
    private String email;
    /*==========银行账户信息============*/
	@ApiModelProperty(value="开户银行",required=false,dataType="String")
    private String accountBank;
	@ApiModelProperty(value="银行帐号",required=false,dataType="String")
    private String accountCode;
	@ApiModelProperty(value="开户名",required=false,dataType="String")
    private String accountName;
    /*==========权限信息============*/
	@ApiModelProperty(value="登录状态",required=false)
	@JsonDeserialize(using = CustomValueEnumDeserializer.class)
	@JsonSerialize(using = CustomValueEnumSerializer.class)
	private SuspendStatus loginStatus;
    /*==========密码设置============*/
	@ApiModelProperty(value="默认语言",required=false,dataType="String")
    private String defaultlanuage;
	@ApiModelProperty(value="代办处加入期数",required=false,dataType="String")
    private String startWeek;
	@ApiModelProperty(value="附件",required=false,dataType="String")
	private String attachmentURL;
	
	
	@ApiModelProperty(value="特批货物额度",required=false,dataType="String")
    private BigDecimal promAmt;
	
	@ApiModelProperty(value="已批货物额度",required=false,dataType="String")
    private BigDecimal promTotalAmt;
    
    @ApiModelProperty(value="FV允许的最低额度",required=false,dataType="String")
    private BigDecimal fvLimit;
   
    @ApiModelProperty(value="F$允许的最低额度",required=false,dataType="String")
    private BigDecimal fpLimit;
    
    @ApiModelProperty(value="F000允许的最低额度",required=false,dataType="String")
    private BigDecimal f0Limit;
    
    @ApiModelProperty(value="奖金发放方式0=发放到代办处，1=发放到经销商",required=false,dataType="String")
    @JsonDeserialize(using = CustomValueEnumDeserializer.class)
	@JsonSerialize(using = CustomValueEnumSerializer.class)
    private BonusSendTypeEnums bonusSendType;
   
    @ApiModelProperty(value="奖金发放到会员模式代办费计提：0 否，1 计提经销商报单 2计提代办处报单",required=false,dataType="String")
    @JsonDeserialize(using = CustomValueEnumDeserializer.class)
	@JsonSerialize(using = CustomValueEnumSerializer.class)
    private IsBonusEnums isBonus;
    
    @ApiModelProperty(value="奖金发放币种",required=false,dataType="String")
    private String balanceType;

    @ApiModelProperty(value="币种",required=false,dataType="String")
    private String currencyCode;
    
    private String parentNo;
    
    private Integer levelType;
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getRegionCode() {
		return regionCode;
	}
	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getRecommendNo() {
		return recommendNo;
	}
	public void setRecommendNo(String recommendNo) {
		this.recommendNo = recommendNo;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getPetName() {
		return petName;
	}
	public void setPetName(String petName) {
		this.petName = petName;
	}
	public LocalDateTime getBirthday() {
		return birthday;
	}
	public void setBirthday(LocalDateTime birthday) {
		this.birthday = birthday;
	}
	public String getPaperNo() {
		return paperNo;
	}
	public void setPaperNo(String paperNo) {
		this.paperNo = paperNo;
	}
	public String getStoreName() {
		return storeName;
	}
	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}
	public String getStoreAddr() {
		return storeAddr;
	}
	public void setStoreAddr(String storeAddr) {
		this.storeAddr = storeAddr;
	}
	public String getStorePost() {
		return storePost;
	}
	public void setStorePost(String storePost) {
		this.storePost = storePost;
	}
	public String getOfficeTel() {
		return officeTel;
	}
	public void setOfficeTel(String officeTel) {
		this.officeTel = officeTel;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getFax() {
		return fax;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getAccountBank() {
		return accountBank;
	}
	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}
	public String getAccountCode() {
		return accountCode;
	}
	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getDefaultlanuage() {
		return defaultlanuage;
	}
	public void setDefaultlanuage(String defaultlanuage) {
		this.defaultlanuage = defaultlanuage;
	}
	public String getStartWeek() {
		return startWeek;
	}
	public void setStartWeek(String startWeek) {
		this.startWeek = startWeek;
	}
	public String getAttachmentURL() {
		return attachmentURL;
	}
	public void setAttachmentURL(String attachmentURL) {
		this.attachmentURL = attachmentURL;
	}
	public BigDecimal getPromAmt() {
		return promAmt;
	}
	public void setPromAmt(BigDecimal promAmt) {
		this.promAmt = promAmt;
	}
	public BigDecimal getPromTotalAmt() {
		return promTotalAmt;
	}
	public void setPromTotalAmt(BigDecimal promTotalAmt) {
		this.promTotalAmt = promTotalAmt;
	}
	public BigDecimal getFvLimit() {
		return fvLimit;
	}
	public void setFvLimit(BigDecimal fvLimit) {
		this.fvLimit = fvLimit;
	}
	public BigDecimal getFpLimit() {
		return fpLimit;
	}
	public void setFpLimit(BigDecimal fpLimit) {
		this.fpLimit = fpLimit;
	}
	public BigDecimal getF0Limit() {
		return f0Limit;
	}
	public void setF0Limit(BigDecimal f0Limit) {
		this.f0Limit = f0Limit;
	}
	public String getBalanceType() {
		return balanceType;
	}
	public void setBalanceType(String balanceType) {
		this.balanceType = balanceType;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public Sex getSex() {
		return sex;
	}
	public void setSex(Sex sex) {
		this.sex = sex;
	}
	public PaperTypeEnums getPaperType() {
		return paperType;
	}
	public void setPaperType(PaperTypeEnums paperType) {
		this.paperType = paperType;
	}
	public SuspendStatus getLoginStatus() {
		return loginStatus;
	}
	public void setLoginStatus(SuspendStatus loginStatus) {
		this.loginStatus = loginStatus;
	}
	public BonusSendTypeEnums getBonusSendType() {
		return bonusSendType;
	}
	public void setBonusSendType(BonusSendTypeEnums bonusSendType) {
		this.bonusSendType = bonusSendType;
	}
	public IsBonusEnums getIsBonus() {
		return isBonus;
	}
	public void setIsBonus(IsBonusEnums isBonus) {
		this.isBonus = isBonus;
	}
	public String getParentNo() {
		return parentNo;
	}
	public void setParentNo(String parentNo) {
		this.parentNo = parentNo;
	}
	public Integer getLevelType() {
		return levelType;
	}
	public void setLevelType(Integer levelType) {
		this.levelType = levelType;
	}
}
