package com.hisun.lemon.urm.dto.ic;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-文件管理查询
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 上午11:36:16
 *
 */
public class AppQueryReqDTO extends GenericDTO<NoBody> {

    private String appName;

    private String version;

    private String platform;

    private Integer orderSn;

    @ApiModelProperty(name = "pageNum", value = "页码", required = true, dataType = "Integer")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量", required = true, dataType = "Integer")
    private int pageSize;
 
	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public Integer getOrderSn() {
		return orderSn;
	}

	public void setOrderSn(Integer orderSn) {
		this.orderSn = orderSn;
	}

	public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
