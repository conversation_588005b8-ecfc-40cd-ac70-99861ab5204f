package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.enums.OrderTypeEnums;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 
 * @ClassName: AcHistoryBalanceItemDTO
 * @Description: TODO
 * @author: fy
 * @date: 2018年4月23日 上午9:42:30
 */
@ClientValidated
@ApiModel(value="AcHistoryBalanceItemDTO", description="历史账户余额传输对象")
public class AcHistoryBalanceItemDTO extends GenericDTO<NoBody>{

	@ApiModelProperty(value="奖金制度")
    private String bonusType;
    @ApiModelProperty(value="分公司编号")
    private String companyCode;
    @ApiModelProperty(value="代办处编号")
    private String agentNo;
    @ApiModelProperty(value="经销商编号")
    private String userCode;
    @ApiModelProperty(value="账户类别")
    private String acType;
	@ApiModelProperty(value="页码")
	private Integer pageNum;
	
	@ApiModelProperty(value="页数")
	private Integer pageSize;
	
	@ApiModelProperty(value="开始时间")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime startCheckTime;
	
	@ApiModelProperty(value="结束时间")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime endCheckTime;
	
	@ApiModelProperty(value = "大于", required = false)
	private String bonusGreate;

	@ApiModelProperty(value = "等于", required = false)
	private String bonusEqual;

	@ApiModelProperty(value = "小于", required = false)
	private String bonusLeast;

	@ApiModelProperty(value = "大于等于", required = false)
	private String bonusGreateEq;

	@ApiModelProperty(value = "小于等于", required = false)
	private String bonusLeastEq;

	@ApiModelProperty(value = "查询范围", required = false)
	private BigDecimal bonusValue;
	
	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public LocalDateTime getStartCheckTime() {
		return startCheckTime;
	}

	public void setStartCheckTime(LocalDateTime startCheckTime) {
		this.startCheckTime = startCheckTime;
	}

	public LocalDateTime getEndCheckTime() {
		return endCheckTime;
	}

	public void setEndCheckTime(LocalDateTime endCheckTime) {
		this.endCheckTime = endCheckTime;
	}

	public String getBonusGreate() {
		return bonusGreate;
	}

	public void setBonusGreate(String bonusGreate) {
		this.bonusGreate = bonusGreate;
	}

	public String getBonusEqual() {
		return bonusEqual;
	}

	public void setBonusEqual(String bonusEqual) {
		this.bonusEqual = bonusEqual;
	}

	public String getBonusLeast() {
		return bonusLeast;
	}

	public void setBonusLeast(String bonusLeast) {
		this.bonusLeast = bonusLeast;
	}

	public String getBonusGreateEq() {
		return bonusGreateEq;
	}

	public void setBonusGreateEq(String bonusGreateEq) {
		this.bonusGreateEq = bonusGreateEq;
	}

	public String getBonusLeastEq() {
		return bonusLeastEq;
	}

	public void setBonusLeastEq(String bonusLeastEq) {
		this.bonusLeastEq = bonusLeastEq;
	}

	public BigDecimal getBonusValue() {
		return bonusValue;
	}

	public void setBonusValue(BigDecimal bonusValue) {
		this.bonusValue = bonusValue;
	}
	
}
