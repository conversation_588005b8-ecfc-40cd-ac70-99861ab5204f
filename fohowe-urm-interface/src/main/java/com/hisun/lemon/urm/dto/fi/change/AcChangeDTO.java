 package com.hisun.lemon.urm.dto.fi.change;

import java.time.LocalDateTime;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 账户补扣传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcChangeDTO", description="账户补扣传输对象")
public class AcChangeDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private AcChangeBean mainInfo = new AcChangeBean();
	
	@ApiModelProperty(name="acType",value="账户类型:f$＝f$,fv＝fv,f0=f000,h0=h0000,fb=fb,pv=活跃pv\r\n" + 
			"p$=活跃p$,b1=重消分配,b2=旅游基金,b3=名车基金,b4=游艇基金,b5=住宅基金,b6=市场发展基金",required=false)
	private String acType;
	
	@ApiModelProperty(value="经销商/代办处编号",required=false,dataType="String")
	private String userCode;
	
	@ApiModelProperty(value="交易类别",required=false)
	private String orderType;
	
	@ApiModelProperty(value="开始审核日期",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minCheckTime;
	
	@ApiModelProperty(value="结束审核日期",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxCheckTime;
	
	@ApiModelProperty(value="状态，1新增2已审核",required=false,dataType="String")
	private String status;
	
	@ApiModelProperty(value="操作类型：1=存入(不判断余额),2=提取(判断余额)",required=false)
	private String changeType;
	
	@ApiModelProperty(value="批量审核ID",required=false)
	private String[] idArr;

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public AcChangeBean getMainInfo() {
		return mainInfo;
	}

	public void setMainInfo(AcChangeBean mainInfo) {
		this.mainInfo = mainInfo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getChangeType() {
		return changeType;
	}

	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}

	public String[] getIdArr() {
		return idArr;
	}

	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}

	public LocalDateTime getMinCheckTime() {
		return minCheckTime;
	}

	public void setMinCheckTime(LocalDateTime minCheckTime) {
		this.minCheckTime = minCheckTime;
	}

	public LocalDateTime getMaxCheckTime() {
		return maxCheckTime;
	}

	public void setMaxCheckTime(LocalDateTime maxCheckTime) {
		this.maxCheckTime = maxCheckTime;
	}

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	
}
