 package com.hisun.lemon.urm.dto.fi.input;

import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputVO 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputVO", description="账户申购对象")
public class AcInputVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
	@ApiModelProperty(value="查询结果集")
	List<AcInputQueryBean> dataList;
	@ApiModelProperty(value="查询结果集")
	List<AcInputQueryDetailBean> dataDetailList;
	@ApiModelProperty(value="总计F$")
	String totalF$;
	@ApiModelProperty(value="已确认金额总计F$")
	String recTotalF$;
	@ApiModelProperty(value="未确认金额总计F$")
	String unRecTotalF$;
	
	String localTotalF$;
	
	String finTotalF$;
    
	public String getTotalF$() {
		return totalF$;
	}

	public void setTotalF$(String totalF$) {
		this.totalF$ = totalF$;
	}

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcInputQueryBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AcInputQueryBean> dataList) {
		this.dataList = dataList;
	}

	public List<AcInputQueryDetailBean> getDataDetailList() {
		return dataDetailList;
	}

	public void setDataDetailList(List<AcInputQueryDetailBean> dataDetailList) {
		this.dataDetailList = dataDetailList;
	}

	public String getRecTotalF$() {
		return recTotalF$;
	}

	public void setRecTotalF$(String recTotalF$) {
		this.recTotalF$ = recTotalF$;
	}

	public String getUnRecTotalF$() {
		return unRecTotalF$;
	}

	public void setUnRecTotalF$(String unRecTotalF$) {
		this.unRecTotalF$ = unRecTotalF$;
	}

	public String getLocalTotalF$() {
		return localTotalF$;
	}

	public void setLocalTotalF$(String localTotalF$) {
		this.localTotalF$ = localTotalF$;
	}

	public String getFinTotalF$() {
		return finTotalF$;
	}

	public void setFinTotalF$(String finTotalF$) {
		this.finTotalF$ = finTotalF$;
	}

}
