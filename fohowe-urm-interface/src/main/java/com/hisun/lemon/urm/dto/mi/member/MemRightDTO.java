package com.hisun.lemon.urm.dto.mi.member;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.apache.commons.lang3.StringUtils;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.common.DateConstant;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 经营权相关
 * 
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ApiModel(value="MemRightDTO", description="经营权相关")
public class MemRightDTO  extends GenericDTO<NoBody>{
	
	/*************hidden fields****************/
	
	@ApiModelProperty(value="隐藏信息" ,hidden=true)
	private LocalDateTime hiddenMinRegDate;
	@ApiModelProperty(value="隐藏信息",hidden=true)
	private LocalDateTime hiddenMaxReDate;
	
	@ApiModelProperty(value="隐藏信息" ,hidden=true)
	private LocalDateTime hiddenMinCreateTime;
	@ApiModelProperty(value="隐藏信息",hidden=true)
	private LocalDateTime hiddenMaxCreateTime;
	/*************hidden fields****************/
	
	
    @ApiModelProperty(value="基本查询信息")
    private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();

    @ApiModelProperty(value="下属经营权编号")
    private String subRightNo;
    
    @ApiModelProperty(value="下属经销商编号")
    private String subMemberNo;
    
    @ApiModelProperty(value="经营权编号")
    private String rightNo;
    
    @ApiModelProperty(value="经营权级别")
    private String[] levelType;
    
    @ApiModelProperty(value="经营权金点")
    private String[] isVirtual;
    
    @ApiModelProperty(value="是否主经营权，1=是 ,0=否")
    private String primRight;
    
    @ApiModelProperty(value="接点权编号")
    private String linkNo;
    
    @ApiModelProperty(value="推荐人编号")
    private String recMemNo;
    
    @ApiModelProperty(value="最小加入金额范围")
    private BigDecimal minRegPv;
    
    @ApiModelProperty(value="最大加入金额范围")
    private BigDecimal maxRegPv;
    
    @ApiModelProperty(value="开始注册日期(yyyy-MM-dd)")
    private String minRegDate;
    
    @ApiModelProperty(value="结束注册日期(yyyy-MM-dd)")
    private String maxRegDate;
    
    @ApiModelProperty(value="开始注册日期(yyyy-MM-dd)")
    private String minCreateTime;
    
    @ApiModelProperty(value="结束注册日期(yyyy-MM-dd)")
    private String maxCreateTime;
    
    @ApiModelProperty(value="最小层数")
    private Integer minLayer;
    
    @ApiModelProperty(value="最大层数")
    private Integer maxLayer;
    
    @ApiModelProperty(value="最小阶段数")
    private Integer minStage;
    @ApiModelProperty(value="最大阶段数")
    private Integer maxStage;
    @ApiModelProperty(value="最小循环数")
    private Integer minCicles;
    @ApiModelProperty(value="最大循环数")
    private Integer maxCicles;
    @ApiModelProperty(value="开始加入期数（用于匹配条件查询）")
    private String minPeriodWeek;
    
    @ApiModelProperty(value="结束加入期数（用于匹配条件查询）")
    private String maxPeriodWeek;
/*    @ApiModelProperty(value="结束注册日期(yyyy-MM-dd HH:mm:ss)")
	@JsonSerialize(using=com.hisun.lemon.fohowe.common.json.DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime test1;*/
    
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public String getRightNo() {
		return rightNo;
	}

	public void setRightNo(String rightNo) {
		this.rightNo = rightNo;
	}

	public void setLevelType(String[] levelType) {
		this.levelType = levelType;
	}


	public String getPrimRight() {
		return primRight;
	}

	public void setPrimRight(String primRight) {
		this.primRight = primRight;
	}

	public String getLinkNo() {
		return linkNo;
	}

	public void setLinkNo(String linkNo) {
		this.linkNo = linkNo;
	}

	public String getRecMemNo() {
		return recMemNo;
	}

	public void setRecMemNo(String recMemNo) {
		this.recMemNo = recMemNo;
	}

	public BigDecimal getMinRegPv() {
		return minRegPv;
	}

	public void setMinRegPv(BigDecimal minRegPv) {
		this.minRegPv = minRegPv;
	}

	public BigDecimal getMaxRegPv() {
		return maxRegPv;
	}

	public void setMaxRegPv(BigDecimal maxRegPv) {
		this.maxRegPv = maxRegPv;
	}

	public String getMinRegDate() {
		return minRegDate;
	}

	public void setMinRegDate(String minRegDate) {
		if(StringUtils.isNotBlank(minRegDate))
		this.hiddenMinRegDate=LocalDateTime.parse(minRegDate+" 00:00:00", DateConstant.FORMATTER1_YMDHMS);
		this.minRegDate = minRegDate;
	}


	public String getMaxRegDate() {
		return maxRegDate;
	}

	public void setMaxRegDate(String maxRegDate) {
		if(StringUtils.isNotBlank(maxRegDate))
		this.hiddenMaxReDate=LocalDateTime.parse(maxRegDate+" 23:59:59", DateConstant.FORMATTER1_YMDHMS);
		this.maxRegDate = maxRegDate;
	}
	
	public String getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(String minCreateTime) {
		if(StringUtils.isNotBlank(minCreateTime))
		this.hiddenMinCreateTime=LocalDateTime.parse(minCreateTime+" 00:00:00", DateConstant.FORMATTER1_YMDHMS);
		this.minCreateTime = minCreateTime;
	}


	public String getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(String maxCreateTime) {
		if(StringUtils.isNotBlank(maxCreateTime))
		this.hiddenMaxCreateTime=LocalDateTime.parse(maxCreateTime+" 23:59:59", DateConstant.FORMATTER1_YMDHMS);
		this.maxCreateTime = maxCreateTime;
	}

	public LocalDateTime getHiddenMinRegDate() {
		return hiddenMinRegDate;
	}

	public void setHiddenMinRegDate(LocalDateTime hiddenMinRegDate) {
		this.hiddenMinRegDate = hiddenMinRegDate;
	}

	public LocalDateTime getHiddenMaxReDate() {
		return hiddenMaxReDate;
	}

	public void setHiddenMaxReDate(LocalDateTime hiddenMaxReDate) {
		this.hiddenMaxReDate = hiddenMaxReDate;
	}

	public String getSubRightNo() {
		return subRightNo;
	}

	public void setSubRightNo(String subRightNo) {
		this.subRightNo = subRightNo;
	}

	public String getSubMemberNo() {
		return subMemberNo;
	}

	public void setSubMemberNo(String subMemberNo) {
		this.subMemberNo = subMemberNo;
	}

	public LocalDateTime getHiddenMinCreateTime() {
		return hiddenMinCreateTime;
	}

	public void setHiddenMinCreateTime(LocalDateTime hiddenMinCreateTime) {
		this.hiddenMinCreateTime = hiddenMinCreateTime;
	}

	public LocalDateTime getHiddenMaxCreateTime() {
		return hiddenMaxCreateTime;
	}

	public void setHiddenMaxCreateTime(LocalDateTime hiddenMaxCreateTime) {
		this.hiddenMaxCreateTime = hiddenMaxCreateTime;
	}

	public Integer getMinLayer() {
		return minLayer;
	}

	public void setMinLayer(Integer minLayer) {
		this.minLayer = minLayer;
	}

	public Integer getMaxLayer() {
		return maxLayer;
	}

	public void setMaxLayer(Integer maxLayer) {
		this.maxLayer = maxLayer;
	}

	public String getMinPeriodWeek() {
		return minPeriodWeek;
	}

	public void setMinPeriodWeek(String minPeriodWeek) {
		this.minPeriodWeek = minPeriodWeek;
	}

	public String getMaxPeriodWeek() {
		return maxPeriodWeek;
	}

	public void setMaxPeriodWeek(String maxPeriodWeek) {
		this.maxPeriodWeek = maxPeriodWeek;
	}

	public String[] getLevelType() {
		return levelType;
	}

	public String[] getIsVirtual() {
		return isVirtual;
	}

	public void setIsVirtual(String[] isVirtual) {
		this.isVirtual = isVirtual;
	}

	public Integer getMinStage() {
		return minStage;
	}

	public void setMinStage(Integer minStage) {
		this.minStage = minStage;
	}

	public Integer getMaxStage() {
		return maxStage;
	}

	public void setMaxStage(Integer maxStage) {
		this.maxStage = maxStage;
	}

	public Integer getMinCicles() {
		return minCicles;
	}

	public void setMinCicles(Integer minCicles) {
		this.minCicles = minCicles;
	}

	public Integer getMaxCicles() {
		return maxCicles;
	}

	public void setMaxCicles(Integer maxCicles) {
		this.maxCicles = maxCicles;
	}

    
	

	
	
	
    
    
}
