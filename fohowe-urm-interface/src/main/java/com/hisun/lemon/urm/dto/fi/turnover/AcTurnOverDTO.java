package com.hisun.lemon.urm.dto.fi.turnover;

import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.enums.OrderTypeEnums;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ClientValidated
@ApiModel(value="AcTurnOverDTO", description="周转款对象")
public class AcTurnOverDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private AcTurnOverBean mainInfo = new AcTurnOverBean();
	@ApiModelProperty(name="userCode",value="用户编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(name="orderType",value="单据类型:1借款、2还款",required=false,dataType="String")
	private String orderType;
	@ApiModelProperty(name="minCreateTime",value="开始创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minCreateTime;
	@ApiModelProperty(name="maxCreateTime",value="结束创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxCreateTime;
	@ApiModelProperty(name="acType",value="账户类型",required=false,dataType="String")
	private String acType;
	@ApiModelProperty(name="status",value="状态：1:新单 2:核准9.入账7.退回",required=false,dataType="String")
    private String status;
	@ApiModelProperty(name="auditResult",value="1通过0退回",required=false,dataType="String")
    private String auditResult;
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public AcTurnOverBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(AcTurnOverBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public LocalDateTime getMinCreateTime() {
		return minCreateTime;
	}
	public void setMinCreateTime(LocalDateTime minCreateTime) {
		this.minCreateTime = minCreateTime;
	}
	public LocalDateTime getMaxCreateTime() {
		return maxCreateTime;
	}
	public void setMaxCreateTime(LocalDateTime maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getAuditResult() {
		return auditResult;
	}
	public void setAuditResult(String auditResult) {
		this.auditResult = auditResult;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
}
