package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;
/**
 * 
 * @ClassName: AcHistoryBalanceItemDTO
 * @Description: TODO
 * @author: fy
 * @date: 2018年4月23日 上午9:42:30
 */

public class AcHistoryBalanceItemBo extends GenericDTO<NoBody>{

	/**
     * @Fields bonusType 奖金制度
     */
    private String bonusType;
    /**
     * @Fields companyCode 分公司编号
     */
    private String companyCode;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields userCode 经销商/代办处编号
     */
    private String userCode;
    /**
     * @Fields acType  账户类别
     */
    private String acType;
    /**
     * @Fields startCheckTime  开始时间
     */
    private LocalDateTime startCheckTime;
    /**
     * @Fields endCheckTime  结束时间
     */
    private LocalDateTime endCheckTime;
    /**
     * @Fields pageNum  页码
     */
	private Integer pageNum;
	
	/**
     * @Fields pageSize  页数
     */
	private Integer pageSize;
	
	private Integer wWeek;

	@ApiModelProperty(value = "大于", required = false)
	private String bonusGreate;

	@ApiModelProperty(value = "等于", required = false)
	private String bonusEqual;

	@ApiModelProperty(value = "小于", required = false)
	private String bonusLeast;

	@ApiModelProperty(value = "大于等于", required = false)
	private String bonusGreateEq;

	@ApiModelProperty(value = "小于等于", required = false)
	private String bonusLeastEq;

	@ApiModelProperty(value = "查询范围", required = false)
	private BigDecimal bonusValue;
	
	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public LocalDateTime getStartCheckTime() {
		return startCheckTime;
	}

	public void setStartCheckTime(LocalDateTime startCheckTime) {
		this.startCheckTime = startCheckTime;
	}

	public LocalDateTime getEndCheckTime() {
		return endCheckTime;
	}

	public void setEndCheckTime(LocalDateTime endCheckTime) {
		this.endCheckTime = endCheckTime;
	}

	public String getBonusGreate() {
		return bonusGreate;
	}

	public void setBonusGreate(String bonusGreate) {
		this.bonusGreate = bonusGreate;
	}

	public String getBonusEqual() {
		return bonusEqual;
	}

	public void setBonusEqual(String bonusEqual) {
		this.bonusEqual = bonusEqual;
	}

	public String getBonusLeast() {
		return bonusLeast;
	}

	public void setBonusLeast(String bonusLeast) {
		this.bonusLeast = bonusLeast;
	}

	public String getBonusGreateEq() {
		return bonusGreateEq;
	}

	public void setBonusGreateEq(String bonusGreateEq) {
		this.bonusGreateEq = bonusGreateEq;
	}

	public String getBonusLeastEq() {
		return bonusLeastEq;
	}

	public void setBonusLeastEq(String bonusLeastEq) {
		this.bonusLeastEq = bonusLeastEq;
	}

	public BigDecimal getBonusValue() {
		return bonusValue;
	}

	public void setBonusValue(BigDecimal bonusValue) {
		this.bonusValue = bonusValue;
	}

	public Integer getwWeek() {
		return wWeek;
	}

	public void setwWeek(Integer wWeek) {
		this.wWeek = wWeek;
	}
}
