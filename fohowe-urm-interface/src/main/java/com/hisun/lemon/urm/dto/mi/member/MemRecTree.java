package com.hisun.lemon.urm.dto.mi.member;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.urm.common.DateConstant;

import io.swagger.annotations.ApiModelProperty;


public class MemRecTree {
	
	
	@ApiModelProperty(value="用户编号",required= true)
    private String memNo;
    
    @ApiModelProperty(value="推荐编号",hidden= true)
    private String parentMemNo;
    
    @ApiModelProperty(value="用户名称",required= true)
    private String memName;
    
    @ApiModelProperty(value="图片地址Key",required= true)
    private String picUrlKey;
    
    @ApiModelProperty(value="级别名称Key",required= true)
    private String levelKey;
    
    @ApiModelProperty(value="促销资格",required= true)
    private int promQual;
    
    @ApiModelProperty(value="是否合格推荐线 0 不合格 1 合格 ",required= true)
    private int isVipLine;
    
    @ApiModelProperty(value="注册时间",required= true)
    @JsonFormat(pattern=DateConstant.STR1_YMD)
    private String regDate;
    
    @ApiModelProperty(value="是否有父节点",hidden= true)
    private boolean hasParent;
    
    @ApiModelProperty(value="循环数(安置网络)",required= true)
    private String circle;
    
    @ApiModelProperty(value="pv值(安置网络)",required= true)
    private String pv;
    
 /*   @ApiModelProperty(value="层级索引",required= true)
    private String index;
    */
   
    /**
     * 节点的子节点
     */
    private List<MemRecTree> children = new ArrayList<MemRecTree>();


	public String getMemNo() {
		return memNo;
	}


	public void setMemNo(String memNo) {
		this.memNo = memNo;
	}


	public String getParentMemNo() {
		return parentMemNo;
	}


	public void setParentMemNo(String parentMemNo) {
		this.parentMemNo = parentMemNo;
	}


	public String getMemName() {
		return memName;
	}


	public void setMemName(String memName) {
		this.memName = memName;
	}



	public String getPicUrlKey() {
		return picUrlKey;
	}


	public void setPicUrlKey(String picUrlKey) {
		this.picUrlKey = picUrlKey;
	}


	public String getLevelKey() {
		return levelKey;
	}


	public void setLevelKey(String levelKey) {
		this.levelKey = levelKey;
	}


	public String getRegDate() {
		return regDate;
	}


	public void setRegDate(String regDate) {
		this.regDate = regDate;
	}





	public List<MemRecTree> getChildren() {
		return children;
	}


	public void setChildren(List<MemRecTree> children) {
		this.children = children;
	}


	public boolean isHasParent() {
		return hasParent;
	}


	public void setHasParent(boolean hasParent) {
		this.hasParent = hasParent;
	}


	public String getCircle() {
		return circle;
	}


	public void setCircle(String circle) {
		this.circle = circle;
	}


	public String getPv() {
		return pv;
	}


	public void setPv(String pv) {
		this.pv = pv;
	}


	public int getPromQual() {
		return promQual;
	}


	public void setPromQual(int promQual) {
		this.promQual = promQual;
	}


	public int getIsVipLine() {
		return isVipLine;
	}


	public void setIsVipLine(int isVipLine) {
		this.isVipLine = isVipLine;
	}
	

}