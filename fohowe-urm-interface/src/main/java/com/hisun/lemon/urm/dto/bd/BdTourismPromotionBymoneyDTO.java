package com.hisun.lemon.urm.dto.bd;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

public class BdTourismPromotionBymoneyDTO extends GenericDTO<NoBody> {
    private Long id;
    private BigDecimal totalAmount;
    private BigDecimal repairAmount;

    private BigDecimal payAmount;

    private Integer vcStatus;
    
    private Integer isOutPay;

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getRepairAmount() {
        return repairAmount;
    }

    public void setRepairAmount(BigDecimal repairAmount) {
        this.repairAmount = repairAmount;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

	public Integer getIsOutPay() {
		return isOutPay;
	}

	public void setIsOutPay(Integer isOutPay) {
		this.isOutPay = isOutPay;
	}
    
}
