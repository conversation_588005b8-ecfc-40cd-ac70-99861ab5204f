package com.hisun.lemon.urm.dto.ic;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-文件管理信息修改
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 上午10:44:41
 *
 */
public class FileDownloadReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(name = "dfileId", value = "文件下载编号,多个以逗号分隔")
    private String dfileId;

    public String getDfileId() {
        return dfileId;
    }

    public void setDfileId(String dfileId) {
        this.dfileId = dfileId;
    }

}
