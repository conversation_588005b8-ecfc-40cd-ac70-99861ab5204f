package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.urm.common.DateConstant;

/**
 * 
 * <AUTHOR>
 * @date 2017-11-13 11:34
 */

@ApiModel(value="ActiveBean")
public class ActiveBean{
	/*=============base data============ */
	@ApiModelProperty(value="id标识")
    private Long id;
    @ApiModelProperty(value="所属分公司")
    private String companyCode;
    @ApiModelProperty(value="代办处")
    private String agentNo;
    @ApiModelProperty(value="经销商编号")
    private String memberNo;
    @ApiModelProperty(value="经销商姓名")
    private String memberName;
    
    /*=============add data============ */
    
    @ApiModelProperty(value="注册日期")
    @JsonFormat(pattern=DateConstant.STR1_YMD)
    private LocalDateTime regDate;
    
    @ApiModelProperty(value="经销商级别图片/名称(数据字典key)")
    private String cardType;
    
    /*=============common data============ */
    @ApiModelProperty(value="活跃期数")
    private String activeWeek;
    
    @ApiModelProperty(value="剩余期数")
    private Integer remainWeek;

    private BigDecimal activePd;
    
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public LocalDateTime getRegDate() {
		return regDate;
	}

	public void setRegDate(LocalDateTime regDate) {
		this.regDate = regDate;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public String getActiveWeek() {
		return activeWeek;
	}

	public void setActiveWeek(String activeWeek) {
		this.activeWeek = activeWeek;
	}

	public Integer getRemainWeek() {
		return remainWeek;
	}

	public void setRemainWeek(Integer remainWeek) {
		this.remainWeek = remainWeek;
	}

	public BigDecimal getActivePd() {
		return activePd;
	}

	public void setActivePd(BigDecimal activePd) {
		this.activePd = activePd;
	}
	
}
