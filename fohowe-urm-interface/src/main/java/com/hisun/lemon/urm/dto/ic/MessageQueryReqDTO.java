package com.hisun.lemon.urm.dto.ic;

import javax.validation.constraints.Pattern;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-陌生人留言管理修改
 * 
 * <AUTHOR>
 * @date 2017年11月4日
 * @time 下午3:58:04
 *
 */
public class MessageQueryReqDTO extends GenericDTO<NoBody> {
    @ApiModelProperty(name = "firstName", value = "名", required = false, dataType = "String")
    private String firstName;

    @ApiModelProperty(name = "lastName", value = "姓", required = false, dataType = "String")
    private String lastName;

    @ApiModelProperty(name = "status", value = "处理状态 0-未读 1-已读", required = true, dataType = "String")
    @Pattern(regexp = "0|1", message = "URM10007")
    private String status;

    @ApiModelProperty(name = "phoneNumber", value = "联系电话", required = false, dataType = "String")
    private String phoneNumber;

    @ApiModelProperty(name = "pageNum", value = "页码", required = true, dataType = "Integer")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量", required = true, dataType = "Integer")
    private int pageSize;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
