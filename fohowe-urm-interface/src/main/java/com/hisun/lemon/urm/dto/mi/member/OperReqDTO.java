package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.DateConstant;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
import com.hisun.lemon.urm.enums.mi.OperationTypeEnums;

/**
 * 
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ClientValidated
@ApiModel(value="OperReqDTO", description="审核/取消审核请求对象")
public class OperReqDTO  extends GenericDTO<NoBody>{
	

    @ApiModelProperty(value="主键，数组")
    @NotEmpty(message="URM10001"/*,groups= {ValidGroup1.class}*/) 
    private String[] ids;
    
    @ApiModelProperty(value="操作类型,传值1=审核、2=取消审核")
    @JsonSerialize(using=CustomValueEnumSerializer.class)
	@JsonDeserialize(using=CustomValueEnumDeserializer.class)
    private OperationTypeEnums operType;

	
    
	public String[] getIds() {
		return ids;
	}

	public void setIds(String[] ids) {
		this.ids = ids;
	}

	public OperationTypeEnums getOperType() {
		return operType;
	}

	public void setOperType(OperationTypeEnums operType) {
		this.operType = operType;
	}

	
	
	
    
    
}
