package com.hisun.lemon.urm.dto.bd;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @ClassName: RuleRightRateDTO 
 * @Description: 系统设置-经营权兑换比例
 * @author: tian
 * @date: 2017年11月15日 上午11:29:13
 */
public class RuleRightRateDTO {

    @ApiModelProperty(name = "id", value = "id", required = true, dataType = "String")
    private String id;

    @ApiModelProperty(name = "sourceType", value = "权的奖金制度,1=欧洲奖金制度，2=非洲奖金制度", required = true, dataType = "String")
    private String sourceType;

    @ApiModelProperty(name = "targetType", value = "接点的奖金制度,1=欧洲奖金制度，2=非洲奖金制度", required = true, dataType = "String")
    private String targetType;

    @ApiModelProperty(name = "point", value = "计为新增业绩", required = true, dataType = "BigDecimal")
    private BigDecimal point;

    @ApiModelProperty(name = "newPoint", value = "新计为新增业绩(查询历史记录时返回字段)", required = false, dataType = "BigDecimal")
    private BigDecimal newPoint;

    @ApiModelProperty(name = "operCode", value = "最后修改人", required = true, dataType = "String")
    private String operCode;

    @ApiModelProperty(name = "operDate", value = "最后修改时间", required = true, dataType = "LocalDateTime")
    private LocalDateTime operDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public BigDecimal getPoint() {
        return point;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public BigDecimal getNewPoint() {
        return newPoint;
    }

    public void setNewPoint(BigDecimal newPoint) {
        this.newPoint = newPoint;
    }

    public String getOperCode() {
        return operCode;
    }

    public void setOperCode(String operCode) {
        this.operCode = operCode;
    }

    public LocalDateTime getOperDate() {
        return operDate;
    }

    public void setOperDate(LocalDateTime operDate) {
        this.operDate = operDate;
    }
}
