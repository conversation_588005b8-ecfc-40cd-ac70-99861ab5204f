 package com.hisun.lemon.urm.dto.fi.appl;

import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcApplVO 传输对象
 * <AUTHOR>
 * @date 2017年11月16号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcApplVO", description="账户传输对象")
public class AcApplVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
    @ApiModelProperty(value="账户列表" )
    private List<AcApplQueryBean> acAppls;
    @ApiModelProperty(value="总计F$")
	String totalF$;
    
    String localTotalF$;
	
	String finTotalF$;
    
	public String getTotalF$() {
		return totalF$;
	}

	public void setTotalF$(String totalF$) {
		this.totalF$ = totalF$;
	}

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcApplQueryBean> getAcAppls() {
		return acAppls;
	}

	public void setAcAppls(List<AcApplQueryBean> acAppls) {
		this.acAppls = acAppls;
	}

	public String getLocalTotalF$() {
		return localTotalF$;
	}

	public void setLocalTotalF$(String localTotalF$) {
		this.localTotalF$ = localTotalF$;
	}

	public String getFinTotalF$() {
		return finTotalF$;
	}

	public void setFinTotalF$(String finTotalF$) {
		this.finTotalF$ = finTotalF$;
	}

}
