package com.hisun.lemon.urm.dto.fi.tickets;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;

@ClientValidated
@ApiModel(value="TicketsVoucherBean", description="转账对象")
public class TicketsVoucherBean {
	private Long id;

    private String ticketsNo;
    
    private Integer serial;
    
    private Integer startSn;
    
    private Integer endSn;

    private BigDecimal amount;

    private String ticketsPwd;
    
    private Integer ticketsStatus;

    private String companyCode;

	private String departmentCode;

    private String meetingNo;

    private String teacher;

    private Integer startWeek;

    private Integer endWeek;

    private Integer vcStatus;

    private String memo;

    private String remark;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fiCheckTime;

    private String fiCheckCode;

    private String ficheckeStatus;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ficheckeTime;
    
    private String ficheckeMemo;

    private String ficheckerCode;

    private String createrCode;
    
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private Integer finStatus;

    private BigDecimal localFinMoney;

    private Integer finNum;
    
    private Integer periodWeek;

    private String orderNo;
    
    private String memberNo;
    
    private String orderCompany;
    
    private String orderAgent;
    
    private String meetingTitle;
	private String currency;
	private BigDecimal localAmount;

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public BigDecimal getLocalAmount() {
		return localAmount;
	}

	public void setLocalAmount(BigDecimal localAmount) {
		this.localAmount = localAmount;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTicketsNo() {
		return ticketsNo;
	}

	public void setTicketsNo(String ticketsNo) {
		this.ticketsNo = ticketsNo;
	}

	public Integer getSerial() {
		return serial;
	}

	public void setSerial(Integer serial) {
		this.serial = serial;
	}

	public Integer getStartSn() {
		return startSn;
	}

	public void setStartSn(Integer startSn) {
		this.startSn = startSn;
	}

	public Integer getEndSn() {
		return endSn;
	}

	public void setEndSn(Integer endSn) {
		this.endSn = endSn;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getTicketsPwd() {
		return ticketsPwd;
	}

	public void setTicketsPwd(String ticketsPwd) {
		this.ticketsPwd = ticketsPwd;
	}

	public Integer getTicketsStatus() {
		return ticketsStatus;
	}

	public void setTicketsStatus(Integer ticketsStatus) {
		this.ticketsStatus = ticketsStatus;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getMeetingNo() {
		return meetingNo;
	}

	public void setMeetingNo(String meetingNo) {
		this.meetingNo = meetingNo;
	}

	public String getTeacher() {
		return teacher;
	}

	public void setTeacher(String teacher) {
		this.teacher = teacher;
	}

	public Integer getStartWeek() {
		return startWeek;
	}

	public void setStartWeek(Integer startWeek) {
		this.startWeek = startWeek;
	}

	public Integer getEndWeek() {
		return endWeek;
	}

	public void setEndWeek(Integer endWeek) {
		this.endWeek = endWeek;
	}

	public Integer getVcStatus() {
		return vcStatus;
	}

	public void setVcStatus(Integer vcStatus) {
		this.vcStatus = vcStatus;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public LocalDateTime getFiCheckTime() {
		return fiCheckTime;
	}

	public void setFiCheckTime(LocalDateTime fiCheckTime) {
		this.fiCheckTime = fiCheckTime;
	}

	public String getFiCheckCode() {
		return fiCheckCode;
	}

	public void setFiCheckCode(String fiCheckCode) {
		this.fiCheckCode = fiCheckCode;
	}

	public String getFicheckeStatus() {
		return ficheckeStatus;
	}

	public void setFicheckeStatus(String ficheckeStatus) {
		this.ficheckeStatus = ficheckeStatus;
	}

	public LocalDateTime getFicheckeTime() {
		return ficheckeTime;
	}

	public void setFicheckeTime(LocalDateTime ficheckeTime) {
		this.ficheckeTime = ficheckeTime;
	}

	public String getFicheckeMemo() {
		return ficheckeMemo;
	}

	public void setFicheckeMemo(String ficheckeMemo) {
		this.ficheckeMemo = ficheckeMemo;
	}

	public String getFicheckerCode() {
		return ficheckerCode;
	}

	public void setFicheckerCode(String ficheckerCode) {
		this.ficheckerCode = ficheckerCode;
	}

	public String getCreaterCode() {
		return createrCode;
	}

	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public Integer getFinStatus() {
		return finStatus;
	}

	public void setFinStatus(Integer finStatus) {
		this.finStatus = finStatus;
	}

	public BigDecimal getLocalFinMoney() {
		return localFinMoney;
	}

	public void setLocalFinMoney(BigDecimal localFinMoney) {
		this.localFinMoney = localFinMoney;
	}

	public Integer getFinNum() {
		return finNum;
	}

	public void setFinNum(Integer finNum) {
		this.finNum = finNum;
	}

	public Integer getPeriodWeek() {
		return periodWeek;
	}

	public void setPeriodWeek(Integer periodWeek) {
		this.periodWeek = periodWeek;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getOrderCompany() {
		return orderCompany;
	}

	public void setOrderCompany(String orderCompany) {
		this.orderCompany = orderCompany;
	}

	public String getOrderAgent() {
		return orderAgent;
	}

	public void setOrderAgent(String orderAgent) {
		this.orderAgent = orderAgent;
	}

	public String getMeetingTitle() {
		return meetingTitle;
	}

	public void setMeetingTitle(String meetingTitle) {
		this.meetingTitle = meetingTitle;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}
}
