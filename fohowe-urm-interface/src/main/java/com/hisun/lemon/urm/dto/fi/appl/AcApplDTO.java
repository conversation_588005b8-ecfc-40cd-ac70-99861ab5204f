 package com.hisun.lemon.urm.dto.fi.appl;


import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 账户申领传输对象
 * <AUTHOR>
 * @date 2017年11月16号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcApplDTO", description="账户申领传输对象")
public class AcApplDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private AcApplBean mainInfo = new AcApplBean();
	
	@ApiModelProperty(value="申领额度",required=false)
	private String applyCredit;
	
	@ApiModelProperty(value="申请编号",required=false)
	private String applNo;
	
	@ApiModelProperty(value="用户编号",required=false)
	private String userCode;
	
	@ApiModelProperty(value="状态：1新建6已取消7已退回2分公司已审核3总公司已审核5发放成功4财务已审核5已发放",required=false)
	private String status;
	
	@ApiModelProperty(value ="eas通知状态")
	private String easStatus;
	
	@ApiModelProperty(value="审核者帐号",required=false)
	private String checkerCode;
	
	@ApiModelProperty(value="开始审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minCheckTime;
	@ApiModelProperty(value="结束审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxCheckTime;
	@ApiModelProperty(value="开始申请时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minCreateTime;
	@ApiModelProperty(value="结束申请时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxCreateTime;
	
	@ApiModelProperty(value="财务确认状态 a:未确认;b:部分确认;c:已确认",required=false)
	private String fiCheckStatus;
	@ApiModelProperty(value="批量审核、删除ID",required=false)
	private String[] idArr;
	@ApiModelProperty(value="审批意见（1通过、0退回）",required=false)
	private String AuditOpinion;
	@ApiModelProperty(value="本次确认金额",required=false)
	private BigDecimal theAmount;
	@ApiModelProperty(value="本次申领金额",required=false)
	private BigDecimal theAppl;
	@ApiModelProperty(value="财务确定备注",required=false)
	private String theRemark;
	
	@ApiModelProperty(value="奖金制度")
    private String[] bonusTypeArr;
    @ApiModelProperty(value="区域")
    private String[] areaCodeArr;
    @ApiModelProperty(value="所属分公司")
    private String[] companyCodeArr;
    
    @ApiModelProperty(value="状态:1:新单 2:分公司已审核 3:总公司已审核  8：已作废  6:已取消",required=false,dataType="String")
	private String[] statusArr;
	@ApiModelProperty(value="财务确认状态 a:未确认;b:部分确认;c:已确认",required=false,dataType="String")
	private String[] fiCheckStatusArr;
	
	@ApiModelProperty(value="开始加入期数（用于匹配条件查询）")
    private String minPeriodWeek;
    
    @ApiModelProperty(value="结束加入期数（用于匹配条件查询）")
    private String maxPeriodWeek;
    //是否财务出入账
    private Integer isFin;

	private String Choose;
	private String mobile;
	private String accountBank;
	private String accountCode;
	private String accountName;

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getAccountBank() {
		return accountBank;
	}

	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getChoose() {
		return Choose;
	}

	public void setChoose(String choose) {
		Choose = choose;
	}

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public AcApplBean getMainInfo() {
		return mainInfo;
	}

	public void setMainInfo(AcApplBean mainInfo) {
		this.mainInfo = mainInfo;
	}

	public String getApplyCredit() {
		return applyCredit;
	}

	public void setApplyCredit(String applyCredit) {
		this.applyCredit = applyCredit;
	}

	public String getApplNo() {
		return applNo;
	}

	public void setApplNo(String applNo) {
		this.applNo = applNo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getCheckerCode() {
		return checkerCode;
	}

	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}

	public LocalDateTime getMinCheckTime() {
		return minCheckTime;
	}

	public void setMinCheckTime(LocalDateTime minCheckTime) {
		this.minCheckTime = minCheckTime;
	}

	public LocalDateTime getMaxCheckTime() {
		return maxCheckTime;
	}

	public void setMaxCheckTime(LocalDateTime maxCheckTime) {
		this.maxCheckTime = maxCheckTime;
	}

	public LocalDateTime getMinCreateTime() {
		return minCreateTime;
	}

	public void setMinCreateTime(LocalDateTime minCreateTime) {
		this.minCreateTime = minCreateTime;
	}

	public LocalDateTime getMaxCreateTime() {
		return maxCreateTime;
	}

	public void setMaxCreateTime(LocalDateTime maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}

	public String getFiCheckStatus() {
		return fiCheckStatus;
	}

	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}

	public String getAuditOpinion() {
		return AuditOpinion;
	}

	public void setAuditOpinion(String auditOpinion) {
		AuditOpinion = auditOpinion;
	}

	public String[] getIdArr() {
		return idArr;
	}

	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}

	public BigDecimal getTheAmount() {
		return theAmount;
	}

	public void setTheAmount(BigDecimal theAmount) {
		this.theAmount = theAmount;
	}

	public BigDecimal getTheAppl() {
		return theAppl;
	}

	public void setTheAppl(BigDecimal theAppl) {
		this.theAppl = theAppl;
	}

	public String getTheRemark() {
		return theRemark;
	}

	public void setTheRemark(String theRemark) {
		this.theRemark = theRemark;
	}

	public String[] getBonusTypeArr() {
		return bonusTypeArr;
	}

	public void setBonusTypeArr(String[] bonusTypeArr) {
		this.bonusTypeArr = bonusTypeArr;
	}

	public String[] getAreaCodeArr() {
		return areaCodeArr;
	}

	public void setAreaCodeArr(String[] areaCodeArr) {
		this.areaCodeArr = areaCodeArr;
	}

	public String[] getCompanyCodeArr() {
		return companyCodeArr;
	}

	public void setCompanyCodeArr(String[] companyCodeArr) {
		this.companyCodeArr = companyCodeArr;
	}

	public String[] getStatusArr() {
		return statusArr;
	}

	public void setStatusArr(String[] statusArr) {
		this.statusArr = statusArr;
	}

	public String[] getFiCheckStatusArr() {
		return fiCheckStatusArr;
	}

	public void setFiCheckStatusArr(String[] fiCheckStatusArr) {
		this.fiCheckStatusArr = fiCheckStatusArr;
	}

	public String getMinPeriodWeek() {
		return minPeriodWeek;
	}

	public void setMinPeriodWeek(String minPeriodWeek) {
		this.minPeriodWeek = minPeriodWeek;
	}

	public String getMaxPeriodWeek() {
		return maxPeriodWeek;
	}

	public void setMaxPeriodWeek(String maxPeriodWeek) {
		this.maxPeriodWeek = maxPeriodWeek;
	}

	public String getEasStatus() {
		return easStatus;
	}

	public void setEasStatus(String easStatus) {
		this.easStatus = easStatus;
	}

	public Integer getIsFin() {
		return isFin;
	}

	public void setIsFin(Integer isFin) {
		this.isFin = isFin;
	}
	
}
