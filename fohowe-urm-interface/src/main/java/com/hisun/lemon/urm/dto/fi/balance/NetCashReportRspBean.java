package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceItem 传输对象
 * <AUTHOR>
 * @date 2018年09月12号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="NetCashReportRspBean", description="账户统计报表传输对象")
public class NetCashReportRspBean extends GenericDTO<NoBody>{
	@ApiModelProperty(value="期数")
    private String period;
	
	@ApiModelProperty(value="奖金制度")
    private String bonusType;
	
    @ApiModelProperty(value="区域")
    private String areaCode;
    
    @ApiModelProperty(value="所属分公司")
    private String companyCode;
    
    @ApiModelProperty(value="代办处")
    private String agentNo;
	
    @ApiModelProperty(value="网币期初")
    private BigDecimal original;
    
    @ApiModelProperty(value="收入-申购")
    private BigDecimal inInput;
    @ApiModelProperty(value="收入-奖金")
    private BigDecimal inBonus;
    @ApiModelProperty(value="收入-代办费")
    private BigDecimal inAgenceFee;
    @ApiModelProperty(value="收入-其他")
    private BigDecimal inOther;
    
    @ApiModelProperty(value="支出-申领")
    private BigDecimal outApply;
    @ApiModelProperty(value="支出-报单")
    private BigDecimal outOrder;
    @ApiModelProperty(value="支出-其他")
    private BigDecimal outOther;
    
    @ApiModelProperty(value="收入合计")
    private BigDecimal inTotal;
    @ApiModelProperty(value="支出合计")
    private BigDecimal outTotal;
    @ApiModelProperty(value="收支和")
    private BigDecimal inOutCash;
	public String getPeriod() {
		return period;
	}
	public void setPeriod(String period) {
		this.period = period;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public BigDecimal getOriginal() {
		return original;
	}
	public void setOriginal(BigDecimal original) {
		this.original = original;
	}
	public BigDecimal getInInput() {
		return inInput;
	}
	public void setInInput(BigDecimal inInput) {
		this.inInput = inInput;
	}
	public BigDecimal getInBonus() {
		return inBonus;
	}
	public void setInBonus(BigDecimal inBonus) {
		this.inBonus = inBonus;
	}
	public BigDecimal getInAgenceFee() {
		return inAgenceFee;
	}
	public void setInAgenceFee(BigDecimal inAgenceFee) {
		this.inAgenceFee = inAgenceFee;
	}
	public BigDecimal getInOther() {
		return inOther;
	}
	public void setInOther(BigDecimal inOther) {
		this.inOther = inOther;
	}
	public BigDecimal getOutApply() {
		return outApply;
	}
	public void setOutApply(BigDecimal outApply) {
		this.outApply = outApply;
	}
	public BigDecimal getOutOrder() {
		return outOrder;
	}
	public void setOutOrder(BigDecimal outOrder) {
		this.outOrder = outOrder;
	}
	public BigDecimal getOutOther() {
		return outOther;
	}
	public void setOutOther(BigDecimal outOther) {
		this.outOther = outOther;
	}
	public BigDecimal getInTotal() {
		return inTotal;
	}
	public void setInTotal(BigDecimal inTotal) {
		this.inTotal = inTotal;
	}
	public BigDecimal getOutTotal() {
		return outTotal;
	}
	public void setOutTotal(BigDecimal outTotal) {
		this.outTotal = outTotal;
	}
	public BigDecimal getInOutCash() {
		return inOutCash;
	}
	public void setInOutCash(BigDecimal inOutCash) {
		this.inOutCash = inOutCash;
	}
	
}
