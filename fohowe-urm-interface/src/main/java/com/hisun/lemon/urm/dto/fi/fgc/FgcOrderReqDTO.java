package com.hisun.lemon.urm.dto.fi.fgc;

import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ClientValidated
@ApiModel(value = "FgcOrderReqDTO", description = "fgc订单传输对象")
public class FgcOrderReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "经销商编号")
    private String memberNo;

    @ApiModelProperty(value = "奖金制度")
    private String[] bonusTypes;

    @ApiModelProperty(value = "代办处编号")
    private String agentNo;

    @ApiModelProperty(value = "区域")
    private String[] areaCodes;

    @ApiModelProperty(value = "可以查询的分公司")
    private String[] companyCodes;

    @ApiModelProperty(value = "类型 1:购买 2:出售")
    private Integer exchangeType;

    @ApiModelProperty(value = "定存时长")
    private String depositLength;

    @ApiModelProperty(value = "状态 1:新建 2:定存中 3:已取消 4:已结束")
    private Integer status;

    @ApiModelProperty(value = "类型 1:活期 2:死期")
    private Integer depositType;


    @ApiModelProperty(value = "提交起始期数", required = false)
    private Integer startWeek;

    @ApiModelProperty(value = "提交结束期数", required = false)
    private Integer endWeek;

    @ApiModelProperty(value = "取消起始期数", required = false)
    private Integer canStartWeek;

    @ApiModelProperty(value = "取消结束期数", required = false)
    private Integer canEndWeek;

    @ApiModelProperty(value = "审核起始期数", required = false)
    private Integer auditStartWeek;

    @ApiModelProperty(value = "审核结束期数", required = false)
    private Integer auditEndWeek;

    @ApiModelProperty(value = "分页查询-第几页", required = true)
    private Integer pageNum = 1;

    @ApiModelProperty(value = "分页查询-每页数据条数", required = true)
    private Integer pageSize = 20;

    @ApiModelProperty(value = "提交起始时间(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using = DateJsonSerializer.class)
    @JsonDeserialize(using = DateJsonDeserializer.class)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "提交结束时间(yyyy-MM-dd HH:mm:ss)", required = false)
    @JsonSerialize(using = DateJsonSerializer.class)
    @JsonDeserialize(using = DateJsonDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "到期起始时间(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using = DateJsonSerializer.class)
    @JsonDeserialize(using = DateJsonDeserializer.class)
    private LocalDateTime dueStartTime;

    @ApiModelProperty(value = "到期结束时间(yyyy-MM-dd HH:mm:ss)", required = false)
    @JsonSerialize(using = DateJsonSerializer.class)
    @JsonDeserialize(using = DateJsonDeserializer.class)
    private LocalDateTime dueEndTime;

    @ApiModelProperty(value = "取消起始时间(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using = DateJsonSerializer.class)
    @JsonDeserialize(using = DateJsonDeserializer.class)
    private LocalDateTime canStartTime;

    @ApiModelProperty(value = "取消结束时间(yyyy-MM-dd HH:mm:ss)", required = false)
    @JsonSerialize(using = DateJsonSerializer.class)
    @JsonDeserialize(using = DateJsonDeserializer.class)
    private LocalDateTime canEndTime;
    
    @ApiModelProperty(value = "金价日期", required = false,notes="格式：20191202")
    private String batchNo;
    
    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public Integer getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(Integer startWeek) {
        this.startWeek = startWeek;
    }

    public Integer getEndWeek() {
        return endWeek;
    }

    public void setEndWeek(Integer endWeek) {
        this.endWeek = endWeek;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String[] getBonusTypes() {
        return bonusTypes;
    }

    public void setBonusTypes(String[] bonusTypes) {
        this.bonusTypes = bonusTypes;
    }

    public String[] getAreaCodes() {
        return areaCodes;
    }

    public void setAreaCodes(String[] areaCodes) {
        this.areaCodes = areaCodes;
    }

    public String[] getCompanyCodes() {
        return companyCodes;
    }

    public void setCompanyCodes(String[] companyCodes) {
        this.companyCodes = companyCodes;
    }

    public String getDepositLength() {
        return depositLength;
    }

    public void setDepositLength(String depositLength) {
        this.depositLength = depositLength;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getDueStartTime() {
        return dueStartTime;
    }

    public void setDueStartTime(LocalDateTime dueStartTime) {
        this.dueStartTime = dueStartTime;
    }

    public LocalDateTime getDueEndTime() {
        return dueEndTime;
    }

    public void setDueEndTime(LocalDateTime dueEndTime) {
        this.dueEndTime = dueEndTime;
    }

    public LocalDateTime getCanStartTime() {
        return canStartTime;
    }

    public void setCanStartTime(LocalDateTime canStartTime) {
        this.canStartTime = canStartTime;
    }

    public LocalDateTime getCanEndTime() {
        return canEndTime;
    }

    public void setCanEndTime(LocalDateTime canEndTime) {
        this.canEndTime = canEndTime;
    }

    public Integer getCanStartWeek() {
        return canStartWeek;
    }

    public void setCanStartWeek(Integer canStartWeek) {
        this.canStartWeek = canStartWeek;
    }

    public Integer getCanEndWeek() {
        return canEndWeek;
    }

    public void setCanEndWeek(Integer canEndWeek) {
        this.canEndWeek = canEndWeek;
    }

    public Integer getAuditStartWeek() {
        return auditStartWeek;
    }

    public void setAuditStartWeek(Integer auditStartWeek) {
        this.auditStartWeek = auditStartWeek;
    }

    public Integer getAuditEndWeek() {
        return auditEndWeek;
    }

    public void setAuditEndWeek(Integer auditEndWeek) {
        this.auditEndWeek = auditEndWeek;
    }

    public Integer getDepositType() {
        return depositType;
    }

    public void setDepositType(Integer depositType) {
        this.depositType = depositType;
    }


    public Integer getExchangeType() {
        return exchangeType;
    }

    public void setExchangeType(Integer exchangeType) {
        this.exchangeType = exchangeType;
    }

	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}
    
}