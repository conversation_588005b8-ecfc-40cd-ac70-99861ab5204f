package com.hisun.lemon.urm.dto.ic;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-公告查询列表信息DTO
 * 
 * <AUTHOR>
 * @date 2017年11月1日
 * @time 下午4:56:44
 *
 */
public class AnnounceQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(name = "announceId", value = "公告ID")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class })
    private String announceId;

    @ApiModelProperty(name = "langId", value = "语言编码ID")
    private String langId;

    @ApiModelProperty(name = "userType", value = "用户类型")
    @Length(max = 10, message = "URM10001", groups = { ValidateGroup.first.class })
    private String userType;

    @ApiModelProperty(name = "type", value = "类型")
    @Length(max = 10, message = "URM10001", groups = { ValidateGroup.first.class })
    private String type;

    @ApiModelProperty(name = "companyCode", value = "公司编号")
    @Length(max = 1000, message = "URM10001", groups = { ValidateGroup.first.class })
    private String companyCode;

    @ApiModelProperty(name = "title", value = "标题")
    @Length(max = 1000, message = "URM10002", groups = { ValidateGroup.first.class })
    private String title;

    @ApiModelProperty(name = "targetTerminal", value = "目标终端(多个以,分隔)")
    @Length(max = 20, message = "URM10002", groups = { ValidateGroup.first.class })
    private String targetTerminal;
    
    @ApiModelProperty(name = "groupId", value = "群组编号")
    @Length(max = 20, message = "URM10002", groups = { ValidateGroup.first.class })
    private String groupId;

    @ApiModelProperty(name = "pageNum", value = "页码")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageSize;
    
    public String getLangId() {
        return langId;
    }

    public void setLangId(String langId) {
        this.langId = langId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getAnnounceId() {
        return announceId;
    }

    public void setAnnounceId(String announceId) {
        this.announceId = announceId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTargetTerminal() {
        return targetTerminal;
    }

    public void setTargetTerminal(String targetTerminal) {
        this.targetTerminal = targetTerminal;
    }

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
    
}
