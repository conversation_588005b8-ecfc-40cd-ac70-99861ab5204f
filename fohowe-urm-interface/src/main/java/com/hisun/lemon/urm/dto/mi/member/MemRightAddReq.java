package com.hisun.lemon.urm.dto.mi.member;

import java.math.BigDecimal;

import com.hisun.lemon.fohowe.common.enums.BonusType;

import io.swagger.annotations.ApiModel;


/**
 * 
 * <AUTHOR>
 * @date 2017-11-15 09:52
 */
@ApiModel(value="增加经营权请求对象")
public class MemRightAddReq {
	

    /**
     * @Fields 经销商编号(必须字段)
     */
    private String memberNo;
	/**
     * 经销商的奖金制度(必须字段)
     */
    private BonusType bonusType;
    
    /**
     * @Fields linkNo 接点权编号(必须字段)
     */
    private String linkNo;
    
   /* 已核实了没有这种枪情况
    * @ApiModelProperty(value="是否限制接下线，0=不限制，1=只允许一个  2 一个都不允许")
    private String cannoLink;
    */
    /**
     * (必须字段)
     * 		0=未参加特别促销
     * 		1=参加了480PV（1+6），
     *       2=参加了750（2+5），
     *       3=参加了1020（3+4）
     *       4=参加了1+5
     *       5=参加了180促销
     *       6=美国模式的30pv注册
     *       7=参加了1020（3+4）B
     */
    private String isPromType;
    /**
     * 购买数量（必须）
     */
    private int buyNum;
    
    /**
     * @Fields 加入PV(必须字段)
     */
    private BigDecimal addPv;
    
    /**
     * (必须字段)
     * 购买时布局
     * 1 =直线
     * 2 =三角形
     * 3 =双直线
     */
    private String displayType;
    
    /**
     * 1=注册时添加经营权(此时需要判断接点权与推荐人关系)
     * 2=非注册时添加经营权(已有主经营权的用户做购买，首单除外)
     * 必须字段
     */
    private String addType;
    
    /**
     * 推荐人编号
     * addType = 1（注册）时，必须
     */
    private String recMemberNo;
    
    /**
     * 经营权编号
     * 用户有填写新的经营权编号 ，则需要
     */
    private String rightNo;

    /**
     * 需要新增的用户信息  urm模块专用，其他模块不需要使用
     * addType = 1（注册）时，必须
     */
    private MemAddRightRspBean memRspBean;
    
    private Integer isPvLimit;
    
    /**
     * 需要增加经营权的经销商，对应的公司编号
     */
    private String companyCode;
    
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getRightNo() {
		return rightNo;
	}
	public void setRightNo(String rightNo) {
		this.rightNo = rightNo;
	}
	public String getMemberNo() {
		return memberNo;
	}
	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}
	
	public BigDecimal getAddPv() {
		return addPv;
	}
	public void setAddPv(BigDecimal addPv) {
		this.addPv = addPv;
	}
	public String getIsPromType() {
		return isPromType;
	}
	public void setIsPromType(String isPromType) {
		this.isPromType = isPromType;
	}
	public int getBuyNum() {
		return buyNum;
	}
	public void setBuyNum(int buyNum) {
		this.buyNum = buyNum;
	}
	public String getDisplayType() {
		return displayType;
	}
	public void setDisplayType(String displayType) {
		this.displayType = displayType;
	}
	public BonusType getBonusType() {
		return bonusType;
	}
	public void setBonusType(BonusType bonusType) {
		this.bonusType = bonusType;
	}
	public String getLinkNo() {
		return linkNo;
	}
	public void setLinkNo(String linkNo) {
		this.linkNo = linkNo;
	}
	public String getAddType() {
		return addType;
	}
	public void setAddType(String addType) {
		this.addType = addType;
	}
	public String getRecMemberNo() {
		return recMemberNo;
	}
	public void setRecMemberNo(String recMemberNo) {
		this.recMemberNo = recMemberNo;
	}
	public MemAddRightRspBean getMemRspBean() {
		return memRspBean;
	}
	public void setMemRspBean(MemAddRightRspBean memRspBean) {
		this.memRspBean = memRspBean;
	}
	public Integer getIsPvLimit() {
		return isPvLimit;
	}
	public void setIsPvLimit(Integer isPvLimit) {
		this.isPvLimit = isPvLimit;
	}
}

