package com.hisun.lemon.urm.dto.mi.member;

import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 用户推荐关系返回
 * 
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ApiModel(value="NetWorkVO", description="用户推荐关系返回")
public class NetWorkVO {
/*    @ApiModelProperty(value="原始的用户编号",required= true)
    private String originalMemNo;*/
    
    @ApiModelProperty(value="当前最顶级的用户编号",required= true)
    private String memNo;
    
    @ApiModelProperty(value="用于jOrgChart的json数据",required= true)
    MemRecTree dataList;
    
    @ApiModelProperty(value="数据字典-经销商图片")
	 Map<String, String> levelUrlKV=null;
	 
	 @ApiModelProperty(value="数据字典-经销商级别名称")
	 Map<String, String> levelNameKV=null;
	  

	public String getMemNo() {
		return memNo;
	}

	public void setMemNo(String memNo) {
		this.memNo = memNo;
	}

	public MemRecTree getDataList() {
		return dataList;
	}

	public void setDataList(MemRecTree dataList) {
		this.dataList = dataList;
	}

	public Map<String, String> getLevelUrlKV() {
		return levelUrlKV;
	}

	public void setLevelUrlKV(Map<String, String> levelUrlKV) {
		this.levelUrlKV = levelUrlKV;
	}

	public Map<String, String> getLevelNameKV() {
		return levelNameKV;
	}

	public void setLevelNameKV(Map<String, String> levelNameKV) {
		this.levelNameKV = levelNameKV;
	}

	


	
    
    
}

