package com.hisun.lemon.urm.dto.fi.tickets;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TicketMeetingReviewDTO {
    private Long id;
    private String reviewFirstType;

    private String reviewMeetingType;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewDateStart;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewDateEnd;

    private Integer reviewNum;

    private String reviewManager;

    private String reviewGuests;

    private BigDecimal reviewAmount;

    private String reviewCurrency;

    private String reviewLecturer;

    private String reviewTeacher;

    private String reviewTranslator;

    private String reviewTransPart;

    private Integer reviewDealQty;

    private BigDecimal reviewDeposit;

    private String reviewRemark;
    private Integer vcStatus;

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReviewFirstType() {
        return reviewFirstType;
    }

    public void setReviewFirstType(String reviewFirstType) {
        this.reviewFirstType = reviewFirstType;
    }

    public String getReviewMeetingType() {
        return reviewMeetingType;
    }

    public void setReviewMeetingType(String reviewMeetingType) {
        this.reviewMeetingType = reviewMeetingType;
    }

    public LocalDateTime getReviewDateStart() {
        return reviewDateStart;
    }

    public void setReviewDateStart(LocalDateTime reviewDateStart) {
        this.reviewDateStart = reviewDateStart;
    }

    public LocalDateTime getReviewDateEnd() {
        return reviewDateEnd;
    }

    public void setReviewDateEnd(LocalDateTime reviewDateEnd) {
        this.reviewDateEnd = reviewDateEnd;
    }

    public Integer getReviewNum() {
        return reviewNum;
    }

    public void setReviewNum(Integer reviewNum) {
        this.reviewNum = reviewNum;
    }

    public String getReviewManager() {
        return reviewManager;
    }

    public void setReviewManager(String reviewManager) {
        this.reviewManager = reviewManager;
    }

    public String getReviewGuests() {
        return reviewGuests;
    }

    public void setReviewGuests(String reviewGuests) {
        this.reviewGuests = reviewGuests;
    }

    public BigDecimal getReviewAmount() {
        return reviewAmount;
    }

    public void setReviewAmount(BigDecimal reviewAmount) {
        this.reviewAmount = reviewAmount;
    }

    public String getReviewCurrency() {
        return reviewCurrency;
    }

    public void setReviewCurrency(String reviewCurrency) {
        this.reviewCurrency = reviewCurrency;
    }

    public String getReviewLecturer() {
        return reviewLecturer;
    }

    public void setReviewLecturer(String reviewLecturer) {
        this.reviewLecturer = reviewLecturer;
    }

    public String getReviewTeacher() {
        return reviewTeacher;
    }

    public void setReviewTeacher(String reviewTeacher) {
        this.reviewTeacher = reviewTeacher;
    }

    public String getReviewTranslator() {
        return reviewTranslator;
    }

    public void setReviewTranslator(String reviewTranslator) {
        this.reviewTranslator = reviewTranslator;
    }

    public String getReviewTransPart() {
        return reviewTransPart;
    }

    public void setReviewTransPart(String reviewTransPart) {
        this.reviewTransPart = reviewTransPart;
    }

    public Integer getReviewDealQty() {
        return reviewDealQty;
    }

    public void setReviewDealQty(Integer reviewDealQty) {
        this.reviewDealQty = reviewDealQty;
    }

    public BigDecimal getReviewDeposit() {
        return reviewDeposit;
    }

    public void setReviewDeposit(BigDecimal reviewDeposit) {
        this.reviewDeposit = reviewDeposit;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }
}
