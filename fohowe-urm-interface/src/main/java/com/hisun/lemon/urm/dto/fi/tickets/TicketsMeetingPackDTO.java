package com.hisun.lemon.urm.dto.fi.tickets;

import com.hisun.lemon.urm.common.PageInfo;

import java.util.List;

public class TicketsMeetingPackDTO extends PageInfo {
    private List<TicketsMeetingRspDTO> FutrueInformationList;
    private List<TicketsMeetingRspDTO> ActualInformationList;
    private List<TicketsMeetingRspDTO> PaymentInformationList;
    private List<TicketsMeetingRspDTO> MeetingInformationList;
    private List<TicketsMeetingRspDTO> OtherInformationList;
    private List<TicketsVoucherBean> getFindList;

    public List<TicketsVoucherBean> getGetFindList() {
        return getFindList;
    }

    public void setGetFindList(List<TicketsVoucherBean> getFindList) {
        this.getFindList = getFindList;
    }

    public List<TicketsMeetingRspDTO> getActualInformationList() {
        return ActualInformationList;
    }

    public void setActualInformationList(List<TicketsMeetingRspDTO> actualInformationList) {
        ActualInformationList = actualInformationList;
    }

    public List<TicketsMeetingRspDTO> getPaymentInformationList() {
        return PaymentInformationList;
    }

    public void setPaymentInformationList(List<TicketsMeetingRspDTO> paymentInformationList) {
        PaymentInformationList = paymentInformationList;
    }

    public List<TicketsMeetingRspDTO> getMeetingInformationList() {
        return MeetingInformationList;
    }

    public void setMeetingInformationList(List<TicketsMeetingRspDTO> meetingInformationList) {
        MeetingInformationList = meetingInformationList;
    }

    public List<TicketsMeetingRspDTO> getOtherInformationList() {
        return OtherInformationList;
    }

    public void setOtherInformationList(List<TicketsMeetingRspDTO> otherInformationList) {
        OtherInformationList = otherInformationList;
    }

    public List<TicketsMeetingRspDTO> getFutrueInformationList() {
        return FutrueInformationList;
    }

    public void setFutrueInformationList(List<TicketsMeetingRspDTO> futrueInformationList) {
        FutrueInformationList = futrueInformationList;
    }
}
