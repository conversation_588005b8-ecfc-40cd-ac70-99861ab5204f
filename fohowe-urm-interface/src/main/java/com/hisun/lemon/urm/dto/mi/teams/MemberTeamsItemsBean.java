package com.hisun.lemon.urm.dto.mi.teams;

public class MemberTeamsItemsBean {
    private Integer id;

    private String teamNo;

    private String memberNo;

    private String memberName;

    private String cardType;

    private Integer itemStatus;

    private Integer passeFlag;
    
    private Integer fendou;
    
    private Integer leader;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTeamNo() {
        return teamNo;
    }

    public void setTeamNo(String teamNo) {
        this.teamNo = teamNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public Integer getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(Integer itemStatus) {
        this.itemStatus = itemStatus;
    }

    public Integer getPasseFlag() {
        return passeFlag;
    }

    public void setPasseFlag(Integer passeFlag) {
        this.passeFlag = passeFlag;
    }

	public Integer getFendou() {
		return fendou;
	}

	public void setFendou(Integer fendou) {
		this.fendou = fendou;
	}

	public Integer getLeader() {
		return leader;
	}

	public void setLeader(Integer leader) {
		this.leader = leader;
	}
}