package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.DateConstant;

/**
 * SubMember 传输对象
 * <AUTHOR>
 * @date 2017年11月1日
 * @time 上午9:27:30
 *
 */

@ClientValidated
@ApiModel(value="SubMember", description="下属经销商查询传输对象")
public class SubMember{
	@ApiModelProperty(value="经销商编号")
	private String memberNo;
    @ApiModelProperty(value="经销商姓名")
	private String memberName; 
    @ApiModelProperty(value="代办处编号")
    private String agentNo;
    @ApiModelProperty(value="推荐人编号")
    private String recommendNo;
    @ApiModelProperty(value="经销商级别")
    private String cardType;
    @ApiModelProperty(value="代数")
    private Integer layer;
    @ApiModelProperty(value="注册时间")
    @JsonFormat(pattern=DateConstant.STR1_YMD)
    private LocalDateTime registerDate;
    @ApiModelProperty(value="促销资格")
    private int promQual;
    
	public String getMemberNo() {
		return memberNo;
	}
	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}
	public String getMemberName() {
		return memberName;
	}
	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getRecommendNo() {
		return recommendNo;
	}
	public void setRecommendNo(String recommendNo) {
		this.recommendNo = recommendNo;
	}
	public LocalDateTime getRegisterDate() {
		return registerDate;
	}
	public void setRegisterDate(LocalDateTime registerDate) {
		this.registerDate = registerDate;
	}
	public String getCardType() {
		return cardType;
	}
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}
	public Integer getLayer() {
		return layer;
	}
	public void setLayer(Integer layer) {
		this.layer = layer;
	}
	public int getPromQual() {
		return promQual;
	}
	public void setPromQual(int promQual) {
		this.promQual = promQual;
	}
	
}
