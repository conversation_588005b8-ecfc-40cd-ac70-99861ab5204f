/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: RegionModifyReqDTO.java 
 * @Prject: fohowe-urm-interface
 * @Package: com.hisun.lemon.urm.dto.al 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月7日 下午4:48:38 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: RegionModifyReqDTO
 * @Description: 公司地区资料维护查询请求DTO
 * @author: tian
 * @date: 2017年11月7日 下午4:48:38
 */
public class RegionCompanyQueryReqDTO extends GenericDTO<NoBody> {
    @ApiModelProperty(name = "bonusType", value = "奖金制度")
    private String bonusType;

    @ApiModelProperty(name = "areaCode", value = "区域编码")
    private String areaCode;

    @ApiModelProperty(name = "companyCode", value = "公司编码")
    private String companyCode;

    @ApiModelProperty(name = "regionCode", value = "地区编码")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.fourth.class, ValidateGroup.fifth.class })
    private String regionCode;

    @ApiModelProperty(name = "regionName", value = "地区名称")
    private String regionName;

    @ApiModelProperty(name = "pageNum", value = "页码")
    @NotNull(message = "URM10005", groups = { ValidateGroup.third.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.third.class })
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数")
    @NotNull(message = "URM10005", groups = { ValidateGroup.third.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.third.class })
    private int pageSize;

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getBonusType() {
        return bonusType;
    }

    public void setBonusType(String bonusType) {
        this.bonusType = bonusType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
