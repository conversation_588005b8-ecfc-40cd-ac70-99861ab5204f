package com.hisun.lemon.urm.dto.fi.fgc;

import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@ClientValidated
@ApiModel(value = "FgcExchangeReqDTO", description = "fgc兑换传输对象")
public class FgcExchangeReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "经销商编号")
    @NotNull(message = "URM62005", groups = {ValidateGroup.first.class,ValidateGroup.third.class,ValidateGroup.second.class})
    private String memberNo;

    @ApiModelProperty(value = "FGC数量")
    @NotNull(message = "URM62003", groups = {ValidateGroup.first.class,ValidateGroup.third.class})
    @Digits(integer = 6, fraction = 2 ,message = "超过可接受的小数位数的最大数目2位" ,groups = {ValidateGroup.first.class,ValidateGroup.third.class})
    private BigDecimal fgcNumber;

    @ApiModelProperty(name = "payPassword", value = "支付密码", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.third.class })
    private String payPassword;

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public BigDecimal getFgcNumber() {
        return fgcNumber;
    }

    public void setFgcNumber(BigDecimal fgcNumber) {
        this.fgcNumber = fgcNumber;
    }

    public String getPayPassword() {
        return payPassword;
    }

    public void setPayPassword(String payPassword) {
        this.payPassword = payPassword;
    }
}
