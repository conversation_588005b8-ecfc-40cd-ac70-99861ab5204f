package com.hisun.lemon.urm.dto.mi.member;

import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * MemberVO 传输对象
 * <AUTHOR>
 * @date 2017年11月1日
 * @time 上午9:27:30
 *
 */

@ClientValidated
@ApiModel(value="MemberVO", description="经销商查询传输对象")
public class MemberVO{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="查询结果集")
	List<MemberQueryBean> dataList;
	List<MemberPictureBean> pictureList;
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<MemberQueryBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<MemberQueryBean> dataList) {
		this.dataList = dataList;
	}

	public List<MemberPictureBean> getPictureList() {
		return pictureList;
	}

	public void setPictureList(List<MemberPictureBean> pictureList) {
		this.pictureList = pictureList;
	}
}
