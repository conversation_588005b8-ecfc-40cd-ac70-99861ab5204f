package com.hisun.lemon.urm.dto.ic;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 信息中心-公告全部已读
 * 
 * <AUTHOR>
 * @date 2017年11月2日
 * @time 下午4:45:33
 *
 */
public class AnnounceModifyLookAllDTO {

    @ApiModelProperty(name = "type", value = "类型TYPE 1公告 2新闻 3通知 4其他 5购物指南 6配送方式 7支付方式 8售后服务 9特色服务 10售后政策")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class, ValidateGroup.first.class })
    private String type;

    @ApiModelProperty(name = "userType", value = "发布对象 0公司用户 1经销商 2代办处")
    private String userType;

    @ApiModelProperty(name = "title", value = "标题")
    @Length(max = 1000, message = "URM10002")
    private String title;

    @ApiModelProperty(name = "targetTerminal", value = "目标终端 0-波兰 1-立陶宛 2-跨境电商 3-美国 4-代办处经销商系统 6-手机app(多个以,分隔)")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class, ValidateGroup.first.class })
    private String targetTerminal;
    
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTargetTerminal() {
		return targetTerminal;
	}

	public void setTargetTerminal(String targetTerminal) {
		this.targetTerminal = targetTerminal;
	}

}
