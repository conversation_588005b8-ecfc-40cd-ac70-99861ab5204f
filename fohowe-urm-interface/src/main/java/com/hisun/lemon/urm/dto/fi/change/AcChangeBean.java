 package com.hisun.lemon.urm.dto.fi.change;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.enums.OrderTypeEnums;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcChangeBean 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcChangeBean", description="账户补扣对象")
public class AcChangeBean{
	@ApiModelProperty(value="id",required=false)
    private Long id;
	@ApiModelProperty(value="分公司编码",required=false,dataType="String")
    private String companyCode;
	@ApiModelProperty(value="账户类型",required=false)
    private String acType;
	@ApiModelProperty(value="经销商/代办处编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(value="代办处编号",required=false,dataType="String")
    private String agentNo;
	@ApiModelProperty(value="交易类别，1x之后的界面定义录入",required=false)
    private String orderType;
	@ApiModelProperty(value="交易日期",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime dealDate;
	@ApiModelProperty(value="货币编码",required=false,dataType="String")
    private String currencyCode;
	@ApiModelProperty(value="余额变化",required=false,dataType="BigDecimal")
    private BigDecimal money;
	@ApiModelProperty(value="摘要",required=false,dataType="String")
    private String remark;
	@ApiModelProperty(value="备注",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(value="建立者帐号",required=false,dataType="String")
    private String createrCode;
	@ApiModelProperty(value="建立者名称 ",required=false,dataType="String")
    private String createrName;
	@ApiModelProperty(value="创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
	@ApiModelProperty(value="转出银行帐号/发票号",required=false,dataType="String")
    private String outCode;
	@ApiModelProperty(value="审核者帐号",required=false,dataType="String")
    private String checkerCode;
	@ApiModelProperty(value="审核者名称",required=false,dataType="String")
    private String checkerName;
	@ApiModelProperty(value="审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkTime;
	@ApiModelProperty(value="状态1新增2已审核",required=false,dataType="String")
    private String status;
	@ApiModelProperty(value="操作类型：1=存入(不判断余额),2=提取(判断余额)",required=false,dataType="String")
    private String changeType;
	@ApiModelProperty(value="批量操作的批号",required=false,dataType="String")
    private String createNo;
	@ApiModelProperty(value="周",required=false,dataType="String")
    private String wWeek;
	@ApiModelProperty(value="月",required=false,dataType="String")
    private String wMonth;
	@ApiModelProperty(value="0 =扣补，1=基金，2=教育币",required=false,dataType="String")
    private String fundSend;
	@ApiModelProperty(value="申请期数",required=false,dataType="String")
    private String periodWeek;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

	public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public LocalDateTime getDealDate() {
        return dealDate;
    }

    public void setDealDate(LocalDateTime dealDate) {
        this.dealDate = dealDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }
    
    public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getOutCode() {
        return outCode;
    }

    public void setOutCode(String outCode) {
        this.outCode = outCode;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public String getCheckerName() {
        return checkerName;
    }

    public void setCheckerName(String checkerName) {
        this.checkerName = checkerName;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getCreateNo() {
        return createNo;
    }

    public void setCreateNo(String createNo) {
        this.createNo = createNo;
    }

    public String getwWeek() {
        return wWeek;
    }

    public void setwWeek(String wWeek) {
        this.wWeek = wWeek;
    }

    public String getwMonth() {
        return wMonth;
    }

    public void setwMonth(String wMonth) {
        this.wMonth = wMonth;
    }

    public String getFundSend() {
        return fundSend;
    }

    public void setFundSend(String fundSend) {
        this.fundSend = fundSend;
    }

    public String getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
}
