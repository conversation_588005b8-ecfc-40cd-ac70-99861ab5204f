 package com.hisun.lemon.urm.dto.fi.input;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputGood 传输对象
 * <AUTHOR>
 * @date 2017年11月14号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputGood", description="账户传输对象")
public class AcInputGood{
	
	@ApiModelProperty(value="商品编号",required=false,dataType="String")
    private String goodsCode;
	
    @ApiModelProperty(value="数量",required=false,dataType="String")
    private int quantity;
	
    @ApiModelProperty(value="出库分公司",required=false,dataType="String")
    private String outCompanyCode;

	public String getGoodsCode() {
		return goodsCode;
	}

	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public String getOutCompanyCode() {
		return outCompanyCode;
	}

	public void setOutCompanyCode(String outCompanyCode) {
		this.outCompanyCode = outCompanyCode;
	}
    
}
