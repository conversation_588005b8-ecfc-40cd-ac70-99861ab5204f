package com.hisun.lemon.urm.dto.fi.tickets;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ClientValidated
@ApiModel(value="TicketsVoucherVO", description="转账对象")
public class TicketsVoucherVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
	@ApiModelProperty(value="查询结果集")
	List<TicketsVoucherBean> dataList;
	
	private BigDecimal totalAmount=BigDecimal.ZERO;
	private BigDecimal jiazengAmount=BigDecimal.ZERO;
	private BigDecimal orderAmount=BigDecimal.ZERO;
	
	Map<String, String> statusMap = new HashMap<String, String>();
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public List<TicketsVoucherBean> getDataList() {
		return dataList;
	}
	public void setDataList(List<TicketsVoucherBean> dataList) {
		this.dataList = dataList;
	}
	public Map<String, String> getStatusMap() {
		return statusMap;
	}
	public void setStatusMap(Map<String, String> statusMap) {
		this.statusMap = statusMap;
	}
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}
	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}
	public BigDecimal getJiazengAmount() {
		return jiazengAmount;
	}
	public void setJiazengAmount(BigDecimal jiazengAmount) {
		this.jiazengAmount = jiazengAmount;
	}
	public BigDecimal getOrderAmount() {
		return orderAmount;
	}
	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}
	
}
