package com.hisun.lemon.urm.dto.fi.balance;
/**
 * 账户变动
 * <AUTHOR>
 * @time 2017.11.22 下午19:35:22
 */

import java.util.List;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
@ApiModel(value="AcChangeRequest", description="账户传输对象")
public class AcChangeRequest extends GenericDTO<NoBody>{
	@ApiModelProperty(value="",required=true,dataType="BigDecimal")
	private List<AcBalanceChange> acBalanceChanges;
	@ApiModelProperty(value="请求ID（每次请求ID唯一,必填）",required=true,dataType="BigDecimal")
	private String requestNum;
	@ApiModelProperty(value="订单编号",required=false)
	private String orderNo;
	@ApiModelProperty(value="是否借款",required=false)
	private boolean isOwe;
	@ApiModelProperty(value="是否跳过校验",required=false)
	private boolean isCheck;
	
	public List<AcBalanceChange> getAcBalanceChanges() {
		return acBalanceChanges;
	}
	public void setAcBalanceChanges(List<AcBalanceChange> acBalanceChanges) {
		this.acBalanceChanges = acBalanceChanges;
	}
	public String getRequestNum() {
		return requestNum;
	}
	public void setRequestNum(String requestNum) {
		this.requestNum = requestNum;
	}
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public boolean isOwe() {
		return isOwe;
	}
	public void setOwe(boolean isOwe) {
		this.isOwe = isOwe;
	}
	public boolean isCheck() {
		return isCheck;
	}
	public void setCheck(boolean isCheck) {
		this.isCheck = isCheck;
	}
}
