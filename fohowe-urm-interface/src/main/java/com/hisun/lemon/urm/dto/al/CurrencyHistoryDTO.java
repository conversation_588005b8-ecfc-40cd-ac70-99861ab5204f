package com.hisun.lemon.urm.dto.al;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class CurrencyHistoryDTO {
	private Long id;
    private String currencyCode;
    private String currencyName;
    private LocalDateTime createTime;
    private BigDecimal rateToUsd;
    private BigDecimal rateToEur;
    private BigDecimal rateToInput;
    private BigDecimal rateToApple;
    private BigDecimal rateToGold;

    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }
    
    public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public BigDecimal getRateToUsd() {
        return rateToUsd;
    }

    public void setRateToUsd(BigDecimal rateToUsd) {
        this.rateToUsd = rateToUsd;
    }

    public BigDecimal getRateToEur() {
        return rateToEur;
    }

    public void setRateToEur(BigDecimal rateToEur) {
        this.rateToEur = rateToEur;
    }

    public BigDecimal getRateToInput() {
        return rateToInput;
    }

    public void setRateToInput(BigDecimal rateToInput) {
        this.rateToInput = rateToInput;
    }

    public BigDecimal getRateToApple() {
        return rateToApple;
    }

    public void setRateToApple(BigDecimal rateToApple) {
        this.rateToApple = rateToApple;
    }

    public BigDecimal getRateToGold() {
        return rateToGold;
    }

    public void setRateToGold(BigDecimal rateToGold) {
        this.rateToGold = rateToGold;
    }

}
