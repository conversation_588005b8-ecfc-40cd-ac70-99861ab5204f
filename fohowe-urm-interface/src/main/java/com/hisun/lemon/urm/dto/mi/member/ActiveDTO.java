package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import javax.validation.constraints.Size;

import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.DateConstant;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

/**
 * 
 * <AUTHOR>
 * @date 2017-11-13 10:45
 */
@ApiModel(value="ActiveDTO", description="活跃相关")
public class ActiveDTO  extends GenericDTO<NoBody>{
	
    @ApiModelProperty(value="基本查询信息")
    private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();

    @ApiModelProperty(value="剩余期数限制(1=等于，2=大于，3=小于，4=大于等于，5=小于等于)")
    private String restrict;
    
    @ApiModelProperty(value="剩余期数")
    private Integer remainWeek;
    
    @ApiModelProperty(value="已过期")
    private Integer overWeek;

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public String getRestrict() {
		return restrict;
	}

	public void setRestrict(String restrict) {
		this.restrict = restrict;
	}

	public Integer getRemainWeek() {
		return remainWeek;
	}

	public void setRemainWeek(Integer remainWeek) {
		this.remainWeek = remainWeek;
	}

	public Integer getOverWeek() {
		return overWeek;
	}

	public void setOverWeek(Integer overWeek) {
		this.overWeek = overWeek;
	}


    
     
    
}