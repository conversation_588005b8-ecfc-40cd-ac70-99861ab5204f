package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 语言管理-语言维护查询
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午5:07:48
 *
 */
public class MsgInfoQueryReqDTO extends GenericDTO<NoBody> {
    @ApiModelProperty(name = "msgCd", value = "字符键值", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.fourth.class, ValidateGroup.fifth.class })
    private String msgCd;

    @ApiModelProperty(name = "msgInfo", value = "字符键值描述", required = false, dataType = "String")
    private String msgInfo;

    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageSize;
 
    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

	public String getMsgCd() {
		return msgCd;
	}

	public void setMsgCd(String msgCd) {
		this.msgCd = msgCd;
	}

	public String getMsgInfo() {
		return msgInfo;
	}

	public void setMsgInfo(String msgInfo) {
		this.msgInfo = msgInfo;
	}
}
