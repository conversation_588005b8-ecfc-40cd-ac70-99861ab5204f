package com.hisun.lemon.urm.dto.mi.member;

import java.util.List;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * MemberDTO 传输对象
 * <AUTHOR>
 * @date 2017年10月30日
 * @time 上午9:27:30
 *
 */
@ClientValidated
@ApiModel(value="MemberNameDTO", description="经销商传输对象")
public class MemberNameDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="经销商编号")
    private List<String> memberNoList;

	public List<String> getMemberNoList() {
		return memberNoList;
	}

	public void setMemberNoList(List<String> memberNoList) {
		this.memberNoList = memberNoList;
	}
	
}
