 package com.hisun.lemon.urm.dto.fi.input;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import com.hisun.lemon.urm.dto.sys.SysAttachReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputBean 传输对象
 * <AUTHOR>
 * @date 2017年11月9号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputDetailBean", description="账户传输对象")
public class AcInputDetailBean{
	@ApiModelProperty(value="记录id",required=false)
    private Long id;
	@ApiModelProperty(value="金额",required=false,dataType="BigDecimal")
    private BigDecimal money;
	@ApiModelProperty(value="充值单id",required=false)
    private Long inputId;
	@ApiModelProperty(value="本地货币代码",required=false,dataType="String")
    private String localCurrency;
	@ApiModelProperty(value="本地货币金额",required=false,dataType="BigDecimal")
    private BigDecimal localMoney;
	@ApiModelProperty(value="汇率",required=false,dataType="BigDecimal")
    private BigDecimal rate;
	@ApiModelProperty(value="备注",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(value="状态:2:核准  6 删除",required=false,dataType="String")
    private String status;
	@ApiModelProperty(value="建档人(确认人)",required=false,dataType="String")
    private String createrCode;
	@ApiModelProperty(value="建档人名称（确认人名称）",required=false,dataType="String")
    private String createrName;
	@ApiModelProperty(value="审核人（取消人）",required=false,dataType="String")
    private String cancelCode;
	@ApiModelProperty(value="审核人名称（取消人名称）",required=false,dataType="String")
    private String cancelName;
	@ApiModelProperty(value="审核时间（取消时间）",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime cancelTime;
	@ApiModelProperty(value="创建时间（确认时间）",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
	@ApiModelProperty(value="通知序号",required=false)
	private Long easSn;

    private String accountCompany;
    private String accountCash;
    private String accountType;

    private String accountName;
    private List<SysAttachReqDTO> filesList = new ArrayList<SysAttachReqDTO>();
	
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public Long getInputId() {
        return inputId;
    }

    public void setInputId(Long inputId) {
        this.inputId = inputId;
    }

    public String getLocalCurrency() {
        return localCurrency;
    }

    public void setLocalCurrency(String localCurrency) {
        this.localCurrency = localCurrency;
    }

    public BigDecimal getLocalMoney() {
        return localMoney;
    }

    public void setLocalMoney(BigDecimal localMoney) {
        this.localMoney = localMoney;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public String getCancelName() {
        return cancelName;
    }

    public void setCancelName(String cancelName) {
        this.cancelName = cancelName;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public Long getEasSn() {
		return easSn;
	}

	public void setEasSn(Long easSn) {
		this.easSn = easSn;
	}

    public String getAccountCompany() {
        return accountCompany;
    }

    public void setAccountCompany(String accountCompany) {
        this.accountCompany = accountCompany;
    }

    public String getAccountCash() {
        return accountCash;
    }

    public void setAccountCash(String accountCash) {
        this.accountCash = accountCash;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public List<SysAttachReqDTO> getFilesList() {
        return filesList;
    }

    public void setFilesList(List<SysAttachReqDTO> filesList) {
        this.filesList = filesList;
    }
}
