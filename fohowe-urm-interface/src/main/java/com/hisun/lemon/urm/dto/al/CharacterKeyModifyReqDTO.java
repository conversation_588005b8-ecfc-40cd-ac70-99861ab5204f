package com.hisun.lemon.urm.dto.al;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * 语言管理-语言维护
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午2:21:33
 *
 */
public class CharacterKeyModifyReqDTO {
    @ApiModelProperty(name = "id", value = "id")
    private Long id;

    @ApiModelProperty(name = "characterKey", value = "字符键值（多条记录时以','分隔）")
    private String characterKey;

    @ApiModelProperty(name = "modifyType", value = "1-新增指定 （默认 0-初始化所有 ）")
    private String modifyType;

    @ApiModelProperty(name = "keyDesc", value = "字符键值描述")
    private String keyDesc;

    @ApiModelProperty(name = "charValueList", value = "语言编码与显示文字list")
    private List<CharacterQueryRspDTO> charValueList;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCharacterKey() {
        return characterKey;
    }

    public void setCharacterKey(String characterKey) {
        this.characterKey = characterKey;
    }

    public String getKeyDesc() {
        return keyDesc;
    }

    public void setKeyDesc(String keyDesc) {
        this.keyDesc = keyDesc;
    }

    public List<CharacterQueryRspDTO> getCharValueList() {
        return charValueList;
    }

    public void setCharValueList(List<CharacterQueryRspDTO> charValueList) {
        this.charValueList = charValueList;
    }

    public String getModifyType() {
        return modifyType;
    }

    public void setModifyType(String modifyType) {
        this.modifyType = modifyType;
    }

}
