 package com.hisun.lemon.urm.dto.fi.balance;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
import com.hisun.lemon.urm.common.PageInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceVO", description="账户传输对象")
public class AcBalanceVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
    @ApiModelProperty(value="账户列表" )
    private List<AcBalanceQueryBean> acBalances;

	@ApiModelProperty(value="汇总金额展示",dataType="AcBalanceQueryBean2")
	private AcBalanceQueryBean2 acBalanceQueryBean2;

	@ApiModelProperty(value="账户类型")
	Map acTypeKV=null;

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcBalanceQueryBean> getAcBalances() {
		return acBalances;
	}

	public void setAcBalances(List<AcBalanceQueryBean> acBalances) {
		this.acBalances = acBalances;
	}

	public Map getAcTypeKV() {
		return acTypeKV;
	}

	public void setAcTypeKV(Map acTypeKV) {
		this.acTypeKV = acTypeKV;
	}

	public AcBalanceQueryBean2 getAcBalanceQueryBean2() {
		return acBalanceQueryBean2;
	}

	public void setAcBalanceQueryBean2(AcBalanceQueryBean2 acBalanceQueryBean2) {
		this.acBalanceQueryBean2 = acBalanceQueryBean2;
	}
}
