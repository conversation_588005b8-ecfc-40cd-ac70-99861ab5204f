package com.hisun.lemon.urm.dto.mi.member;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 购买经营权回退
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ApiModel(value="RightDelBean",description="用于购买经营权回退")
public class RightDelBean {
    /**
     * @Fields rightNo 经营权编号
     */
    @ApiModelProperty(value="经营权编号")
    private String rightNo;

    /**
     * @Fields 该值必须为购买经营权时分配的已支付pv
     */
    private BigDecimal pv;

	public String getRightNo() {
		return rightNo;
	}

	public void setRightNo(String rightNo) {
		this.rightNo = rightNo;
	}

	public BigDecimal getPv() {
		return pv;
	}

	public void setPv(BigDecimal pv) {
		this.pv = pv;
	}

    
    
    
   
    
}

