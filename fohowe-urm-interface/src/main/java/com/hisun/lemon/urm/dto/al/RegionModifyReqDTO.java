/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: RegionModifyReqDTO.java 
 * @Prject: fohowe-urm-interface
 * @Package: com.hisun.lemon.urm.dto.al 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月7日 下午4:48:38 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: RegionModifyReqDTO
 * @Description: 地区资料维护请求DTO
 * @author: tian
 * @date: 2017年11月7日 下午4:48:38
 */
public class RegionModifyReqDTO {

    @ApiModelProperty(name = "regionId", value = "地区ID(删除多条时以逗号分隔)", required = true, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class,ValidateGroup.third.class })
    private String regionId;

    @ApiModelProperty(name = "parentRegionId", value = "上级地区ID", required = true, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class, ValidateGroup.second.class })
    private String parentRegionId;

    @ApiModelProperty(name = "regionCode", value = "地区编码", required = true, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class, ValidateGroup.second.class })
    private String regionCode;

    @ApiModelProperty(name = "regionName", value = "地区名称", required = true, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class, ValidateGroup.second.class })
    private String regionName;

    @ApiModelProperty(name = "isCross", value = "是否为跨境电商 0-否 1-是", required = true, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class, ValidateGroup.second.class })
    @Pattern(regexp = "0|1", message = "URM10007", groups = { ValidateGroup.first.class, ValidateGroup.second.class })
    private String isCross;

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getParentRegionId() {
        return parentRegionId;
    }

    public void setParentRegionId(String parentRegionId) {
        this.parentRegionId = parentRegionId;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getIsCross() {
        return isCross;
    }

    public void setIsCross(String isCross) {
        this.isCross = isCross;
    }

}
