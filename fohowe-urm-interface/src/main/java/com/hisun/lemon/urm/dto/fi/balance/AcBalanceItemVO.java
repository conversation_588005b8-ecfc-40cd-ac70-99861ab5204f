 package com.hisun.lemon.urm.dto.fi.balance;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceItemVO 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceItemVO", description="账户传输对象")
public class AcBalanceItemVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
    @ApiModelProperty(value="账户操作列表" )
    private List<AcBalanceItemQueryBean> acBalances;
    
    @ApiModelProperty(value="交易类型状态")
	Map orderTypeKV=null;
    
    @ApiModelProperty(value="账户类型")
	Map acTypeKV=null;
    
    private String maxCreatTime;
    
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcBalanceItemQueryBean> getAcBalances() {
		return acBalances;
	}

	public void setAcBalances(List<AcBalanceItemQueryBean> acBalances) {
		this.acBalances = acBalances;
	}

	public Map getOrderTypeKV() {
		return orderTypeKV;
	}

	public void setOrderTypeKV(Map orderTypeKV) {
		this.orderTypeKV = orderTypeKV;
	}

	public Map getAcTypeKV() {
		return acTypeKV;
	}

	public void setAcTypeKV(Map acTypeKV) {
		this.acTypeKV = acTypeKV;
	}

	public String getMaxCreatTime() {
		return maxCreatTime;
	}

	public void setMaxCreatTime(String maxCreatTime) {
		this.maxCreatTime = maxCreatTime;
	}
	
}
