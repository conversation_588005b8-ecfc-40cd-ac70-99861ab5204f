package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * 语言管理-语言维护查询
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午5:07:48
 *
 */
public class CharacterQueryReqDTO extends GenericDTO<NoBody> {
    @ApiModelProperty(name = "id", value = "字符键ID", required = false, dataType = "String")
    private long id;

    @ApiModelProperty(name = "characterKey", value = "字符键值", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.fourth.class, ValidateGroup.fifth.class })
    private String characterKey;

    @ApiModelProperty(name = "keyDesc", value = "字符键值描述", required = false, dataType = "String")
    private String keyDesc;

    @ApiModelProperty(name = "keyWords", value = "关键字", required = false, dataType = "String")
    private String keyWords;

    @ApiModelProperty(name = "langCode", value = "语言编码", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.third.class })
    private String langCode;
    
    @ApiModelProperty(name ="groupId" , value = "群组编号"  ,required = false, dataType = "String")
    private String groupId;

    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageSize;

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getCharacterKey() {
        return characterKey;
    }

    public void setCharacterKey(String characterKey) {
        this.characterKey = characterKey;
    }

    public String getKeyDesc() {
        return keyDesc;
    }

    public void setKeyDesc(String keyDesc) {
        this.keyDesc = keyDesc;
    }

    public String getKeyWords() {
        return keyWords;
    }

    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
    
}
