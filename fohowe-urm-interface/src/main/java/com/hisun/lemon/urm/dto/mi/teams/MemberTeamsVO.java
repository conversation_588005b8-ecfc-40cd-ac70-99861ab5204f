package com.hisun.lemon.urm.dto.mi.teams;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * MemberVO 传输对象
 * <AUTHOR>
 * @date 2017年11月1日
 * @time 上午9:27:30
 *
 */

@ClientValidated
@ApiModel(value="MemberTeamsVO", description="经销商查询传输对象")
public class MemberTeamsVO extends PageInfo<MemberTeamsBean>{
	private static final long serialVersionUID = -4986200789746817754L;
	
	private Integer totalTeams;//总队伍数
	private Integer passTeams;//总合格队伍数
	private Integer totalNum;//总人数
	private Integer initialNum;//总人数
	private Integer passNum;//总合格人数
	private Integer initialNum2;//总合格人数
	
	@ApiModelProperty(value="查询结果集")
	List<MemberTeamsBean> dataList; 
	 
	public List<MemberTeamsBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<MemberTeamsBean> dataList) {
		this.dataList = dataList;
	}

	public Integer getTotalTeams() {
		return totalTeams;
	}

	public void setTotalTeams(Integer totalTeams) {
		this.totalTeams = totalTeams;
	}

	public Integer getPassTeams() {
		return passTeams;
	}

	public void setPassTeams(Integer passTeams) {
		this.passTeams = passTeams;
	}

	public Integer getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum;
	}

	public Integer getPassNum() {
		return passNum;
	}

	public void setPassNum(Integer passNum) {
		this.passNum = passNum;
	}

	public Integer getInitialNum() {
		return initialNum;
	}

	public void setInitialNum(Integer initialNum) {
		this.initialNum = initialNum;
	}

	public Integer getInitialNum2() {
		return initialNum2;
	}

	public void setInitialNum2(Integer initialNum2) {
		this.initialNum2 = initialNum2;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}	
