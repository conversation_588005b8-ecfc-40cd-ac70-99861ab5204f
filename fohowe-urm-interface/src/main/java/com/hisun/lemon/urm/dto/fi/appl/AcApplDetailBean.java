 package com.hisun.lemon.urm.dto.fi.appl;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.validator.constraints.NotEmpty;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.dto.mi.group.ValidGroup1;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 账户申领明细传输对象
 * <AUTHOR>
 * @date 2017年11月10号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcApplDetailDTO", description="账户申领明细传输对象")
public class AcApplDetailBean {
	@ApiModelProperty(name="id",value="记录ID",required=false)
    private Long id;
	
	@ApiModelProperty(value="金额",required=false)
	private BigDecimal amount;
	
	@ApiModelProperty(value="提现单id",required=false)
	private Long applId;
	
	@ApiModelProperty(value="本地货币代码",required=false)
	private String localCurrency;
	
	@ApiModelProperty(value="本地货币金额",required=false)
	private BigDecimal localMoney;
	
	@ApiModelProperty(value="汇率",required=false)
	private BigDecimal rate;
	
	@ApiModelProperty(value="备注",required=false)
	private String memo;
	
	@ApiModelProperty(value="状态:2:核准   6删除 ",required=false)
	private String status;
	
	
	@ApiModelProperty(value=" 建档人",required=false)
	private String createrCode;
	
	@ApiModelProperty(value="建档人名称",required=false)
	private String createrName;
	
	@ApiModelProperty(value="取消人",required=false)
	private String cancelCode;
	
	@ApiModelProperty(value="取消人名称",required=false)
	private String cancelName;
	
	@ApiModelProperty(value="取消时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime cancelTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public Long getApplId() {
		return applId;
	}

	public void setApplId(Long applId) {
		this.applId = applId;
	}

	public String getLocalCurrency() {
		return localCurrency;
	}

	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}

	public BigDecimal getLocalMoney() {
		return localMoney;
	}

	public void setLocalMoney(BigDecimal localMoney) {
		this.localMoney = localMoney;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getCreaterCode() {
		return createrCode;
	}

	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}

	public String getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}

	public String getCancelName() {
		return cancelName;
	}

	public void setCancelName(String cancelName) {
		this.cancelName = cancelName;
	}

	public LocalDateTime getCancelTime() {
		return cancelTime;
	}

	public void setCancelTime(LocalDateTime cancelTime) {
		this.cancelTime = cancelTime;
	}
}
