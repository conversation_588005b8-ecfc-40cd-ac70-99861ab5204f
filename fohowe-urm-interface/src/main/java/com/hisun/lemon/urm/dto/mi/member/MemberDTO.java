package com.hisun.lemon.urm.dto.mi.member;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceChange;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * MemberDTO 传输对象
 * <AUTHOR>
 * @date 2017年10月30日
 * @time 上午9:27:30
 *
 */
@ClientValidated
@ApiModel(value="MemberDTO", description="经销商传输对象")
public class MemberDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息（用于分页查询）")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息（用于新增）")
	private MemberBean mainInfo = new MemberBean();
	
	//查询条件
	@ApiModelProperty(value="推荐人编号（用于匹配条件查询）")
	private String recommendNo;
	@ApiModelProperty(value="安置编号（用于匹配条件查询）")
	private String rightNo;
    @ApiModelProperty(value="经销商级别（用于匹配条件查询）")
    private String[] cardType;
    @ApiModelProperty(value="开始加入期数（用于匹配条件查询）")
    private String minPeriodWeek;
    @ApiModelProperty(value="结束加入期数（用于匹配条件查询）")
    private String maxPeriodWeek;
    @ApiModelProperty(value="冻结状态（用于匹配条件查询）")
    private String status;
    @ApiModelProperty(value="是否限制网络接线（用于匹配条件查询）")
    private String isFree;
    @ApiModelProperty(value="是否推广注册")
    private String promoteFlag;
    @ApiModelProperty(value="入网状态(0未入网 1已入网)",required=false,dataType="String")
    private String netStatus;
    
    @ApiModelProperty(value="是否东非董事(0否 1是)",required=false,dataType="String")
    private String director;
    
    @ApiModelProperty(value="开始注册日期(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using=DateJsonSerializer.class)
   	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minRegDate;
    
    @ApiModelProperty(value="结束注册日期(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using=DateJsonSerializer.class)
   	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxRegDate;
    
    @ApiModelProperty(value="开始加入日期(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using=DateJsonSerializer.class)
   	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minActDate;
    
    @ApiModelProperty(value="结束加入日期(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using=DateJsonSerializer.class)
   	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxActDate;
    
    @ApiModelProperty(value="添加选项 1 注册，2 购买经营权")
    private String addType;
    
    @ApiModelProperty(value="扣减资金集合")
	private List<AcBalanceChange> acBalanceChangeList;
    
    @ApiModelProperty(value = "注册渠道：波兰PLW 立陶宛LTW 跨境电商CNK 美国USA")
	private String[] channelTypes;
    
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public MemberBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(MemberBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	public String getRecommendNo() {
		return recommendNo;
	}
	public void setRecommendNo(String recommendNo) {
		this.recommendNo = recommendNo;
	}
	public String getRightNo() {
		return rightNo;
	}
	public void setRightNo(String rightNo) {
		this.rightNo = rightNo;
	}
	public String[] getCardType() {
		return cardType;
	}
	public void setCardType(String[] cardType) {
		this.cardType = cardType;
	}
	public String getMinPeriodWeek() {
		return minPeriodWeek;
	}
	public void setMinPeriodWeek(String minPeriodWeek) {
		this.minPeriodWeek = minPeriodWeek;
	}
	public String getMaxPeriodWeek() {
		return maxPeriodWeek;
	}
	public void setMaxPeriodWeek(String maxPeriodWeek) {
		this.maxPeriodWeek = maxPeriodWeek;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getIsFree() {
		return isFree;
	}
	public void setIsFree(String isFree) {
		this.isFree = isFree;
	}
	public LocalDateTime getMinRegDate() {
		return minRegDate;
	}
	public void setMinRegDate(LocalDateTime minRegDate) {
		this.minRegDate = minRegDate;
	}
	public LocalDateTime getMaxRegDate() {
		return maxRegDate;
	}
	public void setMaxRegDate(LocalDateTime maxRegDate) {
		this.maxRegDate = maxRegDate;
	}
	public LocalDateTime getMinActDate() {
		return minActDate;
	}
	public void setMinActDate(LocalDateTime minActDate) {
		this.minActDate = minActDate;
	}
	public LocalDateTime getMaxActDate() {
		return maxActDate;
	}
	public void setMaxActDate(LocalDateTime maxActDate) {
		this.maxActDate = maxActDate;
	}
	public List<AcBalanceChange> getAcBalanceChangeList() {
		return acBalanceChangeList;
	}
	public void setAcBalanceChangeList(List<AcBalanceChange> acBalanceChangeList) {
		this.acBalanceChangeList = acBalanceChangeList;
	}
	public String[] getChannelTypes() {
		return channelTypes;
	}
	public void setChannelTypes(String[] channelTypes) {
		this.channelTypes = channelTypes;
	}
	public String getNetStatus() {
		return netStatus;
	}
	public void setNetStatus(String netStatus) {
		this.netStatus = netStatus;
	}
	public String getDirector() {
		return director;
	}
	public void setDirector(String director) {
		this.director = director;
	}
	public String getPromoteFlag() {
		return promoteFlag;
	}
	public void setPromoteFlag(String promoteFlag) {
		this.promoteFlag = promoteFlag;
	}
	public String getAddType() {
		return addType;
	}
	public void setAddType(String addType) {
		this.addType = addType;
	}
}
