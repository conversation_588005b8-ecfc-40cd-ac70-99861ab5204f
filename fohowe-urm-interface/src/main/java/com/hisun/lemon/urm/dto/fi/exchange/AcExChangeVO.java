 package com.hisun.lemon.urm.dto.fi.exchange;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcTransVO 传输对象
 * <AUTHOR>
 * @date 2017年11月17号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcExChangeVO", description="转账对象")
public class AcExChangeVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
	@ApiModelProperty(value="查询结果集")
	List<AcExchangeBean> dataList;
	@ApiModelProperty(value="审核状态")
	Map statusKV=null;
	@ApiModelProperty(value="总计F$")
	BigDecimal totalF$;
	@ApiModelProperty(value="总计FV")
	BigDecimal totalFV;
	@ApiModelProperty(value="总计H0")
	BigDecimal totalH0;
	
	
	public BigDecimal getTotalF$() {
		return totalF$;
	}

	public void setTotalF$(BigDecimal totalF$) {
		this.totalF$ = totalF$;
	}

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcExchangeBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AcExchangeBean> dataList) {
		this.dataList = dataList;
	}

	public Map getStatusKV() {
		return statusKV;
	}

	public void setStatusKV(Map statusKV) {
		this.statusKV = statusKV;
	}

	public BigDecimal getTotalFV() {
		return totalFV;
	}

	public void setTotalFV(BigDecimal totalFV) {
		this.totalFV = totalFV;
	}

	public BigDecimal getTotalH0() {
		return totalH0;
	}

	public void setTotalH0(BigDecimal totalH0) {
		this.totalH0 = totalH0;
	}
	
}
