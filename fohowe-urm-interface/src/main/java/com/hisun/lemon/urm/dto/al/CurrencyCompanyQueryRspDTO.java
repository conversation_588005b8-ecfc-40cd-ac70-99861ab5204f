/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: CurrencyCompanyQueryRspDTO.java 
 * @Prject: fohowe-urm-interface
 * @Package: com.hisun.lemon.urm.dto.al 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月9日 上午11:14:50 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.dto.al;

/** 
 * @ClassName: CurrencyCompanyQueryRspDTO 
 * @Description: 查询公司和货币对应返回
 * @author: tian
 * @date: 2017年11月9日 上午11:14:50  
 */
public class CurrencyCompanyQueryRspDTO {

    /**
     * 货币编码
     */
    private String currencyCode;
    /**
     * 公司编码
     */
    private String companyCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 是否可用
     */
    private String isUsable;

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getIsUsable() {
        return isUsable;
    }

    public void setIsUsable(String isUsable) {
        this.isUsable = isUsable;
    }

}
