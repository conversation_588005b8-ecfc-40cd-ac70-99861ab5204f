/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: RegionModifyReqDTO.java 
 * @Prject: fohowe-urm-interface
 * @Package: com.hisun.lemon.urm.dto.al 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月7日 下午4:48:38 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: RegionModifyReqDTO
 * @Description: 地区资料维护查询请求DTO
 * @author: tian
 * @date: 2017年11月7日 下午4:48:38
 */
public class RegionQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(name = "parentRegionId", value = "上级地区ID", required = false, dataType = "String")
    private String parentRegionId;

    @ApiModelProperty(name = "regionName", value = "地区名称", required = false, dataType = "String")
    private String regionName;
    @ApiModelProperty(name = "regionCode", value = "地区编码", required = false, dataType = "String")
    private String regionCode;

    @ApiModelProperty(name = "isCross", value = "是否为跨境电商 0-否 1-是", required = false, dataType = "String")
    private String isCross;
    
    @ApiModelProperty(name = "bonusType", value = "奖金制度", required = false, dataType = "String")
    private String bonusType;

    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    @NotNull(message = "URM10005", groups = { ValidateGroup.first.class })
    @Min(value = 1, message = "URM10006", groups = { ValidateGroup.first.class })
    private int pageSize;
    
    private String bonus;
    
    

    public String getParentRegionId() {
        return parentRegionId;
    }

    public void setParentRegionId(String parentRegionId) {
        this.parentRegionId = parentRegionId;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getIsCross() {
        return isCross;
    }

    public void setIsCross(String isCross) {
        this.isCross = isCross;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getBonus() {
		if(this.bonusType==null || JudgeUtils.equalsAny(this.bonusType, "1","4","5")) {
			bonus="EUR";
		}else if(JudgeUtils.equalsAny(this.bonusType, "3")) {
			bonus="WAFR";
		}else if (JudgeUtils.equalsAny(this.bonusType, "2")) {
			bonus="EAFR";
		}else if (JudgeUtils.equalsAny(this.bonusType, "6")) {
			bonus="SEASIA";
		}
		return bonus;
	}

	public void setBonus(String bonus) {
		this.bonus = bonus;
	}

}
