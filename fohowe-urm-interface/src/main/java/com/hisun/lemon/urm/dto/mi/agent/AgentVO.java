package com.hisun.lemon.urm.dto.mi.agent;

import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月2日
 * @time 下午14:27:30
 *
 */

@ClientValidated
@ApiModel(value="AgentVO", description="代办处查询传输对象")
public class AgentVO{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="查询结果集")
	List<AgentQueryBean> dataList;
	
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AgentQueryBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AgentQueryBean> dataList) {
		this.dataList = dataList;
	}
}
