/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: RegionModifyReqDTO.java 
 * @Prject: fohowe-urm-interface
 * @Package: com.hisun.lemon.urm.dto.al 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月7日 下午4:48:38 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.dto.al;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.fohowe.common.ValidateGroup;

import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: RegionModifyReqDTO
 * @Description: 公司地区资料维护请求DTO
 * @author: tian
 * @date: 2017年11月7日 下午4:48:38
 */
public class RegionCompanyModifyReqDTO {

    @ApiModelProperty(name = "id", value = "记录ID(删除多条时以逗号分隔)", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.second.class })
    private String id;
    
    @ApiModelProperty(name = "regionId", value = "地区ID", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String regionId;

    @ApiModelProperty(name = "companyCode", value = "公司编码,以逗号分隔", required = false, dataType = "String")
    @NotBlank(message = "URM10005", groups = { ValidateGroup.first.class })
    private String companyCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

}
