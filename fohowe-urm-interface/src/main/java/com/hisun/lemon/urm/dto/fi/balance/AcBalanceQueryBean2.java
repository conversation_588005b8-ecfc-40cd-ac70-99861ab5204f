 package com.hisun.lemon.urm.dto.fi.balance;

import java.io.Serializable;
import java.math.BigDecimal;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceQueryBean2", description="账户传输对象")
public class AcBalanceQueryBean2 implements Serializable {
	private static final long serialVersionUID = 1L;
	/*=============base data============ */
    @ApiModelProperty(value="所属分公司",dataType="String")
    private String companyCode;
    @ApiModelProperty(value="所属分公司名称",dataType="String")
    private String companyName;
    /*=============show data============ */
    @ApiModelProperty(value="用户编号",dataType="String")
    private String userCode;
    @ApiModelProperty(value="f$",dataType="BigDecimal")
    private BigDecimal f$;
    @ApiModelProperty(value="fv",dataType="BigDecimal")
    private BigDecimal fv;
    @ApiModelProperty(value="f000",dataType="BigDecimal")
    private BigDecimal f000;
    @ApiModelProperty(value="h000",dataType="BigDecimal")
    private BigDecimal h000;
    @ApiModelProperty(value="fb",dataType="BigDecimal")
    private BigDecimal fb;
    @ApiModelProperty(value="pv",dataType="BigDecimal")
    private BigDecimal pv;
    @ApiModelProperty(value="p$",dataType="BigDecimal")
    private BigDecimal p$;
    @ApiModelProperty(value="b1",dataType="BigDecimal")
    private BigDecimal b1;
    @ApiModelProperty(value="b2",dataType="BigDecimal")
    private BigDecimal b2;
    @ApiModelProperty(value="b3",dataType="BigDecimal")
    private BigDecimal b3;
    @ApiModelProperty(value="b4",dataType="BigDecimal")
    private BigDecimal b4;
    @ApiModelProperty(value="b5",dataType="BigDecimal")
    private BigDecimal b5;
    @ApiModelProperty(value="hv",dataType="BigDecimal")
    private BigDecimal hv;
    @ApiModelProperty(value="fp",dataType="BigDecimal")
    private BigDecimal fp;
	@ApiModelProperty(value="fgc",dataType="BigDecimal")
	private BigDecimal fgc;
	@ApiModelProperty(value="vpfv",dataType="BigDecimal")
	private BigDecimal vpfv;
	@ApiModelProperty(value="ft",dataType="BigDecimal")
	private BigDecimal ft;
    @ApiModelProperty(value="借款余额",dataType="BigDecimal")
    private BigDecimal owtAmt;
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getF$() {
		return f$;
	}
	public void setF$(BigDecimal f$) {
		this.f$ = f$;
	}
	public BigDecimal getFv() {
		return fv;
	}
	public void setFv(BigDecimal fv) {
		this.fv = fv;
	}
	public BigDecimal getF000() {
		return f000;
	}
	public void setF000(BigDecimal f000) {
		this.f000 = f000;
	}
	public BigDecimal getH000() {
		return h000;
	}
	public void setH000(BigDecimal h000) {
		this.h000 = h000;
	}
	public BigDecimal getFb() {
		return fb;
	}
	public void setFb(BigDecimal fb) {
		this.fb = fb;
	}
	public BigDecimal getPv() {
		return pv;
	}
	public void setPv(BigDecimal pv) {
		this.pv = pv;
	}
	public BigDecimal getP$() {
		return p$;
	}
	public void setP$(BigDecimal p$) {
		this.p$ = p$;
	}
	public BigDecimal getB1() {
		return b1;
	}
	public void setB1(BigDecimal b1) {
		this.b1 = b1;
	}
	public BigDecimal getB2() {
		return b2;
	}
	public void setB2(BigDecimal b2) {
		this.b2 = b2;
	}
	public BigDecimal getB3() {
		return b3;
	}
	public void setB3(BigDecimal b3) {
		this.b3 = b3;
	}
	public BigDecimal getB4() {
		return b4;
	}
	public void setB4(BigDecimal b4) {
		this.b4 = b4;
	}
	public BigDecimal getB5() {
		return b5;
	}
	public void setB5(BigDecimal b5) {
		this.b5 = b5;
	}
	public BigDecimal getHv() {
		return hv;
	}
	public void setHv(BigDecimal hv) {
		this.hv = hv;
	}
	public BigDecimal getFp() {
		return fp;
	}
	public void setFp(BigDecimal fp) {
		this.fp = fp;
	}
	public BigDecimal getOwtAmt() {
		return owtAmt;
	}
	public void setOwtAmt(BigDecimal owtAmt) {
		this.owtAmt = owtAmt;
	}

	public BigDecimal getFgc() {
		return fgc;
	}

	public void setFgc(BigDecimal fgc) {
		this.fgc = fgc;
	}

	public BigDecimal getVpfv() {
		return vpfv;
	}

	public void setVpfv(BigDecimal vpfv) {
		this.vpfv = vpfv;
	}

	public BigDecimal getFt() {
		return ft;
	}

	public void setFt(BigDecimal ft) {
		this.ft = ft;
	}


	public AcBalanceQueryBean2() {
	}

	public AcBalanceQueryBean2(BigDecimal f$, BigDecimal fv, BigDecimal f000, BigDecimal h000, BigDecimal fb, BigDecimal fp, BigDecimal fgc, BigDecimal vpfv, BigDecimal ft) {
		this.f$ = f$;
		this.fv = fv;
		this.f000 = f000;
		this.h000 = h000;
		this.fb = fb;
		this.fp = fp;
		this.fgc = fgc;
		this.vpfv = vpfv;
		this.ft = ft;
	}
}
