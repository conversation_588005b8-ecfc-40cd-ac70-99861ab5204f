 package com.hisun.lemon.urm.dto.fi.input;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputBean 传输对象
 * <AUTHOR>
 * @date 2017年11月9号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputBean", description="账户传输对象")
public class AcInputDO{
	/**==========基本信息==============**/
	@ApiModelProperty(value="id(单条记录审核用此id)",required=false)
    private Long id;
	@ApiModelProperty(value="公司编码",required=false,dataType="String")
    private String companyCode;
	@ApiModelProperty(value="用户编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(value="当日交易序号",required=false,dataType="String")
	private String serial;
	@ApiModelProperty(value="单据类型,0=充值单，1=借款单，2=还款单",required=false,dataType="String")
    private String orderType;
	@ApiModelProperty(value="账户类型，F$＝F$，F0=F000",required=false)
    private String acType;
	@ApiModelProperty(value="方式,0=转账，1=现金，2=POS",required=false,dataType="String")
    private String tranType;
	@ApiModelProperty(value="本地货币代码",required=false,dataType="String")
    private String localCurrency;
	@ApiModelProperty(value="本地货币金额",required=false,dataType="BigDecimal")
    private BigDecimal localMoney;
	@ApiModelProperty(value="金额",required=false,dataType="BigDecimal")
    private BigDecimal money;
	@ApiModelProperty(value="汇率",required=false,dataType="BigDecimal")
    private BigDecimal rate;
	@ApiModelProperty(value="备注",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(value="状态:1:新单 2:分公司已审核 3:总公司已审核  8：已作废  6:已取消",required=false,dataType="String")
    private String status;
	
    /**==========审核信息（新建）==============**/
	@ApiModelProperty(value="建档人",required=false,dataType="String")
    private String createrCode;
	@ApiModelProperty(value="建档人名称",required=false,dataType="String")
    private String createrName;
	@ApiModelProperty(value="建档时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime createTime;
	@ApiModelProperty(value="审核人(分公司)",required=false,dataType="String")
	private String checkerCode;
	@ApiModelProperty(value="审核人名称",required=false,dataType="String")
	private String checkerName;
	@ApiModelProperty(value="审核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime checkeTime;
	@ApiModelProperty(value="核准方式 0:手工 1:自动",required=false,dataType="String")
	private String checkType;
	@ApiModelProperty(value="退回原因",required=false,dataType="String")
    private String checkMsg;
	@ApiModelProperty(value="摘要",required=false,dataType="String")
	private String remark;
	@ApiModelProperty(value="入账时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime recheckeTime;
	@ApiModelProperty(value="入账人名称",required=false,dataType="String")
	private String recheckerName;
	@ApiModelProperty(value="入账人",required=false,dataType="String")
	private String recheckerCode;
	@ApiModelProperty(value="已财务确认金额",required=false,dataType="BigDecimal")
    private BigDecimal recAmount;
	@ApiModelProperty(value="财务确认状态 01:未确认;02:部分确认;03:已确认",required=false,dataType="String")
    private String fiCheckStatus;
    /**==========审核信息（分公司审核）==============**/
	@ApiModelProperty(value="审核分公司编码",required=false,dataType="String")
    private String checkCompanyCode;
	@ApiModelProperty(value="充值申请期数",required=false,dataType="String")
	private String periodWeek;
	@ApiModelProperty(value="充值单号",required=false,dataType="String")
	private String inputNo;
	@ApiModelProperty(value="分公司修改备注",required=false,dataType="String")
    private String modifyMemo;
    /**==========审核信息（财务确认）==============**/
	@ApiModelProperty(value="财务确认时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime ficheckeTime;
	@ApiModelProperty(value="财务确认人",required=false,dataType="String")
    private String ficheckerCode;
	
	private Integer finStatus;
	
	private Integer finNum;
	
	private BigDecimal localFinMoney;
	
	//在线支付平台
	private String payPlat;
	//在线支付金额
	private BigDecimal payAmount;
	private String subjectNo;
	private String accountId;
	private BigDecimal handingFee;
	private Integer isHandFee;

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getSubjectNo() {
		return subjectNo;
	}

	public void setSubjectNo(String subjectNo) {
		this.subjectNo = subjectNo;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getSerial() {
		return serial;
	}
	public void setSerial(String serial) {
		this.serial = serial;
	}
	public String getLocalCurrency() {
		return localCurrency;
	}
	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}
	public BigDecimal getLocalMoney() {
		return localMoney;
	}
	public void setLocalMoney(BigDecimal localMoney) {
		this.localMoney = localMoney;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public BigDecimal getRate() {
		return rate;
	}
	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCreaterCode() {
		return createrCode;
	}
	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}
	public String getCreaterName() {
		return createrName;
	}
	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getCheckerCode() {
		return checkerCode;
	}
	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}
	public String getCheckerName() {
		return checkerName;
	}
	public void setCheckerName(String checkerName) {
		this.checkerName = checkerName;
	}
	public LocalDateTime getCheckeTime() {
		return checkeTime;
	}
	public void setCheckeTime(LocalDateTime checkeTime) {
		this.checkeTime = checkeTime;
	}
	public String getCheckType() {
		return checkType;
	}
	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}
	public String getCheckMsg() {
		return checkMsg;
	}
	public void setCheckMsg(String checkMsg) {
		this.checkMsg = checkMsg;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public LocalDateTime getRecheckeTime() {
		return recheckeTime;
	}
	public void setRecheckeTime(LocalDateTime recheckeTime) {
		this.recheckeTime = recheckeTime;
	}
	public String getRecheckerName() {
		return recheckerName;
	}
	public void setRecheckerName(String recheckerName) {
		this.recheckerName = recheckerName;
	}
	public String getRecheckerCode() {
		return recheckerCode;
	}
	public void setRecheckerCode(String recheckerCode) {
		this.recheckerCode = recheckerCode;
	}
	public BigDecimal getRecAmount() {
		return recAmount;
	}
	public void setRecAmount(BigDecimal recAmount) {
		this.recAmount = recAmount;
	}
	public String getFiCheckStatus() {
		return fiCheckStatus;
	}
	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}
	public String getCheckCompanyCode() {
		return checkCompanyCode;
	}
	public void setCheckCompanyCode(String checkCompanyCode) {
		this.checkCompanyCode = checkCompanyCode;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getInputNo() {
		return inputNo;
	}
	public void setInputNo(String inputNo) {
		this.inputNo = inputNo;
	}
	public String getModifyMemo() {
		return modifyMemo;
	}
	public void setModifyMemo(String modifyMemo) {
		this.modifyMemo = modifyMemo;
	}
	public LocalDateTime getFicheckeTime() {
		return ficheckeTime;
	}
	public void setFicheckeTime(LocalDateTime ficheckeTime) {
		this.ficheckeTime = ficheckeTime;
	}
	public String getFicheckerCode() {
		return ficheckerCode;
	}
	public void setFicheckerCode(String ficheckerCode) {
		this.ficheckerCode = ficheckerCode;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getTranType() {
		return tranType;
	}
	public void setTranType(String tranType) {
		this.tranType = tranType;
	}
	public Integer getFinStatus() {
		return finStatus;
	}
	public void setFinStatus(Integer finStatus) {
		this.finStatus = finStatus;
	}
	public Integer getFinNum() {
		return finNum;
	}
	public void setFinNum(Integer finNum) {
		this.finNum = finNum;
	}
	public BigDecimal getLocalFinMoney() {
		return localFinMoney;
	}
	public void setLocalFinMoney(BigDecimal localFinMoney) {
		this.localFinMoney = localFinMoney;
	}
	public String getPayPlat() {
		return payPlat;
	}
	public void setPayPlat(String payPlat) {
		this.payPlat = payPlat;
	}
	public BigDecimal getPayAmount() {
		return payAmount;
	}
	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public BigDecimal getHandingFee() {
		return handingFee;
	}

	public void setHandingFee(BigDecimal handingFee) {
		this.handingFee = handingFee;
	}

	public Integer getIsHandFee() {
		return isHandFee;
	}

	public void setIsHandFee(Integer isHandFee) {
		this.isHandFee = isHandFee;
	}
}
