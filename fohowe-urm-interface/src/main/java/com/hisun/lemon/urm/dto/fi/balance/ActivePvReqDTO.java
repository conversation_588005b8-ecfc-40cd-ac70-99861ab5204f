 package com.hisun.lemon.urm.dto.fi.balance;

import java.time.LocalDateTime;

import org.hibernate.validator.constraints.NotBlank;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2018年01月10号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="ActivePvReqDTO", description="账户传输对象")
public class ActivePvReqDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	
	@ApiModelProperty(value="用户编号(经销商编号)",required=false,dataType="String")
    private String userCode;
	
	@ApiModelProperty(value="开始创建日期")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minDealDate;
	
	@ApiModelProperty(value="结束创建日期")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxDealDate;

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}

	public LocalDateTime getMinDealDate() {
		return minDealDate;
	}

	public void setMinDealDate(LocalDateTime minDealDate) {
		this.minDealDate = minDealDate;
	}

	public LocalDateTime getMaxDealDate() {
		return maxDealDate;
	}

	public void setMaxDealDate(LocalDateTime maxDealDate) {
		this.maxDealDate = maxDealDate;
	}
}
