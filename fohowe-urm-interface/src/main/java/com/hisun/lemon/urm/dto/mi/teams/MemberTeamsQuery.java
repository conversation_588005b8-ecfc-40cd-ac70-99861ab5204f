package com.hisun.lemon.urm.dto.mi.teams;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * MemberTeamsQuery 传输对象  
 */

@ClientValidated
@ApiModel(value="MemberTeamsQuery", description="经销商查询传输对象")
public class MemberTeamsQuery {
	
	//当前页
    private int pageNum;
    //每页的数量
    private int pageSize;
	
	/*=============base data============ */
	@ApiModelProperty(value="奖金制度",dataType="String")
    private String bonusType;
	
    @ApiModelProperty(value="区域",dataType="String")
    private String areaCode;
    
    @ApiModelProperty(value="所属分公司",dataType="String")
    private String companyCode;
    
    @ApiModelProperty(value="所属分公司数组")
    private String[] companyCodes;
    
    @ApiModelProperty(value="代办处",dataType="String")
    private String agentNo;
    
    private String[] idArr;
    
    /*=============show data============ */
    @ApiModelProperty(value="记录编号")
    private Integer id;
    
    private String teamNo;
    
    private String teamName;

    private String memberNo;

    private Integer teamStatus;

    private Integer teamNum;
    
    private Integer teamNums;
    private Integer teamNume;
    
    private Integer initialNums;
    
    private Integer initialNume;

    private Integer passeFlag;
    
    private Integer teamType;

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getTeamNo() {
		return teamNo;
	}

	public void setTeamNo(String teamNo) {
		this.teamNo = teamNo;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public Integer getTeamStatus() {
		return teamStatus;
	}

	public void setTeamStatus(Integer teamStatus) {
		this.teamStatus = teamStatus;
	}

	public Integer getTeamNum() {
		return teamNum;
	}

	public void setTeamNum(Integer teamNum) {
		this.teamNum = teamNum;
	}

	public Integer getPasseFlag() {
		return passeFlag;
	}

	public void setPasseFlag(Integer passeFlag) {
		this.passeFlag = passeFlag;
	}

	public String[] getIdArr() {
		return idArr;
	}

	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}

	public String[] getCompanyCodes() {
		return companyCodes;
	}

	public void setCompanyCodes(String[] companyCodes) {
		this.companyCodes = companyCodes;
	}

	public Integer getTeamNums() {
		return teamNums;
	}

	public void setTeamNums(Integer teamNums) {
		this.teamNums = teamNums;
	}

	public Integer getTeamNume() {
		return teamNume;
	}

	public void setTeamNume(Integer teamNume) {
		this.teamNume = teamNume;
	}

	public String getTeamName() {
		return teamName;
	}

	public void setTeamName(String teamName) {
		this.teamName = teamName;
	}

	public Integer getInitialNums() {
		return initialNums;
	}

	public void setInitialNums(Integer initialNums) {
		this.initialNums = initialNums;
	}

	public Integer getInitialNume() {
		return initialNume;
	}

	public void setInitialNume(Integer initialNume) {
		this.initialNume = initialNume;
	}

	public Integer getTeamType() {
		return teamType;
	}

	public void setTeamType(Integer teamType) {
		this.teamType = teamType;
	}
}
