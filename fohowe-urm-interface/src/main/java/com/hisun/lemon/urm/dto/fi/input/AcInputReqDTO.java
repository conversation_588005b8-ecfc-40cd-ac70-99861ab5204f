 package com.hisun.lemon.urm.dto.fi.input;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputReqDTO 传输对象
 * <AUTHOR>
 * @date 2017年11月9号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputReqDTO", description="账户传输对象")
public class AcInputReqDTO extends GenericDTO<NoBody>{
	/**==========基本信息==============**/
	@ApiModelProperty(value="充值单号",required=false,dataType="String")
	@NotBlank(message = "URM10005")
    private String inputNo;
	
	@ApiModelProperty(value="申请用户编号",required=false,dataType="String")
	@NotBlank(message = "URM10005")
    private String userCode;
	
	@ApiModelProperty(value="单据类型,0=充值单",required=false,dataType="String")
	@NotBlank(message = "URM10005")
	private String orderType;
	
	@ApiModelProperty(value="账户类型，F$＝F$，F0=F000",required=false)
	@NotBlank(message = "URM10005")
    private String acType;
	
	@ApiModelProperty(value="方式,0=转账，1=现金，2=POS",required=false,dataType="String")
    private String tranType;
	
	@ApiModelProperty(value="本地货币代码",required=false,dataType="String")
	@NotBlank(message = "URM10005")
    private String localCurrency;
	
	@ApiModelProperty(value="金额",required=false,dataType="BigDecimal")
	@NotNull(message = "URM10005")
    private BigDecimal money;
	
	@ApiModelProperty(value="摘要(新增填remark)",required=false,dataType="String")
    private String remark;
	
	@ApiModelProperty(value="备注",required=false,dataType="String")
    private String memo;
	
    /**==========审核信息（新建）==============**/
	@ApiModelProperty(value="建档人",required=false,dataType="String")
    private String createrCode;
	
	@ApiModelProperty(value="建档人名称",required=false,dataType="String")
    private String createrName;
	
	@ApiModelProperty(value="状态:1:新单 2:分公司已审核 3:总公司已审核  8：已作废  6:已取消",required=false,dataType="String")
	private String status;
	
	@ApiModelProperty(value="申购类型0代办处 1在线支付 2促销单",required=false,dataType="String")
    private String inputType;
	@ApiModelProperty(value="分公司",required=false,dataType="String")
	private String companyCode;
	@ApiModelProperty(value="代办处",required=false,dataType="String")
	private String agentNo;
	
	//在线支付平台
	private String payPlat;
	//在线支付金额
	private BigDecimal payAmount;
	
	public String getInputNo() {
		return inputNo;
	}
	public void setInputNo(String inputNo) {
		this.inputNo = inputNo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getTranType() {
		return tranType;
	}
	public void setTranType(String tranType) {
		this.tranType = tranType;
	}
	public String getLocalCurrency() {
		return localCurrency;
	}
	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getCreaterCode() {
		return createrCode;
	}
	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}
	public String getCreaterName() {
		return createrName;
	}
	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getInputType() {
		return inputType;
	}
	public void setInputType(String inputType) {
		this.inputType = inputType;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getPayPlat() {
		return payPlat;
	}
	public void setPayPlat(String payPlat) {
		this.payPlat = payPlat;
	}
	public BigDecimal getPayAmount() {
		return payAmount;
	}
	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}
}
