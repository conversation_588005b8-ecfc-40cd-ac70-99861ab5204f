package com.hisun.lemon.urm.dto.al;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;

import io.swagger.annotations.ApiModelProperty;

/**
 * 语言管理-语言编码管理
 * 
 * <AUTHOR>
 * @date 2017年11月6日
 * @time 下午2:39:18
 *
 */
public class LanguageCodeQueryReqDTO extends GenericDTO<NoBody> {

    @ApiModelProperty(name = "pageNum", value = "页码", required = true, dataType = "Integer")
    @NotNull(message = "URM10005")
    @Min(value = 1, message = "URM10006")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数量", required = true, dataType = "Integer")
    @NotNull(message = "URM10005")
    @Min(value = 1, message = "URM10006")
    private int pageSize;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

}
