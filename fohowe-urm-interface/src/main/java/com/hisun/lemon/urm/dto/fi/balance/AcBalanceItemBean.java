 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.enums.OrderTypeEnums;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceItemBean", description="账户传输对象")
public class AcBalanceItemBean{
	@ApiModelProperty(value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(value="分公司",required=false,dataType="String")
    private String companyCode;
	@ApiModelProperty(value="账户类型",required=false)
    private String acType;
	@ApiModelProperty(value="经销商/代办处编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(value="年",required=false,dataType="String")
    private String wYear;
	@ApiModelProperty(value="月",required=false,dataType="String")
    private String wMonth;
	@ApiModelProperty(value="周",required=false,dataType="String")
    private String wWeek;
	@ApiModelProperty(value="交易类别",required=false)
    private String orderType;
	@ApiModelProperty(value="交易日期",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime dealDate;
	@ApiModelProperty(value="余额变化",required=false,dataType="BigDecimal")
    private BigDecimal money;
	@ApiModelProperty(value="可用金额变化",required=false,dataType="BigDecimal")
    private BigDecimal validMoney;
	@ApiModelProperty(value="借款金额变化",required=false,dataType="BigDecimal")
    private BigDecimal oweMoney;
	@ApiModelProperty(value="摘要",required=false,dataType="String")
    private String remark;
	@ApiModelProperty(value="备注",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(value="余额",required=false,dataType="BigDecimal")
    private BigDecimal balance;
	@ApiModelProperty(value="可用金额余额",required=false,dataType="BigDecimal")
    private BigDecimal validBalance;
	@ApiModelProperty(value="借款金额余额",required=false,dataType="BigDecimal")
    private BigDecimal oweBalance;
	@ApiModelProperty(value="建立者帐号",required=false,dataType="String")
    private String createrCode;
	@ApiModelProperty(value="建立者名称",required=false,dataType="String")
    private String createrName;
	@ApiModelProperty(value="关联单号",required=false,dataType="String")
    private String orderNo;
	@ApiModelProperty(value="转出银行帐号/发票号",required=false,dataType="String")
    private String outCode;
	@ApiModelProperty(value="银行户名",required=false,dataType="String")
    private String bName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getwYear() {
        return wYear;
    }

    public void setwYear(String wYear) {
        this.wYear = wYear;
    }

    public String getwMonth() {
        return wMonth;
    }

    public void setwMonth(String wMonth) {
        this.wMonth = wMonth;
    }

    public String getwWeek() {
        return wWeek;
    }

    public void setwWeek(String wWeek) {
        this.wWeek = wWeek;
    }

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public LocalDateTime getDealDate() {
        return dealDate;
    }

    public void setDealDate(LocalDateTime dealDate) {
        this.dealDate = dealDate;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public BigDecimal getValidMoney() {
        return validMoney;
    }

    public void setValidMoney(BigDecimal validMoney) {
        this.validMoney = validMoney;
    }

    public BigDecimal getOweMoney() {
        return oweMoney;
    }

    public void setOweMoney(BigDecimal oweMoney) {
        this.oweMoney = oweMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getValidBalance() {
        return validBalance;
    }

    public void setValidBalance(BigDecimal validBalance) {
        this.validBalance = validBalance;
    }

    public BigDecimal getOweBalance() {
        return oweBalance;
    }

    public void setOweBalance(BigDecimal oweBalance) {
        this.oweBalance = oweBalance;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOutCode() {
        return outCode;
    }

    public void setOutCode(String outCode) {
        this.outCode = outCode;
    }

    public String getbName() {
        return bName;
    }

    public void setbName(String bName) {
        this.bName = bName;
    }
}
