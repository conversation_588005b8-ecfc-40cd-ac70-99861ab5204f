 package com.hisun.lemon.urm.dto.fi.trans;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcTransVO 传输对象
 * <AUTHOR>
 * @date 2017年11月17号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcTransVO", description="转账对象")
public class AcTransVO {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
	@ApiModelProperty(value="查询结果集")
	List<AcTransBean> dataList;
	
	@ApiModelProperty(value="账户类型")
	Map<String,String> acTypeKV=null;
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AcTransBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AcTransBean> dataList) {
		this.dataList = dataList;
	}

	public Map<String,String> getAcTypeKV() {
		return acTypeKV;
	}

	public void setAcTypeKV(Map<String,String> acTypeKV) {
		this.acTypeKV = acTypeKV;
	}
}
