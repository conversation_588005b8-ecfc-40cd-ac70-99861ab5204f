package com.hisun.lemon.urm.dto.fi.fgc;

import java.math.BigDecimal;
import java.util.List;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.PageInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ClientValidated
@ApiModel(value="AcUsdtVO", description="账户申购对象")
public class AcUsdtVO extends PageInfo {
	@ApiModelProperty(value="总USDT")
	private BigDecimal totalNumber;
	@ApiModelProperty(value="已充值USDT")
	private BigDecimal totalNumberIn;
	@ApiModelProperty(value="未充值USDT")
	private BigDecimal totalNumberNO;
	@ApiModelProperty(value="已充值F$")
	private BigDecimal totalNoneyIN;
	@ApiModelProperty(value="未充值F$")
	private BigDecimal totalMoneyNO;
	
	@ApiModelProperty(value="查询结果集")
	List<AcUsdtQueryBean> dataList;

	public List<AcUsdtQueryBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AcUsdtQueryBean> dataList) {
		this.dataList = dataList;
	}

	public BigDecimal getTotalNumber() {
		return totalNumber;
	}

	public void setTotalNumber(BigDecimal totalNumber) {
		this.totalNumber = totalNumber;
	}

	public BigDecimal getTotalNumberIn() {
		return totalNumberIn;
	}

	public void setTotalNumberIn(BigDecimal totalNumberIn) {
		this.totalNumberIn = totalNumberIn;
	}

	public BigDecimal getTotalNumberNO() {
		return totalNumberNO;
	}

	public void setTotalNumberNO(BigDecimal totalNumberNO) {
		this.totalNumberNO = totalNumberNO;
	}

	public BigDecimal getTotalNoneyIN() {
		return totalNoneyIN;
	}

	public void setTotalNoneyIN(BigDecimal totalNoneyIN) {
		this.totalNoneyIN = totalNoneyIN;
	}

	public BigDecimal getTotalMoneyNO() {
		return totalMoneyNO;
	}

	public void setTotalMoneyNO(BigDecimal totalMoneyNO) {
		this.totalMoneyNO = totalMoneyNO;
	}
	
}
