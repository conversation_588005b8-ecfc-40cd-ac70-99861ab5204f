package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月20日
 * @time 下午14:27:30
 *
 */

@ClientValidated
@ApiModel(value="AgentMemUpdateVO", description="代办处查询传输对象")
public class AgentMemUpdateVO{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="查询结果集")
	List<AgentMemUpdateBean> dataList;
	
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<AgentMemUpdateBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<AgentMemUpdateBean> dataList) {
		this.dataList = dataList;
	}

}
