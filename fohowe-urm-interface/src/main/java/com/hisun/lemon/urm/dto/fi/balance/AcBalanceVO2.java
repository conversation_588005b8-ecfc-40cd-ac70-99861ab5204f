 package com.hisun.lemon.urm.dto.fi.balance;

import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceVO", description="账户传输对象")
public class AcBalanceVO2 {
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo;
    @ApiModelProperty(value="账户列表" )
    private List<AcBalanceQueryBean2> acBalances;
    
	@ApiModelProperty(value="账户类型")
	Map acTypeKV=null;
	
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}


	public Map getAcTypeKV() {
		return acTypeKV;
	}

	public void setAcTypeKV(Map acTypeKV) {
		this.acTypeKV = acTypeKV;
	}

	public List<AcBalanceQueryBean2> getAcBalances() {
		return acBalances;
	}

	public void setAcBalances(List<AcBalanceQueryBean2> acBalances) {
		this.acBalances = acBalances;
	}

}
