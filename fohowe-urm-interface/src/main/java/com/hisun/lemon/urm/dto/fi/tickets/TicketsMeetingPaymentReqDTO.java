package com.hisun.lemon.urm.dto.fi.tickets;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

public class TicketsMeetingPaymentReqDTO {
    @ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private int pageNum=1;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    private int pageSize=20;
    private Long id;
    @ApiModelProperty(value = "实际主讲师")
    private String actualLecturer;
    @ApiModelProperty("实际翻译")
    private String actualTranslator;

    private String meetingNo;
    private String meetingTitle;
    private String companyCode;
    private String city;
    private String actualManager;
    private BigDecimal actualAmount;
    private String actualTeacher;
    private Integer actualNum;
    private String actualCurrency;

    private Integer planYearMonth;
    private Integer actalYearMonth;

    private List<String> teachers;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActualCurrency() {
        return actualCurrency;
    }

    public void setActualCurrency(String actualCurrency) {
        this.actualCurrency = actualCurrency;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    public String getActualLecturer() {
		return actualLecturer;
	}

	public void setActualLecturer(String actualLecturer) {
		this.actualLecturer = actualLecturer;
	}

	public String getActualTranslator() {
		return actualTranslator;
	}

	public void setActualTranslator(String actualTranslator) {
		this.actualTranslator = actualTranslator;
	}

    public String getActualManager() {
        return actualManager;
    }

    public void setActualManager(String actualManager) {
        this.actualManager = actualManager;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getActualTeacher() {
        return actualTeacher;
    }

    public void setActualTeacher(String actualTeacher) {
        this.actualTeacher = actualTeacher;
    }

    public Integer getActualNum() {
        return actualNum;
    }

    public void setActualNum(Integer actualNum) {
        this.actualNum = actualNum;
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo;
    }

    public String getMeetingTitle() {
        return meetingTitle;
    }

    public void setMeetingTitle(String meetingTitle) {
        this.meetingTitle = meetingTitle;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

	public Integer getPlanYearMonth() {
		return planYearMonth;
	}

	public void setPlanYearMonth(Integer planYearMonth) {
		this.planYearMonth = planYearMonth;
	}

	public Integer getActalYearMonth() {
		return actalYearMonth;
	}

	public void setActalYearMonth(Integer actalYearMonth) {
		this.actalYearMonth = actalYearMonth;
	}

	public List<String> getTeachers() {
		return teachers;
	}

	public void setTeachers(List<String> teachers) {
		this.teachers = teachers;
	}
    
}
