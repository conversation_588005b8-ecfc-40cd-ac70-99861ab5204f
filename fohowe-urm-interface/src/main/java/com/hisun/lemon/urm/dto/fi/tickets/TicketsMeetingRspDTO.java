package com.hisun.lemon.urm.dto.fi.tickets;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TicketsMeetingRspDTO {
	
	
    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "会议编号")
    private String meetingNo;
    @ApiModelProperty(value = "会议名称")
    private String meetingTitle;
    @ApiModelProperty(value = "区域编号")
    private String areaCode;
    @ApiModelProperty(value = "参加的分公司")
    private String companyCode;
    @ApiModelProperty(value = "城市/代办处")
    private String city;
    @ApiModelProperty(value = "计划会议类型")
    private String planMeetingType;
    @ApiModelProperty(value = "预计开始时间")
    private LocalDateTime meetingDateStart;
    @ApiModelProperty(value = "预计结束时间")
    private LocalDateTime meetingDateEnd;
    @ApiModelProperty(value = "计划人数")
    private Integer planNum;
    @ApiModelProperty(value = "预计参与经理")
    private String planManager;
    @ApiModelProperty(value = "收费标准F$")
    private BigDecimal planAmount;
    @ApiModelProperty(value = "预计收费币种")
    private String planCurrency;
    @ApiModelProperty(value = "预计主讲老师")
    private String planLecturer;
    @ApiModelProperty(value = "预计参会老师")
    private String planTeacher;
    @ApiModelProperty(value = "预计翻译")
    private String planTranslator;
    @ApiModelProperty(value = "实际会议类型")
    private String actalMeetingType;
    @ApiModelProperty(value = "实际举办时间")
    private LocalDateTime heldDateStart;
    @ApiModelProperty(value = "实际结束时间")
    private LocalDateTime heldDateEnd;
    @ApiModelProperty(value = "实际人数")
    private Integer actualNum;
    @ApiModelProperty(value = "实际参与经理")
    private String actualManager;
    @ApiModelProperty(value = "实际收费标准F$")
    private BigDecimal actualAmount;
    @ApiModelProperty(value = "实际收费币种")
    private String actualCurrency;
    @ApiModelProperty(value = "实际主讲老师")
    private String actualLecturer;
    @ApiModelProperty(value = "实际参会老师")
    private String actualTeacher;
    @ApiModelProperty(value = "实际翻译")
    private String actualTranslator;
    @ApiModelProperty(value = "现场成交数量")
    private Integer dealQty;
    @ApiModelProperty(value = "定金")
    private BigDecimal deposit;
    @ApiModelProperty(value = "状态(0：待确定，1：已确定)")
    private Integer vcStatus;
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "会议总结")
    private String remark;
    @ApiModelProperty(value = "创建人账号")
    private String createrCode;
    @ApiModelProperty(value = "创建人名称")
    private String createrName;
    @ApiModelProperty(value = "建立时间")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "nothing")
    private LocalDateTime modifyTime;
    @ApiModelProperty(value = "录单员确认标记")
    private Integer checkeFlag;
    @ApiModelProperty(value = "录单员确认人")
    private String checkeCode;
    @ApiModelProperty(value = "录单员确认时间")
    private LocalDateTime checkeTime;
    @ApiModelProperty(value = "讲师确认人")
    private String reCheckeCode;
    @ApiModelProperty(value = "讲师确认时间")
    private LocalDateTime reCheckeTime;
    @ApiModelProperty(value = "录单员再次确认标记")
    private Integer reCheckeFlag;
    
    private String planFirstType;
	
	private String planTransPart;
	
	private Integer planDealQty;

	private String actalFirstType;
	
	private String actualTransPart;
    
    private String planGuests;

    private String actualGuests;

    private String reviewFirstType;

    private String reviewMeetingType;

    private LocalDateTime reviewDateStart;

    private LocalDateTime reviewDateEnd;

    private Integer reviewNum;

    private String reviewManager;

    private String reviewGuests;

    private BigDecimal reviewAmount;

    private String reviewCurrency;

    private String reviewLecturer;

    private String reviewTeacher;

    private String reviewTranslator;

    private String reviewTransPart;

    private Integer reviewDealQty;

    private BigDecimal reviewDeposit;

    private String reviewRemark;
    
    private String planLecturerName;
	private String planTranslatorName;
	private String actualLecturerName;
	private String actualTranslatorName;
	
	
    public Integer getPlanNum() {
        return planNum;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo;
    }

    public String getMeetingTitle() {
        return meetingTitle;
    }

    public void setMeetingTitle(String meetingTitle) {
        this.meetingTitle = meetingTitle;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPlanMeetingType() {
        return planMeetingType;
    }

    public void setPlanMeetingType(String planMeetingType) {
        this.planMeetingType = planMeetingType;
    }

    public String getActalMeetingType() {
        return actalMeetingType;
    }

    public void setActalMeetingType(String actalMeetingType) {
        this.actalMeetingType = actalMeetingType;
    }

    public LocalDateTime getHeldDateStart() {
        return heldDateStart;
    }

    public void setHeldDateStart(LocalDateTime heldDateStart) {
        this.heldDateStart = heldDateStart;
    }

    public LocalDateTime getHeldDateEnd() {
        return heldDateEnd;
    }

    public void setHeldDateEnd(LocalDateTime heldDateEnd) {
        this.heldDateEnd = heldDateEnd;
    }

    public Integer getActualNum() {
        return actualNum;
    }

    public void setActualNum(Integer actualNum) {
        this.actualNum = actualNum;
    }

    public String getActualManager() {
        return actualManager;
    }

    public void setActualManager(String actualManager) {
        this.actualManager = actualManager;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getActualCurrency() {
        return actualCurrency;
    }

    public void setActualCurrency(String actualCurrency) {
        this.actualCurrency = actualCurrency;
    }

    public String getActualLecturer() {
        return actualLecturer;
    }

    public void setActualLecturer(String actualLecturer) {
        this.actualLecturer = actualLecturer;
    }

    public String getActualTeacher() {
        return actualTeacher;
    }

    public void setActualTeacher(String actualTeacher) {
        this.actualTeacher = actualTeacher;
    }

    public String getActualTranslator() {
        return actualTranslator;
    }

    public void setActualTranslator(String actualTranslator) {
        this.actualTranslator = actualTranslator;
    }

    public Integer getDealQty() {
        return dealQty;
    }

    public void setDealQty(Integer dealQty) {
        this.dealQty = dealQty;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getCheckeFlag() {
        return checkeFlag;
    }

    public void setCheckeFlag(Integer checkeFlag) {
        this.checkeFlag = checkeFlag;
    }

    public String getCheckeCode() {
        return checkeCode;
    }

    public void setCheckeCode(String checkeCode) {
        this.checkeCode = checkeCode;
    }

    public LocalDateTime getCheckeTime() {
        return checkeTime;
    }

    public void setCheckeTime(LocalDateTime checkeTime) {
        this.checkeTime = checkeTime;
    }

    public String getReCheckeCode() {
        return reCheckeCode;
    }

    public void setReCheckeCode(String reCheckeCode) {
        this.reCheckeCode = reCheckeCode;
    }

    public LocalDateTime getReCheckeTime() {
        return reCheckeTime;
    }

    public void setReCheckeTime(LocalDateTime reCheckeTime) {
        this.reCheckeTime = reCheckeTime;
    }

    public Integer getReCheckeFlag() {
        return reCheckeFlag;
    }

    public void setReCheckeFlag(Integer reCheckeFlag) {
        this.reCheckeFlag = reCheckeFlag;
    }

    public void setPlanNum(Integer planNum) {
        this.planNum = planNum;
    }

    public String getPlanManager() {
        return planManager;
    }

    public void setPlanManager(String planManager) {
        this.planManager = planManager;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public String getPlanCurrency() {
        return planCurrency;
    }

    public void setPlanCurrency(String planCurrency) {
        this.planCurrency = planCurrency;
    }

    public String getPlanLecturer() {
        return planLecturer;
    }

    public void setPlanLecturer(String planLecturer) {
        this.planLecturer = planLecturer;
    }

    public String getPlanTeacher() {
        return planTeacher;
    }

    public void setPlanTeacher(String planTeacher) {
        this.planTeacher = planTeacher;
    }

    public String getPlanTranslator() {
        return planTranslator;
    }

    public void setPlanTranslator(String planTranslator) {
        this.planTranslator = planTranslator;
    }

    public LocalDateTime getMeetingDateStart() {
        return meetingDateStart;
    }

    public void setMeetingDateStart(LocalDateTime meetingDateStart) {
        this.meetingDateStart = meetingDateStart;
    }

    public LocalDateTime getMeetingDateEnd() {
        return meetingDateEnd;
    }

    public void setMeetingDateEnd(LocalDateTime meetingDateEnd) {
        this.meetingDateEnd = meetingDateEnd;
    }

	public String getPlanLecturerName() {
		return planLecturerName;
	}

	public void setPlanLecturerName(String planLecturerName) {
		this.planLecturerName = planLecturerName;
	}

	public String getPlanTranslatorName() {
		return planTranslatorName;
	}

	public void setPlanTranslatorName(String planTranslatorName) {
		this.planTranslatorName = planTranslatorName;
	}

	public String getActualLecturerName() {
		return actualLecturerName;
	}

	public void setActualLecturerName(String actualLecturerName) {
		this.actualLecturerName = actualLecturerName;
	}

	public String getActualTranslatorName() {
		return actualTranslatorName;
	}

	public void setActualTranslatorName(String actualTranslatorName) {
		this.actualTranslatorName = actualTranslatorName;
	}

    public String getPlanGuests() {
        return planGuests;
    }

    public void setPlanGuests(String planGuests) {
        this.planGuests = planGuests;
    }

    public String getActualGuests() {
        return actualGuests;
    }

    public void setActualGuests(String actualGuests) {
        this.actualGuests = actualGuests;
    }

    public String getReviewFirstType() {
        return reviewFirstType;
    }

    public void setReviewFirstType(String reviewFirstType) {
        this.reviewFirstType = reviewFirstType;
    }

    public String getReviewMeetingType() {
        return reviewMeetingType;
    }

    public void setReviewMeetingType(String reviewMeetingType) {
        this.reviewMeetingType = reviewMeetingType;
    }

    public LocalDateTime getReviewDateStart() {
        return reviewDateStart;
    }

    public void setReviewDateStart(LocalDateTime reviewDateStart) {
        this.reviewDateStart = reviewDateStart;
    }

    public LocalDateTime getReviewDateEnd() {
        return reviewDateEnd;
    }

    public void setReviewDateEnd(LocalDateTime reviewDateEnd) {
        this.reviewDateEnd = reviewDateEnd;
    }

    public Integer getReviewNum() {
        return reviewNum;
    }

    public void setReviewNum(Integer reviewNum) {
        this.reviewNum = reviewNum;
    }

    public String getReviewManager() {
        return reviewManager;
    }

    public void setReviewManager(String reviewManager) {
        this.reviewManager = reviewManager;
    }

    public String getReviewGuests() {
        return reviewGuests;
    }

    public void setReviewGuests(String reviewGuests) {
        this.reviewGuests = reviewGuests;
    }

    public BigDecimal getReviewAmount() {
        return reviewAmount;
    }

    public void setReviewAmount(BigDecimal reviewAmount) {
        this.reviewAmount = reviewAmount;
    }

    public String getReviewCurrency() {
        return reviewCurrency;
    }

    public void setReviewCurrency(String reviewCurrency) {
        this.reviewCurrency = reviewCurrency;
    }

    public String getReviewLecturer() {
        return reviewLecturer;
    }

    public void setReviewLecturer(String reviewLecturer) {
        this.reviewLecturer = reviewLecturer;
    }

    public String getReviewTeacher() {
        return reviewTeacher;
    }

    public void setReviewTeacher(String reviewTeacher) {
        this.reviewTeacher = reviewTeacher;
    }

    public String getReviewTranslator() {
        return reviewTranslator;
    }

    public void setReviewTranslator(String reviewTranslator) {
        this.reviewTranslator = reviewTranslator;
    }

    public String getReviewTransPart() {
        return reviewTransPart;
    }

    public void setReviewTransPart(String reviewTransPart) {
        this.reviewTransPart = reviewTransPart;
    }

    public Integer getReviewDealQty() {
        return reviewDealQty;
    }

    public void setReviewDealQty(Integer reviewDealQty) {
        this.reviewDealQty = reviewDealQty;
    }

    public BigDecimal getReviewDeposit() {
        return reviewDeposit;
    }

    public void setReviewDeposit(BigDecimal reviewDeposit) {
        this.reviewDeposit = reviewDeposit;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

	public String getPlanFirstType() {
		return planFirstType;
	}

	public void setPlanFirstType(String planFirstType) {
		this.planFirstType = planFirstType;
	}

	public String getPlanTransPart() {
		return planTransPart;
	}

	public void setPlanTransPart(String planTransPart) {
		this.planTransPart = planTransPart;
	}

	public Integer getPlanDealQty() {
		return planDealQty;
	}

	public void setPlanDealQty(Integer planDealQty) {
		this.planDealQty = planDealQty;
	}

	public String getActalFirstType() {
		return actalFirstType;
	}

	public void setActalFirstType(String actalFirstType) {
		this.actalFirstType = actalFirstType;
	}

	public String getActualTransPart() {
		return actualTransPart;
	}

	public void setActualTransPart(String actualTransPart) {
		this.actualTransPart = actualTransPart;
	}
}
