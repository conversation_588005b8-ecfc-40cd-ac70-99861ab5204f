package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
import com.hisun.lemon.urm.dto.mi.group.ValidGroup1;


/**
 * 
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ApiModel(value="RightGoldsVO", description="RightGoldsVO")
public class RightGoldsVO {
	 @ApiModelProperty(value="基本查询信息")
	 private MIBaseQueryInfo baseInfo;
	 
	 @ApiModelProperty(value="查询结果集")
	 List<RightGoldsBean> dataList;

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<RightGoldsBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<RightGoldsBean> dataList) {
		this.dataList = dataList;
	}

	


	
    
    
}

