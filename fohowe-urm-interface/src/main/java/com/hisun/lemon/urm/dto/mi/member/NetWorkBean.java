package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.Range;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.dto.mi.group.ValidGroup1;


/**
 * 用户推荐关系 bean
 * 
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ApiModel(value="NetWorkBean", description="用户推荐关系bean")
public class NetWorkBean {

    @ApiModelProperty(value="用户编号",required= true)
    private String memNo;
    
    @ApiModelProperty(value="推荐编号",hidden= true)
    private String parentMemNo;
    
    @ApiModelProperty(value="用户名称",required= true)
    private String memName;
    
    @ApiModelProperty(value="图片地址",required= true)
    private String picUrl;
    
    @ApiModelProperty(value="级别名称",required= true)
    private String levelName;
    
    @ApiModelProperty(value="子节点",required= false)
    private List<NetWorkBean> subBean;
    

	public String getParentMemNo() {
		return parentMemNo;
	}

	public void setParentMemNo(String parentMemNo) {
		this.parentMemNo = parentMemNo;
	}

	public List<NetWorkBean> getSubBean() {
		return subBean;
	}

	public void setSubBean(List<NetWorkBean> subBean) {
		this.subBean = subBean;
	}

	public String getMemNo() {
		return memNo;
	}

	public void setMemNo(String memNo) {
		this.memNo = memNo;
	}

	public String getMemName() {
		return memName;
	}

	public void setMemName(String memName) {
		this.memName = memName;
	}

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public String getLevelName() {
		return levelName;
	}

	public void setLevelName(String levelName) {
		this.levelName = levelName;
	}
	
    
    
}

