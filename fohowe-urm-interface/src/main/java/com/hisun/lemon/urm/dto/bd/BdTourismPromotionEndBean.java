package com.hisun.lemon.urm.dto.bd;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
public class BdTourismPromotionEndBean  {

    //
    @ApiModelProperty(name="id",value="*")
    private Long id;


    //区域
    @ApiModelProperty(name="areaName",value="区域*")
    private String areaName;


    //分公司
    @ApiModelProperty(name="companyCode",value="分公司*")
    private String companyCode;


    //代办处
    @ApiModelProperty(name="agentNo",value="代办处")
    private String agentNo;


    //经销商编号
    @ApiModelProperty(name="memberNo",value="经销商编号")
    private String memberNo;


    //经销商名称
    @ApiModelProperty(name="memberName",value="经销商名称")
    private String memberName;


    //总FD
    @ApiModelProperty(name="totalAmount",value="总FD")
    private BigDecimal totalAmount;


    //需补交FD
    @ApiModelProperty(name="repairAmount",value="需补交FD")
    private BigDecimal repairAmount;


    //已补交FD
    @ApiModelProperty(name="payAmount",value="已补交FD")
    private BigDecimal payAmount;


    //获得来源
    @ApiModelProperty(name="salesPromotion",value="获得来源")
    private String salesPromotion;

    //补交时间
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name="payTime",value="补交时间")
    private String payTime;


    //补交期数
    @ApiModelProperty(name="payWeek",value="补交期数")
    private Integer payWeek;


    //旅游名额
    @ApiModelProperty(name="quotaNum",value="旅游名额")
    private Integer quotaNum;

    //是否已出行 0 否,1 是
    @ApiModelProperty(name="isTraveled",value="是否已出行")
    private Integer isTraveled;


    //状态 （未补交、已补交）
    @ApiModelProperty(name="vcStatus",value="状态")
    private Integer vcStatus;


    //备注
    @ApiModelProperty(name="remark",value="备注")
    private String remark;

    //确认时间
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name="ficheckeTime",value="确认时间")
    private String ficheckeTime;

    //确认人
    @ApiModelProperty(name="ficheckerCode",value="确认人")
    private String ficheckerCode;

    @ApiModelProperty(name="ficheckeStatus",value="确认状态")
    private Integer ficheckeStatus;

    private String payerCode;

    private Integer agFicheckeStatus;
    
    private Long infoId;
    
    private Integer isOutPay;
    private String lineStatus;
    private Integer linePayStatus;


    public String getLineStatus() {
        return lineStatus;
    }

    public void setLineStatus(String lineStatus) {
        this.lineStatus = lineStatus;
    }

    public Integer getLinePayStatus() {
        return linePayStatus;
    }

    public void setLinePayStatus(Integer linePayStatus) {
        this.linePayStatus = linePayStatus;
    }

    public Integer getAgFicheckeStatus() {
        return agFicheckeStatus;
    }

    public void setAgFicheckeStatus(Integer agFicheckeStatus) {
        this.agFicheckeStatus = agFicheckeStatus;
    }

    public String getPayerCode() {
        return payerCode;
    }

    public void setPayerCode(String payerCode) {
        this.payerCode = payerCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRepairAmount() {
        return repairAmount;
    }

    public void setRepairAmount(BigDecimal repairAmount) {
        this.repairAmount = repairAmount;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getSalesPromotion() {
        return salesPromotion;
    }

    public void setSalesPromotion(String salesPromotion) {
        this.salesPromotion = salesPromotion;
    }

    public Integer getQuotaNum() {
		return quotaNum;
	}

	public void setQuotaNum(Integer quotaNum) {
		this.quotaNum = quotaNum;
	}

	public Integer getPayWeek() {
        return payWeek;
    }
    public void setPayWeek(Integer payWeek) {
        this.payWeek = payWeek;
    }


    public Integer getIsTraveled() {
        return isTraveled;
    }

    public void setIsTraveled(Integer isTraveled) {
        this.isTraveled = isTraveled;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFicheckerCode() {
        return ficheckerCode;
    }

    public void setFicheckerCode(String ficheckerCode) {
        this.ficheckerCode = ficheckerCode;
    }

    public Integer getFicheckeStatus() {
        return ficheckeStatus;
    }

    public void setFicheckeStatus(Integer ficheckeStatus) {
        this.ficheckeStatus = ficheckeStatus;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getFicheckeTime() {
        return ficheckeTime;
    }

    public void setFicheckeTime(String ficheckeTime) {
        this.ficheckeTime = ficheckeTime;
    }

	public Long getInfoId() {
		return infoId;
	}

	public void setInfoId(Long infoId) {
		this.infoId = infoId;
	}

	public Integer getIsOutPay() {
		return isOutPay;
	}

	public void setIsOutPay(Integer isOutPay) {
		this.isOutPay = isOutPay;
	}

}