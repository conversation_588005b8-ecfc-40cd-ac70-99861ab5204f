package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月02日
 * @time 下午14:27:30
 *
 */

@ClientValidated
@ApiModel(value="AgentDTO", description="代办处传输对象")
public class AgentDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息(用于查询)")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息(用于新增、修改)")
	private AgentBean mainInfo = new AgentBean();
	
	//代办处基本信息
    @ApiModelProperty(value="代办处名称(用于分页查询)",required=false,dataType="String")
    private String name;
    @ApiModelProperty(value="开始加入期数(用于分页查询)",dataType="String")
    private String minStartWeek;
    @ApiModelProperty(value="结束加入期数(用于分页查询)",dataType="String")
    private String maxStartWeek;
    
    @ApiModelProperty(value="奖金发放方式0=发放到代办处，1=发放到经销商",required=false,dataType="String")
    private String bonusSendType;
   
    @ApiModelProperty(value="奖金发放到会员模式代办费计提：0 否，1 计提经销商报单 2计提代办处报单",required=false,dataType="String")
    private String isBonus;
    
    @ApiModelProperty(value="最小特批货物额度",required=false,dataType="String")
    private BigDecimal minPromAmt;
    @ApiModelProperty(value="最大特批货物额度",required=false,dataType="String")
    private BigDecimal maxPpromAmt;
    
    @ApiModelProperty(value="最小已批货物额度",required=false,dataType="String")
    private BigDecimal minPromTotalAmt;
    @ApiModelProperty(value="最大已批货物额度",required=false,dataType="String")
    private BigDecimal maxPromTotalAmt;
    
    @ApiModelProperty(value="货币编码",required=false,dataType="BigDecimal")
	private String currencyCode;
    
    @ApiModelProperty(value="上级代办处",required=false,dataType="String")
    private String parentNo;
    
    @ApiModelProperty(value="代办处级别",required=false,dataType="Integer")
    private Integer levelType;
    
	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public AgentBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(AgentBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMinStartWeek() {
		return minStartWeek;
	}
	public void setMinStartWeek(String minStartWeek) {
		this.minStartWeek = minStartWeek;
	}
	public String getMaxStartWeek() {
		return maxStartWeek;
	}
	public void setMaxStartWeek(String maxStartWeek) {
		this.maxStartWeek = maxStartWeek;
	}
	public String getBonusSendType() {
		return bonusSendType;
	}
	public void setBonusSendType(String bonusSendType) {
		this.bonusSendType = bonusSendType;
	}
	public String getIsBonus() {
		return isBonus;
	}
	public void setIsBonus(String isBonus) {
		this.isBonus = isBonus;
	}
	public BigDecimal getMinPromAmt() {
		return minPromAmt;
	}
	public void setMinPromAmt(BigDecimal minPromAmt) {
		this.minPromAmt = minPromAmt;
	}
	public BigDecimal getMaxPpromAmt() {
		return maxPpromAmt;
	}
	public void setMaxPpromAmt(BigDecimal maxPpromAmt) {
		this.maxPpromAmt = maxPpromAmt;
	}
	public BigDecimal getMinPromTotalAmt() {
		return minPromTotalAmt;
	}
	public void setMinPromTotalAmt(BigDecimal minPromTotalAmt) {
		this.minPromTotalAmt = minPromTotalAmt;
	}
	public BigDecimal getMaxPromTotalAmt() {
		return maxPromTotalAmt;
	}
	public void setMaxPromTotalAmt(BigDecimal maxPromTotalAmt) {
		this.maxPromTotalAmt = maxPromTotalAmt;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getParentNo() {
		return parentNo;
	}
	public void setParentNo(String parentNo) {
		this.parentNo = parentNo;
	}
	public Integer getLevelType() {
		return levelType;
	}
	public void setLevelType(Integer levelType) {
		this.levelType = levelType;
	}
	
}
