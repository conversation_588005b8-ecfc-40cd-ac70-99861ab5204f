 package com.hisun.lemon.urm.dto.fi.balance;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceItemQueryBean 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcBalanceItemQueryBean", description="账户传输对象")
public class AcBalanceItemQueryBean{
	/*=============base data============ */
	@ApiModelProperty(value="奖金制度",dataType="String")
    private String bonusType;
    @ApiModelProperty(value="区域",dataType="String")
    private String areaCode;
    @ApiModelProperty(value="所属分公司",dataType="String")
    private String companyCode;
    @ApiModelProperty(value="代办处",dataType="String")
    private String agentNo;
    /*=============show data============ */
    @ApiModelProperty(value="记录编号")
    private long id;
    @ApiModelProperty(value="关联单号")
    private String orderNo;
    @ApiModelProperty(value="账户类型",dataType="String")
    private String acType;
    @ApiModelProperty(value="用户编号",dataType="String")
    private String userCode;
    @ApiModelProperty(value="交易类别",dataType="String")
    private String orderType;
    @ApiModelProperty(value="存入",dataType="BigDecimal")
    private BigDecimal inMoney;
    @ApiModelProperty(value="取出",dataType="BigDecimal")
    private BigDecimal outMoney;
    @ApiModelProperty(value="变化金额",dataType="BigDecimal")
    private BigDecimal money;
    @ApiModelProperty(value="可用余额",dataType="String")
    private String validBalance;
    @ApiModelProperty(value="借款余额",dataType="String")
    private String oweBalance;
    @ApiModelProperty(value="创建时间")
    private LocalDateTime dealDate;
    @ApiModelProperty(value="备注",dataType="String")
    private String memo;
    @ApiModelProperty(value="转出银行帐号/发票号",required=false,dataType="String")
    private String outCode; 
    @ApiModelProperty(value="用户代办处",required=false,dataType="String")
    private String userAgentNo;
    @ApiModelProperty(value="期次",required=false,dataType="String")
    private String wWeek;
    
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getInMoney() {
		return inMoney;
	}
	public void setInMoney(BigDecimal inMoney) {
		this.inMoney = inMoney;
	}
	public BigDecimal getOutMoney() {
		return outMoney;
	}
	public void setOutMoney(BigDecimal outMoney) {
		this.outMoney = outMoney;
	}
	public String getValidBalance() {
		return validBalance;
	}
	public void setValidBalance(String validBalance) {
		this.validBalance = validBalance;
	}
	public String getOweBalance() {
		return oweBalance;
	}
	public void setOweBalance(String oweBalance) {
		this.oweBalance = oweBalance;
	}
	public LocalDateTime getDealDate() {
		return dealDate;
	}
	public void setDealDate(LocalDateTime dealDate) {
		this.dealDate = dealDate;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getOutCode() {
		return outCode;
	}
	public void setOutCode(String outCode) {
		this.outCode = outCode;
	}
	public String getUserAgentNo() {
		return userAgentNo;
	}
	public void setUserAgentNo(String userAgentNo) {
		this.userAgentNo = userAgentNo;
	}
	public String getwWeek() {
		return wWeek;
	}
	public void setwWeek(String wWeek) {
		this.wWeek = wWeek;
	}
}
