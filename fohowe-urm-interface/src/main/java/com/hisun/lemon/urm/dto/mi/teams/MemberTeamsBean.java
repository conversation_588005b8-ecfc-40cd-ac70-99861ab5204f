package com.hisun.lemon.urm.dto.mi.teams;

import java.util.ArrayList;
import java.util.List;

public class MemberTeamsBean {
	
    private Integer id;

    private String companyCode;

    private String agentNo;

    private String teamNo;

    private String teamName;

    private String memberNo;

    private String memberName;

    private String cardType;

    private Integer teamStatus;

    private Integer teamNum;

    private Integer passeFlag;
    
    private Integer initialNum;
    
    private Integer teamType;

    private Integer fendouNum;
    private Integer rankSn;
    private Integer IntegralSn;
    private Integer rankSnWeek;
    private Integer IntegralSnWeek;
    private String remark;
    
    private List<MemberTeamsItemsBean> items=new ArrayList<MemberTeamsItemsBean>();
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getTeamNo() {
        return teamNo;
    }

    public void setTeamNo(String teamNo) {
        this.teamNo = teamNo;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public Integer getTeamStatus() {
        return teamStatus;
    }

    public void setTeamStatus(Integer teamStatus) {
        this.teamStatus = teamStatus;
    }

    public Integer getTeamNum() {
        return teamNum;
    }

    public void setTeamNum(Integer teamNum) {
        this.teamNum = teamNum;
    }

    public Integer getPasseFlag() {
        return passeFlag;
    }

    public void setPasseFlag(Integer passeFlag) {
        this.passeFlag = passeFlag;
    }

	public Integer getInitialNum() {
		return initialNum;
	}

	public void setInitialNum(Integer initialNum) {
		this.initialNum = initialNum;
	}

	public Integer getTeamType() {
		return teamType;
	}

	public void setTeamType(Integer teamType) {
		this.teamType = teamType;
	}

	public Integer getFendouNum() {
		return fendouNum;
	}

	public void setFendouNum(Integer fendouNum) {
		this.fendouNum = fendouNum;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

    public Integer getRankSn() {
        return rankSn;
    }

    public void setRankSn(Integer rankSn) {
        this.rankSn = rankSn;
    }

    public Integer getIntegralSn() {
        return IntegralSn;
    }

    public void setIntegralSn(Integer integralSn) {
        IntegralSn = integralSn;
    }

    public Integer getRankSnWeek() {
        return rankSnWeek;
    }

    public void setRankSnWeek(Integer rankSnWeek) {
        this.rankSnWeek = rankSnWeek;
    }

    public Integer getIntegralSnWeek() {
        return IntegralSnWeek;
    }

    public void setIntegralSnWeek(Integer integralSnWeek) {
        IntegralSnWeek = integralSnWeek;
    }

    public List<MemberTeamsItemsBean> getItems() {
		return items;
	}

	public void setItems(List<MemberTeamsItemsBean> items) {
		this.items = items;
	}

	@Override
	public String toString() {
		return "MemberTeamsBean [id=" + id + ", companyCode=" + companyCode + ", agentNo=" + agentNo + ", teamNo="
				+ teamNo + ", teamName=" + teamName + ", memberNo=" + memberNo + ", memberName=" + memberName
				+ ", cardType=" + cardType + ", teamStatus=" + teamStatus + ", teamNum=" + teamNum + ", passeFlag="
				+ passeFlag + ", items=" + items + "]";
	}
}