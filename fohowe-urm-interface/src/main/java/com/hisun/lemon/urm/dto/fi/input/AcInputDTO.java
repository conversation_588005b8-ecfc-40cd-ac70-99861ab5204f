 package com.hisun.lemon.urm.dto.fi.input;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcInputDTO 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcInputDTO", description="账户传输对象")
public class AcInputDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="基本查询信息")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	@ApiModelProperty(value="基本信息")
	private AcInputBean mainInfo = new AcInputBean();
	
	@ApiModelProperty(value="经销商/代办处编号",required=false,dataType="String")
	private String userCode;
	@ApiModelProperty(value="账户类型",required=false,dataType="String")
	private String acType;
	@ApiModelProperty(value="方式,0=转账，1=现金，2=POS",required=false,dataType="String")
	private String tranType;
	@ApiModelProperty(value="状态:1:新单 2:分公司已审核 3:总公司已审核  8：已作废  6:已取消",required=false,dataType="String")
	private String status;
	@ApiModelProperty(value="财务确认状态 a:未确认;b:部分确认;c:已确认",required=false,dataType="String")
	private String fiCheckStatus;
	@ApiModelProperty(value="充值申请期数",required=false,dataType="String")
	private String periodWeek;
	
	@ApiModelProperty(value="开始加入期数（用于匹配条件查询）")
    private String minPeriodWeek;
    
    @ApiModelProperty(value="结束加入期数（用于匹配条件查询）")
    private String maxPeriodWeek;
    
	@ApiModelProperty(value="开始创建日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime minCreateTime;
	@ApiModelProperty(value="结束创建日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime maxCreateTime;
	@ApiModelProperty(value="批量审核ID",required=false)
	private String[] idArr;
	@ApiModelProperty(value="审批意见（1通过、0退回）",required=false)
	private String auditOpinion;
	@ApiModelProperty(value="本次确认金额",required=false)
	private BigDecimal theAmount;
	
	@ApiModelProperty(value="充值方式",required=false)
	private String[] tranTypeArr;
	
	@ApiModelProperty(value="奖金制度")
    private String[] bonusTypeArr;
    @ApiModelProperty(value="区域")
    private String[] areaCodeArr;
    @ApiModelProperty(value="所属分公司")
    private String[] companyCodeArr;
    
    @ApiModelProperty(value="状态:1:新单 2:分公司已审核 3:总公司已审核  8：已作废  6:已取消",required=false,dataType="String")
	private String[] statusArr;
	@ApiModelProperty(value="财务确认状态 a:未确认;b:部分确认;c:已确认",required=false,dataType="String")
	private String[] fiCheckStatusArr;
	@ApiModelProperty(value="财务确认备注（用于匹配条件查询）")
    private String fiCheckMemo;
	@ApiModelProperty(value="eas确认状态")
	private String easStatus;
	@ApiModelProperty(value="是否代办处查询")
	private Integer isAgent;
	private String accountType;
	private String statuse;

	public String getStatuse() {
		return statuse;
	}

	public void setStatuse(String statuse) {
		this.statuse = statuse;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}
	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}
	public AcInputBean getMainInfo() {
		return mainInfo;
	}
	public void setMainInfo(AcInputBean mainInfo) {
		this.mainInfo = mainInfo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getTranType() {
		return tranType;
	}
	public void setTranType(String tranType) {
		this.tranType = tranType;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getFiCheckStatus() {
		return fiCheckStatus;
	}
	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String[] getIdArr() {
		return idArr;
	}
	public void setIdArr(String[] idArr) {
		this.idArr = idArr;
	}
	public String getAuditOpinion() {
		return auditOpinion;
	}
	public void setAuditOpinion(String auditOpinion) {
		this.auditOpinion = auditOpinion;
	}
	public BigDecimal getTheAmount() {
		return theAmount;
	}
	public void setTheAmount(BigDecimal theAmount) {
		this.theAmount = theAmount;
	}
	public LocalDateTime getMinCreateTime() {
		return minCreateTime;
	}
	public void setMinCreateTime(LocalDateTime minCreateTime) {
		this.minCreateTime = minCreateTime;
	}
	public LocalDateTime getMaxCreateTime() {
		return maxCreateTime;
	}
	public void setMaxCreateTime(LocalDateTime maxCreateTime) {
		this.maxCreateTime = maxCreateTime;
	}
	public String[] getTranTypeArr() {
		return tranTypeArr;
	}
	public void setTranTypeArr(String[] tranTypeArr) {
		this.tranTypeArr = tranTypeArr;
	}
	public String[] getBonusTypeArr() {
		return bonusTypeArr;
	}
	public void setBonusTypeArr(String[] bonusTypeArr) {
		this.bonusTypeArr = bonusTypeArr;
	}
	public String[] getAreaCodeArr() {
		return areaCodeArr;
	}
	public void setAreaCodeArr(String[] areaCodeArr) {
		this.areaCodeArr = areaCodeArr;
	}
	public String[] getCompanyCodeArr() {
		return companyCodeArr;
	}
	public void setCompanyCodeArr(String[] companyCodeArr) {
		this.companyCodeArr = companyCodeArr;
	}
	public String[] getStatusArr() {
		return statusArr;
	}
	public void setStatusArr(String[] statusArr) {
		this.statusArr = statusArr;
	}
	public String[] getFiCheckStatusArr() {
		return fiCheckStatusArr;
	}
	public void setFiCheckStatusArr(String[] fiCheckStatusArr) {
		this.fiCheckStatusArr = fiCheckStatusArr;
	}
	public String getMinPeriodWeek() {
		return minPeriodWeek;
	}
	public void setMinPeriodWeek(String minPeriodWeek) {
		this.minPeriodWeek = minPeriodWeek;
	}
	public String getMaxPeriodWeek() {
		return maxPeriodWeek;
	}
	public void setMaxPeriodWeek(String maxPeriodWeek) {
		this.maxPeriodWeek = maxPeriodWeek;
	}
	public String getFiCheckMemo() {
		return fiCheckMemo;
	}
	public void setFiCheckMemo(String fiCheckMemo) {
		this.fiCheckMemo = fiCheckMemo;
	}
	public String getEasStatus() {
		return easStatus;
	}
	public void setEasStatus(String easStatus) {
		this.easStatus = easStatus;
	}
	public Integer getIsAgent() {
		return isAgent;
	}
	public void setIsAgent(Integer isAgent) {
		this.isAgent = isAgent;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
}
