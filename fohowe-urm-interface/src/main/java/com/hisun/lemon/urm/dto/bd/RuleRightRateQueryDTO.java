package com.hisun.lemon.urm.dto.bd;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * @ClassName: RuleRightRateDTO 
 * @Description: 系统设置-经营权兑换比例
 * @author: tian
 * @date: 2017年11月15日 上午11:29:13
 */
public class RuleRightRateQueryDTO {

    @ApiModelProperty(name = "pageNum", value = "页码", required = true, dataType = "Integer")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = true, dataType = "Integer")
    private int pageSize;

    @ApiModelProperty(name = "beginDate", value = "查询开始日期", required = false, dataType = "LocalDate")
    private LocalDateTime beginDate;

    @ApiModelProperty(name = "endDate", value = "查询结束日期", required = false, dataType = "LocalDate")
    private LocalDateTime endDate;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

}
