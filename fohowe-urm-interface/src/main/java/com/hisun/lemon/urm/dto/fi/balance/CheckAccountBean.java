 package com.hisun.lemon.urm.dto.fi.balance;

import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalance 传输对象
 * <AUTHOR>
 * @date 2017年11月3号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="CheckAccountBean", description="账户传输对象")
public class CheckAccountBean{
	@ApiModelProperty(value="账户类型:f$＝F$,fv＝FV,f0＝F000,h0=H000,fb=FB,pv=活跃PV,p$=活跃P$,b1=重消分配,b2=旅游基金,b3=名车基金,b4=游艇基金,b5=住宅基金,b6=市场发展基金",required=false,dataType="String")
	private String acType;
	@ApiModelProperty(value="用户编号",required=false,dataType="String")
	private String userCode;
	
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
}
