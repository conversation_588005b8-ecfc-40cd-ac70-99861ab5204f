package com.hisun.lemon.urm.dto.fi.balance;

import org.hibernate.validator.constraints.NotBlank;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcBalanceItem 传输对象
 * <AUTHOR>
 * @date 2018年09月12号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="NetCashReportReqDTO", description="账户统计报表传输对象")
public class NetCashReportReqDTO extends GenericDTO<NoBody>{
	@ApiModelProperty(value="奖金制度")
    private String bonusType;
	
    @ApiModelProperty(value="区域")
    private String areaCode;
    
    @ApiModelProperty(value="所属分公司")
    private String companyCode;
    
    @ApiModelProperty(value="代办处")
    private String agentNo;
	
	@ApiModelProperty(value="汇总类型:1奖金制度 2区域 3分公司 4代办处",required=false,dataType="String")
	@NotBlank(message="URM10005")
	private String type;
	
	@ApiModelProperty(value="期数",required=false,dataType="String")
	private String period;
	
	@ApiModelProperty(name = "pageNum", value = "页码", required = false, dataType = "Integer")
    private int pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页数", required = false, dataType = "Integer")
    private int pageSize;

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
    
}
