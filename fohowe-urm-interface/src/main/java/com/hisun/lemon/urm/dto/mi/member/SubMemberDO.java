package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

import org.hibernate.validator.constraints.NotEmpty;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.ValidateGroup;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;

/**
 * SubMemberDO 传输对象
 * <AUTHOR>
 * @date 2017年11月1日
 * @time 上午9:27:30
 *
 */

@ClientValidated
@ApiModel(value="SubMemberDO", description="下属经销商查询传输对象")
public class SubMemberDO{
	@ApiModelProperty(value="基本查询信息（下属经销商条件）")
	private MIBaseQueryInfo baseInfo=new MIBaseQueryInfo();
	
	@ApiModelProperty(value="经销商编号（对应主经销商编号查询条件）")
	@NotEmpty(groups= {ValidateGroup.first.class})
	private String mainMemberNo;
	
	@ApiModelProperty(value="经销商级别（用于匹配条件查询）")
    private String[] cardType;
	
	@ApiModelProperty(value="最小代数")
	private Integer minlayer;
	
	@ApiModelProperty(value="最大代数")
	private Integer maxlayer;
	
	@ApiModelProperty(value="推荐索引")
	private String index;
	
	@ApiModelProperty(value="开始注册日期(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using=DateJsonSerializer.class)
   	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime minRegDate;
    
    @ApiModelProperty(value="结束注册日期(yyyy-MM-dd HH:mm:ss)")
    @JsonSerialize(using=DateJsonSerializer.class)
   	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime maxRegDate;
	//查询展示字段
	private List<SubMember> members;
	

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public String getMainMemberNo() {
		return mainMemberNo;
	}

	public void setMainMemberNo(String mainMemberNo) {
		this.mainMemberNo = mainMemberNo;
	}
	
	public List<SubMember> getMembers() {
		return members;
	}

	public void setMembers(List<SubMember> members) {
		this.members = members;
	}

	public String[] getCardType() {
		return cardType;
	}

	public void setCardType(String[] cardType) {
		this.cardType = cardType;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public Integer getMinlayer() {
		return minlayer;
	}

	public void setMinlayer(Integer minlayer) {
		this.minlayer = minlayer;
	}

	public Integer getMaxlayer() {
		return maxlayer;
	}

	public void setMaxlayer(Integer maxlayer) {
		this.maxlayer = maxlayer;
	}

	public LocalDateTime getMinRegDate() {
		return minRegDate;
	}

	public void setMinRegDate(LocalDateTime minRegDate) {
		this.minRegDate = minRegDate;
	}

	public LocalDateTime getMaxRegDate() {
		return maxRegDate;
	}

	public void setMaxRegDate(LocalDateTime maxRegDate) {
		this.maxRegDate = maxRegDate;
	}
	
}
