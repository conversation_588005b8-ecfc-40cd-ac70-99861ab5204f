package com.hisun.lemon.urm.dto.mi.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumDeserializer;
import com.hisun.lemon.fohowe.common.json.CustomValueEnumSerializer;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.enums.fi.StatusEnums;
import com.hisun.lemon.urm.enums.mi.ReceiptTypeEnums;

/**
 * Agent 传输对象
 * <AUTHOR>
 * @date 2017年11月20日
 * @time 下午14:27:30
 *
 */

@ClientValidated
@ApiModel(value="AgentMemUpdateBean", description="代办处传输对象")
public class AgentMemUpdateBean{
	@ApiModelProperty(value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(value="区域/分公司",required=false,dataType="String")
    private String companycode;
	@ApiModelProperty(value="原代办处编号",required=false,dataType="String")
    private String agentNo;
	@ApiModelProperty(value="经销商编号（需要的经销商网络的根节点）",required=false,dataType="String")
    private String memberNo;
	@ApiModelProperty(value="目标代办处编号",required=false,dataType="String")
    private String toAgentNo;
	@ApiModelProperty(value="单据状态:0=新增1=审核",required=false,dataType="String")
	@JsonDeserialize(using = CustomValueEnumDeserializer.class)
	@JsonSerialize(using = CustomValueEnumSerializer.class)
    private StatusEnums receiptstatus;
	@ApiModelProperty(value="单据类型:1.经销商所属代办处更改2.代办处下属经销商转移3.mi.chAgent.linkNet",required=false)
	@JsonDeserialize(using = CustomValueEnumDeserializer.class)
	@JsonSerialize(using = CustomValueEnumSerializer.class)
    private ReceiptTypeEnums receipttype;
	@ApiModelProperty(value="备注",required=false,dataType="String")
    private String remark;
	@ApiModelProperty(value="创建日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime orderdate;
	@ApiModelProperty(value="审核日期",required=false,dataType="String")
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime ordercheckdate;
	@ApiModelProperty(value="创建人",required=false,dataType="String")
    private String orderUser;
	@ApiModelProperty(value="审核人",required=false,dataType="String")
    private String orderChecker;
	@ApiModelProperty(value="期次",required=false)
	private String wWeek;
	private Integer ctrlFlag;
    private BigDecimal fdTotal;
    private BigDecimal fund1;
    private BigDecimal fund2;
    private BigDecimal fund3;
    private BigDecimal fund4;
    private BigDecimal share1;
    private BigDecimal share2;
    private BigDecimal f000;
    private BigDecimal h000;
    private BigDecimal fgc;
    private BigDecimal fp;
    private String delRoles;
    private String addRoles;
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCompanycode() {
		return companycode;
	}
	public void setCompanycode(String companycode) {
		this.companycode = companycode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getMemberNo() {
		return memberNo;
	}
	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}
	public String getToAgentNo() {
		return toAgentNo;
	}
	public void setToAgentNo(String toAgentNo) {
		this.toAgentNo = toAgentNo;
	}
	public StatusEnums getReceiptstatus() {
		return receiptstatus;
	}
	public void setReceiptstatus(StatusEnums receiptstatus) {
		this.receiptstatus = receiptstatus;
	}
	public ReceiptTypeEnums getReceipttype() {
		return receipttype;
	}
	public void setReceipttype(ReceiptTypeEnums receipttype) {
		this.receipttype = receipttype;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getOrderUser() {
		return orderUser;
	}
	public void setOrderUser(String orderUser) {
		this.orderUser = orderUser;
	}
	public String getOrderChecker() {
		return orderChecker;
	}
	public void setOrderChecker(String orderChecker) {
		this.orderChecker = orderChecker;
	}
	public LocalDateTime getOrderdate() {
		return orderdate;
	}
	public void setOrderdate(LocalDateTime orderdate) {
		this.orderdate = orderdate;
	}
	public LocalDateTime getOrdercheckdate() {
		return ordercheckdate;
	}
	public void setOrdercheckdate(LocalDateTime ordercheckdate) {
		this.ordercheckdate = ordercheckdate;
	}
	public String getwWeek() {
		return wWeek;
	}
	public void setwWeek(String wWeek) {
		this.wWeek = wWeek;
	}
	public Integer getCtrlFlag() {
		return ctrlFlag;
	}
	public void setCtrlFlag(Integer ctrlFlag) {
		this.ctrlFlag = ctrlFlag;
	}
	public BigDecimal getFdTotal() {
		return fdTotal;
	}
	public void setFdTotal(BigDecimal fdTotal) {
		this.fdTotal = fdTotal;
	}
	public BigDecimal getFund1() {
		return fund1;
	}
	public void setFund1(BigDecimal fund1) {
		this.fund1 = fund1;
	}
	public BigDecimal getFund2() {
		return fund2;
	}
	public void setFund2(BigDecimal fund2) {
		this.fund2 = fund2;
	}
	public BigDecimal getFund3() {
		return fund3;
	}
	public void setFund3(BigDecimal fund3) {
		this.fund3 = fund3;
	}
	public BigDecimal getFund4() {
		return fund4;
	}
	public void setFund4(BigDecimal fund4) {
		this.fund4 = fund4;
	}
	public BigDecimal getShare1() {
		return share1;
	}
	public void setShare1(BigDecimal share1) {
		this.share1 = share1;
	}
	public BigDecimal getShare2() {
		return share2;
	}
	public void setShare2(BigDecimal share2) {
		this.share2 = share2;
	}
	public BigDecimal getF000() {
		return f000;
	}
	public void setF000(BigDecimal f000) {
		this.f000 = f000;
	}
	public BigDecimal getH000() {
		return h000;
	}
	public void setH000(BigDecimal h000) {
		this.h000 = h000;
	}
	public BigDecimal getFgc() {
		return fgc;
	}
	public void setFgc(BigDecimal fgc) {
		this.fgc = fgc;
	}
	public BigDecimal getFp() {
		return fp;
	}
	public void setFp(BigDecimal fp) {
		this.fp = fp;
	}
	public String getDelRoles() {
		return delRoles;
	}
	public void setDelRoles(String delRoles) {
		this.delRoles = delRoles;
	}
	public String getAddRoles() {
		return addRoles;
	}
	public void setAddRoles(String addRoles) {
		this.addRoles = addRoles;
	}
	
}
