package com.hisun.lemon.urm.dto.mi.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.urm.common.DateConstant;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;


/**
 * 
 * <AUTHOR>
 * @date 2017-11-01 10:45
 */
@ApiModel(value="ActiveVO")
public class ActiveVO {
	 @ApiModelProperty(value="基本查询信息")
	 private MIBaseQueryInfo baseInfo;
	 
	 @ApiModelProperty(value="查询结果集")
	 private List<ActiveBean> dataList;
	 
	 @ApiModelProperty(value="当前期数")
	 private String currentWeek;
	 
	 @ApiModelProperty(value="数据字典KV-图片")
	 private Map cardUrlKV=null;
	 
	 @ApiModelProperty(value="数据字典KV-级别名称")
	 private Map cardType=null;
	  
	 
	public Map getCardUrlKV() {
		return cardUrlKV;
	}

	public void setCardUrlKV(Map cardUrlKV) {
		this.cardUrlKV = cardUrlKV;
	}

	public Map getCardType() {
		return cardType;
	}

	public void setCardType(Map cardType) {
		this.cardType = cardType;
	}

	public MIBaseQueryInfo getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(MIBaseQueryInfo baseInfo) {
		this.baseInfo = baseInfo;
	}

	public List<ActiveBean> getDataList() {
		return dataList;
	}

	public void setDataList(List<ActiveBean> dataList) {
		this.dataList = dataList;
	}

	public String getCurrentWeek() {
		return currentWeek;
	}

	public void setCurrentWeek(String currentWeek) {
		this.currentWeek = currentWeek;
	}

	
	
	
	 
    
}

