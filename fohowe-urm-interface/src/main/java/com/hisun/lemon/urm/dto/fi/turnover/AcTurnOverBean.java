 package com.hisun.lemon.urm.dto.fi.turnover;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hisun.lemon.fohowe.common.json.DateJsonDeserializer;
import com.hisun.lemon.fohowe.common.json.DateJsonSerializer;
import com.hisun.lemon.framework.validation.ClientValidated;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * AcTransBean 传输对象
 * <AUTHOR>
 * @date 2017年11月10号
 * @time 下午15:24:22
 */
@ClientValidated
@ApiModel(value="AcTurnOverBean", description="转账对象")
public class AcTurnOverBean{
	@ApiModelProperty(name="id",value="记录ID",required=false)
    private Long id;
	@ApiModelProperty(name="acType",value="账户类型")
    private String acType;
	@ApiModelProperty(name="orderType",value="单据类型:1借款、2还款",required=false)
    private String orderType;
	@ApiModelProperty(name="companyCode",value="公司编号",required=false,dataType="String")
    private String companyCode;
	@ApiModelProperty(name="companyName",value="公司名称",required=false,dataType="String")
    private String companyName;
	@ApiModelProperty(name="userCode",value="用户编号",required=false,dataType="String")
    private String userCode;
	@ApiModelProperty(name="money",value="金额",required=false,dataType="BigDecimal")
    private BigDecimal money;
	@ApiModelProperty(name="handlingFee",value="手续费",required=false,dataType="BigDecimal")
    private BigDecimal handlingFee;
	@ApiModelProperty(name="status",value="状态：1:新单 2:核准9.入账7.退回",required=false,dataType="String")
    private String status;
	@ApiModelProperty(name="creatorCode",value="创建人编号",required=false,dataType="String")
    private String creatorCode;
	@ApiModelProperty(name="creatorName",value="创建人姓名",required=false,dataType="String")
    private String creatorName;
	@ApiModelProperty(name="createTime",value="创建时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
	private LocalDateTime createTime;
	@ApiModelProperty(name="checkerCode",value="确认人编号",required=false,dataType="String")
    private String checkerCode;
	@ApiModelProperty(name="checkTime",value="确认时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime checkTime;
	@ApiModelProperty(name="reCheckerCode",value="复核人编号",required=false,dataType="String")
    private String reCheckerCode;
	@ApiModelProperty(name="reCheckTime",value="复核时间",required=false)
	@JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime reCheckTime;
	@ApiModelProperty(name="memo",value="备注(会员)",required=false,dataType="String")
    private String memo;
	@ApiModelProperty(name="remark",value="摘要(公司)",required=false,dataType="String")
    private String remark;
	@ApiModelProperty(name="periodWeek",value="兑换申请期数",required=false,dataType="String")
    private String periodWeek;
	@ApiModelProperty(name="agentNo",value="代办处编号",required=false,dataType="String")
    private String agentNo;
	@ApiModelProperty(name="agentName",value="代办处名称",required=false,dataType="String")
    private String agentName;
	@ApiModelProperty(name="result",value="处理结果",required=false,dataType="String")
    private String result;
	@ApiModelProperty(name="cancleCode",value="取消人编号",required=false,dataType="String")
    private String cancleCode;
    @ApiModelProperty(name="cancleTime",value="取消时间",required=false)
    @JsonSerialize(using=DateJsonSerializer.class)
	@JsonDeserialize(using=DateJsonDeserializer.class)
    private LocalDateTime cancleTime;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public BigDecimal getHandlingFee() {
		return handlingFee;
	}
	public void setHandlingFee(BigDecimal handlingFee) {
		this.handlingFee = handlingFee;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCreatorCode() {
		return creatorCode;
	}
	public void setCreatorCode(String creatorCode) {
		this.creatorCode = creatorCode;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getCheckerCode() {
		return checkerCode;
	}
	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}
	public LocalDateTime getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(LocalDateTime checkTime) {
		this.checkTime = checkTime;
	}
	public String getReCheckerCode() {
		return reCheckerCode;
	}
	public void setReCheckerCode(String reCheckerCode) {
		this.reCheckerCode = reCheckerCode;
	}
	public LocalDateTime getReCheckTime() {
		return reCheckTime;
	}
	public void setReCheckTime(LocalDateTime reCheckTime) {
		this.reCheckTime = reCheckTime;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	public String getAgentName() {
		return agentName;
	}
	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	public String getCancleCode() {
		return cancleCode;
	}
	public void setCancleCode(String cancleCode) {
		this.cancleCode = cancleCode;
	}
	public LocalDateTime getCancleTime() {
		return cancleTime;
	}
	public void setCancleTime(LocalDateTime cancleTime) {
		this.cancleTime = cancleTime;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
}
