apply plugin: 'application'

dependencies {
    compile project(":fohowe-urm-interface")
    //compile("com.hisun:lemon-swagger:1.0.1-SNAPSHOT"){ changing = true }
    //compile("com.hisun:lemon-framework:1.0.1-SNAPSHOT"){ changing = true }
    compile("com.hisun:lemon-swagger")
    compile("com.hisun:lemon-framework"){
    	exclude module: 'lemon-framework-rabbitmq'
    }
    compile("com.hisun:lemon-gateway-dto")
    compile("org.springframework.boot:spring-boot-starter-thymeleaf")
    compile("com.hisun:lemon-framework-lock")
    compile("com.hisun:fohowe-common-api")
    compile('com.hisun:fohowe-api-ec')
    compile("com.hisun:bns-interface") 
    compile("com.hisun:fohowe-fin-interface")
    //compile("com.hisun:lemon-fohow-authority-validate")
    compile("org.springframework:springloaded:1.2.8.RELEASE")
    compile("com.alibaba:fastjson:1.2.33")
    compile("com.alibaba:easyexcel:2.2.4")  
    compile("com.openhtmltopdf:openhtmltopdf-core:1.0.10")
    compile("com.openhtmltopdf:openhtmltopdf-pdfbox:1.0.10")
    compile("com.github.jhonnymertz:java-wkhtmltopdf-wrapper:1.1.15-RELEASE")
    compile("cn.hutool:hutool-all:5.8.16")
    compile("com.sun.mail:javax.mail:1.6.2")
    //compile("xml-apis:xml-apis:2.0.2")
    //compile('org.dom4j:dom4j-1.6.1:1.6.1')
  //  compile("com.hisun:lemon-framework-accdate-bind")
    
    
}

mainClassName = 'com.hisun.lemon.Application'

jar {
    manifest {
        attributes(
                   "Implementation-Title": "Gradle",
                   "Implementation-Version": "${version}",
                   "Class-Path": '. config/'
                  )
    }
    //exclude('config/')
}

bootRepackage {
    enabled = true
    mainClass = 'com.hisun.lemon.Application'
}

task clearTarget(type:Delete){  
   delete 'build/target'  
}  

task release(type: Copy,dependsOn: [clearTarget,build]) {  
    from('build/libs') {  
        include '*.jar'
        exclude '*-sources.jar'  
    }  
    //from('src/main/resources') {
    //    include 'config/*'
    //}
    into ('build/target') 
    
    into('bin') {
        from 'shell'
    } 
} 

task dist(type: Zip,dependsOn: [release]) {  
    from ('build/target/') {
    } 
}
