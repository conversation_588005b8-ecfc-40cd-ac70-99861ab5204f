package com.hisun.lemon.urm.controller;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import com.hisun.lemon.urm.uitls.HttpClientUtil;

public class test {

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		try {
//			System.out.println(MD5Tool.getMD5("APP", ""));
//			System.out.println(String.valueOf(Digests.md5("WTCHgnxYzsmN7th".getBytes())));
//			System.out.println(Digests.md5(("WTCHgnxYzsmN7th" + "YhBYFXWDCZaNZdb").getBytes()));
//			System.out.println(Encodes.encodeHex(Digests.md5(("WTCHgnxYzsmN7th" + "YhBYFXWDCZaNZdb").getBytes())));
//			String url = "https://robot.davpay.com/api/app/getAdvPriceList";
//			Map<String, String> param = new HashMap<String, String>();
//	        param.put("asset", "USDT");
//	        param.put("fiat", "RUB");
//	        param.put("tradeType", "buy");
//	        String result = HttpClientUtil.doPosts(url, param);
//
//	        System.out.println("GET返回信息：" + result);
//	        String result1 = HttpClientUtil.doPost(url, param);
//	        System.out.println("POST返回信息：" + result1);
	        
	        BigDecimal aa = new BigDecimal("1862.0300");
	        System.out.println("compareTo：" + aa.doubleValue());
	        System.out.println("compareTo：" + aa.compareTo(BigDecimal.ZERO));
	        
	        
	        String tem="DDFD0082,SDREEW";
	        if(tem.indexOf("FD")>=0 && tem.length()>=6) {
				String teamNo = tem.substring(tem.indexOf("FD"), tem.indexOf("FD")+6);
				System.out.println(teamNo);
				
			}
	        
	        
	        Calendar calendar = Calendar.getInstance();
	        // 将日期调整到上一周的星期六
	        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
	        if (dayOfWeek != Calendar.SATURDAY) {
	            calendar.add(Calendar.DATE, -((dayOfWeek + 1) % 7));
	        } else {
	            calendar.add(Calendar.DATE, -6);
	        }
	        System.out.println(calendar.getTime());
	        System.out.println(calendar.get(Calendar.YEAR));
	        System.out.println(calendar.get(Calendar.MONTH)+1);
	        System.out.println(calendar.get(Calendar.DAY_OF_MONTH));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	
}
