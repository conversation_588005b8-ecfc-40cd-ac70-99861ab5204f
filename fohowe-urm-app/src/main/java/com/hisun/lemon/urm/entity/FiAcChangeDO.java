/*
 * @ClassName FiAcChangeDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiAcChangeDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields companyCode 分公司编码 company_code
     */
    private String companyCode;
    /**
     * @Fields acType 账户类型，fb=fb ，f$＝f$，fv＝fv, f0=f000，pv=活跃pv，b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，s1=全球分红，s2=凤凰大使分红
     */
	private String acType;
    /**
     * @Fields userCode 经销商/代办处编号
     */
    private String userCode;
    /**
     * @Fields orderType 交易类别，1x之后的界面定义录入
     */
	private String orderType;
    /**
     * @Fields dealDate 交易日期 deal_date
     */
    private LocalDateTime dealDate;
    /**
     * @Fields currencyCode 货币编码
     */
    private String currencyCode;
    /**
     * @Fields money 余额变化 money
     */
    private BigDecimal money;
    /**
     * @Fields remark 摘要 notes
     */
    private String remark;
    /**
     * @Fields memo 备注 remark
     */
    private String memo;
    /**
     * @Fields createrCode 建立者帐号 creater_code
     */
    private String createrCode;
    /**
     * @Fields createrName 建立者名称 creater_name
     */
    private String createrName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields outCode 转出银行帐号/发票号
     */
    private String outCode;
    /**
     * @Fields checkerCode 审核者帐号
     */
    private String checkerCode;
    /**
     * @Fields checkerName 审核者名称
     */
    private String checkerName;
    /**
     * @Fields checkTime 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * @Fields status 状态，0新增，1审核  2发放  3 删除
     */
    private String status;
    /**
     * @Fields changeType 操作类型：a=存入(不判断余额),d=提取(判断余额)
     */
    private String changeType;
    /**
     * @Fields createNo 批量操作的批号
     */
    private String createNo;
    /**
     * @Fields wWeek 周
     */
    private String wWeek;
    /**
     * @Fields wMonth 月
     */
    private String wMonth;
    /**
     * @Fields fundSend 0 =扣补，1=基金，2=教育币
     */
    private String fundSend;
    /**
     * @Fields periodWeek 申请期数
     */
    private String periodWeek;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public LocalDateTime getDealDate() {
        return dealDate;
    }

    public void setDealDate(LocalDateTime dealDate) {
        this.dealDate = dealDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }
    
    public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getOutCode() {
        return outCode;
    }

    public void setOutCode(String outCode) {
        this.outCode = outCode;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public String getCheckerName() {
        return checkerName;
    }

    public void setCheckerName(String checkerName) {
        this.checkerName = checkerName;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getCreateNo() {
        return createNo;
    }

    public void setCreateNo(String createNo) {
        this.createNo = createNo;
    }

    public String getwWeek() {
        return wWeek;
    }

    public void setwWeek(String wWeek) {
        this.wWeek = wWeek;
    }

    public String getwMonth() {
        return wMonth;
    }

    public void setwMonth(String wMonth) {
        this.wMonth = wMonth;
    }

    public String getFundSend() {
        return fundSend;
    }

    public void setFundSend(String fundSend) {
        this.fundSend = fundSend;
    }

    public String getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
}