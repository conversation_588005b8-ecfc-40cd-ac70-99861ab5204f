package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;

public class SendInforExcelDO extends BaseDO {
    private  String companyCode;
    private String agentNo;
    private String comboReceiptno;
    private String receiptno;
    private String orderNo;
    private String goodScode;
    private BigDecimal orderQty;
    private Integer statue;
    private BigDecimal standardPrice;
    private Integer sendStatus;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getComboReceiptno() {
        return comboReceiptno;
    }

    public void setComboReceiptno(String comboReceiptno) {
        this.comboReceiptno = comboReceiptno;
    }

    public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGoodScode() {
        return goodScode;
    }

    public void setGoodScode(String goodScode) {
        this.goodScode = goodScode;
    }

    public BigDecimal getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(BigDecimal orderQty) {
        this.orderQty = orderQty;
    }

    public Integer getStatue() {
        return statue;
    }

    public void setStatue(Integer statue) {
        this.statue = statue;
    }

    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }
}
