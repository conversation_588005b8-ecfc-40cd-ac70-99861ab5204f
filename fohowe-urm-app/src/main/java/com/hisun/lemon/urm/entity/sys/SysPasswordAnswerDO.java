package com.hisun.lemon.urm.entity.sys;

import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class SysPasswordAnswerDO extends BaseDO {
	
	private Long id;
	private String userCode;
	private String questionNo;
	private String questionKey;
	private String questionAnswer;
	private LocalDateTime createTime;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getQuestionNo() {
		return questionNo;
	}
	public void setQuestionNo(String questionNo) {
		this.questionNo = questionNo;
	}
	public String getQuestionAnswer() {
		return questionAnswer;
	}
	public void setQuestionAnswer(String questionAnswer) {
		this.questionAnswer = questionAnswer;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getQuestionKey() {
		return questionKey;
	}
	public void setQuestionKey(String questionKey) {
		this.questionKey = questionKey;
	}
	@Override
	public String toString() {
		return "SysPasswordAnswerDO [id=" + id + ", userCode=" + userCode + ", questionNo=" + questionNo
				+ ", questionAnswer=" + questionAnswer + ", createTime=" + createTime + "]";
	}
	
}
