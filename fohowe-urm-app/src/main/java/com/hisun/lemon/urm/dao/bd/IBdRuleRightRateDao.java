/*
 * @ClassName IBdRuleRightRateDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-15 15:06:12
 */
package com.hisun.lemon.urm.dao.bd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.bd.BdRuleRightRateDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IBdRuleRightRateDao extends BaseDao<BdRuleRightRateDO> {

    /** 
     * @Title: getAllRecord 
     * @Description: 查询所有记录
     * @return
     * @return: List<BdRuleRightRateDO>
     */
    public List<BdRuleRightRateDO> getAllRecord();
}