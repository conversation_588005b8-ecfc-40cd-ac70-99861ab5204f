/*
 * @ClassName ISysRoleDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-16 11:08:14
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysRoleDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysRoleDao extends BaseDao<SysRoleDO> {
    /**
     * 
     * @Title: findRecord 
     * @Description: 根据角色名称模糊查询记录
     * @param roleName
     * @return
     * @return: List<SysRoleDO>
     */
    public List<SysRoleDO> findRecord(@Param("roleId") String roleId,@Param("roleName") String roleName);

    /**
     * 
     * @Title: findAllRecord 
     * @Description: 查询所有记录
     * @return
     * @return: List<SysRoleDO>
     */
    public List<SysRoleDO> findAllRecord(@Param("userCode") String userCode);
}