package com.hisun.lemon.urm.uitls.excel.fi;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.bns.enums.MsgInfo;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.entity.ticket.TicketsMeetingDO;
import com.hisun.lemon.urm.uitls.EncryptionUtils;

public class MeetingExcelImport extends BaseService{
    private Logger logger = LoggerFactory.getLogger(MeetingExcelImport.class);

    EncryptionUtils encryptionUtil;

    public final static Integer  MAX_ABLE_CNT=100000;

    public List<TicketsMeetingDO> importBatchPreAdd(InputStream in) {
        List<TicketsMeetingDO> list = new ArrayList<TicketsMeetingDO>();
        Workbook book = null;
        try {
            book = WorkbookFactory.create(in);
        } catch (Exception e) {
            LemonException.throwBusinessException(MsgInfo.IS_NOT_FILE.getMsgCd());
        }
        if (book == null) {
            LemonException.throwBusinessException(MsgInfo.IS_NOT_FILE.getMsgCd());
        }
        Sheet xssfSheet = book.getSheetAt(0);
        int rowNum = xssfSheet.getLastRowNum();
        //忽略表头
        for (int i = 0; i <= rowNum; i++) {
            if(i==0){
                logger.debug("跳过表头");
                continue;
            }
            if(rowNum>MAX_ABLE_CNT){
                logger.debug("导入数量大于{}条，忽略后续数据",MAX_ABLE_CNT);
                LemonException.throwBusinessException(MsgInfo.OVER_THEMAXIMUMNUMBER.getMsgCd());
                break;
            }
            TicketsMeetingDO importDO = new TicketsMeetingDO();
            Row row = xssfSheet.getRow(i);
            try {
                //会议类型 标题	  区域	城市	分公司	计划日期	计划人数	预计代金卷数量
                //门票金额	币种 	预计主讲老师	主讲嘉宾	参与老师  主讲翻译 参与翻译 参与经理/领导人 备注
                int index=0;
                String planMeetingType = this.getCellValue(row.getCell(index++));//会议类型
                String meetingTitle = this.getCellValue(row.getCell(index++));//标题
                String areaCode = this.getCellValue(row.getCell(index++));//区域
                String city = this.getCellValue(row.getCell(index++));//城市
                String companyCode = this.getCellValue(row.getCell(index++));//分公司
                String meetingDateStartStr = this.getCellValue(row.getCell(index++));//开始日期
                String meetingDateEndStr = this.getCellValue(row.getCell(index++));//结束日期
                String planNumStr = this.getCellValue(row.getCell(index++));//计划人数
                String planDealQtyStr = this.getCellValue(row.getCell(index++));//预计代金卷数量
                String planAmountStr = this.getCellValue(row.getCell(index++));//门票金额
                String planCurrency = this.getCellValue(row.getCell(index++));//币种
                String planLecturer = this.getCellValue(row.getCell(index++));//预计主讲老师
                String planGuests = this.getCellValue(row.getCell(index++));//主讲嘉宾
                String planTeacher = this.getCellValue(row.getCell(index++));//参与老师
                String planTranslator = this.getCellValue(row.getCell(index++));//主讲翻译
                String planTransPart = this.getCellValue(row.getCell(index++));//参与翻译
                String planManager = this.getCellValue(row.getCell(index++));//参与经理/领导人
                String memo = this.getNameCellValue(row.getCell(index++));//备注
                importDO.setMeetingNo(IdGenUtils.generateIdWithDate(URMConstants.TM_MEETING_KEY, URMConstants.TM_MEETING_KEY_PREFIX, 5));
                importDO.setPlanMeetingType(planMeetingType);
                String[] s = planMeetingType.split("_");
                importDO.setPlanFirstType(s[0]);
                importDO.setMeetingTitle(meetingTitle);
                importDO.setAreaCode(areaCode);
                importDO.setCity(city);
                importDO.setCompanyCode(companyCode);
                importDO.setMeetingDateStart(getDateTime(meetingDateStartStr));
                importDO.setMeetingDateEnd(getDateTime(meetingDateEndStr));
                Integer planNum =0;
                Integer planDealQty =0;
                if(JudgeUtils.isNotBlank(planNumStr))
                    planNum = Integer.parseInt(planNumStr);
                if(JudgeUtils.isNotBlank(planDealQtyStr))
                    planDealQty = Integer.parseInt(planDealQtyStr);
                BigDecimal planAmount =BigDecimal.ZERO;
                if(JudgeUtils.isNotBlank(planAmountStr))
                    planAmount = new BigDecimal(planAmountStr);
                importDO.setPlanAmount(planAmount);
                importDO.setPlanCurrency(planCurrency);
                importDO.setPlanLecturer(planLecturer);
                importDO.setPlanGuests(planGuests);
                importDO.setPlanTeacher(planTeacher);
                importDO.setPlanTranslator(planTranslator);
                importDO.setPlanTransPart(planTransPart);
                importDO.setPlanManager(planManager);
                importDO.setMemo(memo);
                importDO.setVcStatus(0);
                importDO.setPlanNum(planNum);
                importDO.setPlanDealQty(planDealQty);
                importDO.setCreaterCode(LemonUtils.getUserId());
                importDO.setCreateTime(LocalDateTime.now());
            } catch (Exception e) {
                logger.info("多少行"+i+"报错"+"编号:"+this.getCellValue(row.getCell(0)));
                e.printStackTrace();
                LemonException.throwBusinessException(MsgInfo.IMPORT_FILE_FAILED.getMsgCd());
            }
            if(JudgeUtils.isNotBlank(importDO.getMeetingNo())) {
                list.add(importDO);
            }
        }
        return list;
    }

    private static LocalDateTime getDateTime(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr); // 解析日期部分
        LocalTime time = LocalTime.of(0, 0, 0); // 设置时间部分为 00:00:00
        return LocalDateTime.of(date, time); // 将日期和时间组合成 LocalDateTime 对象
    }

    public String getCellValue(Cell cell){
        if(cell==null){
            return null;
        }
        String str = null;
        if (cell.getCellTypeEnum() == CellType.BOOLEAN){
            return null;
        }else if (cell.getCellTypeEnum() == CellType.NUMERIC){
            short format = cell.getCellStyle().getDataFormat();
            SimpleDateFormat sdf = null;
            if (format == 14 || format == 31 || format == 57 || format == 58
                    || (176<=format && format<=178) || (182<=format && format<=196)
                    || (210<=format && format<=213) || (208==format ) ) { // 日期
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                    str = sdf.format(date);
                }else {
                    str ="0";
                }
            } else if (format == 20 || format == 32 || format==183 || (200<=format && format<=209) ) { // 时间
                sdf = new SimpleDateFormat("HH:mm");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                    str = sdf.format(date);
                }else {
                    str ="0";
                }
            } else { // 不是日期格式
                DecimalFormat df = new DecimalFormat("0.##");
                str=df.format(cell.getNumericCellValue());
            }
        }else if (cell.getCellTypeEnum() == CellType.STRING){
            str=cell.getStringCellValue();
        }else{
            return null;
        }
        return str.replaceAll("[\\s\\?]", "").replace("　", "");
    }

    /**
     * 保留空格
     * @param cell
     * @return
     */
    public String getNameCellValue(Cell cell){
        if(cell==null){
            return null;
        }
        String str = null;

        if (cell.getCellTypeEnum() == CellType.BOOLEAN){
            return null;
        }else if (cell.getCellTypeEnum() == CellType.NUMERIC){
            short format = cell.getCellStyle().getDataFormat();
            SimpleDateFormat sdf = null;
            if (format == 14 || format == 31 || format == 57 || format == 58
                    || (176<=format && format<=178) || (182<=format && format<=196)
                    || (210<=format && format<=213) || (208==format ) ) { // 日期
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                    str = sdf.format(date);
                }else {
                    str ="0";
                }
            } else if (format == 20 || format == 32 || format==183 || (200<=format && format<=209) ) { // 时间
                sdf = new SimpleDateFormat("HH:mm");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                    str = sdf.format(date);
                }else {
                    str ="0";
                }
            } else { // 不是日期格式
                DecimalFormat df = new DecimalFormat("0.##");
                str=df.format(cell.getNumericCellValue());
            }
        }else if (cell.getCellTypeEnum() == CellType.STRING){
            str=cell.getStringCellValue();
        }else{
            return null;
        }
        return str.replaceAll("[\\s\\?]", "");
    }

    public EncryptionUtils getEncryptionUtil() {
        return encryptionUtil;
    }

    public void setEncryptionUtil(EncryptionUtils encryptionUtil) {
        this.encryptionUtil = encryptionUtil;
    }
}
