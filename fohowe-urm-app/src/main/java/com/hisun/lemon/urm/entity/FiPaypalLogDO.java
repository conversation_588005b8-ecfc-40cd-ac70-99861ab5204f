/*
 * @ClassName FiPaypalLogDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiPaypalLogDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields orderNo 订单编号
     */
    private String orderNo;
    /**
     * @Fields buyerCode 购买者编码
     */
    private String buyerCode;
    /**
     * @Fields companyCode 分公司编码
     */
    private String companyCode;
    /**
     * @Fields outTradeNo 系统交易流水号
     */
    private String outTradeNo;
    /**
     * @Fields returnTime 支付结果返回时间
     */
    private LocalDateTime returnTime;
    /**
     * @Fields payResult 支付结果：0：未成功，1：成功
     */
    private String payResult;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createUser 创建人
     */
    private String createUser;
    /**
     * @Fields status 操作状态,1:充值成功 
     */
    private String status;
    /**
     * @Fields localCurrency 本地货币代码
     */
    private String localCurrency;
    /**
     * @Fields localMoney 本地货币金额
     */
    private BigDecimal localMoney;
    /**
     * @Fields rate 汇率
     */
    private BigDecimal rate;
    /**
     * @Fields transAmountUsd 实际交易金额(usd)
     */
    private BigDecimal transAmountUsd;
    /**
     * @Fields transAmountLocale 实际交易金额(本地)
     */
    private BigDecimal transAmountLocale;
    /**
     * @Fields buyNo 购货订单号
     */
    private String buyNo;
    /**
     * @Fields regNo 注册订单号
     */
    private String regNo;
    /**
     * @Fields checkUser 审核人
     */
    private String checkUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBuyerCode() {
        return buyerCode;
    }

    public void setBuyerCode(String buyerCode) {
        this.buyerCode = buyerCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public LocalDateTime getReturnTime() {
        return returnTime;
    }

    public void setReturnTime(LocalDateTime returnTime) {
        this.returnTime = returnTime;
    }

    public String getPayResult() {
        return payResult;
    }

    public void setPayResult(String payResult) {
        this.payResult = payResult;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLocalCurrency() {
        return localCurrency;
    }

    public void setLocalCurrency(String localCurrency) {
        this.localCurrency = localCurrency;
    }

    public BigDecimal getLocalMoney() {
        return localMoney;
    }

    public void setLocalMoney(BigDecimal localMoney) {
        this.localMoney = localMoney;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getTransAmountUsd() {
        return transAmountUsd;
    }

    public void setTransAmountUsd(BigDecimal transAmountUsd) {
        this.transAmountUsd = transAmountUsd;
    }

    public BigDecimal getTransAmountLocale() {
        return transAmountLocale;
    }

    public void setTransAmountLocale(BigDecimal transAmountLocale) {
        this.transAmountLocale = transAmountLocale;
    }

    public String getBuyNo() {
        return buyNo;
    }

    public void setBuyNo(String buyNo) {
        this.buyNo = buyNo;
    }

    public String getRegNo() {
        return regNo;
    }

    public void setRegNo(String regNo) {
        this.regNo = regNo;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }
}