package com.hisun.lemon.urm.uitls.excel.st;

import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.OrderType;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.pd.SendInfoSummaryDTO;
import com.hisun.lemon.urm.entity.pd.SendInfoSummaryRspDTO;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils.ExcelType;

public class SendInfoSummaryExcelExporter {
    public DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public void export(SendInfoSummaryRspDTO sendInfoSummaryRspDTO, HttpServletResponse response) {
        String fileName = "分公司发货跨境发货单";
        try {
            ExcelHelperUtils.setResponseHead(fileName, response, ExcelType.XLSX);
            addCell(response.getOutputStream(), sendInfoSummaryRspDTO);
        } catch (IOException e) {
            e.printStackTrace();
            LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
    }

    public void addCell(OutputStream oss, SendInfoSummaryRspDTO sendInfoSummaryRspDTO) throws IOException {
    	Map<String,Integer> totalNumMap1 = new HashMap<String,Integer>();
    	
        XSSFWorkbook workbook = null;
        workbook = new XSSFWorkbook();

        int rowNums = 0;
        String sheetName = "分公司发货跨境发货单";
        XSSFSheet sheet = workbook.createSheet(sheetName);
        XSSFCellStyle columnCenterStyle = this.getColumnCenterStyle(workbook);// 获取中间对齐样式对象

        XSSFRow rowRowName = sheet.createRow(rowNums);
        rowRowName.setHeight((short) 500);
        Iterator<String> it = sendInfoSummaryRspDTO.getGoodSet().iterator();
        int i=0;
        XSSFCell cellRowName0 = rowRowName.createCell(i);
        cellRowName0.setCellType(CellType.STRING);
        cellRowName0.setCellValue("报单代办处编号");
        cellRowName0.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 10 * 2 * 256);
        i++;
        XSSFCell cellRowName1 = rowRowName.createCell(i);
        cellRowName1.setCellType(CellType.STRING);
        cellRowName1.setCellValue("经销商编号");
        cellRowName1.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 10 * 2 * 256);
        i++;
        XSSFCell cellRowName2 = rowRowName.createCell(i);
        cellRowName2.setCellType(CellType.STRING);
        cellRowName2.setCellValue("经销商姓名");
        cellRowName2.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 10 * 2 * 256);
        i++;
        XSSFCell cellRowName6 = rowRowName.createCell(i);
        cellRowName6.setCellType(CellType.STRING);
        cellRowName6.setCellValue("单号");
        cellRowName6.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 13 * 2 * 256);
        i++;
        XSSFCell cellRowName61 = rowRowName.createCell(i);
        cellRowName61.setCellType(CellType.STRING);
        cellRowName61.setCellValue("订单类型");
        cellRowName61.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 13 * 2 * 256);
        i++;
        XSSFCell cellRowName3 = rowRowName.createCell(i);
        cellRowName3.setCellType(CellType.STRING);
        cellRowName3.setCellValue("订单金额");
        cellRowName3.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 6 * 2 * 256);
        i++;
        XSSFCell cellRowName4 = rowRowName.createCell(i);
        cellRowName4.setCellType(CellType.STRING);
        cellRowName4.setCellValue("F$");
        cellRowName4.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 6 * 2 * 256);
        i++;
        XSSFCell cellRowName5 = rowRowName.createCell(i);
        cellRowName5.setCellType(CellType.STRING);
        cellRowName5.setCellValue("支付FB");
        cellRowName5.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 6 * 2 * 256);       
        i++;
        while(it.hasNext()) {
            XSSFCell cellRowName = rowRowName.createCell(i);
            i++;
            cellRowName.setCellType(CellType.STRING);
            XSSFRichTextString text = new XSSFRichTextString(it.next());
            cellRowName.setCellValue(text.toString());
            cellRowName.setCellStyle(columnCenterStyle);
            sheet.setColumnWidth(i, 6 * 2 * 256);
        }
        XSSFCell cellRowName7 = rowRowName.createCell(i);
        cellRowName7.setCellType(CellType.STRING);
        cellRowName7.setCellValue("备注");
        cellRowName7.setCellStyle(columnCenterStyle);
        sheet.setColumnWidth(i, 10 * 2 * 256);

        for (SendInfoSummaryDTO o : sendInfoSummaryRspDTO.getSendInfoSummaryList()) {
            rowNums++;// 行数增1
            XSSFRow row = sheet.createRow(rowNums);
            row.setHeight((short) 300);
            int j = 0;
            row.createCell(j).setCellValue(o.getAgentNo());
            j++;
            row.createCell(j).setCellValue(o.getMemberNo());
            j++;
            row.createCell(j).setCellValue(o.getMemberName());
            j++;
            row.createCell(j).setCellValue(o.getBiOrderNo());
            j++;
            Integer orderTypeInt=JudgeUtils.isBlank(o.getOrderType())? null:Integer.parseInt(o.getOrderType());
            row.createCell(j).setCellValue(orderTypeInt==null ? "":OrderType.getByCode(orderTypeInt).getName());
            j++;
            row.createCell(j,CellType.NUMERIC).setCellValue(JudgeUtils.isNull(o.getTotalAmount()) ? 0 : o.getTotalAmount().doubleValue());
            j++;
            row.createCell(j,CellType.NUMERIC).setCellValue(JudgeUtils.isNull(o.getPayAmount()) ? 0 : o.getPayAmount().doubleValue());
            j++;
            row.createCell(j,CellType.NUMERIC).setCellValue(JudgeUtils.isNull(o.getPayFb()) ? 0 : o.getPayFb().doubleValue());
            j++;
            Iterator<String> it1 = sendInfoSummaryRspDTO.getGoodSet().iterator();
            while(it1.hasNext()) {
            	String goodsCode = it1.next();
            	int number = 0;
            	if(o.getGoodsMap().containsKey(goodsCode)) {
            		number = o.getGoodsMap().get(goodsCode);
            	}
            	row.createCell(j).setCellValue(number+"");
            	j++;
            	if(totalNumMap1.containsKey(goodsCode)) {
            		totalNumMap1.replace(goodsCode, totalNumMap1.get(goodsCode)+number);
            	}else {
            		totalNumMap1.put(goodsCode, number);
            	}
            }
            row.createCell(j).setCellValue(o.getMemo());
        }
        
        rowNums++;// 行数增1
        XSSFRow row = sheet.createRow(rowNums);
        row.setHeight((short) 300);
        int m = 0;
        sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 0, 7));
        XSSFCell cell = row.createCell(m);
        cell.setCellValue("合计");
        cell.setCellStyle(this.getColumnRightStyle(workbook));
        m=m+8;
        Iterator<String> it2 = sendInfoSummaryRspDTO.getGoodSet().iterator();
        while(it2.hasNext()) {
        	row.createCell(m).setCellValue(totalNumMap1.get(it2.next())+"");
        	m++;
        }
        row.createCell(m).setCellValue("");
        
        // 第二个页签
        Map<String,Integer> totalNumMap2 = new HashMap<String,Integer>();
        rowNums = 0;
        String sheetName2 = "自选套餐产品汇总";
        XSSFSheet sheet2 = workbook.createSheet(sheetName2);

        XSSFRow rowRowName2 = sheet2.createRow(rowNums);
        rowRowName2.setHeight((short) 500);
        Iterator<String> itp2 = sendInfoSummaryRspDTO.getPackageSet().iterator();
        i=0;
        XSSFCell cellRowName02 = rowRowName2.createCell(i);
        cellRowName02.setCellType(CellType.STRING);
        cellRowName02.setCellValue("报单代办处编号");
        cellRowName02.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 10 * 2 * 256);
        i++;
        XSSFCell cellRowName12 = rowRowName2.createCell(i);
        cellRowName12.setCellType(CellType.STRING);
        cellRowName12.setCellValue("经销商编号");
        cellRowName12.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 10 * 2 * 256);
        i++;
        XSSFCell cellRowName22 = rowRowName2.createCell(i);
        cellRowName22.setCellType(CellType.STRING);
        cellRowName22.setCellValue("经销商姓名");
        cellRowName22.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 10 * 2 * 256);
        i++;
        XSSFCell cellRowName62 = rowRowName2.createCell(i);
        cellRowName62.setCellType(CellType.STRING);
        cellRowName62.setCellValue("单号");
        cellRowName62.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 13 * 2 * 256);
        i++;
        XSSFCell cellRowName612 = rowRowName2.createCell(i);
        cellRowName612.setCellType(CellType.STRING);
        cellRowName612.setCellValue("订单类型");
        cellRowName612.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 13 * 2 * 256);
        i++;
        XSSFCell cellRowName32 = rowRowName2.createCell(i);
        cellRowName32.setCellType(CellType.STRING);
        cellRowName32.setCellValue("订单金额");
        cellRowName32.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 6 * 2 * 256);
        i++;
        XSSFCell cellRowName42 = rowRowName2.createCell(i);
        cellRowName42.setCellType(CellType.STRING);
        cellRowName42.setCellValue("F$");
        cellRowName42.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 6 * 2 * 256);
        i++;
        XSSFCell cellRowName52 = rowRowName2.createCell(i);
        cellRowName52.setCellType(CellType.STRING);
        cellRowName52.setCellValue("支付FB");
        cellRowName52.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 6 * 2 * 256);       
        i++;
        while(itp2.hasNext()) {
            XSSFCell cellRowName222 = rowRowName2.createCell(i);
            i++;
            cellRowName222.setCellType(CellType.STRING);
            XSSFRichTextString text = new XSSFRichTextString(itp2.next());
            cellRowName222.setCellValue(text.toString());
            cellRowName222.setCellStyle(columnCenterStyle);
            sheet2.setColumnWidth(i, 6 * 2 * 256);
        }
        XSSFCell cellRowName72 = rowRowName2.createCell(i);
        cellRowName72.setCellType(CellType.STRING);
        cellRowName72.setCellValue("备注");
        cellRowName72.setCellStyle(columnCenterStyle);
        sheet2.setColumnWidth(i, 10 * 2 * 256);

        for (SendInfoSummaryDTO pck : sendInfoSummaryRspDTO.getPackageSummaryList()) {
            rowNums++;// 行数增1
            XSSFRow row2 = sheet2.createRow(rowNums);
            row2.setHeight((short) 300);
            int j = 0;
            row2.createCell(j).setCellValue(pck.getAgentNo());
            j++;
            row2.createCell(j).setCellValue(pck.getMemberNo());
            j++;
            row2.createCell(j).setCellValue(pck.getMemberName());
            j++;
            row2.createCell(j).setCellValue(pck.getBiOrderNo());
            j++;
            Integer orderTypeInt=JudgeUtils.isBlank(pck.getOrderType())? null:Integer.parseInt(pck.getOrderType());
            row2.createCell(j).setCellValue(orderTypeInt==null ? "":OrderType.getByCode(orderTypeInt).getName());
            j++;
            row2.createCell(j,CellType.NUMERIC).setCellValue(JudgeUtils.isNull(pck.getTotalAmount()) ? 0 : pck.getTotalAmount().doubleValue());
            j++;
            row2.createCell(j,CellType.NUMERIC).setCellValue(JudgeUtils.isNull(pck.getPayAmount()) ? 0 : pck.getPayAmount().doubleValue());
            j++;
            row2.createCell(j,CellType.NUMERIC).setCellValue(JudgeUtils.isNull(pck.getPayFb()) ? 0 : pck.getPayFb().doubleValue());
            j++;
            Iterator<String> it1 = sendInfoSummaryRspDTO.getPackageSet().iterator();
            while(it1.hasNext()) {
            	String goodsCode = it1.next();
            	int number = 0;
            	if(pck.getGoodsMap().containsKey(goodsCode)) {
            		number = pck.getGoodsMap().get(goodsCode);
            	}
            	row2.createCell(j).setCellValue(number+"");
            	j++;
            	if(totalNumMap2.containsKey(goodsCode)) {
            		totalNumMap2.replace(goodsCode, totalNumMap2.get(goodsCode)+number);
            	}else {
            		totalNumMap2.put(goodsCode, number);
            	}
            }
            row2.createCell(j).setCellValue(pck.getMemo());
        }
        
        rowNums++;// 行数增1
        XSSFRow row2 = sheet2.createRow(rowNums);
        row2.setHeight((short) 300);
         m = 0;
        sheet2.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 0, 7));
        XSSFCell cell2 = row2.createCell(m);
        cell2.setCellValue("合计");
        cell2.setCellStyle(this.getColumnRightStyle(workbook));
        m=m+8;
        Iterator<String> itp22 = sendInfoSummaryRspDTO.getPackageSet().iterator();
        while(itp22.hasNext()) {
        	row2.createCell(m).setCellValue(totalNumMap2.get(itp22.next())+"");
        	m++;
        }
        row2.createCell(m).setCellValue("");
        
        workbook.write(oss);
    }

    public XSSFCellStyle getColumnCenterStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    public XSSFCellStyle getColumnRedStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setColor(XSSFFont.COLOR_RED);
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
    
    public XSSFCellStyle getColumnRightStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
}
