package com.hisun.lemon.urm.uitls.excel.pd;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.dto.al.CharacterQueryListRspDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryReqDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryRspDTO;
import com.hisun.lemon.urm.entity.bd.BdTourismInformationBO;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.ConvertUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class TourismInfoExcelExporter extends URMExcelExportFactory{
	private static final Logger logger = LoggerFactory.getLogger(TourismInfoExcelExporter.class);
	
	private ILanguageService languageService;

	public ILanguageService getLanguageService() {
		return languageService;
	}

	public void setLanguageService(ILanguageService languageService) {
		this.languageService = languageService;
	}
	
	public TourismInfoExcelExporter(ILanguageService languageService) {
		this.languageService=languageService;
	}
	
	public TourismInfoExcelExporter() {
		
	}
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="旅游人员信息";
		//增加原资格人信息（经销商编码、姓名、代办处、分公司、获取来源
        String[] colNames=new String[] {
        		"原资格经销商编码","原资格姓名","原资格代办处","原资格分公司","原资格获取来源",
				"名额确认","旅游人员确认","分公司","代办处","经销商编号","经销商姓名","个人级别","经销商名字（英语）","经销商姓（英语）","经销商名（英语）",
				"性别","国籍","出生日期","护照号码","护照发放日期","护照有效日期","是否穆斯林","是否夫妻同行","妻子护照号","是否儿童同行","儿童护照号","旅游路线"
				};
		String multiLangFileName="menu.tourismInformation";
		String[] multiLangColNames=new String[] {
				"原资格经销商编码","原资格姓名","原资格代办处","原资格分公司","原资格获取来源",
				"名额确认","旅游人员确认","column.company","column.agencyNo","common.jiang_xiao_shang_bian_hao","column.memName","column.memType","经销商名字（英语）","经销商姓（英语）","经销商名（英语）",
				"column.sex","国籍","出生日期","护照号码","护照发放日期","护照有效日期","是否穆斯林","是否夫妻同行","妻子护照号","是否儿童同行","儿童护照号","旅游路线"
		};
		 
		Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
		if(result!=null) {
			colNames=(String[]) result.get("titles");
			fileName=(String) result.get("fileName");
		}
		
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		List<BdTourismInformationBO> dataList=(List<BdTourismInformationBO>)obj;
		CharacterQueryReqDTO reqDTO = new CharacterQueryReqDTO();
        String charKey = "listvalue.trainee.member,listvalue.qualified.member,listvalue.emerald.member,"
        		+ "listvalue.sapphire.member,listvalue.diamond.member,column.woman,common.nan,column.isNo,column.isYes,"
        		+ "fohowe-store.san_zuan_shi_jing_xiao_shang,fohowe-store.wu_zuan_shi_jing_xiao_shang,fohowe-store.qi_zuan_shi_jing_xiao_shang,"
        		+ "fohow.ambassador,fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang,common.wuwuwu";
        
        String langCode = LemonUtils.getLocale().getLanguage() + "-" + LemonUtils.getLocale().getCountry();
        reqDTO.setCharacterKey(charKey);
        reqDTO.setLangCode(langCode);
        CharacterQueryListRspDTO rspDto = languageService.languageQueryList(reqDTO);
        List<CharacterQueryRspDTO> valueList = rspDto.getCharValueList();
        //将列表转化为Map
        Map<String, CharacterQueryRspDTO> map = ConvertUtils.list2Map3(valueList, "getCharacterKey", CharacterQueryRspDTO.class);
		
        List<BdTourismInformationBO> newLists = new ArrayList<BdTourismInformationBO>();
        
        List<Long> newListIds = new ArrayList<Long>();
//		BeanUtils.copyProperties(newLists,dataList);
        for(BdTourismInformationBO item:dataList) {
        	//logger.info(item.toString());
        	if(!newListIds.contains(item.getTourId())) {
        		newListIds.add(item.getTourId());
        		newLists.add(item);
//        		logger.info(item.toString());
        		if(JudgeUtils.isNotBlank(item.getWifePassport())) {
        			//如果是夫妻两,则要排在一起
        			for(BdTourismInformationBO item2:dataList) {
        				if(!newListIds.contains(item2.getTourId())) {
        					if(JudgeUtils.equals(item.getWifePassport(), item2.getPassportsNo())) {
        						newListIds.add(item2.getTourId());
        						newLists.add(item2);
        					}
        				}
        			}
        		}
        	}
        }
        logger.info("newListIds:"+newListIds.size());
        
		for(BdTourismInformationBO item:newLists) {
				XSSFRow row = sheet.createRow(beginRow++);
				int index=0;
				
				row.createCell(index++).setCellValue(item.getTourMemberNo());
				row.createCell(index++).setCellValue(item.getTourMemberName());
				row.createCell(index++).setCellValue(item.getTourAgentNo());
				row.createCell(index++).setCellValue(item.getTourCompanyCode());
				row.createCell(index++).setCellValue(item.getSalesPromotion());
		        String vcStatusStr = "";
				if (item.getVcStatus()==null){
					vcStatusStr = "";
				}
				else {
					vcStatusStr = "参与旅游";
				}
				String fiCheckeCodeStr = "";
				if (item.getFiCheckeCode()==null){
					fiCheckeCodeStr = "";
				}else {
					if(item.getFiCheckeCode() ==1) {
						fiCheckeCodeStr = "本人使用";
					}
					if(item.getFiCheckeCode() ==2) {
						fiCheckeCodeStr = "转让经销商";
					}
					if(item.getFiCheckeCode() ==3) {
						fiCheckeCodeStr = "转让非经销商";
					}
				}
		        row.createCell(index++).setCellValue(vcStatusStr);
		        row.createCell(index++).setCellValue(fiCheckeCodeStr);
				row.createCell(index++).setCellValue(item.getCompanyCode()==null?"":item.getCompanyCode());
				row.createCell(index++).setCellValue(item.getAgentNo()==null?"":item.getAgentNo());
				row.createCell(index++).setCellValue(item.getMemberNo()==null?"":item.getMemberNo().trim());
				row.createCell(index++).setCellValue(item.getMemberName()==null?"":item.getMemberName().trim());
				String memberType="";
				if(item.getMemberType() != null) {
					memberType= ConvertUtils.languageConversion(map, item.getMemberType());
					if(JudgeUtils.isBlank(memberType)) {
						memberType=item.getMemberType();
					}
				}
				row.createCell(index++).setCellValue(memberType);
//				row.createCell(index++).setCellValue(item.getMemberLastName()==null?"":item.getMemberLastName());
//				row.createCell(index++).setCellValue(item.getMenberFirstName()==null?"":item.getMenberFirstName());
//				row.createCell(index++).setCellValue(item.getPassportsLastName()==null?"":item.getPassportsLastName());
				row.createCell(index++).setCellValue(item.getPassportsLastName()== null && item.getPassportsFirstName()==null?"":item.getPassportsLastName()+" "+item.getPassportsFirstName());
				row.createCell(index++).setCellValue(item.getPassportsLastName());
				row.createCell(index++).setCellValue(item.getPassportsFirstName());
				String sexStr=item.getSex();
				if(item.getSex() != null 
						&& (JudgeUtils.equals(item.getSex(), "1") || JudgeUtils.equals(item.getSex(), "女"))) {
					sexStr=map.get("column.woman").getCharacterValue();
				}
				if(item.getSex() != null 
						&& (JudgeUtils.equals(item.getSex(), "0") || JudgeUtils.equals(item.getSex(), "男"))) {
					sexStr=map.get("common.nan").getCharacterValue();
				}
				if (item.getSex() == null ){sexStr = "";}
				row.createCell(index++).setCellValue(sexStr);
				
				row.createCell(index++).setCellValue(item.getMenberNationality()==null?"":item.getMenberNationality());
				row.createCell(index++).setCellValue(item.getMenberBirthday()==null?"":item.getMenberBirthday());
				row.createCell(index++).setCellValue(item.getPassportsNo()==null?"":item.getPassportsNo());
				row.createCell(index++).setCellValue(item.getPassportsData()==null?"":item.getPassportsData().format(yms));
				row.createCell(index++).setCellValue(item.getPassportsEffectiveData()==null?"":item.getPassportsEffectiveData().format(yms));
				String fiVcStatus="否";
				if(item.getFiVcStatus() != null && item.getFiVcStatus() == 0) {
					fiVcStatus=map.get("column.isNo").getCharacterValue();
				}
				if(item.getFiVcStatus() != null && item.getFiVcStatus() == 1) {
					fiVcStatus=map.get("column.isYes").getCharacterValue();
				}
				if (item.getFiVcStatus() == null ){fiVcStatus = "";}
				row.createCell(index++).setCellValue(fiVcStatus);
				String fiHusbandPeer="否";
				if(item.getFiHusbandPeer() != null && item.getFiHusbandPeer() == 0) {
					fiHusbandPeer=map.get("column.isNo").getCharacterValue();
				}
				if(item.getFiHusbandPeer() != null && item.getFiHusbandPeer() == 1) {
					fiHusbandPeer=map.get("column.isYes").getCharacterValue();
				}
				if (item.getFiHusbandPeer() == null ){fiHusbandPeer = "";}
				row.createCell(index++).setCellValue(fiHusbandPeer);
				row.createCell(index++).setCellValue(item.getWifePassport()==null?"":item.getWifePassport());
				String fiChildren="否";
				if(item.getFiChildren() != null && item.getFiChildren() == 0) {
					fiChildren=map.get("column.isNo").getCharacterValue();
				}
				if(item.getFiChildren() != null && item.getFiChildren() == 1) {
					fiChildren=map.get("column.isYes").getCharacterValue();
				}
				if (item.getFiChildren() == null ){fiChildren = "";}
				row.createCell(index++).setCellValue(fiChildren);
				row.createCell(index++).setCellValue(item.getChildrenPassportNo()==null?"":item.getChildrenPassportNo());
				row.createCell(index++).setCellValue(item.getLineStatus());

		}
		
	}
	public static TourismPromExcelExporter builder() {
		return new TourismPromExcelExporter();
	}
}
