package com.hisun.lemon.urm.dao.mi;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.successor.MiSuccessorReqDTO;
import com.hisun.lemon.urm.entity.mi.MiSuccessorDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import org.apache.ibatis.annotations.Mapper;
@Mapper
public interface IMiSuccessorDao extends BaseDao<MiSuccessorDO> {


    int deleteByPrimaryKey(Long id);

    int insert(MiSuccessorDO row);

    int insertSelective(MiSuccessorReqDTO row);

    List<MiSuccessorDO> selectByExample(MiSuccessorReqDTO example);

    MiSuccessorDO selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(MiSuccessorReqDTO row);

    int updateByPrimaryKey(MiSuccessorDO row);
}