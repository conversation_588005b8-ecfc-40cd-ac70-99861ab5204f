package com.hisun.lemon.urm.entity.fi;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class FiAcUsdtDO extends BaseDO {
	private Long id;

	private String periodWeek;

	private String orderNo;

	private String companyCode;

	private String agentNo;

	private String userCode;

	private String serial;

	private String usdtCode;

	private String coinType;

	private String transferType;

	private BigDecimal transferAmount;

	private String localCurrency;

	private BigDecimal localMoney;

	private BigDecimal rate;

	private BigDecimal fdMoney;

	private BigDecimal rewardRate;

	private String comments;

	private String transferStatus;
	
	private String status;

	private LocalDateTime transferTime;

	private BigDecimal transactionFee;

	private String payee;

	private String dataType;

	private String address;

	private String stockType;

	private String createrCode;

	private String createrName;

	private LocalDateTime createTime;

	private String checkerCode;

	private String checkerName;

	private LocalDateTime checkeTime;

	private String checkType;

	private LocalDateTime dealDate;

	private String remark;

	private LocalDateTime cancelTime;

	private String cancelCode;

	private String requestId;
	/**
     * @Fields fiCheckStatus 财务确认状态 0:未确认;1:部分确认;2:已确认
     */
    private String fiCheckStatus;
    /**
     * @Fields ficheckeTime 财务确认时间
     */
    private LocalDateTime ficheckeTime;
    /**
     * @Fields ficheckerCode 财务确认人
     */
    private String ficheckerCode;
	/**
     * @Fields memo 备注
     */
    private String memo;
    
    
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getPeriodWeek() {
		return periodWeek;
	}

	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek == null ? null : periodWeek.trim();
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo == null ? null : orderNo.trim();
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode == null ? null : companyCode.trim();
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo == null ? null : agentNo.trim();
	}

	public String getUserCode() {
		return userCode;
	}

	public void setUserCode(String userCode) {
		this.userCode = userCode == null ? null : userCode.trim();
	}

	public String getSerial() {
		return serial;
	}

	public void setSerial(String serial) {
		this.serial = serial == null ? null : serial.trim();
	}

	public String getUsdtCode() {
		return usdtCode;
	}

	public void setUsdtCode(String usdtCode) {
		this.usdtCode = usdtCode == null ? null : usdtCode.trim();
	}

	public String getCoinType() {
		return coinType;
	}

	public void setCoinType(String coinType) {
		this.coinType = coinType == null ? null : coinType.trim();
	}

	public String getTransferType() {
		return transferType;
	}

	public void setTransferType(String transferType) {
		this.transferType = transferType == null ? null : transferType.trim();
	}

	public BigDecimal getTransferAmount() {
		return transferAmount;
	}

	public void setTransferAmount(BigDecimal transferAmount) {
		this.transferAmount = transferAmount;
	}

	public String getLocalCurrency() {
		return localCurrency;
	}

	public void setLocalCurrency(String localCurrency) {
		this.localCurrency = localCurrency;
	}

	public BigDecimal getLocalMoney() {
		return localMoney;
	}

	public void setLocalMoney(BigDecimal localMoney) {
		this.localMoney = localMoney;
	}

	public BigDecimal getRate() {
		return rate;
	}

	public void setRate(BigDecimal rate) {
		this.rate = rate;
	}

	public BigDecimal getFdMoney() {
		return fdMoney;
	}

	public void setFdMoney(BigDecimal fdMoney) {
		this.fdMoney = fdMoney;
	}

	public BigDecimal getRewardRate() {
		return rewardRate;
	}

	public void setRewardRate(BigDecimal rewardRate) {
		this.rewardRate = rewardRate;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments == null ? null : comments.trim();
	}

	public String getTransferStatus() {
		return transferStatus;
	}

	public void setTransferStatus(String transferStatus) {
		this.transferStatus = transferStatus == null ? null : transferStatus.trim();
	}

	public LocalDateTime getTransferTime() {
		return transferTime;
	}

	public void setTransferTime(LocalDateTime transferTime) {
		this.transferTime = transferTime;
	}

	public BigDecimal getTransactionFee() {
		return transactionFee;
	}

	public void setTransactionFee(BigDecimal transactionFee) {
		this.transactionFee = transactionFee;
	}

	public String getPayee() {
		return payee;
	}

	public void setPayee(String payee) {
		this.payee = payee == null ? null : payee.trim();
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType == null ? null : dataType.trim();
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address == null ? null : address.trim();
	}

	public String getStockType() {
		return stockType;
	}

	public void setStockType(String stockType) {
		this.stockType = stockType == null ? null : stockType.trim();
	}

	public String getCreaterCode() {
		return createrCode;
	}

	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode == null ? null : createrCode.trim();
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName == null ? null : createrName.trim();
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getCheckerCode() {
		return checkerCode;
	}

	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode == null ? null : checkerCode.trim();
	}

	public String getCheckerName() {
		return checkerName;
	}

	public void setCheckerName(String checkerName) {
		this.checkerName = checkerName == null ? null : checkerName.trim();
	}

	public LocalDateTime getCheckeTime() {
		return checkeTime;
	}

	public void setCheckeTime(LocalDateTime checkeTime) {
		this.checkeTime = checkeTime;
	}

	public String getCheckType() {
		return checkType;
	}

	public void setCheckType(String checkType) {
		this.checkType = checkType == null ? null : checkType.trim();
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark == null ? null : remark.trim();
	}

	public LocalDateTime getCancelTime() {
		return cancelTime;
	}

	public void setCancelTime(LocalDateTime cancelTime) {
		this.cancelTime = cancelTime;
	}

	public String getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode == null ? null : cancelCode.trim();
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId == null ? null : requestId.trim();
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public LocalDateTime getDealDate() {
		return dealDate;
	}

	public void setDealDate(LocalDateTime dealDate) {
		this.dealDate = dealDate;
	}

	public String getFiCheckStatus() {
		return fiCheckStatus;
	}

	public void setFiCheckStatus(String fiCheckStatus) {
		this.fiCheckStatus = fiCheckStatus;
	}

	public LocalDateTime getFicheckeTime() {
		return ficheckeTime;
	}

	public void setFicheckeTime(LocalDateTime ficheckeTime) {
		this.ficheckeTime = ficheckeTime;
	}

	public String getFicheckerCode() {
		return ficheckerCode;
	}

	public void setFicheckerCode(String ficheckerCode) {
		this.ficheckerCode = ficheckerCode;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	@Override
	public String toString() {
		return "FiAcUsdtDO [id=" + id + ", periodWeek=" + periodWeek + ", orderNo=" + orderNo + ", companyCode="
				+ companyCode + ", agentNo=" + agentNo + ", userCode=" + userCode + ", serial=" + serial + ", usdtCode="
				+ usdtCode + ", coinType=" + coinType + ", transferType=" + transferType + ", transferAmount="
				+ transferAmount + ", localCurrency=" + localCurrency + ", localMoney=" + localMoney + ", rate=" + rate
				+ ", fdMoney=" + fdMoney + ", rewardRate=" + rewardRate + ", comments=" + comments + ", transferStatus="
				+ transferStatus + ", transferTime=" + transferTime + ", transactionFee=" + transactionFee + ", payee="
				+ payee + ", dataType=" + dataType + ", address=" + address + ", stockType=" + stockType
				+ ", createrCode=" + createrCode + ", createrName=" + createrName + ", createTime=" + createTime
				+ ", checkerCode=" + checkerCode + ", checkerName=" + checkerName + ", checkeTime=" + checkeTime
				+ ", checkType=" + checkType + ", dealDate=" + dealDate + ", remark=" + remark + ", cancelTime="+",status="+status
				+ cancelTime + ", cancelCode=" + cancelCode + ", requestId=" + requestId + "]";
	}
}