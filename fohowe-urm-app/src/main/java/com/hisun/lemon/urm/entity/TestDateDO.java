/*
 * @ClassName TestDateDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class TestDateDO extends BaseDO {
    /**
     * @Fields dt 
     */
    private LocalDate dt;
    /**
     * @Fields dtm 
     */
    private LocalDateTime dtm;
    /**
     * @Fields tmp 
     */
    private LocalDateTime tmp;
    /**
     * @Fields tm 
     */
    private LocalTime tm;

    public LocalDate getDt() {
        return dt;
    }

    public void setDt(LocalDate dt) {
        this.dt = dt;
    }

    public LocalDateTime getDtm() {
        return dtm;
    }

    public void setDtm(LocalDateTime dtm) {
        this.dtm = dtm;
    }

    public LocalDateTime getTmp() {
        return tmp;
    }

    public void setTmp(LocalDateTime tmp) {
        this.tmp = tmp;
    }

    public LocalTime getTm() {
        return tm;
    }

    public void setTm(LocalTime tm) {
        this.tm = tm;
    }
}