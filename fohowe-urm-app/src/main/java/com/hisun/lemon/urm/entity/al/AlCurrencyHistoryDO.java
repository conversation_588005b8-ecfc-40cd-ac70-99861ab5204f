/*
 * @ClassName AlCurrencyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-06 10:45:13
 */
package com.hisun.lemon.urm.entity.al;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class AlCurrencyHistoryDO extends BaseDO {
	/**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields currencyCode 货币编码
     */
    private String currencyCode;
    /**
     * @Fields currencyName 货币名称
     */
    private String currencyName;
    /**
     * @Fields lastUpdateTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields rateToUsd 对美元汇率
     */
    private BigDecimal rateToUsd;
    /**
     * @Fields rateToEur 对欧元汇率
     */
    private BigDecimal rateToEur;
    /**
     * @Fields rateToInput 对F$充值汇率
     */
    private BigDecimal rateToInput;
    /**
     * @Fields rateToApple 对F$提现汇率
     */
    private BigDecimal rateToApple;
    /**
     * @Fields rateToApple 兑黄金美元汇率
     */
    private BigDecimal rateToGold;
    
    
    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }
 
    public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public BigDecimal getRateToUsd() {
        return rateToUsd;
    }

    public void setRateToUsd(BigDecimal rateToUsd) {
        this.rateToUsd = rateToUsd;
    }

    public BigDecimal getRateToEur() {
        return rateToEur;
    }

    public void setRateToEur(BigDecimal rateToEur) {
        this.rateToEur = rateToEur;
    }

    public BigDecimal getRateToInput() {
        return rateToInput;
    }

    public void setRateToInput(BigDecimal rateToInput) {
        this.rateToInput = rateToInput;
    }

    public BigDecimal getRateToApple() {
        return rateToApple;
    }

    public void setRateToApple(BigDecimal rateToApple) {
        this.rateToApple = rateToApple;
    }

    public BigDecimal getRateToGold() {
        return rateToGold;
    }

    public void setRateToGold(BigDecimal rateToGold) {
        this.rateToGold = rateToGold;
    }

}