/*
 * @ClassName IBdUpdatePointDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-11 17:02:49
 */
package com.hisun.lemon.urm.dao.bd;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.RightPointAdjustQryDTO;
import com.hisun.lemon.urm.dto.mi.member.RightPointPVBean;
import com.hisun.lemon.urm.entity.bd.BdUpdatePointPVDO;

@Mapper
public interface IBdUpdatePointPVDao extends BaseDao<BdUpdatePointPVDO> {

	int getTotalCount(RightPointAdjustQryDTO dto);

	List<RightPointPVBean> getListByPageBreak(RightPointAdjustQryDTO dto);

	RightPointPVBean getExtensionDetailById(@Param("id") long id);

	BdUpdatePointPVDO get(@Param("id")long id);
}