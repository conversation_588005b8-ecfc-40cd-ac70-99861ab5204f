/*
 * @ClassName IBdRuleRightRateLogDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-15 15:06:12
 */
package com.hisun.lemon.urm.dao.bd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.bd.BdRuleRightRateLogDO;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IBdRuleRightRateLogDao extends BaseDao<BdRuleRightRateLogDO> {

    /** 
     * @param endDate 
     * @param beginDate 
     * @Title: getAllRecord 
     * @Description: 根据日期查询所有记录
     * @return
     * @return: List<T>
     */
    public List<BdRuleRightRateLogDO> getAllRecord(@Param("beginDate") LocalDateTime beginDate,
            @Param("endDate") LocalDateTime endDate);
}