/*
 * @ClassName BiAreaSendfeeDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-19 19:36:06
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class BiAreaSendfeeDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private Integer id;
    /**
     * @Fields companyCode 商城分公司编码
     */
    private String companyCode;
    /**
     * @Fields areaCode 规则名称
     */
    private String areaCode;
    /**
     * @Fields feeType 运费类型 （0-固定 1-变动）
     */
    private String feeType;
    /**
     * @Fields startPrice 起步货物价格
     */
    private BigDecimal startPrice;
    /**
     * @Fields sendFee 运费
     */
    private BigDecimal sendFee;
    /**
     * @Fields extraPrice 额外货物价格
     */
    private BigDecimal extraPrice;
    /**
     * @Fields addFee 附加费（3000欧递增）
     */
    private BigDecimal addFee;
    /**
     * @Fields status  0 新建 1有效  3作废
     */
    private Integer status;
    /**
     * @Fields creatorCode 创建人
     */
    private String creatorCode;
    /**
     * @Fields lowAmt 起始金额>
     */
    private BigDecimal lowAmt;
    /**
     * @Fields upperAmt 截至金额<=
     */
    private BigDecimal upperAmt;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    
    List<BiAreaSendfeeItemDO> sendFeeItem;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getStartPrice() {
        return startPrice;
    }

    public void setStartPrice(BigDecimal startPrice) {
        this.startPrice = startPrice;
    }

    public BigDecimal getSendFee() {
        return sendFee;
    }

    public void setSendFee(BigDecimal sendFee) {
        this.sendFee = sendFee;
    }

    public BigDecimal getExtraPrice() {
        return extraPrice;
    }

    public void setExtraPrice(BigDecimal extraPrice) {
        this.extraPrice = extraPrice;
    }

    public BigDecimal getAddFee() {
        return addFee;
    }

    public void setAddFee(BigDecimal addFee) {
        this.addFee = addFee;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public BigDecimal getLowAmt() {
        return lowAmt;
    }

    public void setLowAmt(BigDecimal lowAmt) {
        this.lowAmt = lowAmt;
    }

    public BigDecimal getUpperAmt() {
        return upperAmt;
    }

    public void setUpperAmt(BigDecimal upperAmt) {
        this.upperAmt = upperAmt;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

	public List<BiAreaSendfeeItemDO> getSendFeeItem() {
		return sendFeeItem;
	}

	public void setSendFeeItem(List<BiAreaSendfeeItemDO> sendFeeItem) {
		this.sendFeeItem = sendFeeItem;
	}
    
}