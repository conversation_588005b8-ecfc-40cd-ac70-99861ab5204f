/*
 * @ClassName IMiAgentDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.agent.AgentBeanDO;
import com.hisun.lemon.urm.dto.mi.agent.AgentCompany;
import com.hisun.lemon.urm.dto.mi.agent.AgentCurrencyDTO;
import com.hisun.lemon.urm.dto.mi.agent.AgentDTO;
import com.hisun.lemon.urm.dto.mi.agent.AgentDetailBeanDO;
import com.hisun.lemon.urm.dto.mi.agent.AgentQueryBean;
import com.hisun.lemon.urm.dto.mi.agent.IsAgentOrMember;
import com.hisun.lemon.urm.entity.MiAgentDO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IMiAgentDao extends BaseDao<MiAgentDO> {

	/**
	 * 查找总页数
	 * @param agentDTO
	 * @return
	 */
	int getTotalCount(AgentDTO agentDTO);
	/**
	 * 分页查询
	 * @param agentDTO
	 * @return
	 */
	List<AgentQueryBean> getListByCondition(AgentDTO agentDTO);
	/**
	 * 通过代办处编号查找代办处
	 * @param agentNo
	 * @param userType 
	 */
	AgentBeanDO getByNo(String agentNo);
	
	/** 
	 * @Title: getAgentByNo 
	 * @Description: 促销赠品单专用（用过代办处编号查询代办处所属区域和奖金制度）
	 * @param agentNo
	 * @return
	 * @return: AgentBeanDO
	 */
	AgentBeanDO getAgentByNo(String agentNo);
	
	
	AgentDetailBeanDO getAgentDetailByNo(String agentNo);
	/**
	 * 校验用户是否存在
	 * @param userCode
	 * @param userType 
	 * @param userType2 
	 * @return
	 */
	IsAgentOrMember searchUserCode(@Param("userCode") String userCode, @Param("userType1") String userType1, @Param("userType2") String userType2);
	
	List<AgentCompany> searchAgentByAgent(List<String> agentList);
	
	List<AgentCompany> searchAgentByParent(@Param("agentNo") String agentNo);
	
	List<AgentCompany> searchAgentByCompany(@Param("companyList") List<String> companyList,@Param("levelType") Integer levelType);
	/**
	 * 波兰立陶代办处查询
	 * @param channelType 渠道
	 * @param userType 
	 * @return
	 */
	List<MiAgentDO> searchMiAgent(@Param("companyCode")String companyCode,@Param("userType") String userType);
	/**
	 * 根据货币编码查询使用该货币的经销商及奖金制度
	 * @param currencyCode
	 * @return
	 */
	List<AgentCurrencyDTO> searchAgentByCurrency(String currencyCode);

    int goodsNumImport(@Param("goodsNumMap")Map<String, BigDecimal> goodsNumMap);
}