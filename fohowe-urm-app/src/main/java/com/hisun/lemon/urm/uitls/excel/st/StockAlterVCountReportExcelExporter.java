package com.hisun.lemon.urm.uitls.excel.st;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.dto.pd.ReportRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class StockAlterVCountReportExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "库存虚拟盘亏盘盈统计报表";
        ReportRspDTO vo = (ReportRspDTO) obj;
        String[] colNames = null;
        if (JudgeUtils.isEmpty(vo.getColumns())) {
            colNames = new String[] { "商品编码", "虚拟盘盈", "虚拟盘亏" };
        } else {
            if (vo.getColumns().size() == 5) {
                colNames = new String[vo.getColumns().size() - 1];
            } else if (vo.getColumns().size() == 7) {
                colNames = new String[vo.getColumns().size() - 2];
            } else {
                colNames = new String[vo.getColumns().size()];
            }
            for (int i = 0; i < vo.getColumns().size(); i++) {
                if (JudgeUtils.equals(vo.getColumns().get(i), "COMPANY_CODE")) {
                    colNames[1] = "分公司";
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "AREA_CODE")) {
                    colNames[0] = "区域";
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "GOODS_CODE")) {
                    colNames[colNames.length - 3] = "商品编码";
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "VIRTUAL_INVEN")) {
                    colNames[colNames.length - 2] = "虚拟盘盈";
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "VIRTUAL_LOSS")) {
                    colNames[colNames.length - 1] = "虚拟盘亏";
                } else if (JudgeUtils.equalsAny(vo.getColumns().get(i), "COMPANY_NAME", "AREA_NAME")) {
                    continue;
                }
            }
        }
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        ReportRspDTO vo = (ReportRspDTO) obj;
        if (JudgeUtils.isEmpty(vo.getList())) {
            return;
        }
        List<Map<String, Object>> dataList = vo.getList();
        for (Map<String, Object> o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);

            String companyCode = null;
            String companyName = null;
            String areaCode = null;
            String areaName = null;
            String goodsCode = null;
            BigDecimal virtualInven = null;
            BigDecimal virtualLoss = null;
            for (int i = 0; i < vo.getColumns().size(); i++) {
                if (JudgeUtils.equals(vo.getColumns().get(i), "COMPANY_CODE")) {
                    companyCode = o.get("COMPANY_CODE").toString();
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "COMPANY_NAME")) {
                    companyName = o.get("COMPANY_NAME").toString();
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "AREA_CODE")) {
                    areaCode = o.get("AREA_CODE").toString();
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "AREA_NAME")) {
                    areaName = o.get("AREA_NAME").toString();
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "GOODS_CODE")) {
                    goodsCode = o.get("GOODS_CODE").toString();
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "VIRTUAL_INVEN")) {
                    virtualInven = (BigDecimal) o.get("VIRTUAL_INVEN");
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "VIRTUAL_LOSS")) {
                    virtualLoss = (BigDecimal) o.get("VIRTUAL_LOSS");
                }
            }
            int colNums = 0;
            if (JudgeUtils.isNotBlank(companyCode)) {
                row.createCell(colNums).setCellValue(companyCode + "->" + companyName);
                colNums++;
            }
            if (JudgeUtils.isNotNull(areaCode)) {
                row.createCell(colNums).setCellValue(JudgeUtils.isBlank(areaCode) ? "" : areaCode + "->" + areaName);
                colNums++;
            }
            row.createCell(colNums).setCellValue(goodsCode);
            colNums++;
            row.createCell(colNums).setCellValue(virtualInven.toString());
            colNums++;
            row.createCell(colNums).setCellValue(virtualLoss.toString());
            colNums++;
        }

    }

    public static StockAlterVCountReportExcelExporter builder() {
        return new StockAlterVCountReportExcelExporter();
    }
}
