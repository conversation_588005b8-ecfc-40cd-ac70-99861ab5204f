/*
 * @ClassName PdSendInfoItemsDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 16:19:40
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

public class PdSendInfoOweDO extends BaseDO {
    /**
     * COMPANY_CODE,AGENT_NO,COMBO_RECEIPT_NO,SEND_STATUS,RECEIPT_NO,GOODS_CODE,ORDER_QTY,order_type,period_week
     */
	private String companyCode;
	private String agentNo;
	private String comboReceiptNo;
    private String sendStatus;
    private String receiptNo;
    private String goodsCode;
    private Integer orderQty; 
    private Integer orderType;
    private Integer periodWeek;
    
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getReceiptNo() {
		return receiptNo;
	}
	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}
	public String getSendStatus() {
		return sendStatus;
	}
	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}
	public String getComboReceiptNo() {
		return comboReceiptNo;
	}
	public void setComboReceiptNo(String comboReceiptNo) {
		this.comboReceiptNo = comboReceiptNo;
	}
	public String getGoodsCode() {
		return goodsCode;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	public Integer getOrderQty() {
		return orderQty;
	}
	public void setOrderQty(Integer orderQty) {
		this.orderQty = orderQty;
	}
	public Integer getOrderType() {
		return orderType;
	}
	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}
	public Integer getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(Integer periodWeek) {
		this.periodWeek = periodWeek;
	}
}