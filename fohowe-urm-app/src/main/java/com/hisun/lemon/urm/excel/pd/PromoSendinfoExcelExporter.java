package com.hisun.lemon.urm.excel.pd;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.pd.PromoSendinfoPageRspDTO;
import com.hisun.lemon.urm.dto.pd.PromoSendinfoRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class PromoSendinfoExcelExporter extends URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="促销赠品发货导出"; 
		String[] colNames=new String[] {"出货仓库","分公司","订单编号","出库单号","经销商编号","经销商姓名","代办处编号","期次","活动名称","发放日期","发货状态"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		PromoSendinfoPageRspDTO vo = (PromoSendinfoPageRspDTO)obj;
		List<PromoSendinfoRspDTO> dataList=vo.getPromoSendinfoRspList();
		for(PromoSendinfoRspDTO o:dataList) {	
			XSSFRow row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getSalesCompanyCode());
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getRequestNo());
			row.createCell(index++).setCellValue(o.getReceiptno());
			row.createCell(index++).setCellValue(o.getMemberNo());
			row.createCell(index++).setCellValue(o.getName());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getwWeek());
			row.createCell(index++).setCellValue(o.getActiveName());
			row.createCell(index++).setCellValue(o.getSendTime()==null?"":o.getSendTime().format(ymdhms));
			String statusName ="";
			if(o.getSendStatus() == 0){
				statusName = "未发货";
            }else  if(o.getSendStatus() == 1){
            	statusName ="已发货";
            }else  if(o.getSendStatus() == 2){
            	statusName = "部分发货";
            }else {
            	statusName="";
            }
			row.createCell(index++).setCellValue(statusName);
		}
	}
	
	public static PromoSendinfoExcelExporter builder() {
		return new PromoSendinfoExcelExporter();
	}
}
