package com.hisun.lemon.urm.entity.mi;

import java.util.ArrayList;
import java.util.List;

import com.hisun.lemon.framework.data.BaseDO;

public class MiMemberTeams extends BaseDO {
    private Integer id;

    private String companyCode;

    private String agentNo;

    private String teamNo;

    private String teamName;

    private String memberNo;

    private String memberName;

    private String cardType;

    private Integer teamStatus;

    private Integer teamNum;

    private Integer passeFlag;

    private Integer sourceFlag;
    
    private Integer initialNum;
    
    private Integer teamType;
    
    private Integer fendouNum;
    
    private Integer recdouNum;
    
    private Integer rankSn;
    private Integer IntegralSn;
    private String rankSnWeek;
    private String IntegralSnWeek;

    private String remark;
    
    private List<MiMemberTeamsItems> items = new ArrayList<MiMemberTeamsItems>();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getTeamNo() {
        return teamNo;
    }

    public void setTeamNo(String teamNo) {
        this.teamNo = teamNo;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public Integer getTeamStatus() {
        return teamStatus;
    }

    public void setTeamStatus(Integer teamStatus) {
        this.teamStatus = teamStatus;
    }

    public Integer getTeamNum() {
        return teamNum;
    }

    public void setTeamNum(Integer teamNum) {
        this.teamNum = teamNum;
    }

    public Integer getPasseFlag() {
        return passeFlag;
    }

    public void setPasseFlag(Integer passeFlag) {
        this.passeFlag = passeFlag;
    }

    public Integer getSourceFlag() {
        return sourceFlag;
    }

    public void setSourceFlag(Integer sourceFlag) {
        this.sourceFlag = sourceFlag;
    }

	public Integer getInitialNum() {
		return initialNum;
	}

	public void setInitialNum(Integer initialNum) {
		this.initialNum = initialNum;
	}

	public Integer getTeamType() {
		return teamType;
	}

	public void setTeamType(Integer teamType) {
		this.teamType = teamType;
	}

	public Integer getFendouNum() {
		return fendouNum;
	}

	public void setFendouNum(Integer fendouNum) {
		this.fendouNum = fendouNum;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getRecdouNum() {
		return recdouNum;
	}

	public void setRecdouNum(Integer recdouNum) {
		this.recdouNum = recdouNum;
	}

	public Integer getRankSn() {
		return rankSn;
	}

	public void setRankSn(Integer rankSn) {
		this.rankSn = rankSn;
	}

	public List<MiMemberTeamsItems> getItems() {
		return items;
	}

    public Integer getIntegralSn() {
        return IntegralSn;
    }

    public void setIntegralSn(Integer integralSn) {
        IntegralSn = integralSn;
    }

    public String getRankSnWeek() {
        return rankSnWeek;
    }

    public void setRankSnWeek(String rankSnWeek) {
        this.rankSnWeek = rankSnWeek;
    }

    public String getIntegralSnWeek() {
        return IntegralSnWeek;
    }

    public void setIntegralSnWeek(String integralSnWeek) {
        IntegralSnWeek = integralSnWeek;
    }

    public void setItems(List<MiMemberTeamsItems> items) {
		this.items = items;
	}
    
    
}