/*
 * @ClassName ISysAreaCompanyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-25 11:13:55
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysAreaCompanyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysAreaCompanyDao extends BaseDao<SysAreaCompanyDO> {

    /* @Title: findByCompany 
    * @Description: 根据公司编号查询所属区域
    * @param companyCode
    * @return
    * @return: SysAreaCompanyDO
    */
   public SysAreaCompanyDO findByCompany(@Param("companyCode") String companyCode);
}