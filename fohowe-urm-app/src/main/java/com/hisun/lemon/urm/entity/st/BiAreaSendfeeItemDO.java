/*
 * @ClassName BiAreaSendfeeItemDO
 * @Description 
 * @version 1.0
 * @Date 2018-11-28 11:42:20
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;


public class BiAreaSendfeeItemDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private Integer id;
    /**
     * @Fields sendfeeId 商品运费表id
     */
    private String sendfeeId;
    /**
     * @Fields lowAmt 起始金额>
     */
    private BigDecimal lowAmt;
    /**
     * @Fields upperAmt 截至金额<=
     */
    private BigDecimal upperAmt;
    /**
     * @Fields sendFee 运费
     */
    private BigDecimal sendFee;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSendfeeId() {
        return sendfeeId;
    }

    public void setSendfeeId(String sendfeeId) {
        this.sendfeeId = sendfeeId;
    }

    public BigDecimal getLowAmt() {
        return lowAmt;
    }

    public void setLowAmt(BigDecimal lowAmt) {
        this.lowAmt = lowAmt;
    }

    public BigDecimal getUpperAmt() {
        return upperAmt;
    }

    public void setUpperAmt(BigDecimal upperAmt) {
        this.upperAmt = upperAmt;
    }

    public BigDecimal getSendFee() {
        return sendFee;
    }

    public void setSendFee(BigDecimal sendFee) {
        this.sendFee = sendFee;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}