/*
 * @ClassName IMiMemberUpgradeDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.MemDivideOrMergeReqDTO;
import com.hisun.lemon.urm.dto.mi.member.MiMemberUpgradeBean;
import com.hisun.lemon.urm.entity.MiMemberUpgradeDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IMiMemberUpgradeDao extends BaseDao<MiMemberUpgradeDO> {

	int getDivideOrMergeTotalCount(MemDivideOrMergeReqDTO dto);

	List<MiMemberUpgradeBean> getDivideOrMergeListByPageBreak(MemDivideOrMergeReqDTO dto);

	
	MiMemberUpgradeDO get(@Param("id") long id);
	int delete(@Param("id") long id);
}