/*
 * @ClassName AlRateAdjustDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:31:42
 */
package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AlRateAdjustDO extends BaseDO {
    /**
     * @Fields adjustCode 调整单号 ADJUST_CODE
     */
    private String adjustCode;
    /**
     * @Fields status 0:新建 1:审批
     */
    private String status;
    /**
     * @Fields creatorCode 创建人编号 CREATOR_CODE
     */
    private String creatorCode;
    /**
     * @Fields checkerCode 审核人编号
     */
    private String checkerCode;
    /**
     * @Fields checkTime 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * @Fields isBalance 是否调整帐户余额 0 否 1是
     */
    private String isBalance;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getAdjustCode() {
        return adjustCode;
    }

    public void setAdjustCode(String adjustCode) {
        this.adjustCode = adjustCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getIsBalance() {
        return isBalance;
    }

    public void setIsBalance(String isBalance) {
        this.isBalance = isBalance;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}