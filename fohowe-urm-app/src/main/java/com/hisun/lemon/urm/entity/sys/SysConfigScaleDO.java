package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;

public class SysConfigScaleDO extends BaseDO {
    private Long id;

    private String companyCode;

    private BigDecimal configTotalPrice;

    private BigDecimal configPrice;

    private BigDecimal configIntegral;

    private BigDecimal configFv;

    private BigDecimal configMallPrice;
    private BigDecimal configMallIntegral;

    private BigDecimal configScaleFv;

    private BigDecimal configScalePrice;

    private Integer configKeyId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public BigDecimal getConfigTotalPrice() {
        return configTotalPrice;
    }

    public void setConfigTotalPrice(BigDecimal configTotalPrice) {
        this.configTotalPrice = configTotalPrice;
    }

    public BigDecimal getConfigPrice() {
        return configPrice;
    }

    public void setConfigPrice(BigDecimal configPrice) {
        this.configPrice = configPrice;
    }

    public BigDecimal getConfigIntegral() {
        return configIntegral;
    }

    public void setConfigIntegral(BigDecimal configIntegral) {
        this.configIntegral = configIntegral;
    }

    public BigDecimal getConfigFv() {
        return configFv;
    }

    public void setConfigFv(BigDecimal configFv) {
        this.configFv = configFv;
    }

    public BigDecimal getConfigMallPrice() {
        return configMallPrice;
    }

    public void setConfigMallPrice(BigDecimal configMallPrice) {
        this.configMallPrice = configMallPrice;
    }

    public BigDecimal getConfigMallIntegral() {
        return configMallIntegral;
    }

    public void setConfigMallIntegral(BigDecimal configMallIntegral) {
        this.configMallIntegral = configMallIntegral;
    }

    public BigDecimal getConfigScaleFv() {
        return configScaleFv;
    }

    public void setConfigScaleFv(BigDecimal configScaleFv) {
        this.configScaleFv = configScaleFv;
    }

    public BigDecimal getConfigScalePrice() {
        return configScalePrice;
    }

    public void setConfigScalePrice(BigDecimal configScalePrice) {
        this.configScalePrice = configScalePrice;
    }

    public Integer getConfigKeyId() {
        return configKeyId;
    }

    public void setConfigKeyId(Integer configKeyId) {
        this.configKeyId = configKeyId;
    }
}