/*
 * @ClassName IFiAcApplDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.appl.AcApplDetailBean;
import com.hisun.lemon.urm.entity.BonusFreezeStore;
import com.hisun.lemon.urm.entity.FiAcApplDetailDO;

import java.util.List;

import feign.Param;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IFiAcApplDetailDao extends BaseDao<FiAcApplDetailDO> {

	List<AcApplDetailBean> getListByCondition(FiAcApplDetailDO acApplDetail);
	List<FiAcApplDetailDO> getListByEAS(FiAcApplDetailDO acApplDetail);

	int getTotalCount(FiAcApplDetailDO acApplDetail);
	
	int deleteApplId(FiAcApplDetailDO acApplDetail);
	BonusFreezeStore selectByCompanyCode(@Param("memberNo") String memberNo);
}