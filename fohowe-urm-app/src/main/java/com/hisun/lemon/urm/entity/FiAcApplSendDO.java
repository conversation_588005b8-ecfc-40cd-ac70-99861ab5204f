/*
 * @ClassName FiAcApplSendDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiAcApplSendDO extends BaseDO {
    /**
     * @Fields operNo 
     */
    private Long operNo;
    /**
     * @Fields operDate 操作日期
     */
    private LocalDateTime operDate;
    /**
     * @Fields operCode 建立者帐号 creater_code
     */
    private String operCode;
    /**
     * @Fields operName 建立者名称 creater_name
     */
    private String operName;
    /**
     * @Fields status 状态，0新增，1已扣奖金钱包,2正在发放，3发放错误 4发放完成
     */
    private String status;
    /**
     * @Fields sendBank 汇出银行
     */
    private String sendBank;
    /**
     * @Fields receiveBank 汇款银行
     */
    private String receiveBank;
    /**
     * @Fields amountLow 金额下限
     */
    private BigDecimal amountLow;
    /**
     * @Fields amountUp 金额上限
     */
    private BigDecimal amountUp;
    /**
     * @Fields companyCode 国别
     */
    private String companyCode;
    /**
     * @Fields acType 账户类型，f$＝f$，fv＝fv, f0=f000，pv=活跃pv，b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，s1=全球分红，s2=凤凰大使分红
     */
    private String acType;
    /**
     * @Fields memo 备注(会员) 
     */
    private String memo;
    /**
     * @Fields remark 摘要(公司) 
     */
    private String remark;

    public Long getOperNo() {
        return operNo;
    }

    public void setOperNo(Long operNo) {
        this.operNo = operNo;
    }

    public LocalDateTime getOperDate() {
        return operDate;
    }

    public void setOperDate(LocalDateTime operDate) {
        this.operDate = operDate;
    }

    public String getOperCode() {
        return operCode;
    }

    public void setOperCode(String operCode) {
        this.operCode = operCode;
    }

    public String getOperName() {
        return operName;
    }

    public void setOperName(String operName) {
        this.operName = operName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSendBank() {
        return sendBank;
    }

    public void setSendBank(String sendBank) {
        this.sendBank = sendBank;
    }

    public String getReceiveBank() {
        return receiveBank;
    }

    public void setReceiveBank(String receiveBank) {
        this.receiveBank = receiveBank;
    }

    public BigDecimal getAmountLow() {
        return amountLow;
    }

    public void setAmountLow(BigDecimal amountLow) {
        this.amountLow = amountLow;
    }

    public BigDecimal getAmountUp() {
        return amountUp;
    }

    public void setAmountUp(BigDecimal amountUp) {
        this.amountUp = amountUp;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAcType() {
        return acType;
    }

    public void setAcType(String acType) {
        this.acType = acType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}