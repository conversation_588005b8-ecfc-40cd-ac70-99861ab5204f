package com.hisun.lemon.urm.common;

public enum MsgCdPZ {

    PROMO_SEND_WEEK_NULL("URM50001", "促销赠品单开始期次与结束期次为空"),
    PROMO_SEND_INT_PARAM("URM50002", "促销赠品单导入为空"),
    PROMO_SEND_INT_NO_COMPLE("URM50003", "促销赠品单未完全导入！"),
    PROMO_SEND_CHECK_PARAM("URM50004", "促销赠品单批量审核错误！"),
    PROMO_SEND_CHECK("URM50005", "促销赠品单批量审核遗漏！"),
    PROMO_SEND_DEL_ERR("URM50006", "促销赠品单删除失败！"),
    PROMO_SEND_STATUS_ERR("URM50007", "促销赠品单状态检测失败！"),
    PROMO_SEND_STORE_ERR("URM50008", "促销赠品单存储为出库单失败！"),
    PROMO_SEND_COMBO_ERR("URM50009", "促销赠品单合并为出库单，不是同一个代办处！"),
    PROMO_SEND_INFO_DEL_ERR("URM50010", "促销赠品单合单数据库删除不成功！"),
    PROMO_SEND_INFO_SEND_PARAM("URM50011", "促销赠品单发放失败,参数为空！"),
    PROMO_SEND_INFO_SEND_ERR("URM50012", "促销赠品单发放失败,数据未改成功！"),
    PROMO_SEND_INFO_SPLIT_ERR("URM50013", "促销赠品单合并发货拆分失败,拆分成功数量小于等于1！"),
    PROMO_SEND_INFO_COMBO_DEL("URM50014", "促销赠品单合并发货拆分失败,合并单删除失败！"),
    PROMO_SEND_INFO_COMBO_ITEM("URM50015", "促销赠品合并单获取子项失败！"),
    PROMO_SEND_INFO_COMBO_SPLIT_STATUS("URM50016", "促销赠品合并单拆分合并单已发放！"),
    QMANI_ITEM_QUERY_ERR("URM50017", "成品发运管理商品项查询失败！"),
    QMANI_SEND_ADD_PARAM("URM50018", "成品发运发货添加失败，参数为空！"),
    MANI_SEND_SENDQTY_ERR("URM50019", "成品发运单发货数量错误，修改发货数量错误！"),
    MANI_SEND_SENDQTY_PARAM("URM50020", "成品发运单发货数量错误，修改发货数量参数错误！"),
    MANI_SEND_SENDQTY_QUERY_ERR("URM50021", "成品发运单新建发放单，查询错误！"),
    MANI_SEND_ITEM_QUERY_ERR("URM50022", "成品发运单新建发放单商品详情查询失败！"),
    MANI_SEND_UPD_ERR("URM50023", "成品发运单修改失败！"),
    MANI_SEND_DEL_ERR("URM50024", "成品发运单删除失败！"),
    MANI_SEND_CHECK_ERR("URM50025", "成品发运单审核失败！"),
    MANI_SEND_CANCEL_ERR("URM50026", "成品发运单取消失败！"),
    MANI_REC_ITEM_QUERY_ERR("URM50027", "成品发运单查询商品接收详情失败！"),
    QMANI_SEND_DELIVERY_ERR("URM50028", "成品发运单发货失败！"),
    QMANI_SEND_RECITEM_INT_ERR("URM50029", "成品发运单收货商品项导入失败！"),
    QMANI_REC_STAT_ERR("URM50030", "成品发运单收货单状态错误！"),
    QMANI_REC_STAT_UPD_ERR("URM50031", "成品发运单收货单状态修改失败！"),
    QMANI_REC_STORE_UPD_ERR("URM50032", "成品发运单收货仓库修改失败！"),
    QMANI_REC_NUM_UPD_ERR("URM50033", "成品发运单收货数量修改失败！"),
    GET_USERID_FAIL("URM50034", "成品发运单获取userId失败！"),
    SHIP_SEND_INT_FAIL("URM50035", "添加成品分货发货单失败！"),
    SHIP_SEND_DEL_FAIL("URM50036", "删除成品分货发货单失败！"),
    SHIP_SEND_UPD_FAIL("URM50037", "修改成品分货发货单失败！"),
    FROM_STORE_QUERY_ERR("URM50038", "获取发货仓库失败！"),
    PROMO_SEND_INFO_GOODS("URM50039", "促销赠品单获取商品项失败！"),
    
    
    
    PROMO_SEND_COMBO_NUMERR("URM50040", "一个促销赠品单不允许合并！"),
    PROMO_SEND_NOTEXIT_ERR("URM50041", "促销赠品单发货单不存在！"),
    PROMO_SEND_STATUSNOTNEW_ERR("URM50042", "促销赠品单发货单不存在！"),
    FROM_STORE_NOTEQUAL_ERR("URM50043", "发货仓库不一致！"),
    PROMO_SEND_REPEAT_COMBO("URM50044", "重复合并！"),
    ;
    private String msgCd;
    private String msgInfo;
    private MsgCdPZ(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }
    public String getMsgCd() {
        return msgCd;
    }
    public String getMsgInfo() {
        return msgInfo;
    }
}
