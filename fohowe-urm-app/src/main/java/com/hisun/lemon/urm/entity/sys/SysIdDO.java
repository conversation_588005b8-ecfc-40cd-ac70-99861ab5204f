/*
 * @ClassName SysIdDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-14 11:41:12
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysIdDO extends BaseDO {
    /**
     * @Fields id 当前ID ID
     */
    private long id;
    /**
     * @Fields idName 当前ID名 ID_NAME
     */
    private String idName;
    /**
     * @Fields idValue 当前ID值 ID_VALUE
     */
    private Long idValue;
    /**
     * @Fields idCode ID编码 ID_CODE
     */
    private String idCode;
    /**
     * @Fields dateFormat 日期格式 DATE_FORMAT
     */
    private String dateFormat;
    /**
     * @Fields dateValue 日期值 DATE_VALUE
     */
    private String dateValue;
    /**
     * @Fields prefix ID前缀 PREFIX
     */
    private String prefix;
    /**
     * @Fields postfix ID后缀 POSTFIX
     */
    private String postfix;
    /**
     * @Fields idLength ID长度 ID_LENGTH
     */
    private Long idLength;
    /**
     * @Fields infix ID中缀  INFIX
     */
    private String infix;
    /**
     * @Fields valueRandom 随机跳跃生成 0-不随机 1-按顺序随机 2-完全随机
     */
    private String valueRandom;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getIdName() {
        return idName;
    }

    public void setIdName(String idName) {
        this.idName = idName;
    }

    public Long getIdValue() {
        return idValue;
    }

    public void setIdValue(Long idValue) {
        this.idValue = idValue;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public String getDateValue() {
        return dateValue;
    }

    public void setDateValue(String dateValue) {
        this.dateValue = dateValue;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getPostfix() {
        return postfix;
    }

    public void setPostfix(String postfix) {
        this.postfix = postfix;
    }

    public Long getIdLength() {
        return idLength;
    }

    public void setIdLength(Long idLength) {
        this.idLength = idLength;
    }

    public String getInfix() {
        return infix;
    }

    public void setInfix(String infix) {
        this.infix = infix;
    }

    public String getValueRandom() {
        return valueRandom;
    }

    public void setValueRandom(String valueRandom) {
        this.valueRandom = valueRandom;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}