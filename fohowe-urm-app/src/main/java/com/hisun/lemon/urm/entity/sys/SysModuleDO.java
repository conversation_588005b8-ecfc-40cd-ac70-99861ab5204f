/*
 * @ClassName SysModuleDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-17 15:41:40
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysModuleDO extends BaseDO {
    /**
     * @Fields moduleId 模块ID MODULE_ID
     */
    private String moduleId;
    /**
     * @Fields parentId 上级模块ID PARENT_ID
     */
    private String parentId;
    /**
     * @Fields moduleName 模块名称 MODULE_NAME
     */
    private String moduleName;
    /**
     * @Fields moduleDesc 模块描述 MODULE_DESC
     */
    private String moduleDesc;
    /**
     * @Fields moduleType 0:功能模块 1:菜单
     */
    private String moduleType;
    /**
     * @Fields linkUrl 访问地址 LINK_URL
     */
    private String linkUrl;
    /**
     * @Fields orderNo 排序号码 ORDER_NO
     */
    private Integer orderNo;
    /**
     * @Fields isValidate 0:不验证 1:验证
            1:验证
     */
    private String isValidate;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    
    private String linkIndex;

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getModuleDesc() {
        return moduleDesc;
    }

    public void setModuleDesc(String moduleDesc) {
        this.moduleDesc = moduleDesc;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public String getIsValidate() {
        return isValidate;
    }

    public void setIsValidate(String isValidate) {
        this.isValidate = isValidate;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

	public String getLinkIndex() {
		return linkIndex;
	}

	public void setLinkIndex(String linkIndex) {
		this.linkIndex = linkIndex;
	}
	
}