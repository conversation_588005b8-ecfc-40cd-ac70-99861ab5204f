
package com.hisun.lemon.urm.uitls.excel;


import java.io.UnsupportedEncodingException;

import javax.servlet.http.HttpServletResponse;

import com.hisun.lemon.common.utils.DateTimeUtils;

/**
 * ExcelHelperUtils
 * <AUTHOR>
 * @date 2017-12-25 16:59
 */
public class ExcelHelperUtils {

	public static final int MAX_RECORDS=5000;
	public static final int MAX_RECORDS2=30000;
	public static final int MAX_RECORDS3=200000;
	
	public static void setResponseHead(String fileName, HttpServletResponse response,ExcelType type) throws UnsupportedEncodingException {
		try {
			response.reset();
			response.setContentType("application/octet-stream; charset=utf-8");
			response.setHeader("Content-Disposition", "attachment; filename=" + new String((fileName+DateTimeUtils.getCurrentDateTimeStr()+type.getName()).getBytes("utf-8"), "ISO-8859-1"));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			throw e;
		}
    	
    	
		
	}
	public static enum ExcelType{
		XLS(".xls","1"),XLSX(".xlsx","2");

		private String name;
		private String value;

		private ExcelType(String name,String value) {
			this.name = name;
			this.value = value;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getValue() {
			return value;
		}

		public void setValue(String value) {
			this.value = value;
		}
	}
	
	
	
}
