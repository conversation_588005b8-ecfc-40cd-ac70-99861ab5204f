/*
 * @ClassName IAlRegionCompanyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-07 16:43:35
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlRegionCompanyDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlRegionCompanyDao extends BaseDao<AlRegionCompanyDO> {

    /** 
     * @Title: deleteById 
     * @Description: 根据regionId删除多条记录
     * @param regionList
     * @return
     * @return: int
     */
    public int deleteById(@Param("idList") List<String> idList);

    /**
     * 查询公司地区list
     * @Title: getRegionCompanyList 
     * @Description: TODO
     * @param bonusType
     * @param areaCode
     * @param companyCode
     * @param regionName
     * @return
     * @return: List<AlRegionCompanyDO>
     */
    public List<AlRegionCompanyDO> getRegionCompanyList(@Param("bonusType") String bonusType,
            @Param("areaCode") String areaCode, @Param("companyCode") String companyCode,
            @Param("regionName") String regionName);

    /** 
     * @Title: findCompanyExists 
     * @Description: 查询是否存在地区和分公司对应关系
     * @param regionId
     * @return
     * @return: AlRegionCompanyDO
     */
    public AlRegionCompanyDO findCompanyExists(@Param("regionId") String regionId);

    /** 
     * @Title: getCompanyList 
     * @Description: TODO
     * @param regionCode
     * @return
     * @return: List<AlRegionCompanyDO>
     */
    public List<AlRegionCompanyDO> getCompanyList(@Param("regionCode") String regionCode);

    /** 
     * @Title: deleteByCompany 
     * @Description: 根据公司编码删除多条记录
     * @param companyList
     * @return
     * @return: int
     */
    public int deleteByCompany(@Param("companyList") List<String> companyList);

    /** 
     * @Title: getChooseCompanyList 
     * @Description: 查询可选分公司列表
     * @param regionCode
     * @return
     * @return: List<AlRegionCompanyDO>
     */
    public List<AlRegionCompanyDO> getChooseCompanyList(@Param("regionCode") String regionCode);
}