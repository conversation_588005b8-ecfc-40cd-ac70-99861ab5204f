package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.OrderType;
import com.hisun.lemon.urm.entity.pd.PdSendInfoOweDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class SendInfoOweExcelExporter extends URMExcelExportFactory {
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName = "订单欠货明细表"; 
		String[] colNames = new String[] { "期数","分公司","代办处","合并单号","订单号", "产品编码", "未发数量","单据类型"};
		super.exportActually(fileName, colNames, obj, response);
	}

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        List<PdSendInfoOweDO> dataList = (List<PdSendInfoOweDO>) obj;
        for (PdSendInfoOweDO o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);
            int index=0;
            //"期数","分公司","代办处","合并单号","订单号", "产品编码", "未发数量","单据类型"          
            row.createCell(index++).setCellValue(o.getPeriodWeek());
            row.createCell(index++).setCellValue(o.getCompanyCode());
            row.createCell(index++).setCellValue(o.getAgentNo());
            row.createCell(index++).setCellValue(o.getComboReceiptNo());
            row.createCell(index++).setCellValue(o.getReceiptNo());
            row.createCell(index++).setCellValue(o.getGoodsCode());
            row.createCell(index++).setCellValue(o.getOrderQty()); 
            row.createCell(index++).setCellValue(JudgeUtils.isNull(OrderType.getByCode(o.getOrderType())) ? o.getOrderType().toString() : OrderType.getByCode(o.getOrderType()).getName().toString()); 
        }
    }

    public static SendInfoOweExcelExporter builder() {
        return new SendInfoOweExcelExporter();
    }
}
