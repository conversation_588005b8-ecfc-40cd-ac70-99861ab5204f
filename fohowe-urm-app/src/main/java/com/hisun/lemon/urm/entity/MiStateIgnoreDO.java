/*
 * @ClassName MiStateIgnoreDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class MiStateIgnoreDO extends BaseDO {
    /**
     * @Fields ignoreId 
     */
    private Long ignoreId;
    /**
     * @Fields adjustCode 调整单编号 ADJUST_CODE
     */
    private String adjustCode;
    /**
     * @Fields memberNo 会员编号 MEMBER_NO
     */
    private String memberNo;

    public Long getIgnoreId() {
        return ignoreId;
    }

    public void setIgnoreId(Long ignoreId) {
        this.ignoreId = ignoreId;
    }

    public String getAdjustCode() {
        return adjustCode;
    }

    public void setAdjustCode(String adjustCode) {
        this.adjustCode = adjustCode;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }
}