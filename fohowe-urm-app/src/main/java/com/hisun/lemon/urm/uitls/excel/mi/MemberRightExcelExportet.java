package com.hisun.lemon.urm.uitls.excel.mi;



import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.servlet.http.HttpServletResponse;

import com.hisun.lemon.urm.dto.mi.member.*;
import com.hisun.lemon.urm.enums.sys.SuspendStatus;
import com.hisun.lemon.urm.uitls.excel.fi.AcInputExcelExporter;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class MemberRightExcelExportet extends  URMExcelExportFactorys{

	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="经营权业绩";
		String[] colNames=new String[] {"生效期数", "经营权编号", "调整区", "调整点数", "状态", "创建时间", "审核时间", "取消时间","备注"};
		super.exportActually(fileName, colNames, obj, response);
	}

	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		RightPointAdjustRspVO vo=(RightPointAdjustRspVO) obj;
		List<RightPointAdjustBean> dataList=vo.getDataList();
		for(RightPointAdjustBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getwWeek());
			row.createCell(index++).setCellValue(o.getRightNo());
			row.createCell(index++).setCellValue("0".equals(o.getRuleType()) ? "左区" : "右区");//0代表左区，1代表右区
			row.createCell(index++).setCellValue(String.valueOf(o.getPoints()));
			String statusName = "";
			switch (o.getStatus()) {
				case "0":
					statusName = "未审核";
					break;
				case "1":
					statusName = "已审核";
					break;
				case "2":
					statusName = "已取消";
					break;
				case "3":
					statusName = "已锁定";
					break;
				default:
					statusName = "未知状态";
			}
			row.createCell(index++).setCellValue(statusName);
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			String createTime = null;
			String modifyTime = null;
			String cancelTime = null;
			if (o.getCreateTime() != null){
				createTime = o.getCreateTime().format(formatter);
			}
			if (o.getModifyTime() != null){
				modifyTime = o.getModifyTime().format(formatter);
			}
			if (o.getCancelTime() != null){
				cancelTime = o.getCancelTime().format(formatter);
			}
			row.createCell(index++).setCellValue(createTime);
			row.createCell(index++).setCellValue(modifyTime);
			row.createCell(index++).setCellValue(cancelTime);
			row.createCell(index++).setCellValue(o.getRemark());


		}

	}

	public static MemberRightExcelExportet builder() {
		return new MemberRightExcelExportet();
	}
}
