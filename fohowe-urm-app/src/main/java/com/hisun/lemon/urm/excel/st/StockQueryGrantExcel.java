/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: EntityGranExcel.java 
 * @Prject: fohowe-web-ec
 * @Package: com.hisun.lemon.fohowe.ec.excel 
 * @author: liubao   
 * @date: 2017年12月6日 上午10:46:28 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.excel.st;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.excel.ExcelExportFactorys;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.st.StockBeanDTO;

/**
 * @ClassName: StockQueryGrantExcel 
 * @author: tian
 * @date: 2018年2月24日 下午5:13:33 
 * @param <T>
 */
public class StockQueryGrantExcel<T extends StockBeanDTO> extends ExcelExportFactorys<T> {
	
	public void export2(Object obj, String fileName,String[] colNames,HttpServletResponse response) {
		try {
			response.reset();
			response.setHeader("Content-disposition", "attachment; filename=" +fileName+ ".xlsx");
            response.setContentType("application/msexcel;charset=UTF-8");//设置类型
            response.setHeader("Pragma", "No-cache");//设置头
            response.setHeader("Cache-Control", "no-cache");//设置头
            response.setDateHeader("Expires", 0);//设置日期头
            List<List<String>> head = new ArrayList<List<String>>();
            for (int i = 0; i < colNames.length; i++) {
            	List<String> head0 = new ArrayList<String>();
            	head0.add(colNames[i]);
            	head.add(head0);
    		}
			WriteWorkbook workBook = new WriteWorkbook();
			workBook.setExcelType(ExcelTypeEnum.XLSX);
			workBook.setOutputStream(response.getOutputStream());
			workBook.setNeedHead(true);
			WriteSheet sheet= new WriteSheet();
			sheet.setSheetNo(0);
			sheet.setSheetName(fileName); 
			sheet.setHead(head);
			ExcelWriter write = new ExcelWriter(workBook);
			write.write(addData(obj), sheet); 
			write.finish();
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
    @Override
    public void addCell(Sheet sheet, CellStyle style, List<T> dataList) throws Exception {
        if (dataList != null && dataList.size() > 0) {
            for (int i = 0; i < dataList.size(); i++) {
                StockBeanDTO stockDO = dataList.get(i);
                // 创建所需的行数
                Row row = sheet.createRow(i + 3);
                Cell cell = null;

                // 分公司/仓库编号
                cell = row.createCell(0, CellType.STRING);
                cell.setCellValue(stockDO.getCompanyCode());
                cell.setCellStyle(style);

                // 商品代码
                cell = row.createCell(1, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockDO.getGoodsCode()) ? "" : stockDO.getGoodsCode());
                cell.setCellStyle(style);

                // 商品名称
                cell = row.createCell(2, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockDO.getGoodsName()) ? "" : stockDO.getGoodsName());
                cell.setCellStyle(style);
                
                // 库存量
                cell = row.createCell(3, CellType.NUMERIC);
                cell.setCellValue(stockDO.getQuantity());
                cell.setCellStyle(style);

                // 可用库存量 
                cell = row.createCell(4, CellType.NUMERIC);
                cell.setCellValue(stockDO.getValidQty());
                cell.setCellStyle(style);
                
                // 虚拟库存量 
                cell = row.createCell(5, CellType.NUMERIC);
                cell.setCellValue(stockDO.getVirtualQty());
                cell.setCellStyle(style);

                // 调入在途
                cell = row.createCell(6, CellType.NUMERIC);
                cell.setCellValue(stockDO.getDonWay());
                cell.setCellStyle(style);

                // 调出在途
                cell = row.createCell(7, CellType.NUMERIC);
                cell.setCellValue(stockDO.getOutWay());
                cell.setCellStyle(style);

                // 订货在途
                cell = row.createCell(8, CellType.NUMERIC);
                cell.setCellValue(stockDO.getOnWay());
                cell.setCellStyle(style);

                // 待发货库存
                cell = row.createCell(9, CellType.NUMERIC);
                cell.setCellValue(stockDO.getWaitQty());
                cell.setCellStyle(style);

                // 发货在途
                cell = row.createCell(10, CellType.NUMERIC);
                cell.setCellValue(stockDO.getSaleWay());
                cell.setCellStyle(style);

                // 待报损库存
                cell = row.createCell(11, CellType.NUMERIC);
                cell.setCellValue(stockDO.getAlterQty());
                cell.setCellStyle(style);
                
                // 待审批销售库存
                cell = row.createCell(12, CellType.NUMERIC);
                cell.setCellValue(stockDO.getSaleWait());
                cell.setCellStyle(style);
            }
        }
    }
    public List<List<Object>> addData(Object obj) throws Exception {
		List<List<Object>> list = new ArrayList<List<Object>>();
		List<StockBeanDTO> dataList = (List<StockBeanDTO>)obj;
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			StockBeanDTO stockDO = (StockBeanDTO)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(stockDO.getCompanyCode());
            // 商品代码
			data.add(JudgeUtils.isNull(stockDO.getGoodsCode()) ? "" : stockDO.getGoodsCode());
            // 商品名称
			data.add(JudgeUtils.isNull(stockDO.getGoodsName()) ? "" : stockDO.getGoodsName());
            //商品归类
            String codeName = stockDO.getCodeName();
            Map<String, String> goodsKindMap = new HashMap<>();
            goodsKindMap.put("b1", "下架");
            goodsKindMap.put("b2", "主打产品");
            goodsKindMap.put("b3", "促销赠品");
            goodsKindMap.put("b4", "配件耗材");
            goodsKindMap.put("b5", "新产品");
            goodsKindMap.put("b6", "报单套餐");
            data.add(JudgeUtils.isNull(codeName) ? "" : goodsKindMap.getOrDefault(codeName, codeName));

            // 库存量
			data.add(stockDO.getQuantity());
            // 可用库存量 
			data.add(stockDO.getValidQty());
            // 虚拟库存量 
			data.add(stockDO.getVirtualQty());
            // 调入在途
			data.add(stockDO.getDonWay());
            // 调出在途
			data.add(stockDO.getOutWay());
            // 订货在途
			data.add(stockDO.getOnWay());
            // 待发货库存
			data.add(stockDO.getWaitQty());
            // 发货在途
			data.add(stockDO.getSaleWay());
            // 待报损库存
			data.add(stockDO.getAlterQty());
            // 待审批销售库存
			data.add(stockDO.getSaleWait());
			list.add(data);
		}
		return list;
	}
}
