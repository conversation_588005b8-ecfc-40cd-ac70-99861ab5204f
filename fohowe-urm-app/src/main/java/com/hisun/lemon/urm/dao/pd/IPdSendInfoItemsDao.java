/*
 * @ClassName IPdSendInfoItemsDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 16:19:40
 */
package com.hisun.lemon.urm.dao.pd;

import java.time.LocalDateTime;
import java.util.List;

import com.hisun.lemon.urm.dto.pd.SendInfoItemsBeanDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdSendInfoItemsDO;
import com.hisun.lemon.urm.entity.pd.PdSendInfoOweDO;

@Mapper
public interface IPdSendInfoItemsDao extends BaseDao<PdSendInfoItemsDO> {
	
	int updateSendNums(List<Long> ids);
		
    /** 
     * @Title: getByReceiptNo 
     * @param receiptNo
     * @return
     * @return: List<PdSendInfoItemsDO>
     */
    List<PdSendInfoItemsDO> getByReceiptNo(@Param("receiptNo") String receiptNo, @Param("delFlag") String delFlag);
    
    List<PdSendInfoItemsDO> getAllByReceiptNo(@Param("receiptNo") String receiptNo);
    /** 
     * @Title: getByReceiptNo 
     * @param receiptNo
     * @return
     * @return: List<PdSendInfoItemsDO>
     */
    List<PdSendInfoItemsDO> getByReceiptNos(@Param("receiptNo") String receiptNo, @Param("delFlag") String delFlag,@Param("lists") List<Long> lists);
    /**
     * 根据发货单列表获取产品信息
     * @param lists
     * @return
     */
    List<PdSendInfoItemsDO> getItemsByPdIds(@Param("lists") List<Long> lists);

    /** 
     * @Title: getByComboReceiptNo 
     * @Description: TODO
     * @param comboReceiptNo
     * @param orderStatus 
     * @return
     * @return: List<PdSendInfoItemsDO>
     */
    List<PdSendInfoItemsDO> getByComboReceiptNo(@Param("comboReceiptNo") String comboReceiptNo,
            @Param("orderStatus") String orderStatus);
    
    /** 
     * @Title: getByComboReceiptNo 
     * @Description: TODO
     * @param comboReceiptNo
     * @param orderStatus 
     * @return
     * @return: List<PdSendInfoItemsDO>
     */
    List<PdSendInfoItemsDO> getByComboReceiptNos(@Param("comboReceiptNo") String comboReceiptNo,
    		@Param("orderStatus") String orderStatus, @Param("lists") List<Long> lists);
    /**
     * 根据ID获取产品列表
     * @param lists
     * @return
     */
    List<PdSendInfoItemsDO> getItemsListByIds(@Param("lists") List<Long> lists);
    
    /**
     * 合并单批量选择，
     * @param lists 合并单ID列表
     * @return
     */
    List<PdSendInfoItemsDO> getByComboReceiptNosList(@Param("lists") List<Long> lists);

   int UpdateSendStatusByCombo(@Param("comboReceiptNo") String comboReceiptNo);
   PdSendInfoItemsDO getComboSendStatusNum(@Param("comboReceiptNo") String comboReceiptNo);
   int UpdateGoodsOrderFlagByCombo(@Param("comboReceiptNo") String comboReceiptNo);
   int UpdateOrderFlagByCombo(@Param("comboReceiptNo") String comboReceiptNo);
    
    /** 
     * @Title: getByComboReceiptNoCnt 
     * @Description: TODO
     * @param comboReceiptNo
     * @param code
     * @return
     * @return: List<PdSendInfoItemsDO>
     */
    List<PdSendInfoItemsDO> getByComboReceiptNoCnt(@Param("comboReceiptNo") String comboReceiptNo,
            @Param("orderStatus") String orderStatus);
    
    int updateMergeHeadByReceiptNo(@Param("comboReceiptNo") String comboReceiptNo,@Param("receiptNo") String receiptNo);//合并
    int updateHeadSplitByComboReceiptNo(@Param("comboReceiptNo") String comboReceiptNo);//拆分
    
    List<PdSendInfoOweDO> oweQueryList(@Param("bonusType") String bonusType,@Param("agentNo") String agentNo,
    		@Param("beginWeek") Integer beginWeek,@Param("endWeek") Integer endWeek,
    		@Param("companyList") List<String> companyList,@Param("goodsList") List<String> goodsList
    		,@Param("orderTypeList") List<String> orderTypeList);
    
    List<PdSendInfoOweDO> oweQueryPromList(@Param("bonusType") String bonusType,@Param("agentNo") String agentNo,
    		@Param("beginWeek") Integer beginWeek,@Param("endWeek") Integer endWeek,
    		@Param("companyList") List<String> companyList,@Param("goodsList") List<String> goodsList
    		);

    int updateItemsComboByNo(@Param("reNoList") List<String> reNoList,@Param("comboReceiptNo") String comboReceiptNo);

	List<PdSendInfoItemsDO> getByReceiptNoDel(@Param("receiptNo") String receiptNo, @Param("delFlag") String delFlag);

	int findAllCount(@Param("receiptNo") String receiptNo);

	List<PdSendInfoItemsDO> getPacKageList(@Param("receiptNo") String receiptNo);

	void UpdateSendStatusByReceipt(@Param("receiptNo") String receiptNo);
	PdSendInfoItemsDO getReceiptSendStatusNum(@Param("receiptNo") String receiptNo);
	int UpdateGoodsOrderFlagByReceipt(@Param("receiptNo") String receiptNo);
	int UpdateOrderFlagByReceipt(@Param("receiptNo") String receiptNo);

    List<SendInfoItemsBeanDTO> getGroupByComboReceiptNo(@Param("comboReceiptNo") String comboReceiptNo,
                                                        @Param("orderStatus") String orderStatus);

    List<PdSendInfoItemsDO> getByMemberNoAndStatus(@Param("memberNo") String memberNo,
                                                   @Param("beginDate")LocalDateTime beginDate,
                                                   @Param("endDate") LocalDateTime endDate,
                                                   @Param("orderStatus") String orderStatus);

    List<SendInfoItemsBeanDTO> getGroupByMemberNoAndStatus(@Param("memberNo") String memberNo,
                                                           @Param("beginDate")LocalDateTime beginDate,
                                                           @Param("endDate") LocalDateTime endDate,
                                                           @Param("orderStatus") String orderStatus);
}