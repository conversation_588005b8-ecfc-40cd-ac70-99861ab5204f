/*
 * @ClassName MiTempTreeDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class MiTempTreeDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields memberNo 会员编号
     */
    private String memberNo;
    /**
     * @Fields rightNo 经营权编号
     */
    private String rightNo;
    /**
     * @Fields linkNo 接点编号
     */
    private String linkNo;
    /**
     * @Fields status 状态：0 过滤掉的节点  1有效节点
     */
    private String status;
    /**
     * @Fields qtyMember 查询的会员
     */
    private String qtyMember;
    /**
     * @Fields leftMem 左区接点
     */
    private String leftMem;
    /**
     * @Fields rightMem 右区接点
     */
    private String rightMem;
    /**
     * @Fields sType 查找类型: 0 个人点位缩图 1网络骨干缩图
     */
    private String sType;
    /**
     * @Fields isEmpty 是否是空点 ：1是
     */
    private String isEmpty;
    /**
     * @Fields oldLeftMem 原左区节点
     */
    private String oldLeftMem;
    /**
     * @Fields oldRightMem 原右区节点
     */
    private String oldRightMem;
    /**
     * @Fields oldLinkNo 原节点人
     */
    private String oldLinkNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getRightNo() {
        return rightNo;
    }

    public void setRightNo(String rightNo) {
        this.rightNo = rightNo;
    }

    public String getLinkNo() {
        return linkNo;
    }

    public void setLinkNo(String linkNo) {
        this.linkNo = linkNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getQtyMember() {
        return qtyMember;
    }

    public void setQtyMember(String qtyMember) {
        this.qtyMember = qtyMember;
    }

    public String getLeftMem() {
        return leftMem;
    }

    public void setLeftMem(String leftMem) {
        this.leftMem = leftMem;
    }

    public String getRightMem() {
        return rightMem;
    }

    public void setRightMem(String rightMem) {
        this.rightMem = rightMem;
    }

    public String getsType() {
        return sType;
    }

    public void setsType(String sType) {
        this.sType = sType;
    }

    public String getIsEmpty() {
        return isEmpty;
    }

    public void setIsEmpty(String isEmpty) {
        this.isEmpty = isEmpty;
    }

    public String getOldLeftMem() {
        return oldLeftMem;
    }

    public void setOldLeftMem(String oldLeftMem) {
        this.oldLeftMem = oldLeftMem;
    }

    public String getOldRightMem() {
        return oldRightMem;
    }

    public void setOldRightMem(String oldRightMem) {
        this.oldRightMem = oldRightMem;
    }

    public String getOldLinkNo() {
        return oldLinkNo;
    }

    public void setOldLinkNo(String oldLinkNo) {
        this.oldLinkNo = oldLinkNo;
    }
}