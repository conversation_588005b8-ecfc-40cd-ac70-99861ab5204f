package com.hisun.lemon.urm.uitls;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.fohowe.common.enums.RightDisplayWayEnums;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.mi.member.RightIsExistRspDTO;
import com.hisun.lemon.urm.entity.MiMemberRightDO;
import com.hisun.lemon.urm.service.mi.IMemberRightService;

/**
 * <AUTHOR>
 * @date 2017-11-16 14:15
 */
@Component
public class RightGenerator {

	@Resource
	private IMemberRightService rightService;

	private static final Logger logger = LoggerFactory.getLogger(RightGenerator.class);

	public static void main(String[] args) {
		/*
		 * List<String> exits=new ArrayList<>(); exits.add("C"); exits.add("F");
		 * List<String> s=getSequence("","K",200,exits); //传入的这个一个也算一个，一共10个，不传则默认从A开始
		 * System.out.println(s);
		 */
		/*
		 * MiMemberRightDO linkRight=new MiMemberRightDO(); linkRight.setRightNo("888");
		 * linkRight.setLinkIndex("0"); linkRight.setLinkLayer("1"); //为ture的时候
		 * 个数会增加一个,如下代码实际会返回3个对象 List<MiMemberRightDO> beans=null;
		 * //beans=getTriangleRightBeans("323dsfdsfs",null,2,true,linkRight);
		 * beans=getStraightRightBeans("323dsfdsfs",null,0,true,linkRight,null);
		 * //beans=getDoubleStraightRightBeans("323dsfdsfs",null,8,false,linkRight);
		 * 
		 * System.out.println(beans.size());
		 */
	}

	public List<String> getSequence(String prefix, String begin, int length, List<String> exits) {
		List<String> arr = new ArrayList<String>();
		generateSequence2(arr, begin, length, prefix);
		joinPrefix(prefix, arr);
		// System.out.println(arr);
		return arr;

	}

	private void joinPrefix(String prefix, List<String> arr) {
		for (int i = 0; i < arr.size(); i++) {
			String s = arr.get(i);
			arr.set(i, prefix + s);
		}

	}
	/*
	 * public static List<String> generateSequence(List<String> arr,String begin,int
	 * length, List<String> existRightNoList){
	 * 
	 * if(length==0) { return arr; } if(StringUtils.isBlank(begin)) { begin="A";
	 * 
	 * } arr.add(begin);//传入的这个也算，所以length要减去1 ,
	 * //也是由于这个原因，导致这个加入的可能会被重复，解决办法时判断重复后再加入，而不是先加入，这就要改一下规则，现在就不改了难得调
	 * //所以必须规定，购买的时候用户必输指定一个不重复的编号 length--;
	 * 
	 * char[] c=begin.toCharArray();
	 * 
	 * for(int i=0;i<length;i++) {
	 * 
	 * char lastChar=c[c.length-1];
	 * 
	 * if('0'<=lastChar&&lastChar<='9') {//0-9 lastChar++; if(lastChar>'9') {
	 * lastChar='A'; } c[c.length-1]=lastChar; boolean
	 * flag=chargeIsExist(String.valueOf(c),existRightNoList); if(flag) { length++;
	 * }else { arr.add(String.valueOf(c)); }
	 * 
	 * continue; }
	 * 
	 * if(65<=lastChar&&lastChar<=90) {//A-Z lastChar++; if(lastChar>90) {
	 * c=getNewBeginCharArr(c,1); }else { c[c.length-1]=lastChar; } boolean
	 * flag=chargeIsExist(String.valueOf(c),existRightNoList); if(flag) { length++;
	 * }else { arr.add(String.valueOf(c)); } } }
	 * 
	 * return arr;
	 * 
	 * }
	 */

	private boolean chargeIsExist(String suffix, String memNo) {
		RightIsExistRspDTO dto = rightService.chargeRightExsit(memNo + suffix);
		if (dto != null) {
			return true;// 已存在
		}

		return false;
	}

	private char[] getNewBeginCharArr(char[] c, int index) {
		index++;
		if (c.length >= index) {
			char firstChar = c[c.length - index]; // 前一个字符
			if ('0' <= firstChar && firstChar <= '9') {// 0-9
				firstChar++;
				if (firstChar > '9') {
					firstChar = 'A';
				}

			} else if (65 <= firstChar && firstChar <= 90) {// A-Z
				firstChar++;
				if (firstChar > 90) {
					c = getNewBeginCharArr(c, index);
					return c;
				}
			} else {
				// 报错 超出范围
			}
			c[c.length - index] = firstChar;
			for (int i = c.length - 1; i > c.length - index; i--) {
				c[i] = '0';
			}

		} else {
			c = new char[index];
			for (int i = 0; i < index; i++) {
				c[i] = '0';
			}
		}
		return c;
	}

	/**
	 * 三角型数据模型
	 * 
	 * @param prefix
	 *            经销商编号
	 * @param begin
	 *            编码开始编号 为空时从A开始
	 * @param length
	 *            生成编码的个数
	 * @param isAddSelf
	 *            是否将自己作为主经营权 ，为true时，编码个数会加1，要特别注意
	 * @param linkRight
	 *            接点权对象
	 * @return
	 * <AUTHOR>
	 * @param existRightNoList
	 */
	public List<MiMemberRightDO> getTriangleRightBeans(String prefix, String begin, int length, boolean isAddSelf,
			MiMemberRightDO linkRight) {
		List<String> arr = new ArrayList<String>();
		generateSequence2(arr, begin, length, prefix);
		List<MiMemberRightDO> beans = generatorTriangleBeans(prefix, arr, isAddSelf, linkRight);
		return beans;
	}

	private List<MiMemberRightDO> generatorTriangleBeans(String prefix, List<String> arr, boolean isAddSelf,
			MiMemberRightDO linkRight) {
		List<MiMemberRightDO> beans = new ArrayList<MiMemberRightDO>();
		if (isAddSelf) {// 将自己作为主经营权
			MiMemberRightDO b = new MiMemberRightDO();
			b.setRightNo(prefix);
			b.setPrimRight("1");
			b.setLinkNo(linkRight.getRightNo());
			if (StringUtils.isBlank(linkRight.getLeftMem())) {
				b.setLinkIndex(linkRight.getLinkIndex() + "0");
			} else {
				b.setLinkIndex(linkRight.getLinkIndex() + "1");
			}
			b.setLinkLayer(linkRight.getLinkLayer() + 1);
			beans.add(b);
		}
		for (int i = 0; i < arr.size(); i++) {
			MiMemberRightDO b = new MiMemberRightDO();
			b.setRightNo(prefix + arr.get(i));
			if (!isAddSelf && i == 0) { // 不将自己作为主经营权时,直接将第一个生成的点接入网络
				b.setLinkNo(linkRight.getRightNo());
				if (StringUtils.isBlank(linkRight.getLeftMem())) {
					b.setLinkIndex(linkRight.getLinkIndex() + "0");
				} else {
					b.setLinkIndex(linkRight.getLinkIndex() + "1");
				}
				b.setLinkLayer(linkRight.getLinkLayer() + 1);
			}
			beans.add(b);
		}

		for (int n = 0; n <= beans.size() / 2 - 1; n++) {
			if (n == beans.size() / 2 - 1) {// 因为最后一个可能时没有左右子节点的，所要额外判断下
				int lastParentIndex = beans.size() / 2 - 1;
				MiMemberRightDO lastParent = beans.get(lastParentIndex);
				MiMemberRightDO left = beans.get(lastParentIndex * 2 + 1);

				int layer = lastParent.getLinkLayer() + 1;
				left.setLinkNo(lastParent.getRightNo());
				left.setLinkIndex(lastParent.getLinkIndex() + "0");
				left.setLinkLayer(layer);

				lastParent.setLeftMem(left.getRightNo());

				if (beans.size() % 2 == 1) {
					MiMemberRightDO right = beans.get(lastParentIndex * 2 + 2);
					right.setLinkNo(lastParent.getRightNo());
					right.setLinkIndex(lastParent.getLinkIndex() + "1");
					right.setLinkLayer(layer);
					lastParent.setRightMem(right.getRightNo());
				}
			} else {
				MiMemberRightDO parent = beans.get(n);
				MiMemberRightDO left = beans.get(n * 2 + 1);
				MiMemberRightDO right = beans.get(n * 2 + 2);

				int layer = parent.getLinkLayer() + 1;
				left.setLinkNo(parent.getRightNo());
				left.setLinkIndex(parent.getLinkIndex() + "0");
				left.setLinkLayer(layer);

				right.setLinkNo(parent.getRightNo());
				right.setLinkIndex(parent.getLinkIndex() + "1");
				right.setLinkLayer(layer);

				parent.setLeftMem(left.getRightNo());
				parent.setRightMem(right.getRightNo());
			}
		}

		return beans;
	}

	/**
	 * 生成直线模型
	 * 
	 * @param prefix
	 *            经销商编号
	 * @param begin
	 *            编码开始编号 为空时从A开始
	 * @param length
	 *            生成编码的个数
	 * @param isAddSelf
	 *            是否将自己作为主经营权 ，为true时，编码个数会加1，要特别注意
	 * @param linkRight
	 *            接点权对象
	 * @return
	 * <AUTHOR>
	 * @param existRightNoList
	 */
	public List<MiMemberRightDO> getStraightRightBeans(String prefix, String begin, int i, boolean isAddSelf,
			MiMemberRightDO linkRight) {
		List<String> arr = new ArrayList<String>();
		generateSequence2(arr, begin, i, prefix);
		// System.out.println(arr);
		List<MiMemberRightDO> beans = generatorStraightBeans(prefix, arr, isAddSelf, linkRight);
		return beans;

	}

	private static List<MiMemberRightDO> generatorStraightBeans(String prefix, List<String> arr, boolean isAddSelf,
			MiMemberRightDO linkRight) {
		List<MiMemberRightDO> beans = new ArrayList<MiMemberRightDO>();
		if (isAddSelf) {// 将自己作为主经营权
			MiMemberRightDO b = new MiMemberRightDO();
			b.setRightNo(prefix);
			b.setPrimRight("1");
			b.setLinkNo(linkRight.getRightNo());
			if (StringUtils.isBlank(linkRight.getLeftMem())) {
				b.setLinkIndex(linkRight.getLinkIndex() + "0");
			} else {
				b.setLinkIndex(linkRight.getLinkIndex() + "1");
			}
			b.setLinkLayer(linkRight.getLinkLayer() + 1);
			beans.add(b);
		}
		for (int i = 0; i < arr.size(); i++) {
			MiMemberRightDO b = new MiMemberRightDO();
			b.setRightNo(prefix + arr.get(i));
			if (!isAddSelf && i == 0) {// 不将自己作为主经营权时,直接将第一个生成的点接入网络
				b.setLinkNo(linkRight.getRightNo());
				if (StringUtils.isBlank(linkRight.getLeftMem())) {
					b.setLinkIndex(linkRight.getLinkIndex() + "0");
				} else {
					b.setLinkIndex(linkRight.getLinkIndex() + "1");
				}
				b.setLinkLayer(linkRight.getLinkLayer() + 1);
			}
			beans.add(b);
		}

		for (int i = 0; i < beans.size() - 1; i++) {
			MiMemberRightDO parent = beans.get(i);
			MiMemberRightDO leftSub = beans.get(i + 1);
			int layer = parent.getLinkLayer() + 1;
			leftSub.setLinkNo(parent.getRightNo());
			leftSub.setLinkIndex(parent.getLinkIndex() + "0");
			leftSub.setLinkLayer(layer);
			parent.setLeftMem(leftSub.getRightNo());

		}

		return beans;
	}

	/**
	 * 双直线模型
	 * 
	 * @param prefix
	 *            经销商编号
	 * @param begin
	 *            编码开始编号 为空时从A开始
	 * @param length
	 *            生成编码的个数
	 * @param isAddSelf
	 *            是否将自己作为主经营权 ，为true时，编码个数会加1，要特别注意
	 * @param linkRight
	 *            接点权对象
	 * @return
	 * <AUTHOR>
	 * @param existRightNoList
	 */
	public List<MiMemberRightDO> getDoubleStraightRightBeans(String prefix, String begin, int i, boolean isAddSelf,
			MiMemberRightDO linkRight) {
		List<String> arr = new ArrayList<String>();
		generateSequence2(arr, begin, i, prefix);
		// System.out.println(arr);
		List<MiMemberRightDO> beans = generatorDoubleStraightBeans(prefix, arr, isAddSelf, linkRight);
		return beans;

	}

	private static List<MiMemberRightDO> generatorDoubleStraightBeans(String prefix, List<String> arr,
			boolean isAddSelf, MiMemberRightDO linkRight) {
		List<MiMemberRightDO> beans = new ArrayList<MiMemberRightDO>();
		if (isAddSelf) {// 将自己作为主经营权
			MiMemberRightDO b = new MiMemberRightDO();
			b.setRightNo(prefix);
			b.setPrimRight("1");
			b.setLinkNo(linkRight.getRightNo());
			if (StringUtils.isBlank(linkRight.getLeftMem())) {
				b.setLinkIndex(linkRight.getLinkIndex() + "0");
			} else {
				b.setLinkIndex(linkRight.getLinkIndex() + "1");
			}
			b.setLinkLayer(linkRight.getLinkLayer() + 1);
			beans.add(b);
		}
		for (int i = 0; i < arr.size(); i++) {
			MiMemberRightDO b = new MiMemberRightDO();
			b.setRightNo(prefix + arr.get(i));
			if (!isAddSelf && i == 0) {// 不将自己作为主经营权时,直接将第一个生成的点接入网络
				b.setLinkNo(linkRight.getRightNo());
				if (StringUtils.isBlank(linkRight.getLeftMem())) {
					b.setLinkIndex(linkRight.getLinkIndex() + "0");
				} else {
					b.setLinkIndex(linkRight.getLinkIndex() + "1");
				}
				b.setLinkLayer(linkRight.getLinkLayer() + 1);
			}
			beans.add(b);
		}

		for (int parentIndex = 0; parentIndex <= beans.size() / 2 - 1; parentIndex++) {

			if (parentIndex == 0) {
				if (beans.size() < 3) {
					MiMemberRightDO leftParent = beans.get(parentIndex);

					MiMemberRightDO left = beans.get(parentIndex * 2 + 1);

					int layer = leftParent.getLinkLayer() + 1;
					left.setLinkNo(leftParent.getRightNo());
					left.setLinkIndex(leftParent.getLinkIndex() + "0");
					left.setLinkLayer(layer);

					leftParent.setLeftMem(left.getRightNo());
				} else {
					MiMemberRightDO leftParent = beans.get(parentIndex);

					MiMemberRightDO left = beans.get(parentIndex * 2 + 1);
					MiMemberRightDO right = beans.get(parentIndex * 2 + 2);

					int layer = leftParent.getLinkLayer() + 1;
					left.setLinkNo(leftParent.getRightNo());
					left.setLinkIndex(leftParent.getLinkIndex() + "0");
					left.setLinkLayer(layer);

					right.setLinkNo(leftParent.getRightNo());
					right.setLinkIndex(leftParent.getLinkIndex() + "1");
					right.setLinkLayer(layer);

					leftParent.setLeftMem(left.getRightNo());
					leftParent.setRightMem(right.getRightNo());

				}

			} else {
				if (parentIndex == beans.size() / 2 - 1) {
					// 最后一个额外判断
					int lastParentIndex = beans.size() / 2 - 1;
					MiMemberRightDO leftParent = beans.get((lastParentIndex - 1) * 2 + 1);

					MiMemberRightDO left = beans.get(lastParentIndex * 2 + 1);

					int layer = leftParent.getLinkLayer() + 1;
					left.setLinkNo(leftParent.getRightNo());
					left.setLinkIndex(leftParent.getLinkIndex() + "0");
					left.setLinkLayer(layer);

					leftParent.setLeftMem(left.getRightNo());

					if (beans.size() % 2 == 1) {
						MiMemberRightDO rightParent = beans.get((lastParentIndex - 1) * 2 + 2);
						MiMemberRightDO right = beans.get(lastParentIndex * 2 + 2);

						right.setLinkNo(rightParent.getRightNo());
						right.setLinkIndex(rightParent.getLinkIndex() + "0"); // 都是左区
						right.setLinkLayer(layer);

						rightParent.setLeftMem(right.getRightNo());// 都是左区
					}
				} else {
					MiMemberRightDO leftParent = beans.get((parentIndex - 1) * 2 + 1);
					MiMemberRightDO rightParent = beans.get((parentIndex - 1) * 2 + 2);
					MiMemberRightDO left = beans.get(parentIndex * 2 + 1);
					MiMemberRightDO right = beans.get(parentIndex * 2 + 2);

					int layer = leftParent.getLinkLayer() + 1;
					left.setLinkNo(leftParent.getRightNo());
					left.setLinkIndex(leftParent.getLinkIndex() + "0");
					left.setLinkLayer(layer);

					right.setLinkNo(rightParent.getRightNo());
					right.setLinkIndex(rightParent.getLinkIndex() + "0"); // 都是左区
					right.setLinkLayer(layer);

					leftParent.setLeftMem(left.getRightNo());
					rightParent.setLeftMem(right.getRightNo()); // 都是左区
				}

			}

		}

		return beans;
	}

	/**
	 * 生成相应规则的经营权对象
	 * 
	 * @param prefix
	 *            经销商编号
	 * @param begin
	 *            编码开始编号 为空时从A开始
	 * @param length
	 *            生成编码的个数
	 * @param isAddSelf
	 *            是否将自己作为主经营权 ，为true时，编码个数会加1，要特别注意
	 * @param linkRight
	 *            接点权对象
	 * @param rightNoList
	 *            需要排除的列表，防止重复
	 * @return
	 * <AUTHOR>
	 */
	public List<MiMemberRightDO> generatorRightBeans(RightDisplayWayEnums displayEnums, String memberNo, String begin,
			int length, boolean isAddSelf, MiMemberRightDO right) {
		if (isAddSelf) {
			length = (length - 1) < 0 ? 0 : (length - 1); // 添加自己是时购买个数要减去1,length 可以为0
		}
		List<MiMemberRightDO> list = null;
		switch (displayEnums) {
		case STRAIGHT: // 直线型
			logger.info("直线型");
			list = getStraightRightBeans(memberNo, begin, length, isAddSelf, right);

			break;
		case DOUBLE_STRAIGHT: // 双直线
			logger.info("双直线");
			list = getDoubleStraightRightBeans(memberNo, begin, length, isAddSelf, right);

			break;
		case TRIANGLE: // 三角型
			logger.info("三角型");

			list = this.getTriangleRightBeans(memberNo, begin, length, isAddSelf, right);

			break;
		default:
			LemonException.throwBusinessException(MsgCdLC.ENUM_TYPE_ERROR.getMsgCd());
		}

		return list;
	}

	public List<String> generateSequence2(List<String> arr, String begin, int length, String memNo) {

		if (length == 0) {
			return arr;
		}
		if (StringUtils.isBlank(begin)) {
			begin = "A";

		}
		// arr.add(begin);//传入的这个也算，所以length要减去1 ,
		// 也是由于这个原因，导致这个加入的可能会被重复，解决办法时判断重复后再加入，而不是先加入，这就要改一下规则，现在就不改了难得调
		// 所以必须规定，购买的时候用户必输指定一个不重复的编号
		// length--;

		char[] c = begin.toCharArray();

		for (int i = 0; i < length; i++) {

			char lastChar = c[c.length - 1];

			if ('0' <= lastChar && lastChar <= '9') {// 0-9 '9'=57
				/*
				 * lastChar++; if(lastChar>'9') { lastChar='A'; }
				 * 
				 */
				// c[c.length-1]=lastChar;
				boolean flag = chargeIsExist(String.valueOf(c), memNo);
				if (flag) {
					length++;
				} else {
					arr.add(String.valueOf(c));
				}
				lastChar++;
				if (lastChar > '9') {
					lastChar = 'A';
				}
				c[c.length - 1] = lastChar;
				continue;
			}

			if (65 <= lastChar && lastChar <= 90) {// A-Z
				/*
				 * 
				 * lastChar++; if(lastChar>90) { c=getNewBeginCharArr(c,1); }else {
				 * c[c.length-1]=lastChar; }
				 */
				boolean flag = chargeIsExist(String.valueOf(c), memNo);
				if (flag) {
					length++;
				} else {
					arr.add(String.valueOf(c));
				}
			}
			lastChar++;
			if (lastChar > 90) {
				c = getNewBeginCharArr(c, 1);
			} else {
				c[c.length - 1] = lastChar;
			}

		}

		return arr;

	}

}
