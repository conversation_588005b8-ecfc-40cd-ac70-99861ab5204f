package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.dto.pd.ReportRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class SendInfoSendReportExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "销售发货明细表";
        ReportRspDTO vo = (ReportRspDTO) obj;
        String[] colNames = new String[vo.getColumns().size()];
        colNames[0] = "出库公司";
        colNames[1] = "订货分公司";
        colNames[2] = "代办处";
        int colNums = 3;
        for (int i = 0; i < vo.getColumns().size(); i++) {
            if (JudgeUtils.equalsAny(vo.getColumns().get(i),"COMPANY_CODE", "ORDER_COMPANY_CODE", "AGENT_NO")) {
                continue;
            }
            colNames[colNums] = vo.getColumns().get(i);
            colNums++;
        }
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        ReportRspDTO vo = (ReportRspDTO) obj;
        List<Map<String, Object>> dataList = vo.getList();
        for (Map<String, Object> o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);
            int colNums = 3;
            for (int i = 0; i < vo.getColumns().size(); i++) {
                String cellValue = vo.getColumns().get(i);
                if (JudgeUtils.equals(vo.getColumns().get(i), "COMPANY_CODE")) {
                    row.createCell(0).setCellValue(o.get(cellValue).toString());
                }else if (JudgeUtils.equals(vo.getColumns().get(i), "ORDER_COMPANY_CODE")) {
                    row.createCell(1).setCellValue(o.get(cellValue).toString());
                } else if (JudgeUtils.equals(vo.getColumns().get(i), "AGENT_NO")) {
                    row.createCell(2).setCellValue(o.get(cellValue).toString());
                } else {
                    row.createCell(colNums).setCellValue(o.get(cellValue).toString());
                    colNums++;
                }
            }
        }
    }

    public static SendInfoSendReportExcelExporter builder() {
        return new SendInfoSendReportExcelExporter();
    }
}
