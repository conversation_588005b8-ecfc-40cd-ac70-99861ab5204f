package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.st.StLogisticsBeanDO;
import com.hisun.lemon.urm.dto.st.StLogisticsQueryReqDTO;
import com.hisun.lemon.urm.entity.st.StLogisticsLines;

@Mapper
public interface IStLogisticsLinesDao extends BaseDao<StLogisticsLines> {
	
	List<StLogisticsBeanDO> queryList(StLogisticsQueryReqDTO reqDTO);
	
    int deleteByPrimaryKey(Integer id);

    int insert(StLogisticsLines record);

    int insertSelective(StLogisticsLines record);

    StLogisticsLines selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(StLogisticsLines record);

    int updateByPrimaryKey(StLogisticsLines record);
}