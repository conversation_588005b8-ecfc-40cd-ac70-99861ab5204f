/*
 * @ClassName MiActTimeDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class MiActTimeDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields activeTime 有效时间
     */
    private LocalDateTime activeTime;
    /**
     * @Fields activeNum 累积次数
     */
    private String activeNum;
    /**
     * @Fields preActiveTime 上次有效时间
     */
    private LocalDateTime preActiveTime;
    /**
     * @Fields preActiveNum 上阶段累积次数
     */
    private String preActiveNum;
    /**
     * @Fields memberNo 会员编号
     */
    private String memberNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(LocalDateTime activeTime) {
        this.activeTime = activeTime;
    }

    public String getActiveNum() {
        return activeNum;
    }

    public void setActiveNum(String activeNum) {
        this.activeNum = activeNum;
    }

    public LocalDateTime getPreActiveTime() {
        return preActiveTime;
    }

    public void setPreActiveTime(LocalDateTime preActiveTime) {
        this.preActiveTime = preActiveTime;
    }

    public String getPreActiveNum() {
        return preActiveNum;
    }

    public void setPreActiveNum(String preActiveNum) {
        this.preActiveNum = preActiveNum;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }
}