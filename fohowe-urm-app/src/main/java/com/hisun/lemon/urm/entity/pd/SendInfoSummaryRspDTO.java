package com.hisun.lemon.urm.entity.pd;

import java.util.HashSet;
import java.util.List;

import com.hisun.lemon.urm.dto.pd.SendInfoSummaryDTO;

public class SendInfoSummaryRspDTO {
	
	private List<SendInfoSummaryDTO>  sendInfoSummaryList;
	
	private HashSet<String> goodSet;
	
	private List<SendInfoSummaryDTO>  packageSummaryList;
	
	private HashSet<String> packageSet;
	
	private Integer isPlw;
	
	public List<SendInfoSummaryDTO> getSendInfoSummaryList() {
		return sendInfoSummaryList;
	}
	public void setSendInfoSummaryList(List<SendInfoSummaryDTO> sendInfoSummaryList) {
		this.sendInfoSummaryList = sendInfoSummaryList;
	}
	public HashSet<String> getGoodSet() {
		return goodSet;
	}
	public void setGoodSet(HashSet<String> goodSet) {
		this.goodSet = goodSet;
	}
	public List<SendInfoSummaryDTO> getPackageSummaryList() {
		return packageSummaryList;
	}
	public void setPackageSummaryList(List<SendInfoSummaryDTO> packageSummaryList) {
		this.packageSummaryList = packageSummaryList;
	}
	public HashSet<String> getPackageSet() {
		return packageSet;
	}
	public void setPackageSet(HashSet<String> packageSet) {
		this.packageSet = packageSet;
	}
	public Integer getIsPlw() {
		return isPlw;
	}
	public void setIsPlw(Integer isPlw) {
		this.isPlw = isPlw;
	}
}
