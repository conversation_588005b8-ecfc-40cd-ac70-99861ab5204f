package com.hisun.lemon.urm.uitls.excel.st;

import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.YesNoStatus;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.pd.SendInfoBeanDTO;
import com.hisun.lemon.urm.dto.pd.SendInfoItemsBeanDTO;
import com.hisun.lemon.urm.dto.pd.SendInfoQueryReqDTO;
import com.hisun.lemon.urm.dto.pd.SendInfoQueryRspDTO;
import com.hisun.lemon.urm.enums.pd.OrderStatus;
import com.hisun.lemon.urm.service.pd.ISendInfoService;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils.ExcelType;

public class SendInfoDataExcelExporter {
    public DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public void export(ISendInfoService sendInfoService, Object obj, HttpServletResponse response) {
        String fileName = "分公司发货发货单";
        try {
            ExcelHelperUtils.setResponseHead(fileName, response, ExcelType.XLSX);
            addCell(sendInfoService, response.getOutputStream(), obj);
        } catch (IOException e) {
            e.printStackTrace();
            LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
    }

    public void addCell(ISendInfoService sendInfoService, OutputStream oss, Object obj) throws IOException {
        XSSFWorkbook workbook = null;
        workbook = new XSSFWorkbook();

        SendInfoQueryRspDTO vo = (SendInfoQueryRspDTO) obj;
        List<SendInfoBeanDTO> dataList = vo.getSendInfoPage().getList();

        int rowNums = 0;
        String sheetName = "分公司发货单";
        XSSFSheet sheet = workbook.createSheet(sheetName);
        XSSFCellStyle columnTopStyle = this.getColumnTopStyle(workbook);// 获取列头样式对象
        XSSFCellStyle columnCenterStyle = this.getColumnCenterStyle(workbook);// 获取中间对齐样式对象
        XSSFCellStyle columnRightStyle = this.getColumnRightStyle(workbook);// 获取右边对齐样式对象
        XSSFCellStyle columnLeftStyle = this.getColumnLeftStyle(workbook);// 获取右边对齐样式对象

        XSSFRow row = sheet.createRow(rowNums);
        XSSFCellStyle columnRedStyle = this.getColumnRedStyle(workbook);
        setRowStyle(row, columnRedStyle, 6);
        row.getCell(0).setCellStyle(columnRedStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 1, 5));
        row.getCell(0).setCellValue("单位参数:");
        row.getCell(1).setCellValue("0.个 1.件 2.包 3.双 4.套 5.本 6.条 7.片 8.瓶 9.盒 10.袋");

        for (SendInfoBeanDTO o : dataList) {
            // 若该笔订单信息为已经合并的单则直接跳过
            if (JudgeUtils.equals(o.getOrderStatus(), OrderStatus.MERAGE.getCode())) {
                continue;
            }

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnTopStyle, 6);
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums + 1, 0, 5));
            row.getCell(0).setCellValue("凤凰集团    分公司发货单");
            rowNums++;// 行数增1

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnTopStyle, 6);
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums + 1, 0, 5));
            row.getCell(0).setCellValue("Накладная выдачи товара  филиала Компании Финикса");
            rowNums++;// 行数增1

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnTopStyle, 6);
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums + 1, 0, 5));
            row.getCell(0).setCellValue("合并单号："+o.getComboReceiptNo());
            rowNums++;// 行数增1
            
            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellStyle(columnRightStyle);
            row.getCell(2).setCellStyle(columnRightStyle);
            row.getCell(4).setCellStyle(columnRightStyle);
            row.getCell(0).setCellValue("公司编号:");
            row.getCell(1).setCellValue(o.getCompanyCode());
            row.getCell(2).setCellValue("出库单号:");
            row.getCell(3).setCellValue(o.getReceiptNo());
            row.getCell(4).setCellValue("提货申请单号:");
            if (JudgeUtils.equals(o.getIsMergeHead(), YesNoStatus.YES.getCode())) {
                row.getCell(5).setCellValue("");
            } else {
                row.getCell(5).setCellValue(JudgeUtils.isNull(o.getBiOrderNo()) ? "" : o.getBiOrderNo());
            }

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellStyle(columnRightStyle);
            row.getCell(2).setCellStyle(columnRightStyle);
            row.getCell(4).setCellStyle(columnRightStyle);
            row.getCell(0).setCellValue("代办处编号:");
            row.getCell(1).setCellValue(JudgeUtils.isNull(o.getAgentNo()) ? "" : o.getAgentNo());
            row.getCell(2).setCellValue("发货人:");
            row.getCell(3).setCellValue(JudgeUtils.isNull(o.getSender()) ? "" : o.getSender());
            row.getCell(4).setCellValue("审核人:");
            row.getCell(5).setCellValue(JudgeUtils.isNull(o.getFaChecker()) ? "" : o.getFaChecker());

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellStyle(columnRightStyle);
            row.getCell(2).setCellStyle(columnRightStyle);
            row.getCell(4).setCellStyle(columnRightStyle);
            row.getCell(0).setCellValue("收货人姓名:");
            row.getCell(1).setCellValue(JudgeUtils.isNull(o.getRecName()) ? "" : o.getRecName());
            row.getCell(2).setCellValue("收货人联系电话:");
            row.getCell(3).setCellValue(JudgeUtils.isNull(o.getRecPhone()) ? "" : o.getRecPhone());
            row.getCell(4).setCellValue("经销商编号:");
            row.getCell(5).setCellValue(JudgeUtils.isNull(o.getMemberNo()) ? "" : o.getMemberNo());

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellStyle(columnRightStyle);
            row.getCell(2).setCellStyle(columnRightStyle);
            row.getCell(4).setCellStyle(columnRightStyle);
            row.getCell(0).setCellValue("创建人:");
            row.getCell(1).setCellValue(JudgeUtils.isNull(o.getOrderor()) ? "" : o.getOrderor());
            row.getCell(2).setCellValue("创建时间:");
            row.getCell(3).setCellValue(JudgeUtils.isNull(o.getOrderDate()) ? "" : o.getOrderDate().format(ymdhms));
            row.getCell(4).setCellValue("物流单号");
            row.getCell(5).setCellValue(JudgeUtils.isNull(o.getDcsendNo()) ? "" : o.getDcsendNo());

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnLeftStyle, 6);
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 1, 5));
            row.getCell(0).setCellStyle(columnRightStyle);
            row.getCell(0).setCellValue("收货地址:");
            row.getCell(1).setCellValue(JudgeUtils.isNull(o.getRecAddr()) ? "" : o.getRecAddr());

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnLeftStyle, 6);
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 1, 5));
            row.getCell(0).setCellStyle(columnRightStyle);
            row.getCell(0).setCellValue("备注:");
            row.getCell(1).setCellValue(JudgeUtils.isNull(o.getMemo()) ? "" : o.getMemo());

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellValue("");
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 0, 5));

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 0, 5));
            row.getCell(0).setCellValue("成品分货单明细");

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellValue("商品编号");
            sheet.setColumnWidth(0, "商品编号".getBytes().length * 2 * 256);
            row.getCell(1).setCellValue("商品名称");
            sheet.setColumnWidth(1, "商品名称".getBytes().length * 2 * 256);
            row.getCell(2).setCellValue("单位");
            sheet.setColumnWidth(2, "单位".getBytes().length * 2 * 256);
            row.getCell(3).setCellValue("单价");
            sheet.setColumnWidth(3, "单价".getBytes().length * 2 * 256);
            row.getCell(4).setCellValue("数量");
            sheet.setColumnWidth(4, "数量".getBytes().length * 2 * 256);
            row.getCell(5).setCellValue("单品金额小计");
            sheet.setColumnWidth(5, "单品金额小计".getBytes().length * 2 * 256);

            SendInfoQueryReqDTO reqDTO = new SendInfoQueryReqDTO();
            if (JudgeUtils.equals(o.getIsMergeHead(), YesNoStatus.YES.getCode())) {
                reqDTO.setComboReceiptNo(o.getComboReceiptNo());
                reqDTO.setIsCnt(true);
            } else {
                reqDTO.setReceiptNo(o.getReceiptNo());
            }
            SendInfoQueryRspDTO rsp = sendInfoService.sendInfoGoodsQuery(reqDTO);
            List<SendInfoItemsBeanDTO> goodsItemsList = rsp.getGoodsItemsList();
            for (SendInfoItemsBeanDTO itemsDTO : goodsItemsList) {
                rowNums++;// 行数增1
                row = sheet.createRow(rowNums);
                setRowStyle(row, columnCenterStyle, 6);
                row.getCell(0).setCellValue(itemsDTO.getGoodsCode());
                row.getCell(1).setCellValue(JudgeUtils.isNull(itemsDTO.getGoodsName()) ? "" : itemsDTO.getGoodsName());
                row.getCell(2).setCellValue(JudgeUtils.isNull(itemsDTO.getSpec()) ? "" : itemsDTO.getSpec());
                row.getCell(3)
                        .setCellValue(JudgeUtils.isNull(itemsDTO.getPrice()) ? "" : itemsDTO.getPrice().toString());
                row.getCell(4).setCellValue(
                        JudgeUtils.isNull(itemsDTO.getOrderQty()) ? "" : itemsDTO.getOrderQty().toString());
                row.getCell(5).setCellValue(JudgeUtils.isNullAny(itemsDTO.getPrice(), itemsDTO.getOrderQty()) ? ""
                        : itemsDTO.getPrice().multiply(BigDecimal.valueOf(itemsDTO.getOrderQty())).toString());
            }

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellValue("合计:");

            rowNums++;// 行数增1
            row = sheet.createRow(rowNums);
            setRowStyle(row, columnCenterStyle, 6);
            row.getCell(0).setCellValue("");
            sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums + 1, 0, 5));
            rowNums++;

        }
        workbook.write(oss);
    }

    public void setRowStyle(XSSFRow row, XSSFCellStyle style, int cellNum) {
        for (int i = 0; i < cellNum; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellStyle(style);
        }
    }

    public XSSFCellStyle getColumnTopStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        font.setFontName("Courier New");
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    public XSSFCellStyle getColumnCenterStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    public XSSFCellStyle getColumnRightStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    public XSSFCellStyle getColumnLeftStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    public XSSFCellStyle getColumnRedStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setColor(XSSFFont.COLOR_RED);
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
}
