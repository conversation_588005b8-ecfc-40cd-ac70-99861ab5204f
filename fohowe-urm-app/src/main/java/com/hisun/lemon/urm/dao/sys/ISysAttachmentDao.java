package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import com.hisun.lemon.fin.dto.attach.AttachItemBean;
import com.hisun.lemon.urm.dto.sys.SysAttachReqDTO;
import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.sys.SysAttachmentBean;
import com.hisun.lemon.urm.entity.sys.SysAttachmentDO;

@Mapper
public interface ISysAttachmentDao extends BaseDao<SysAttachmentDO> {
    int deleteByPrimaryKey(Long id);

    int insert(SysAttachmentDO row);

    int insertSelective(SysAttachmentDO row);

    SysAttachmentDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysAttachmentDO row);

    int updateByPrimaryKey(SysAttachmentDO row);

	List<SysAttachmentDO> selectByLists(SysAttachmentBean reqDTO);

	List<SysAttachmentDO> getListByOrderId(SysAttachmentBean reqDTO);

    AttachItemBean selectFinAttachById(Long id);

    void updateFinAttachByPrimaryKeySelective(AttachItemBean attach);

    List<SysAttachReqDTO> getBeanListByOrder(String code, Long id);
}