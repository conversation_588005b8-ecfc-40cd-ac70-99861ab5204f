package com.hisun.lemon.urm.uitls.pdf;

import java.math.BigDecimal;

public class GoodsOrderItem {

	private String goodsCode;
	private String goodsName;
	private Integer isPkg;// 是否套餐
	private String pkgCode;// 套餐编号
	private Integer orderQty;
	private BigDecimal price;
	private BigDecimal euPrice;
	private BigDecimal unitHv;//积分
	public String getGoodsCode() {
		return goodsCode;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Integer getIsPkg() {
		return isPkg;
	}
	public void setIsPkg(Integer isPkg) {
		this.isPkg = isPkg;
	}
	public String getPkgCode() {
		return pkgCode;
	}
	public void setPkgCode(String pkgCode) {
		this.pkgCode = pkgCode;
	}
	public Integer getOrderQty() {
		return orderQty;
	}
	public void setOrderQty(Integer orderQty) {
		this.orderQty = orderQty;
	}
	public BigDecimal getPrice() {
		return price;
	}
	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	public BigDecimal getEuPrice() {
		return euPrice;
	}
	public void setEuPrice(BigDecimal euPrice) {
		this.euPrice = euPrice;
	}
	public BigDecimal getUnitHv() {
		return unitHv;
	}
	public void setUnitHv(BigDecimal unitHv) {
		this.unitHv = unitHv;
	}
	
}
