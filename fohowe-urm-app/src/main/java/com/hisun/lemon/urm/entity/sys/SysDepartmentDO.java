/*
 * @ClassName SysDepartmentDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-09 20:39:39
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysDepartmentDO extends BaseDO {
    /**
     * @Fields departmentId 部门ID
     */
    private String departmentId;
    /**
     * @Fields companyCode 公司编码
     */
    private String companyCode;
    /**
     * @Fields parentId 上级部门
     */
    private String parentId;
    /**
     * @Fields departmentName 部门名称
     */
    private String departmentName;
    /**
     * @Fields fullName 部门全称
     */
    private String fullName;
    /**
     * @Fields tel 联系电话
     */
    private String tel;
    /**
     * @Fields fax 传真
     */
    private String fax;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}