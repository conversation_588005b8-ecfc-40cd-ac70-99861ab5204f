package com.hisun.lemon.urm.dao.ticket;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.urm.dto.fi.tickets.TicketsVoucherFDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.urm.dto.fi.tickets.TicketsVoucherBean;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsVoucherDTO;
import com.hisun.lemon.urm.entity.ticket.TicketsVoucherDO;

@Mapper
public interface ITicketsVoucherDao {
	
	List<TicketsVoucherBean> getListByCondition(TicketsVoucherDTO queryDTO);

	Map<String, Object> getTotalCount(TicketsVoucherDTO acVoucherDTO);
	
    List<TicketsVoucherDO> queryList(TicketsVoucherDTO query);
    List<TicketsVoucherDO> selectTicketsByTeacherNo(TicketsVoucherFDTO query);

    int insertList(@Param("list") List<TicketsVoucherDO> list);

    TicketsVoucherDO selectByNo(@Param("ticketsNo") String ticketsNo);
    TicketsVoucherDO selectByPwdNO(@Param("ticketsNo") String ticketsNo);
    TicketsVoucherDO selectByPwd(@Param("ticketsPwd") String ticketsPwd,@Param("ticketsNo") String ticketsNo);

    int deleteByPrimaryKey(Long id);

    int insert(TicketsVoucherDO record);

    int insertSelective(TicketsVoucherDO record);

    TicketsVoucherDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TicketsVoucherDO record);

    int updateByPrimaryKey(TicketsVoucherDO record);
    
    int updateFinStatus(@Param("idArrs") String[] idArrs,
    		@Param("finStatus") int finStatus,
    		@Param("addFinMoney") BigDecimal addFinMoney,
    		@Param("ficheckeStatus") String ficheckeStatus,
    		@Param("ficheckeMemo") String ficheckeMemo
    		);
    //获取入账金额
    TicketsVoucherDO getCashRunningIncome(@Param("ticketsNo") String ticketsNo);
    
    
    
    
    
    
    
}