/*
 * @ClassName MiAgentMemLogDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class MiAgentMemLogDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields updateId 关联ID号
     */
    private Long updateId;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields agentNo 原代办处
     */
    private String agentNo;
    /**
     * @Fields toAgentNo 新代办处
     */
    private String toAgentNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUpdateId() {
        return updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getToAgentNo() {
        return toAgentNo;
    }

    public void setToAgentNo(String toAgentNo) {
        this.toAgentNo = toAgentNo;
    }
}