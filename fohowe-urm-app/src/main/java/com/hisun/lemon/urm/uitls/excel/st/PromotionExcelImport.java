package com.hisun.lemon.urm.uitls.excel.st;

import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.urm.common.MsgCd;
import com.hisun.lemon.urm.dto.pd.PromotionBeanDTO;
import com.hisun.lemon.urm.dto.pd.PromotionGoodsDTO;

public class PromotionExcelImport extends BaseService{
	private Logger logger = LoggerFactory.getLogger(PromotionExcelImport.class);
	private static FormulaEvaluator evaluator;
	public final static Integer  MAX_ABLE_CNT=10000;
	
	
	public List<PromotionBeanDTO>  promotionBatchPreAdd(InputStream in) {
		List<PromotionBeanDTO> list = new ArrayList<>();
		Workbook book = null;
		//XSSFWorkbook book = null;
		try {
			//book = new XSSFWorkbook(in);
			book = WorkbookFactory.create(in);
			evaluator=book.getCreationHelper().createFormulaEvaluator();
		} catch (Exception e) {
			LemonException.throwBusinessException(MsgCd.IS_NOT_FILE.getMsgCd());
		}  
    	if (book == null) {
			LemonException.throwBusinessException(MsgCd.IS_NOT_FILE.getMsgCd());
		}
    	Sheet xssfSheet = book.getSheetAt(0);
    	int rowNum = xssfSheet.getLastRowNum();
    	//忽略表头
    	for (int i = 0; i <= rowNum; i++) {
    		if(i==0){
				logger.debug("跳过表头");
				continue;
			}

			if(rowNum>MAX_ABLE_CNT){
				logger.debug("导入数量大于{}条，忽略后续数据",MAX_ABLE_CNT);
				LemonException.throwBusinessException(MsgCd.IMPORT_FILE_MAX_LINE.getMsgCd());
				break;
			}
    		
    		PromotionBeanDTO promotionBeanDTO = new PromotionBeanDTO(); 
    		List<PromotionGoodsDTO> promoGoodsList = new ArrayList<>();
    		Row row = xssfSheet.getRow(i);
    		
    		try {
    			PromotionGoodsDTO promotionGoodsDTO = new PromotionGoodsDTO();
    			String activeName =getCellValue(row.getCell(0));
        		String memberNo = getCellValue(row.getCell(1));
        		String repoAgentNo = getCellValue(row.getCell(2));
        		String repoCompanyCode = getCellValue(row.getCell(3));
        		String goodsCode = getCellValue(row.getCell(4));
        		Integer quantity = new Integer(getCellValue(row.getCell(5))) ;
        		Integer wWeek = new Integer(getCellValue(row.getCell(6)));
        		
        		
        		promotionBeanDTO.setActiveName(activeName);
        		promotionBeanDTO.setMemberNo(memberNo);
        		promotionBeanDTO.setRepoAgentNo(repoAgentNo);
        		promotionBeanDTO.setRepoCompanyCode(repoCompanyCode);
        		promotionBeanDTO.setCompanyCode(repoCompanyCode);
        		promotionBeanDTO.setwWeek(wWeek);
        		promotionGoodsDTO.setGoodsCode(goodsCode);
        		promotionGoodsDTO.setQuantity(quantity);
        		promoGoodsList.add(promotionGoodsDTO);
        		
        		promotionBeanDTO.setPromoGoodsList(promoGoodsList);
			} catch (Exception e) {
				logger.info("多少行"+i+"报错"+"经销商"+getCellValue(row.getCell(1)));
				e.printStackTrace();
				LemonException.throwBusinessException(MsgCd.IMPORT_FILE_FAILED.getMsgCd());
			}
    		list.add(promotionBeanDTO);
		}
		return list;
	}
	
	
	private String getCellValue(Cell cell){
		if(cell==null){
			return null;
		}
		String str = null;

		if (cell == null || "".equals(cell)){
			return null;
		}else if (cell.getCellTypeEnum() == CellType.BOOLEAN){
			return null;
		}else if (cell.getCellTypeEnum() == CellType.NUMERIC){
			DecimalFormat df = new DecimalFormat("0");
			str=df.format(cell.getNumericCellValue());
		}else if (cell.getCellTypeEnum() == CellType.STRING){
			str=cell.getStringCellValue();
		}else if (cell.getCellTypeEnum() == CellType.FORMULA) {
			str=getCellValue(evaluator.evaluate(cell)); 
		}else{
			return null;
		}
		return str.replaceAll("[\\s\\?]", "").replace("　", "");
	}
	private static String getCellValue(CellValue cell) {
        String cellValue = null;
        switch (cell.getCellTypeEnum()) {
        case STRING:
            cellValue=cell.getStringValue();
            break;
        case NUMERIC:
        	DecimalFormat df = new DecimalFormat("0");
        	cellValue=df.format(cell.getNumberValue());
            break;
        case FORMULA:
            break;
        default:
            break;
        }
        return cellValue;
    }
}
