/*
 * @ClassName FiFgcDepositDO
 * @Description 
 * @version 1.0
 * @Date 2019-10-30 14:43:27
 */
package com.hisun.lemon.urm.entity.fi;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;


public class FiFgcDepositDO extends BaseDO {
    /**
     * @Fields id 存款单头
     */
    private String id;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields companyCode 公司编码
     */
    private String companyCode;
    /**
     * @Fields name 经销商姓名
     */
    private String name;
    /**
     * @Fields rate 利率（购买时的利率）
     */
    private BigDecimal rate;
    /**
     * @Fields yearRate 系统参数利率*（12/定存时长）*100）%
     */
    private BigDecimal yearRate;
    /**
     * @Fields number 存款数量
     */
    private BigDecimal number;
    /**
     * @Fields depositLength 定存时间 单位(月)
     */
    private String depositLength;
    /**
     * @Fields depositTime 到期时间
     */
    private LocalDateTime depositTime;
    /**
     * @Fields periodWeek 购买时期数
     */
    private Integer periodWeek;
    /**
     * @Fields goldPrive 购买时黄金单价
     */
    private BigDecimal goldPrive;
    /**
     * @Fields status 状态 1:新建 2:定存中 3:已取消 4:已结束
     */
    private Integer status;
    /**
     * @Fields depositType 定存类型 1:活期 2:死期
     */
    private Integer depositType;
    /**
     * @Fields cancelPeriod 取消期数
     */
    private Integer cancelPeriod;
    /**
     * @Fields cancelTime 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields cancelCode 取消人
     */
    private String cancelCode;

    /**
     * @Fields auditPeriod 审核期数
     */
    private Integer auditPeriod;
    /**
     * @Fields auditTime 审核时间
     */
    private LocalDateTime auditTime;
    /**
     * @Fields auditCode 审核人
     */
    private String auditCode;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields createCode 创建人
     */
    private String createCode;
    /**
     * @Fields updateCode 更新人
     */
    private String updateCode;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    //总利息
    private BigDecimal interest;
    //总FGC数
    private BigDecimal countFgc;

    /*查询条件*/
    private String[] bonusTypes;

    private String[] areaCodes;

    private String[] companyCodes;


    private Integer startWeek;

    private Integer endWeek;

    private Integer canStartWeek;

    private Integer canEndWeek;

    private Integer auditStartWeek;

    private Integer auditEndWeek;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime dueStartTime;

    private LocalDateTime dueEndTime;

    private LocalDateTime canStartTime;

    private LocalDateTime canEndTime;
    
    private String cardType;
    
    private BigDecimal preorderAmount;
    
    private BigDecimal payAmount;
    
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public String getDepositLength() {
        return depositLength;
    }

    public void setDepositLength(String depositLength) {
        this.depositLength = depositLength;
    }

    public LocalDateTime getDepositTime() {
        return depositTime;
    }

    public void setDepositTime(LocalDateTime depositTime) {
        this.depositTime = depositTime;
    }

    public Integer getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(Integer periodWeek) {
        this.periodWeek = periodWeek;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCancelPeriod() {
        return cancelPeriod;
    }

    public void setCancelPeriod(Integer cancelPeriod) {
        this.cancelPeriod = cancelPeriod;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public String getUpdateCode() {
        return updateCode;
    }

    public void setUpdateCode(String updateCode) {
        this.updateCode = updateCode;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String[] getBonusTypes() {
        return bonusTypes;
    }

    public void setBonusTypes(String[] bonusTypes) {
        this.bonusTypes = bonusTypes;
    }

    public String[] getAreaCodes() {
        return areaCodes;
    }

    public void setAreaCodes(String[] areaCodes) {
        this.areaCodes = areaCodes;
    }

    public String[] getCompanyCodes() {
        return companyCodes;
    }

    public void setCompanyCodes(String[] companyCodes) {
        this.companyCodes = companyCodes;
    }

    public LocalDateTime getDueStartTime() {
        return dueStartTime;
    }

    public void setDueStartTime(LocalDateTime dueStartTime) {
        this.dueStartTime = dueStartTime;
    }

    public LocalDateTime getDueEndTime() {
        return dueEndTime;
    }

    public void setDueEndTime(LocalDateTime dueEndTime) {
        this.dueEndTime = dueEndTime;
    }

    public LocalDateTime getCanStartTime() {
        return canStartTime;
    }

    public void setCanStartTime(LocalDateTime canStartTime) {
        this.canStartTime = canStartTime;
    }

    public LocalDateTime getCanEndTime() {
        return canEndTime;
    }

    public void setCanEndTime(LocalDateTime canEndTime) {
        this.canEndTime = canEndTime;
    }

    public Integer getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(Integer startWeek) {
        this.startWeek = startWeek;
    }

    public Integer getEndWeek() {
        return endWeek;
    }

    public void setEndWeek(Integer endWeek) {
        this.endWeek = endWeek;
    }

    public Integer getCanStartWeek() {
        return canStartWeek;
    }

    public void setCanStartWeek(Integer canStartWeek) {
        this.canStartWeek = canStartWeek;
    }

    public Integer getCanEndWeek() {
        return canEndWeek;
    }

    public void setCanEndWeek(Integer canEndWeek) {
        this.canEndWeek = canEndWeek;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public Integer getDepositType() {
        return depositType;
    }

    public void setDepositType(Integer depositType) {
        this.depositType = depositType;
    }

    public Integer getAuditPeriod() {
        return auditPeriod;
    }

    public void setAuditPeriod(Integer auditPeriod) {
        this.auditPeriod = auditPeriod;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public void setAuditCode(String auditCode) {
        this.auditCode = auditCode;
    }

    public BigDecimal getCountFgc() {
        return countFgc;
    }

    public void setCountFgc(BigDecimal countFgc) {
        this.countFgc = countFgc;
    }

    public Integer getAuditStartWeek() {
        return auditStartWeek;
    }

    public void setAuditStartWeek(Integer auditStartWeek) {
        this.auditStartWeek = auditStartWeek;
    }

    public Integer getAuditEndWeek() {
        return auditEndWeek;
    }

    public void setAuditEndWeek(Integer auditEndWeek) {
        this.auditEndWeek = auditEndWeek;
    }

    public BigDecimal getYearRate() {
        return yearRate;
    }

    public void setYearRate(BigDecimal yearRate) {
        this.yearRate = yearRate;
    }

    public BigDecimal getGoldPrive() {
        return goldPrive;
    }

    public void setGoldPrive(BigDecimal goldPrive) {
        this.goldPrive = goldPrive;
    }

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public BigDecimal getPreorderAmount() {
		return preorderAmount;
	}

	public void setPreorderAmount(BigDecimal preorderAmount) {
		this.preorderAmount = preorderAmount;
	}

	public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}
    
}