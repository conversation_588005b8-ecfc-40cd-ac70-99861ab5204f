/*
 * @ClassName IMiMemberDao
<<<<<<< HEAD
 * @Description
=======
 * @Description 
>>>>>>> f898215655a22e6962d23519fb1514495762d680
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceDTO;
import com.hisun.lemon.urm.dto.mi.member.ActiveBean;
import com.hisun.lemon.urm.dto.mi.member.ActiveDTO;
import com.hisun.lemon.urm.dto.mi.member.FgcMemberReqDTO;
import com.hisun.lemon.urm.dto.mi.member.FgcMemberRspDTO;
import com.hisun.lemon.urm.dto.mi.member.MIBaseInfo;
import com.hisun.lemon.urm.dto.mi.member.MemAgentQueryDTO;
import com.hisun.lemon.urm.dto.mi.member.MemberBeanDO;
import com.hisun.lemon.urm.dto.mi.member.MemberCoreInfoVO;
import com.hisun.lemon.urm.dto.mi.member.MemberDTO;
import com.hisun.lemon.urm.dto.mi.member.MemberDetailBeanDO;
import com.hisun.lemon.urm.dto.mi.member.MemberQueryBean;
import com.hisun.lemon.urm.dto.mi.member.SubMember;
import com.hisun.lemon.urm.dto.mi.member.SubMemberDO;
import com.hisun.lemon.urm.entity.MiMemberDO;

/**
 * 此类由lemon-generator 自动生成
 * dao的其他方法在此扩展
 *
 * <AUTHOR>
 * @date 2017年10月31日
 * @time 上午9：45：21
 */
@Mapper
public interface IMiMemberDao extends BaseDao<MiMemberDO> {
    List<MemberQueryBean> getListByCondition(MemberDTO memberDTO);

    int updateBelongAgentByMember(@Param("memberNo") String memberNo, @Param("toAgentNo") String toAgentNo, @Param("companyCode") String companyCode, @Param("channelType") String channelType);

    int updateBelongAgentByAgent(@Param("agentNo") String agentNo, @Param("toAgentNo") String toAgentNo, @Param("companyCode") String companyCode, @Param("channelType") String channelType);

    int updateBelongAgentByLinkNet(@Param("memberNo") String memberNo,
                                   @Param("toAgentNo") String toAgentNo, @Param("agentNo") String agentNo, @Param("companyCode") String companyCode, @Param("channelType") String channelType);

    /**
     * 通过经销商编号查询经销商
     *
     * @param memberNo
     * @return
     */
    MiMemberDO getByNo(String memberNo);

    MemberBeanDO getByMemNo(String memberNo);
    
    MemberBeanDO getMemAndProm(String memberNo);

    MemberDetailBeanDO getDetailByMemNo(String memberNo);

    /**
     * 通过所属代办处编号,批量修改经销商
     *
     * @param memberDO
     */
    void updateByAgent(MiMemberDO memberDO);

    /**
     * 查询总记录数
     *
     * @param memberDTO
     * @return
     */
    int getTotalCount(MemberDTO memberDTO);

    /**
     * 根据用户编号查询基本信息
     *
     * @param userCode
     * @return
     */
    MIBaseInfo searchBaseInfo(String userCode);

    /**
     * 获取经销商的主要信息
     *
     * @param memberNO
     * @return
     * <AUTHOR>
     */
    MemberCoreInfoVO getMemberCoreInfo(@Param("memberNO") String memberNO);

    MemberCoreInfoVO getMemberCoreInfoByRightNo(@Param("rightNo") String rightNo);

    /**
     * 将推荐人为recMemberNO的用户  的推荐人更新为newMemberNO
     *
     * @param recMemberNO
     * @param newMemberNO
     * <AUTHOR>
     */
    void updateMemToAnotherByRecmem(@Param("recMemberNO") String recMemNO, @Param("newMemberNO") String newMemberNO);

    /**
     * 将用户置为注销
     *
     * @param memberNo
     * <AUTHOR>
     */

    void updateMemExitStatusByMemNO(@Param("memberNo") String memberNo, @Param("exitStatus") String exitStatus);

    /**
     * 下属经销商查询总条数统计
     *
     * @param subMemberDO
     * @return
     */
    int getSubTotalCount(SubMemberDO subMemberDO);

    /**
     * 下属经销商查询
     *
     * @param subMemberDO
     * @return
     */
    List<SubMember> getSubListByCondition(SubMemberDO subMemberDO);

    /**
     * 活跃期数查询-总数
     *
     * @param dto
     * @return
     * <AUTHOR>
     */
    int getActiveTotalCount(ActiveDTO dto);

    /**
     * 活跃期数查询-分页
     *
     * @param dto
     * @return
     * <AUTHOR>
     */
    List<ActiveBean> getActiveListByPageBreak(ActiveDTO dto);

    void deleteByNo(String memberNo);

    /**
     * 根据linkIndex查询是否有下线
     *
     * @param linkIndex
     * @param memberNO
     * @return
     */
    int searchSubByLinkIndex(@Param("linkIndex") String linkIndex, @Param("memberNo") String memberNO);

    /**
     * 根据所属代办处查询经销商
     *
     * @param agentNo
     * @return
     */
    List<MemAgentQueryDTO> searchByAgentNo(String agentNo);

    /**
     * 获取用户活跃期数
     *
     * @param memberNo
     * @return
     * <AUTHOR>
     */
    String getActiveWeekNum(@Param("memberNo") String memberNo);

    /**
     * 更新合格经营权时间为空
     *
     * @param memberNo
     * <AUTHOR>
     */
    void updateActiveTimeNull(@Param("memberNo") String memberNo);

    /**
     * 更新入网时间为空
     *
     * @param memberNo
     */
    void updatePeriodWeekNull(String memberNo);

    /**
     * 修改用户推荐人
     *
     * @param newRecNo
     * @param memberNo
     * <AUTHOR>
     */
    void updateRecNoByMemberNo(@Param("newRecNo") String newRecNo, @Param("memberNo") String memberNo);

    List<MemberQueryBean> getNameList(@Param("memberNoList") List<String> memberNoList);


    List<FgcMemberRspDTO> getListByMember(AcBalanceDTO acBalanceDTO);

    int updateFgcBalance(FgcMemberReqDTO fgcMemberReqDTO);

    FgcMemberRspDTO Memberlock(FgcMemberReqDTO fgcMemberReqDTO);

	int updateMemberActivePd(@Param("memberNo") String memberNo,@Param("activePd") BigDecimal activePd);
	
	void updatePromQual(@Param("memberNo") String memberNo,@Param("promQual") Integer promQual);
	
//	MemberBeanDO queryPromPlus(@Param("memberNo") String memberNo,@Param("promType") String promType,@Param("periodWeek") Integer periodWeek);
	MemberBeanDO queryPromPlus(@Param("memberNo") String memberNo);
	
	MemberBeanDO queryPromVip(@Param("memberNo") String memberNo);
	
	MemberBeanDO queryFendou(@Param("memberNo") String memberNo);

	MemberBeanDO getMyfRecInfo(@Param("memberNo") String memberNo);

}