package com.hisun.lemon.urm.uitls.excel.fi;



import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.fohowe.common.enums.CardTypes;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.urm.entity.FiFgcExchangeDO;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcFgcExchangeExcelExporter extends  URMExcelExportFactorys{
	/**
	 * fgc兑换 FGC购买销售导出
	 * @param fileName
	 * @param colNames
	 * @param obj
	 * @param response
	 */
	public void export(String fileName,Object obj, HttpServletResponse response) { 
		String[] colNames= {"分公司","代办处","经销商编号","经销商姓名","经销商级别","数量","总金额","可预购数量","创建时间","期次","黄金单价","兑美元汇率","兑F$汇率"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		PageInfo<FiFgcExchangeDO> info =(PageInfo<FiFgcExchangeDO>)obj;
		List<FiFgcExchangeDO> dataList=info.getList();
		Map<Object, String> carTypeKV = EnumsUtils.EnumToMap(CardTypes.class);
		for(FiFgcExchangeDO fgc:dataList) {
			Row row = sheet.createRow(beginRow++);
		    String memberNo =fgc.getMemberNo();///经销商编号
		    String agentNo =fgc.getAgentNo();//代办处编号
		    String companyCode =fgc.getCompanyCode();//公司编码
		    String name =fgc.getName();//经销商姓名
		    BigDecimal goldPrive =fgc.getGoldPrive()==null?BigDecimal.ZERO:fgc.getGoldPrive();//黄金单价
		    BigDecimal usaRate =fgc.getUsaRate()==null?BigDecimal.ZERO:fgc.getUsaRate();//兑美元汇率
		    BigDecimal fRate =fgc.getfRate()==null?BigDecimal.ZERO:fgc.getfRate();//兑F$汇率
		    BigDecimal payAmount =fgc.getPayAmount()==null?BigDecimal.ZERO:fgc.getPayAmount();//需支付的F$
		    BigDecimal number =fgc.getNumber()==null?BigDecimal.ZERO:fgc.getNumber();//兑换数量
		    Integer periodWeek =fgc.getPeriodWeek()==null?0:fgc.getPeriodWeek();;//购买期数
		    LocalDateTime createTime =fgc.getCreateTime();//创建时间
		    String cardType =fgc.getCardType()==null?"":carTypeKV.get(fgc.getCardType());//经销商级别
		    BigDecimal preorderAmount =fgc.getPreorderAmount()==null?BigDecimal.ZERO:fgc.getPreorderAmount();//可购买数量 
		    
			row.createCell(0).setCellValue(companyCode);
			row.createCell(1).setCellValue(agentNo);
			row.createCell(2).setCellValue(memberNo);
			row.createCell(3).setCellValue(name);
			row.createCell(4).setCellValue(cardType);
			row.createCell(5).setCellValue(number.doubleValue());
			row.createCell(6).setCellValue(payAmount.doubleValue());
			row.createCell(7).setCellValue(preorderAmount.doubleValue());
			row.createCell(8).setCellValue(createTime==null?"":createTime.format(ymdhms));
			row.createCell(9).setCellValue(periodWeek);
			row.createCell(10).setCellValue(goldPrive.doubleValue());
			row.createCell(11).setCellValue(usaRate.doubleValue());
			row.createCell(12).setCellValue(fRate.doubleValue()); 
		}
		
	}
	public static AcFgcExchangeExcelExporter builder() {
		return new AcFgcExchangeExcelExporter();
	}
}
