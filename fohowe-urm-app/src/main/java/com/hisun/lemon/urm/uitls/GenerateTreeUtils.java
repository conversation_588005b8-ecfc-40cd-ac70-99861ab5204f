package com.hisun.lemon.urm.uitls;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.hisun.lemon.urm.dto.mi.member.MemMainTree;
import com.hisun.lemon.urm.dto.mi.member.MemRecTree;
import com.hisun.lemon.urm.dto.mi.member.NetWorkBean;
import com.hisun.lemon.urm.dto.mi.member.NetWorkTO;
import com.hisun.lemon.urm.dto.mi.member.RightIndivShortBean;
import com.hisun.lemon.urm.dto.mi.member.RightNetworkBean;
import com.hisun.lemon.urm.enums.TreeDirectionEnums;



public class GenerateTreeUtils {
	public GenerateTreeUtils() {
		// TODO Auto-generated constructor stub
	}
	private List<NetWorkBean> treeList=new ArrayList<NetWorkBean>();
	List<NetWorkBean> list=null;
	
	
	public List<NetWorkBean> getList() {
		return list;
	}

	public void setList(List<NetWorkBean> list) {
		this.list = list;
	}

	public List<NetWorkBean> generateTree2(NetWorkBean node,int flag) {
		if (hasChild2(list, node)) {
			List<NetWorkBean> childList = getChildList2(list, node); 
			for(NetWorkBean child:childList){
				if(flag==0) {
					treeList.add(child);
				}else{
					node.getSubBean().add(child);
				}
				generateTree2(child,1);
			}
			
		} else {
			
		}
		return treeList;
	}

	public boolean hasChild2(List<NetWorkBean> list, NetWorkBean node) { 
		boolean flag = false;
		List<NetWorkBean> l = getChildList2(list, node);
		if (l != null && l.size() > 0) {
			flag = true;
		}
		return flag;
	}
	public List<NetWorkBean> getChildList2(List<NetWorkBean> list, NetWorkBean node) { 
		List<NetWorkBean> li = new ArrayList<NetWorkBean>();
		Iterator<NetWorkBean> it = list.iterator();
		while (it.hasNext()) {
			NetWorkBean n = (NetWorkBean) it.next();
			if (n != null) {
				if (StringUtils.isNotBlank((n.getParentMemNo()))) {
					if (n.getParentMemNo().equals(node.getMemNo())) {
						li.add(n);
					}
				}
			}
		}
		return li;
	}


	public MemRecTree generateMemRecTree(List<MemRecTree> list, NetWorkTO to) throws Exception {
		
		MemRecTree t = null;
		if(TreeDirectionEnums.UP.toString().equals(to.getDirection())) {
			t=BuildTree.buildTreeUp(list,to);
		}else {
			t=BuildTree.buildTreeDown(list,to);
		}
				
		
		return t;
	}

	public RightNetworkBean generateRightTree(List<RightNetworkBean> list, NetWorkTO to) throws Exception {
		RightNetworkBean t = null;
		if(TreeDirectionEnums.UP.toString().equals(to.getDirection())) {
			t=BuildTree.buildRightTreeUp(list,to);
		}else {
			t=BuildTree.buildRightTreeDown(list,to);
		}
		
		return t;
	}

	public RightIndivShortBean generateRightIndivNetworkShortTree(List<RightIndivShortBean> list2, NetWorkTO to) {
		// TODO Auto-generated method stub
		return BuildTree.buildRightIndivNetworkShortTreeDown(list2,to);
	}

	public MemMainTree generateMemberMainTree(List<MemMainTree> list2, NetWorkTO to, String type) {
		// TODO Auto-generated method stub
		return BuildTree.buildMemberMainDown(list2,to,type);
	}
	

	
	



}
