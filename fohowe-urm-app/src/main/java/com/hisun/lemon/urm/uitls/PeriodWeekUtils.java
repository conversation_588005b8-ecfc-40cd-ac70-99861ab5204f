package com.hisun.lemon.urm.uitls;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hisun.lemon.bns.client.period.PeriodClient;
import com.hisun.lemon.bns.dto.period.PeriodQueryDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.UserType;
import com.hisun.lemon.fohowe.ec.enums.ResCode;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.sys.ConfigValueQueryReqDTO;
import com.hisun.lemon.urm.dto.sys.ConfigValueQueryRspDTO;
import com.hisun.lemon.urm.entity.sys.SysUserDO;
import com.hisun.lemon.urm.service.sys.IConfigService;
import com.hisun.lemon.urm.service.sys.IUserService;
/**
 * 获取期数工具
 * <AUTHOR>
 * @date 2017-11-14 17:18
 */
@Component
public class PeriodWeekUtils {
	public static final DateTimeFormatter FORMATTER1_YMD = DateTimeFormatter.ofPattern("yyyyMMdd");
	private static String SYS_USER_QUERY_LIMIT="sys_user_query_limit";
	@Autowired 
	private PeriodClient periodClient;
	@Autowired 
	private IConfigService configService;
	@Autowired 
	private IUserService userService;
	
	private PeriodWeekUtils() {
		// TODO Auto-generated constructor stub
	}
	
/*	public static String getCurrentPeriodWeek() {
		  GenericRspDTO<List<RightDistributeBean>> rep = memberRightClient.getRightList(dto);
	        if (JudgeUtils.isNotSuccess(rep.getMsgCd())) {
	            LemonException.throwLemonException(ResCode.EC0065.getCode(), ResCode.EC0065.getName());
	        }

		return LocalDate.now().minusWeeks(1l).with(DayOfWeek.SATURDAY).format(FORMATTER1_YMD).substring(2);
	}*/
	
	public  String getCurrentPeriodWeekFromBns() {

		//获取当前期数
        GenericDTO<NoBody> dto = new GenericDTO<NoBody>();
        GenericRspDTO<PeriodQueryDTO.Period> rsp = periodClient.queryCurPeriod(dto);
        if (JudgeUtils.isNotSuccess(rsp.getMsgCd())) {
            LemonException.throwBusinessException(rsp.getMsgCd());
        }
        PeriodQueryDTO.Period period = rsp.getBody();
        if (period == null) {
            LemonException.throwBusinessException(MsgCdLC.GET_PERIOD_ERROR.getMsgCd());
        }
        return period.getwWeek()+"";
       
	}
	public Integer getCurrentPeriodWeek() {
		
		//获取当前期数
		GenericDTO<NoBody> dto = new GenericDTO<NoBody>();
		GenericRspDTO<PeriodQueryDTO.Period> rsp = periodClient.queryCurPeriod(dto);
		if (JudgeUtils.isNotSuccess(rsp.getMsgCd())) {
			LemonException.throwBusinessException(rsp.getMsgCd());
		}
		PeriodQueryDTO.Period period = rsp.getBody();
		if (period == null) {
			LemonException.throwBusinessException(MsgCdLC.GET_PERIOD_ERROR.getMsgCd());
		}
		return period.getwWeek();
		
	}
	/**
	 * 根据登录用户所属分公司校验是否限制查询
	 * @param companyCode
	 * @return
	 */
	public LocalDateTime getQueryLimitNum() {
		 SysUserDO userDO = userService.getLoginInf(LemonUtils.getUserId());
		 if(userDO == null) return null;
		 if(UserType.COMPANY.getCode().equals(userDO.getUserType())) {
			 return null;
		 }
		 if(UserType.HEAD.getCode().equals(userDO.getUserType())) {
			 return null;
		 }
		 String companyCode=userDO.getCompanyCode();
    	 ConfigValueQueryReqDTO configReqDTO =new ConfigValueQueryReqDTO();
         configReqDTO.setCompanyCode(companyCode);
         configReqDTO.setConfigCode(SYS_USER_QUERY_LIMIT);
         ConfigValueQueryRspDTO configValue = configService.configValueQueryList(configReqDTO);
		 int limitNum = 0;
		 //logger.info("configValue:"+configValue);
		 if(configValue != null && configValue.getValueList().size()>0 
				&& JudgeUtils.isNotBlank(configValue.getValueList().get(0).getConfigValue())) {
			limitNum = Integer.parseInt(configValue.getValueList().get(0).getConfigValue());
		 }
		 LocalDateTime startTime = null;
		 if(limitNum>0) {
			GenericDTO<NoBody> dto1 = new GenericDTO<NoBody>();
	        GenericRspDTO<PeriodQueryDTO.Period> periodRsp = periodClient.queryCurPeriod(dto1);
	        if (JudgeUtils.isNotSuccess(periodRsp.getMsgCd())) {
	            LemonException.throwBusinessException(periodRsp.getMsgCd());
	        }
	        PeriodQueryDTO.Period period = periodRsp.getBody();
	        if (period == null) {
	            LemonException.throwBusinessException(ResCode.EC0083.getCode());
	        }else {
	        	//将天数减少n*7
	        	startTime=period.getStartDate().minusDays(limitNum*7);
	        }
		 }
		 return startTime;
	}
}
