/*
 * @ClassName ISysUserDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:09:01
 */
package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysUserUsdtDO;

@Mapper
public interface ISysUserUsdtDao extends BaseDao<SysUserUsdtDO> {
    /**
     * 
     * @Title: pageQueryRecord 
     * @Description: 分页查询操作员信息
     * @return
     * @return: List<SysUserUsdtDO>
     */
    public List<SysUserUsdtDO> pageQuery(SysUserUsdtDO usdtDo);

    /** 
     * @Title: recordIsExists 
     * @Description: 检查用户是否存在
     * @param usdtCode
     * @return
     * @return: SysUserUsdtDO
     */
    public SysUserUsdtDO recordIsExists(@Param("usdtCode") String usdtCode);
    
    public SysUserUsdtDO getUsdtInfo(@Param("usdtCode") String usdtCode);
    public SysUserUsdtDO getUsdtInfoById(@Param("usdtId") String usdtId);
    
    public SysUserUsdtDO getById(@Param("id") Long id);
    
    public int deleteBYId(@Param("id") Long id);

	public void updateByEmail(SysUserUsdtDO usdtDO);
}