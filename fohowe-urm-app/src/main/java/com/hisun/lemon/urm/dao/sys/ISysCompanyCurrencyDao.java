/*
 * @ClassName ISysCompanyCurrencyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-04 20:08:11
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysCompanyCurrencyDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysCompanyCurrencyDao extends BaseDao<SysCompanyCurrencyDO> {

    /**
     * 
     * @Title: findRecord
     * @Description: 根据公司编号和货币编号查询记录
     * @param companyCode
     * @param currencyCode
     * @return
     * @return: SysCompanyCurrencyDO
     */
    public SysCompanyCurrencyDO findRecord(@Param("companyCode") String companyCode,
            @Param("currencyCode") String currencyCode);

    /**
     * @Title: deleteByCompany
     * @Description: 删除公司和货币对应关系
     * @param currencyCode
     * @param list
     * @return
     * @return: int
     */
    public int deleteByCompany(@Param("currencyCode") String currencyCode,
            @Param("companyList") List<String> companyList);

    /**
     * @Title: deleteByCurrency
     * @Description: 删除公司和货币对应关系
     * @param currencyCode
     * @return
     * @return: int
     */
    public int deleteByCurrency(@Param("currencyCode") String currencyCode);

    /**
     * @Title: findCompanyRecord
     * @Description: 查询货币对应分公司列表
     * @param currencyCode
     * @return
     * @return: List<SysCompanyCurrencyDO>
     */
    public List<SysCompanyCurrencyDO> findCompanyRecord(@Param("currencyCode") String currencyCode);

    /**
     * @Title: findCompanyRecord
     * @Description: 查询货币可使用分公司列表
     * @param currencyCode
     * @return
     * @return: List<SysCompanyCurrencyDO>
     */
    public List<SysCompanyCurrencyDO> findEnableCompany(@Param("currencyCode") String currencyCode);

    /**
     * @Title: findRecordByCompany
     * @Description: 根据分公司查询对应货币
     * @param companyCode
     * @return
     * @return: SysCompanyCurrencyDO
     */
    public List<SysCompanyCurrencyDO> findRecordByCompany(@Param("companyCode") String companyCode);
    
    public List<SysCompanyCurrencyDO> findRecordByCompanys(@Param("companyList") String[] companyList);
}