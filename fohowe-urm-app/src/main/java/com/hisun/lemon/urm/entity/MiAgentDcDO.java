/*
 * @ClassName MiAgentDcDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class MiAgentDcDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields agentNo 编号
     */
    private String agentNo;
    /**
     * @Fields companyCode 允许订货仓库
     */
    private String companyCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
}