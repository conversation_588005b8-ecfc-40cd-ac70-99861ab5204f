package com.hisun.lemon.urm.uitls;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.hisun.lemon.common.utils.JudgeUtils;

/**
 * <AUTHOR> fy
 * @description :
 * @create : 2019-11-30 15:43
 **/
public class HttpClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);
    private final static String CONTENT_TYPE_TEXT_JSON = "text/json";
    
    @Resource(name = "secondSkipRestTemplate")
    private RestTemplate secondSkipRestTemplate;
    
    private HttpClientUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static String doGet(String url, Map<String, String> param) throws IOException, URISyntaxException {
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            // 创建uri
            URIBuilder builder;
            try {
                builder = new URIBuilder(url);
                if (param != null) {
                    for (String key : param.keySet()) {
                        builder.addParameter(key, param.get(key));
                    }
                }
                URI uri = builder.build();
                // 创建http GET请求
                HttpGet httpGet = new HttpGet(uri);
                // 执行请求
                response = httpclient.execute(httpGet);
                // 判断返回状态是否为200
                if (response.getStatusLine().getStatusCode() == 200) {
                    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                }
            } finally {
                if (null != response) {
                    response.close();
                }
            }
        } catch (URISyntaxException e) {
            logger.debug("Network err occured at http send", e);
            throw e;
        } catch (ClientProtocolException e) {
            logger.debug("Network err occured at http send", e);
            throw e;
        } catch (IOException e) {
            logger.debug("Network err occured at http send", e);
            throw e;
        }
        return resultString;
    }

    public static String doGet(String url) throws IOException, URISyntaxException {
        return doGet(url, null);
    }

    public static String doPost(String url, Map<String, String> param) throws IOException {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            try {
                // 创建Http Post请求
                HttpPost httpPost = new HttpPost(url);
                // 创建参数列表
                if (param != null) {
                    List<NameValuePair> paramList = new ArrayList<>();
                    for (String key : param.keySet()) {
                        paramList.add(new BasicNameValuePair(key, param.get(key)));
                    }
                    // 模拟表单
                    UrlEncodedFormEntity entity;
                    entity = new UrlEncodedFormEntity(paramList);
                    httpPost.setEntity(entity);
                }
                // 执行http请求
                response = httpClient.execute(httpPost);
                resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            } finally {
                if (null != response) {
                    response.close();
                }
            }
        } catch (UnsupportedEncodingException e) {
            logger.debug("Network err occured at http send", e);
            throw e;
        } catch (ClientProtocolException e) {
            logger.debug("Network err occured at http send", e);
            throw e;
        } catch (IOException e) {
            logger.debug("Network err occured at http send", e);
            throw e;
        }
        return resultString;
    }
    
    public static HttpClient getHttpsClient() {
        HttpClient httpClient = null;
        SSLContext context;
        try {
            context = SSLContext.getInstance("SSL");
            context.init(null, new TrustManager[] {new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] paramArrayOfX509Certificate, String paramString)
                        throws CertificateException {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] paramArrayOfX509Certificate, String paramString)
                        throws CertificateException {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

            }}, new SecureRandom());

            HostnameVerifier verifier = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(context, verifier);
            httpClient = HttpClients.custom().setSSLSocketFactory(sslConnectionSocketFactory).build();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return httpClient;
    }
    /**
     * 解决SSL访问问题 
     * @param url
     * @param param
     * @return
     * @throws IOException
     */
    public static String doPosts(String url, Map<String, String> param) throws IOException {
    	// 创建Httpclient对象
    	HttpClient httpClient = getHttpsClient();
    	HttpResponse response = null;
    	String resultString = "";
    	
    	try {
    		try {
    			// 创建Http Post请求
    			HttpPost httpPost = new HttpPost(url);
    			// 创建参数列表
    			if (param != null) {
    				List<NameValuePair> paramList = new ArrayList<>();
    				for (String key : param.keySet()) {
    					paramList.add(new BasicNameValuePair(key, param.get(key)));
    				}
    				// 模拟表单
    				UrlEncodedFormEntity entity;
    				entity = new UrlEncodedFormEntity(paramList);
    				httpPost.setEntity(entity);
    			}
    			// 执行http请求
    			response = httpClient.execute(httpPost);
    			resultString = EntityUtils.toString(response.getEntity(), "utf-8");
    		} finally {
    			 
    		}
    	} catch (UnsupportedEncodingException e) {
    		logger.debug("Network err occured at http send", e);
    		throw e;
    	} catch (ClientProtocolException e) {
    		logger.debug("Network err occured at http send", e);
    		throw e;
    	} catch (IOException e) {
    		logger.debug("Network err occured at http send", e);
    		throw e;
    	}
    	return resultString;
    }

    public static String doPost(String url) throws IOException {
        return doPost(url, null);
    }

    public static String doPostJson(String url, String json) throws IOException {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            try {
                // 创建Http Post请求
                HttpPost httpPost = new HttpPost(url);
                // 创建请求内容
                StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
                httpPost.setEntity(entity);
                // 执行http请求
                response = httpClient.execute(httpPost);
                resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            } finally {
                if (null != response) {
                    response.close();
                }
            }
        } catch (ClientProtocolException e) {
            logger.debug("Unexpected err occured at http send", e);
            throw e;
        } catch (IOException e) {
            logger.debug("Network err occured at http send", e);
            throw e;
        }
        return resultString;
    }

    public static String sendPostHead(String url, Map<String, String> heads, Map<String, String> params)
            throws IOException {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        if (JudgeUtils.isNotNull(heads)) {
            for (String key : heads.keySet()) {
                System.out.println("key= " + key + " and value= " + heads.get(key));
                httpPost.setHeader(key, heads.get(key));
            }
        }
        if (JudgeUtils.isNotNull(params)) {
            //Gson gson = new Gson();
            //String parameter = gson.toJson(params);
        	String parameter = JSON.toJSONString(params); 
            StringEntity se = new StringEntity(parameter);
            se.setContentType(CONTENT_TYPE_TEXT_JSON);
            httpPost.setEntity(se);
        }
        CloseableHttpResponse response = client.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String result = EntityUtils.toString(entity, "UTF-8");

        return result;
    }
    

	public static boolean getJSONType(String str) {
		boolean result = false;
		if (JudgeUtils.isNotBlank(str)) {
			str = str.trim();
			if (str.startsWith("{") && str.endsWith("}")) {
				result = true;
			} else if (str.startsWith("[") && str.endsWith("]")) {
				result = true;
			}
		}
		return result; 
	}
}
