/*
 * @ClassName FiAcBalanceRequestDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-13 21:08:15
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;

public class StStockInventoryDO extends BaseDO {
	
	private Long id;
	private Integer wWeek;
	private String bonusType;
	private String areaCode;
	private String companyCode;
	private String goodsCode;
	private Integer initialQty;
	private Integer outQty;
	private Integer alterOut;
	private Integer inQty;
	private Integer alterIn;
	private Integer finalQty;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getwWeek() {
		return wWeek;
	}
	public void setwWeek(Integer wWeek) {
		this.wWeek = wWeek;
	}
	public String getBonusType() {
		return bonusType;
	}
	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getGoodsCode() {
		return goodsCode;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	public Integer getInitialQty() {
		return initialQty;
	}
	public void setInitialQty(Integer initialQty) {
		this.initialQty = initialQty;
	}
	public Integer getOutQty() {
		return outQty;
	}
	public void setOutQty(Integer outQty) {
		this.outQty = outQty;
	}
	public Integer getAlterOut() {
		return alterOut;
	}
	public void setAlterOut(Integer alterOut) {
		this.alterOut = alterOut;
	}
	public Integer getInQty() {
		return inQty;
	}
	public void setInQty(Integer inQty) {
		this.inQty = inQty;
	}
	public Integer getAlterIn() {
		return alterIn;
	}
	public void setAlterIn(Integer alterIn) {
		this.alterIn = alterIn;
	}
	public Integer getFinalQty() {
		return finalQty;
	}
	public void setFinalQty(Integer finalQty) {
		this.finalQty = finalQty;
	}
}