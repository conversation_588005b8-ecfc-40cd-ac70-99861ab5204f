/*
 * @ClassName ISysUserDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:09:01
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysUserDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysUserDao extends BaseDao<SysUserDO> {
    /**
     * 
     * @Title: pageQueryRecord 
     * @Description: 分页查询操作员信息
     * @param userCode
     * @param userName
     * @param companyCode
     * @param departmentId
     * @param areaCode 
     * @return
     * @return: List<SysUserDO>
     */
    public List<SysUserDO> pageQueryRecord(@Param("userCode") String userCode, @Param("userName") String userName,
            @Param("companyCode") String companyCode, @Param("departmentId") String departmentId,
            @Param("areaCode") String areaCode);

    /** 
     * @Title: findIsDefLang 
     * @Description: 是否存在用户为该默认语言
     * @param langCode
     * @return
     * @return: SysUserDO
     */
    public SysUserDO findIsDefLang(@Param("defLang") String defLang);

    /** 
     * @Title: recordIsExists 
     * @Description: 检查用户是否存在
     * @param userCode
     * @return
     * @return: SysUserDO
     */
    public SysUserDO recordIsExists(@Param("userCode") String userCode);

    /** 
     * @Title: getLoginInf 
     * @Description: 用户登录查询信息
     * @param userCode
     * @return
     * @return: SysUserDO
     */
    public SysUserDO getLoginInf(@Param("userCode") String userCode);
    /**
     * 通过经销商编号变更用户所属分公司
     * @param memberNo
     * @param companyCode
     */
	public void updateCompanyByMember(@Param("memberNo")String memberNo,@Param("companyCode") String companyCode);
	/**
	 * 通过所属代办处批量变更用户所属分公司
	 * @param toAgentNo
	 * @param companyCode
	 */
	public void updateCompanyByAgent(@Param("toAgentNo")String toAgentNo,@Param("companyCode") String companyCode);
	/**
	 * 批量变更经销商及其下属所属分公司
	 * @param memberNo
	 * @param toAgentNo
	 * @param companyCode
	 */
	public void updateCompanyByAgentLink(@Param("memberNo")String memberNo,@Param("toAgentNo")String toAgentNo, @Param("companyCode")String companyCode);

	public int recordIsExistsTkt515(@Param("userCode") String userCode);
}