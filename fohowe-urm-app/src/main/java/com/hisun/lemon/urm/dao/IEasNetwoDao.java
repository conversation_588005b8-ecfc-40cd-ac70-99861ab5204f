/*
 * @ClassName IEasNetwoDao
 * @Description 
 * @version 1.0
 * @Date 2018-08-24 11:42:20
 */
package com.hisun.lemon.urm.dao;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.EasNetwork;

@Mapper
public interface IEasNetwoDao extends BaseDao<EasNetwork> {
	
	int searchBusinessid(EasNetwork eas);
	
	int getBusinessSn(EasNetwork eas);
	
	int updateAppl(Integer id);
	
	int updateInput(Integer id);

	int updateBonusFund(Integer id);

	int updateBonusShare(Integer id);
	
	String getInputEasStatus(Integer id);

	String getApplEasStatus(Integer id);

	String getBonusFundEasStatus(Integer id);

	String getBonusShareEasStatus(Integer id);

}