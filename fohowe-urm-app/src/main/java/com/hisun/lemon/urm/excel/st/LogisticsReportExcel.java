package com.hisun.lemon.urm.excel.st;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.excel.ExcelExportFactorys;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.entity.st.StLogisticsReport;

/**
 * 物流预警报表
 * <AUTHOR>
 *
 * @param <T>
 */
public class LogisticsReportExcel<T extends StLogisticsReport> extends ExcelExportFactorys<T> {
	
	public void export2(List<StLogisticsReport> dataList, String fileName,String[] colNames,HttpServletResponse response) {
		try {
			response.reset();
			response.setHeader("Content-disposition", "attachment; filename=" +fileName+ ".xlsx");
            response.setContentType("application/msexcel;charset=UTF-8");//设置类型
            response.setHeader("Pragma", "No-cache");//设置头
            response.setHeader("Cache-Control", "no-cache");//设置头
            response.setDateHeader("Expires", 0);//设置日期头
            List<List<String>> head = new ArrayList<List<String>>();
            for (int i = 0; i < colNames.length; i++) {
            	List<String> head0 = new ArrayList<String>();
            	head0.add(colNames[i]);
            	head.add(head0);
    		}
			WriteWorkbook workBook = new WriteWorkbook();
			workBook.setExcelType(ExcelTypeEnum.XLSX);
			workBook.setOutputStream(response.getOutputStream());
			workBook.setNeedHead(true);
			WriteSheet sheet= new WriteSheet();
			sheet.setSheetNo(0);
			sheet.setSheetName(fileName); 
			sheet.setHead(head);
			ExcelWriter write = new ExcelWriter(workBook);
			write.write(addData(dataList), sheet); 
			write.finish();
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
    @Override
    public void addCell(Sheet sheet, CellStyle style, List<T> dataList) throws Exception {
        if (dataList != null && dataList.size() > 0) {
            for (int i = 0; i < dataList.size(); i++) {
            	StLogisticsReport reportDO = dataList.get(i);
                // 创建所需的行数
                Row row = sheet.createRow(i + 3);
                Cell cell = null;
                int index=0;
                // 序号 物理线编号
                cell = row.createCell(index++, CellType.STRING);
                cell.setCellValue(reportDO.getLogisticsNo());
                cell.setCellStyle(style);
                // 序号 物理线名称
                cell = row.createCell(index++, CellType.STRING);
                cell.setCellValue(reportDO.getLogisticsName());
                cell.setCellStyle(style);

                // 商品代码
                cell = row.createCell(index++, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(reportDO.getGoodsCode()) ? "" : reportDO.getGoodsCode());
                cell.setCellStyle(style);

                // 商品名称
                cell = row.createCell(index++, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(reportDO.getGoodsName()) ? "" : reportDO.getGoodsName());
                cell.setCellStyle(style);
 
                //单价
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getStandardPrice()==null?0:reportDO.getStandardPrice().doubleValue());
                cell.setCellStyle(style);

                // 可用库存量 
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getValidQty()==null?0:reportDO.getValidQty().doubleValue());
                cell.setCellStyle(style);

                // 调入在途（总部）
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getDonWayHead()==null?0:reportDO.getDonWayHead().doubleValue());
                cell.setCellStyle(style);
                
                //调入在途（分公司）
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getDonWay()==null?0:reportDO.getDonWay().doubleValue());
                cell.setCellStyle(style);
                
                //最近一年用量
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getRecentUseYear()==null?0:reportDO.getRecentUseYear());
                cell.setCellStyle(style);
                
                //最近一年用量
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getRecentUseMonth3()==null?0:reportDO.getRecentUseMonth3());
                cell.setCellStyle(style);

                // 周平均用量（近一年）
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getAverageSales()==null?0:reportDO.getAverageSales().doubleValue());
                cell.setCellStyle(style);

                // 周平均用量（近三个月）
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getAverageSales3()==null?0:reportDO.getAverageSales3().doubleValue());
                cell.setCellStyle(style);

                // 库存预计可销售周数
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getExpectWeek()==null?0:reportDO.getExpectWeek().doubleValue());
                cell.setCellStyle(style);
                
                //预计可销售周数
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getButSalesWeek()==null?0:reportDO.getButSalesWeek().doubleValue());
                cell.setCellStyle(style);
                
                //已下单未发货
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getOrderNum()==null?0:reportDO.getOrderNum().doubleValue());
                cell.setCellStyle(style);
                
                //补充后预测可销售周数
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getTotalSalesWeek()==null?0:reportDO.getTotalSalesWeek().doubleValue());
                cell.setCellStyle(style);
                
                //预测年销售量
                cell = row.createCell(index++, CellType.NUMERIC);
                cell.setCellValue(reportDO.getButSalesYear()==null?0:reportDO.getButSalesYear().doubleValue());
                cell.setCellStyle(style);
                
            }
        }
    }
    public List<List<Object>> addData(List<StLogisticsReport> dataList) throws Exception {
		List<List<Object>> list = new ArrayList<List<Object>>();
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			StLogisticsReport reportDO = (StLogisticsReport)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(reportDO.getLogisticsNo());
			data.add(reportDO.getLogisticsName());
            // 商品代码
			data.add(JudgeUtils.isNull(reportDO.getGoodsCode()) ? "" : reportDO.getGoodsCode());
            // 商品名称
			data.add(JudgeUtils.isNull(reportDO.getGoodsName()) ? "" : reportDO.getGoodsName());
            //单价
			data.add(reportDO.getStandardPrice()==null?0:reportDO.getStandardPrice().doubleValue());
             //可用库存量
			data.add(reportDO.getValidQty()==null?0:reportDO.getValidQty().doubleValue());
			
            // 调入在途（总部）
            data.add(reportDO.getDonWayHead()==null?0:reportDO.getDonWayHead().doubleValue());
            
            // 调入在途（分公司）
            data.add(reportDO.getDonWay()==null?0:reportDO.getDonWay().doubleValue());

            //最近一年用量
            data.add(reportDO.getRecentUseYear()==null?0:reportDO.getRecentUseYear());
            
            //最近3个月用量
            data.add(reportDO.getRecentUseMonth3()==null?0:reportDO.getRecentUseMonth3());

            // 周平均用量（近一年）
            data.add(reportDO.getAverageSales()==null?0:reportDO.getAverageSales().doubleValue());

            // 周平均用量（近三个月）
            data.add(reportDO.getAverageSales3()==null?0:reportDO.getAverageSales3().doubleValue());

            // 库存预计可销售周数
            data.add(reportDO.getExpectWeek()==null?0:reportDO.getExpectWeek().doubleValue());
            
            //预计可销售周数
            data.add(reportDO.getButSalesWeek()==null?0:reportDO.getButSalesWeek().doubleValue());
            
            //已下单未发货
            data.add(reportDO.getOrderNum()==null?0:reportDO.getOrderNum().doubleValue());
            
            //补充后预测可销售周数
            data.add(reportDO.getTotalSalesWeek()==null?0:reportDO.getTotalSalesWeek().doubleValue());
            
            //预测年销售量
            data.add(reportDO.getButSalesYear()==null?0:reportDO.getButSalesYear().doubleValue());
            
			list.add(data);
		}
		return list;
	}
}
