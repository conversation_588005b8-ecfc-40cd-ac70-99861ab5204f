/*
 * @ClassName SysVerifyCodeDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-04 14:50:17
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysVerifyCodeDO extends BaseDO {
    /**
     * @Fields id 验证码流水号
     */
    private Long id;
    /**
     * @Fields verifyCode 验证码
     */
    private String verifyCode;
    /**
     * @Fields token 验证码token
     */
    private String token;
    /**
     * @Fields codeStats 验证码状态
     */
    private String codeStats;
    /**
     * @Fields effTime 生效时间
     */
    private LocalDateTime effTime;
    /**
     * @Fields expTime 失效时间
     */
    private LocalDateTime expTime;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getCodeStats() {
        return codeStats;
    }

    public void setCodeStats(String codeStats) {
        this.codeStats = codeStats;
    }

    public LocalDateTime getEffTime() {
        return effTime;
    }

    public void setEffTime(LocalDateTime effTime) {
        this.effTime = effTime;
    }

    public LocalDateTime getExpTime() {
        return expTime;
    }

    public void setExpTime(LocalDateTime expTime) {
        this.expTime = expTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}