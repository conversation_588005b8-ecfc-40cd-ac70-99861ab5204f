/*
 * @ClassName IPdManifestItemDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-25 10:44:08
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdManifestItemDO;
import com.hisun.lemon.urm.entity.pd.PdManifestItemsRspDO;
import com.hisun.lemon.urm.service.pd.bo.PdManifestItemBO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IPdManifestItemDao extends BaseDao<PdManifestItemDO> {

    List<PdManifestItemsRspDO> manifestItemsQuery(Integer id);

    int updateSendQty(@Param("maniList") List<PdManifestItemDO> maniItemDOList, @Param("maniId") Integer maniId);
    int updateCanceSendQty(@Param("maniList") List<PdManifestItemDO> maniItemDOList, @Param("maniId") Integer maniId);

    List<PdManifestItemDO> queryListByIds(List<PdManifestItemDO> maniItemDOList);
    
    List<PdManifestItemDO> getByManifestId(@Param("manifestId") Long manifestId);

    /** 
     * @Title: get 
     * @param id
     * @return
     * @return: PdManifestItemDO
     */
    PdManifestItemDO get(@Param("id") long id);

    /** 
     * @Title: getOrderByManifestId 
     * @param manifestId
     * @param code
     * @return
     * @return: PdManifestDO
     */
    List<PdManifestItemBO> getOrderByManifestId(@Param("manifestId") long manifestId, @Param("delFlag") String delFlag);
}