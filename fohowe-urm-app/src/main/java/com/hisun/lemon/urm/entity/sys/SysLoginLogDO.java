/*
 * @ClassName SysLoginLogDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-28 14:48:29
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysLoginLogDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields userCode 用户编号
     */
    private String userCode;
    /**
     * @Fields regionCode 登录国家
     */
    private String regionCode;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields actUser 代理用户
     */
    private String actUser;
    private String companyCode;

    /**
     * @Fields actUser 代理用户refreshToken
     */
    private String actRefreshToken;
    
    private String loginIp;
    
    private String channel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getActUser() {
        return actUser;
    }

    public void setActUser(String actUser) {
        this.actUser = actUser;
    }

    public String getActRefreshToken() {
        return actRefreshToken;
    }

    public void setActRefreshToken(String actRefreshToken) {
        this.actRefreshToken = actRefreshToken;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

	public String getLoginIp() {
		return loginIp;
	}

	public void setLoginIp(String loginIp) {
		this.loginIp = loginIp;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}
    
}