package com.hisun.lemon.urm.uitls.excel.fi;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsRegistrationMemberBean;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsRegistrationMemberRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;

public class RegistrationExcelExporter extends URMExcelExportFactorys {
	private static final Map<Integer, String> VC_STATUS_MAP;
	static {
		VC_STATUS_MAP = new HashMap<>();
		VC_STATUS_MAP.put(0, "创建");
		VC_STATUS_MAP.put(1, "已审核");
		VC_STATUS_MAP.put(2, "已确认");
		VC_STATUS_MAP.put(3, "已完成");
	}
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		try {
			String fileName="参会经销商";
			String[] colNames=new String[] {"会议编号","会议名称","会议类型","会议状态","会议所属分公司","经销商编号","经销商名称","用户所属分公司","代办处","产品编号","订单编号","确认状态"};
			List<List<Object>> data= addData(obj);
			super.exportActually2(data,fileName,colNames, response);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}

	public List<List<Object>> addData(Object obj) throws Exception {
		TicketsRegistrationMemberRspDTO vo=(TicketsRegistrationMemberRspDTO) obj;
		List<TicketsRegistrationMemberBean> dataList=vo.getDataList();
		List<List<Object>> list = new ArrayList<List<Object>>();
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			TicketsRegistrationMemberBean o = (TicketsRegistrationMemberBean)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(o.getMeetingNo());
			data.add(o.getMeetingTitle());
			data.add(o.getPlanMeetingType());
			data.add(VC_STATUS_MAP.get(o.getVcStatus()));
			data.add(o.getMeetCompanyCode());
			data.add(o.getMemberNo());
			data.add(o.getMemberName());
			data.add(o.getCompanyCode());
			data.add(o.getAgentNo());
			data.add(o.getGoodsCode());
			data.add(o.getOrderNo());
			data.add(o.getStatus() == 1 ? "已确认" : (o.getStatus() == 2 ? "已取消" : "未确认"));
			list.add(data);
		}
		return list;
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		TicketsRegistrationMemberRspDTO vo=(TicketsRegistrationMemberRspDTO) obj;
		List<TicketsRegistrationMemberBean> dataList=vo.getDataList();
		for(TicketsRegistrationMemberBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getMeetingNo());
			row.createCell(index++).setCellValue(o.getMeetingTitle());
			row.createCell(index++).setCellValue(o.getPlanMeetingType());
			row.createCell(index++).setCellValue(o.getVcStatus());
			row.createCell(index++).setCellValue(o.getMeetCompanyCode());
			row.createCell(index++).setCellValue(o.getMemberNo());
			row.createCell(index++).setCellValue(o.getMemberName());
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getStatus());
		}
		
	}
	public static RegistrationExcelExporter builder() {
		return new RegistrationExcelExporter();
	}
}
