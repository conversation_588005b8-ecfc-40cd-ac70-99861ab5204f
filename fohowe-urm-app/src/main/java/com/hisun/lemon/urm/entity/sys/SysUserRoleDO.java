/*
 * @ClassName SysUserRoleDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-16 11:08:14
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysUserRoleDO extends BaseDO {
    /**
     * @Fields ruId 主键ID RU_ID
     */
    private String ruId;
    /**
     * @Fields roleId 角 色 ID  ROLE_ID
     */
    private String roleId;
    /**
     * @Fields userCode 登录帐号 USER_CODE
     */
    private String userCode;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getRuId() {
        return ruId;
    }

    public void setRuId(String ruId) {
        this.ruId = ruId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}