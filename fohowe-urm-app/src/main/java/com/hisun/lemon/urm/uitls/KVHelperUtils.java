package com.hisun.lemon.urm.uitls;

import java.math.BigDecimal;
import java.util.List;

import javax.annotation.Resource;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.enums.OrderTypeEnums;
import com.hisun.lemon.fohowe.ec.enums.ResCode;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.client.sys.ConfigClient;
import com.hisun.lemon.urm.dao.IFiAcBalanceDao;
import com.hisun.lemon.urm.dao.IFiAcExchangeDao;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceChange;
import com.hisun.lemon.urm.dto.fi.exchange.AcExchangeBean;
import com.hisun.lemon.urm.entity.FiAcBalanceDO;
import com.hisun.lemon.urm.entity.FiAcExchangeDO;
import com.hisun.lemon.urm.enums.fi.StatusEnums;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.dto.sys.ConfigValueQueryDTO;
import com.hisun.lemon.urm.dto.sys.ConfigValueQueryReqDTO;
import com.hisun.lemon.urm.dto.sys.ConfigValueQueryRspDTO;
import com.hisun.lemon.urm.dto.sys.ListValueQueryReqDTO;
import com.hisun.lemon.urm.dto.sys.ListValueQueryRspDTO;
import com.hisun.lemon.urm.dto.sys.UserBasicInfDTO;
import com.hisun.lemon.urm.service.sys.IConfigService;
import com.hisun.lemon.urm.service.sys.IListService;
import com.hisun.lemon.urm.service.sys.IUserService;

@Component
public class KVHelperUtils {
	private static final Logger logger = LoggerFactory.getLogger(KVHelperUtils.class);
	@Autowired
	IListService keyList;
    @Resource
    IUserService userService;
	@Resource
    private IConfigService configService;

	@Resource
	private PeriodWeekUtils periodWeek;

	@Resource
	private IFiAcExchangeDao acExchangeDao;

    public  UserBasicInfDTO getLoginUserById(String userCode) {
    	UserBasicInfDTO user=null;
    	try {
    		user=userService.queryUserByLoginId(userCode);
		} catch (Exception e) {
			logger.warn("获取用户信息失败，但已经忽略，正式环境请去除该代码！userCode:"+userCode);
			e.printStackTrace();
		}
    	return user;
    }
    
    public  BigDecimal[] getPvLimitByBonusType(String bonusType) {
    	BigDecimal[] decimal=new BigDecimal[2];
    	//UserBasicInfDTO user=this.getLoginUserById(userCode);
    	
    	decimal[0]=URMConstants.RIGHT_LOW_PV_DEFAULT;
		decimal[1]=URMConstants.RIGHT_FULL_PV_DEFAULT;
    	if(StringUtils.isNotBlank(bonusType)) {
    		ConfigValueQueryReqDTO reqDto=new ConfigValueQueryReqDTO();
			reqDto.setCompanyCode(bonusType); //修改为通过奖金制度获取pv
			reqDto.setConfigCode(URMConstants.RIGHT_FULL_PV_KEY);
			
			ConfigValueQueryRspDTO fullDto=configService.configValueQueryListByBonus(reqDto);
			List<ConfigValueQueryDTO> valueList=fullDto.getValueList();
			if(logger.isDebugEnabled()) {
				logger.debug("pv配置结果为："+ReflectionToStringBuilder.toString(valueList));
			
			}
			if(valueList!=null&&valueList.size()!=0) {
				String fullPv=valueList.get(0).getConfigValue();
				if(logger.isDebugEnabled())logger.debug("满额pv为："+fullPv);
				if(StringUtils.isNotBlank(fullPv)) {
					decimal[1]=new BigDecimal(fullPv);
				}
					
			}
			reqDto.setConfigCode(URMConstants.RIGHT_LOW_PV_KEY);
			ConfigValueQueryRspDTO lowDto=configService.configValueQueryListByBonus(reqDto);
			List<ConfigValueQueryDTO> lowList=lowDto.getValueList();
			if(lowList!=null&&lowList.size()!=0) {
				String lowPv=lowList.get(0).getConfigValue();
				if(StringUtils.isNotBlank(lowPv)) {
					if(logger.isDebugEnabled()) {
						logger.debug("低额pv为："+lowPv);
					
					}
					decimal[0]=new BigDecimal(lowPv);
				}
					
			}
    		
    	}else {
    		logger.error("奖金制度编号为空,将获取默认PV配置！！");
    		LemonException.throwBusinessException(MsgCdLC.PARAM_NOT_NULL.getMsgCd()); 
			
    	}
    	return decimal;
    }
    
    @Transactional(propagation=Propagation.NOT_SUPPORTED)
    public  BigDecimal[] getPvLimitByLoginUser(String usercode) {
    	
    	BigDecimal[] decimal=new BigDecimal[2];
    	UserBasicInfDTO user=this.getLoginUserById(usercode);
    	
    	decimal[0]=URMConstants.RIGHT_LOW_PV_DEFAULT;
		decimal[1]=URMConstants.RIGHT_FULL_PV_DEFAULT;
    	if(user!=null) {
    		ConfigValueQueryReqDTO reqDto=new ConfigValueQueryReqDTO();
			reqDto.setCompanyCode(user.getBonusType());
			reqDto.setConfigCode(URMConstants.RIGHT_FULL_PV_KEY);
			
			ConfigValueQueryRspDTO fullDto=configService.configValueQueryListByBonus(reqDto);
			List<ConfigValueQueryDTO> valueList=fullDto.getValueList();
			
			if(logger.isDebugEnabled()) {
				logger.debug("pv配置结果为："+ReflectionToStringBuilder.toString(valueList));
			
			}
			if(valueList!=null&&valueList.size()!=0) {
				String fullPv=valueList.get(0).getConfigValue();
				if(logger.isDebugEnabled())logger.debug("满额pv为："+fullPv);
				if(StringUtils.isNotBlank(fullPv)) {
					decimal[1]=new BigDecimal(fullPv);
				}
					
			}
			reqDto.setConfigCode(URMConstants.RIGHT_LOW_PV_KEY);
			ConfigValueQueryRspDTO lowDto=configService.configValueQueryListByBonus(reqDto);
			List<ConfigValueQueryDTO> lowList=lowDto.getValueList();
			if(lowList!=null&&lowList.size()!=0) {
				String lowPv=lowList.get(0).getConfigValue();
				if(StringUtils.isNotBlank(lowPv)) {
					if(logger.isDebugEnabled()) {
						logger.debug("低额pv为："+lowPv);
					
					}
					decimal[0]=new BigDecimal(lowPv);
				}
					
			}
    		
    	}
    	return decimal;
    }
    /**
     * 美国制度经营权个数限制
    * @return 
    * <AUTHOR>
     */
    public int getUSARightNumLimit(){
    	int num=URMConstants.RIGHT_NUM_LIMIT_USA_DEFAULT;
    	 //数据字典
		ListValueQueryReqDTO listReqDto=new ListValueQueryReqDTO();
		listReqDto.setListCode(URMConstants.RIGHT_NUM_LIMIT_KEY);
		listReqDto.setValueCode(URMConstants.RIGHT_NUM_LIMIT_USA);
		ListValueQueryRspDTO kv=keyList.listValueQuery(listReqDto);
    	
		if(kv.getValueList()!=null&&kv.getValueList().size()!=0) {
			String numStr=kv.getValueList().get(0).getValueTitle();
			num=Integer.parseInt(numStr);
		}
    	return num;
    }


	/**
	 * 处理账户自动转换逻辑
	 *
	 * @param acExchangeDO 订单信息
	 * @param acBalanceFB 账户余额中FB信息
	 * @param totalFb 订单所需总FB金额
	 * @param payerCode 付款人代码
	 * @param acBalanceChangeList 账户变动列表
	 * @throws LemonException 当配置值无效时抛出异常
	 */
	public void processAutoExchange(FiAcExchangeDO acExchangeDO,BigDecimal totalFB, BigDecimal totalFb, String payerCode, List<AcBalanceChange> acBalanceChangeList) throws LemonException {
		// 查询配置值
		ConfigValueQueryReqDTO configReqDTO = new ConfigValueQueryReqDTO();
		configReqDTO.setConfigCode("auto_exchange");
		configReqDTO.setConfigType("1");
		configReqDTO.setCompanyCode(acExchangeDO.getCompanyCode());

		ConfigValueQueryRspDTO configValueDTO = configService.configValueQueryList(configReqDTO);

		String configValue = null;
		if (configValueDTO != null && !configValueDTO.getValueList().isEmpty()
				&& JudgeUtils.isNotBlank(configValueDTO.getValueList().get(0).getConfigValue())) {
			configValue = configValueDTO.getValueList().get(0).getConfigValue();
		}

		logger.info("是否自动转化:" + configValue);

		// 检查配置值是否允许自动转换
		if ("1".equals(configValue)) {
			// 创建自动转换记录
			AcExchangeBean acExchangeDOAudo = new AcExchangeBean();
			acExchangeDOAudo.setExchangeNo(IdGenUtils.generateIdWithDate(URMConstants.AC_EXCHANGE_KEY, URMConstants.AC_EXCHANGE_KEY_PREFIX, 5));
			acExchangeDOAudo.setCreatorCode(JudgeUtils.isNull(LemonUtils.getUserId()) ? "NULL" : LemonUtils.getUserId());
			acExchangeDOAudo.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
			acExchangeDOAudo.setStatus(StatusEnums.NEW.getCode());
			acExchangeDOAudo.setPeriodWeek(periodWeek.getCurrentPeriodWeekFromBns());
			acExchangeDOAudo.setExType(5);
			acExchangeDOAudo.setUserCode(JudgeUtils.isNull(LemonUtils.getUserId()) ? "NULL" : LemonUtils.getUserId());
			acExchangeDOAudo.setMoney(totalFb.subtract(totalFB));
			acExchangeDOAudo.setAcType(AcTypeEnums.F$.getCode());
			acExchangeDOAudo.setCompanyCode(acExchangeDO.getCompanyCode());
			acExchangeDOAudo.setAgentNo(acExchangeDO.getAgentNo());
			acExchangeDOAudo.setId(null);

			logger.info("开始添加对应记录");
			acExchangeDao.insertACExchangeDO(acExchangeDOAudo);
			logger.info("开始账户变动...");

			// 记录账户变动
			AcBalanceChange changeF$ = new AcBalanceChange();
			changeF$.setAcType(AcTypeEnums.F$.getCode());
			changeF$.setMoney(totalFB.subtract(totalFb));
			changeF$.setOrderType(OrderTypeEnums.EXCHANGE_CURRENCY.getCode());
			changeF$.setUserCode(payerCode);
			changeF$.setMemo("【经销商F$转FB  订单号：" + acExchangeDO.getExchangeNo() + "】");
			acBalanceChangeList.add(changeF$);

			AcBalanceChange changeFB = new AcBalanceChange();
			changeFB.setAcType(AcTypeEnums.FB.getCode());
			changeFB.setMoney(totalFb.subtract(totalFB));
			changeFB.setOrderType(OrderTypeEnums.EXCHANGE_CURRENCY.getCode());
			changeFB.setUserCode(payerCode);
			changeFB.setMemo("【经销商F$转FB   订单号：" + acExchangeDO.getExchangeNo() + "】");
			acBalanceChangeList.add(changeFB);
		} else {
			// 配置不允许自动转换，抛出业务异常
			LemonException.throwLemonException(ResCode.EC0045.getCode(), ResCode.EC0045.getName());
		}
	}
}
