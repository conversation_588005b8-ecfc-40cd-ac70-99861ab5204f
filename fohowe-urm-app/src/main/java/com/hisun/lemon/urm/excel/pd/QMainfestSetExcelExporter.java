package com.hisun.lemon.urm.excel.pd;



import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.pd.PdQmanifestPageRspDTO;
import com.hisun.lemon.urm.dto.pd.PdQmanifestRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class QMainfestSetExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="成品发运单发货";
		String[] colNames=new String[] {"单据编号","是否审核","是否发货","是否收货","入库单日期",
				"发货日期","收货日期","F$","FV","供货方","调拨申请分公司","创建人","调拨发货人","备注"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		PdQmanifestPageRspDTO  vo=(PdQmanifestPageRspDTO) obj;
		List<PdQmanifestRspDTO> dataList=vo.getQmanifestRspList();
		for(PdQmanifestRspDTO o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getReceiptno());
			row.createCell(1).setCellValue(o.getOrderstatus()==1?"是":"否");
			row.createCell(2).setCellValue(o.getSendstatus()==1?"是":"否");
			row.createCell(3).setCellValue(o.getRecstatus()==1?"是":"否");
			row.createCell(4).setCellValue(o.getInHouseDate()==null?"":o.getInHouseDate().format(ymdhms));
			row.createCell(5).setCellValue(o.getSenddate()==null?"":o.getSenddate().format(ymdhms));
			row.createCell(6).setCellValue(o.getRecdate()==null?"":o.getRecdate().format(ymdhms));
			row.createCell(7).setCellValue(o.getTotalCost()+"");
			row.createCell(8).setCellValue(o.getTotalPv()+"");
			row.createCell(9).setCellValue(o.getFromstore());
			row.createCell(10).setCellValue(o.getReqstore());
			row.createCell(11).setCellValue(o.getOrderor());
			row.createCell(12).setCellValue(o.getSender());
			row.createCell(13).setCellValue(o.getMemo());
		}
		
	}
	public static QMainfestSetExcelExporter builder() {
		return new QMainfestSetExcelExporter();
	}
}
