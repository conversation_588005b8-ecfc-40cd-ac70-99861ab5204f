package com.hisun.lemon.urm.entity.bd;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class BdTourismPromotionDO extends BaseDO {
    private Long id;

    private String areaName;

    private String companyCode;

    private String agentNo;

    private String memberNo;

    private String memberName;

    private BigDecimal totalAmount;

    private BigDecimal repairAmount;

    private BigDecimal payAmount;

    private String salesPromotion;

	private LocalDateTime payTime;

    private Integer payWeek;

    private Integer quotaNum;

    private Integer isTraveled;

    private Integer vcStatus;

    private String remark;

	private LocalDateTime ficheckeTime;

    private String ficheckerCode;

    private Integer ficheckeStatus;

	private LocalDateTime createTime;

    private String payerCode;
    
    private Integer agFicheckeStatus;
    
    private Integer isOutPay;
    
    private Long infoId;
    private String lineStatus;
    private Integer linePayStatus;

    public Integer getLinePayStatus() {
        return linePayStatus;
    }

    public void setLinePayStatus(Integer linePayStatus) {
        this.linePayStatus = linePayStatus;
    }

    public String getLineStatus() {
        return lineStatus;
    }

    public void setLineStatus(String lineStatus) {
        this.lineStatus = lineStatus;
    }

    public Integer getAgFicheckeStatus() {
        return agFicheckeStatus;
    }

    public void setAgFicheckeStatus(Integer agFicheckeStatus) {
        this.agFicheckeStatus = agFicheckeStatus;
    }

    public String getPayerCode() {
        return payerCode;
    }

    public void setPayerCode(String payerCode) {
        this.payerCode = payerCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRepairAmount() {
        return repairAmount;
    }

    public void setRepairAmount(BigDecimal repairAmount) {
        this.repairAmount = repairAmount;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getSalesPromotion() {
        return salesPromotion;
    }

    public void setSalesPromotion(String salesPromotion) {
        this.salesPromotion = salesPromotion;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public Integer getPayWeek() {
        return payWeek;
    }

    public void setPayWeek(Integer payWeek) {
        this.payWeek = payWeek;
    }

    public Integer getQuotaNum() {
        return quotaNum;
    }

    public void setQuotaNum(Integer quotaNum) {
        this.quotaNum = quotaNum;
    }

    public Integer getIsTraveled() {
        return isTraveled;
    }

    public void setIsTraveled(Integer isTraveled) {
        this.isTraveled = isTraveled;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getFicheckeTime() {
        return ficheckeTime;
    }

    public void setFicheckeTime(LocalDateTime ficheckeTime) {
        this.ficheckeTime = ficheckeTime;
    }

    public String getFicheckerCode() {
        return ficheckerCode;
    }

    public void setFicheckerCode(String ficheckerCode) {
        this.ficheckerCode = ficheckerCode;
    }

    public Integer getFicheckeStatus() {
        return ficheckeStatus;
    }

    public void setFicheckeStatus(Integer ficheckeStatus) {
        this.ficheckeStatus = ficheckeStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

	public Long getInfoId() {
		return infoId;
	}

	public void setInfoId(Long infoId) {
		this.infoId = infoId;
	}

	public Integer getIsOutPay() {
		return isOutPay;
	}

	public void setIsOutPay(Integer isOutPay) {
		this.isOutPay = isOutPay;
	}

	@Override
	public String toString() {
		return "BdTourismPromotionDO [id=" + id + ", areaName=" + areaName + ", companyCode=" + companyCode
				+ ", agentNo=" + agentNo + ", memberNo=" + memberNo + ", memberName=" + memberName + ", totalAmount="
				+ totalAmount + ", repairAmount=" + repairAmount + ", payAmount=" + payAmount + ", salesPromotion="
				+ salesPromotion + ", payTime=" + payTime + ", payWeek=" + payWeek + ", quotaNum=" + quotaNum
				+ ", isTraveled=" + isTraveled + ", vcStatus=" + vcStatus + ", remark=" + remark + ", ficheckeTime="
				+ ficheckeTime + ", ficheckerCode=" + ficheckerCode + ", ficheckeStatus=" + ficheckeStatus
				+ ", createTime=" + createTime + ", payerCode=" + payerCode + "]";
	}


}
