/*
 * @ClassName SysRolePowerDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-16 11:08:14
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysRolePowerDO extends BaseDO {
    /**
     * @Fields rpId 主键ID RP_ID
     */
    private String rpId;
    /**
     * @Fields roleId 角色ID  ROLE_ID
     */
    private String roleId;
    /**
     * @Fields moduleId 模块ID MODULE_ID
     */
    private String moduleId;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getRpId() {
        return rpId;
    }

    public void setRpId(String rpId) {
        this.rpId = rpId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}