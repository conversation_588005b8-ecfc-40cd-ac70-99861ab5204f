/*
 * @ClassName IMiChgLogDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.NetWorkChangedBean;
import com.hisun.lemon.urm.dto.mi.member.NetWorkChangedListReqDTO;
import com.hisun.lemon.urm.entity.MiChgLogDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IMiChgLogDao extends BaseDao<MiChgLogDO> {

	int getTotalCount(NetWorkChangedListReqDTO dto);

	List<NetWorkChangedBean> getListByPageBreak(NetWorkChangedListReqDTO dto);
}