/*
 * @ClassName ISysModuleDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-17 15:41:40
 */
package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.sys.ModuleQueryReqDTO;
import com.hisun.lemon.urm.entity.sys.SysModuleDO;

@Mapper
public interface ISysModuleDao extends BaseDao<SysModuleDO> {

    /** 
     * @Title: findByRole 
     * @Description: 查询角色所属的模块信息
     * @param roleId
     * @param userCode 
     * @return
     * @return: List<SysModuleDO>
     */
    public List<SysModuleDO> findList(ModuleQueryReqDTO query);
    
    public List<SysModuleDO> findByRole(@Param("roleId") String roleId, @Param("userCode") String userCode);

    /** 
     * @Title: findUserPower 
     * @Description: 查询用户权限信息
     * @param userCode
     * @param linkUrl 
     * @return
     * @return: List<SysModuleDO>
     */
    public SysModuleDO findUserPower(@Param("userCode") String userCode, @Param("linkUrl") String linkUrl);

    /** 
     * @Title: getByUrl 
     * @Description: 查询模块是否存在
     * @param linkUrl
     * @return
     * @return: SysModuleDO
     */
    public SysModuleDO getByUrl(@Param("linkUrl") String linkUrl);

    /** 
     * @Title: findByUser 
     * @param userCode
     * @param moduleType
     * @param moduleIdList 
     * @return
     * @return: List<SysModuleDO>
     */
    public List<SysModuleDO> findByUser(@Param("userCode") String userCode, @Param("moduleType") String moduleType,
            @Param("moduleIdList") List<String> moduleIdList);
    public List<SysModuleDO> findByUserNew(@Param("userCode") String userCode, @Param("moduleType") String moduleType,
    		@Param("linkIndex") String linkIndex);
}