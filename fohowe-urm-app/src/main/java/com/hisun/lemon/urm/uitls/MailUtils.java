package com.hisun.lemon.urm.uitls;


import cn.hutool.extra.mail.MailUtil;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.ec.enums.ResCode;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.client.al.LanguageClient;
import com.hisun.lemon.urm.dto.al.CharacterQueryListRspDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryReqDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryRspDTO;
import com.hisun.lemon.urm.dto.mi.member.MemberBeanDO;

import javax.annotation.Resource;
import java.io.*;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

public class MailUtils {

    @Resource
    private LanguageClient languageClient;
    public static void sendHtml(MemberBeanDO body, String contentText, String contentTextEn) {
        StringBuilder stringBuilder = null;
        try {
            stringBuilder = getStringBuilder(LemonUtils.getProperty("urm.filePath.mailhtml"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        String title = body.getMemberNo()+"-"+body.getName();
        //落款处需要时间则加上
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        String inscription = "fohow";
        String htmlText = MessageFormat.format(stringBuilder.toString(),title,contentText,contentTextEn,inscription,date);
        MailUtil.send(body.getEmail(), body.getMemberNo()+body.getName(), htmlText, true);
    }

    private  void languageQueryList() {
        //+多语言处理
        CharacterQueryReqDTO reqDTO = new CharacterQueryReqDTO();
        String charKey = "column.memberRegister,column.rightPurchase,column.activeOrder,column.rightPromotion,"
                + "column.qualifyOrder,column.goodsTurnOver,column.goodsF000Order,column.goodsApplication";
        String langCode = LemonUtils.getLocale().getLanguage() + "-" + LemonUtils.getLocale().getCountry();
        reqDTO.setCharacterKey(charKey);
        reqDTO.setLangCode(langCode);
        GenericRspDTO<CharacterQueryListRspDTO> resultDto = languageClient.languageQueryList(reqDTO);
        if (JudgeUtils.isNotSuccess(resultDto.getMsgCd())) {
            LemonException.throwBusinessException(ResCode.EC0146.getCode());
        }
        CharacterQueryListRspDTO resultList = resultDto.getBody();
        List<CharacterQueryRspDTO> valueList = resultList.getCharValueList();

        //将经营权列表转化为Map
        Map<String, CharacterQueryRspDTO> map = ConvertUtils.list2Map3(valueList, "getCharacterKey", CharacterQueryRspDTO.class);
    }

    private static StringBuilder getStringBuilder(String filePath) throws IOException {
        File file = new File(filePath);
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
        String line;
        while ((line = bufferedReader.readLine()) != null){
            stringBuilder.append(line);
        }
        bufferedReader.close();
        return stringBuilder;
    }
    public static String getBySetting(String prefix){
        Properties properties = new Properties();

        try(FileInputStream fileInputStream = new FileInputStream(LemonUtils.getProperty("urm.filePath.mail"))) {
            properties.load(fileInputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String value = properties.getProperty(prefix);
        return changeCharset(value,"ISO-8859-1","UTF-8");
    }

    public static String changeCharset(String a,String charset1,String charset2) {
        try {
            return new String(a.getBytes(charset1), charset2);
        } catch (UnsupportedEncodingException e) {
            throw new Error(e);
        }
    }
}
