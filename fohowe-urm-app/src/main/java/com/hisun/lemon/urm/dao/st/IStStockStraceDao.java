/*
 * @ClassName IStStockStraceDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-08 14:49:31
 */
package com.hisun.lemon.urm.dao.st;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StStockHistoryDO;
import com.hisun.lemon.urm.entity.st.StStockStraceDO;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IStStockStraceDao extends BaseDao<StStockStraceDO> {

    /** 
     * @Title: getPageList 
     * @Description: TODO
     * @param areaCode
     * @param companyList
     * @param goodsList
     * @param orderNo
     * @param agentNo
     * @param orderType
     * @param actionTypeGiveList 
     * @param string 
     * @param actionType
     * @param actionType
     * @return
     * @return: List<T>
     */
    List<StStockStraceDO> getPageList(@Param("areaCode") String areaCode,
            @Param("companyList") List<String> companyList, @Param("goodsList") List<String> goodsList,
            @Param("orderNo") String orderNo, @Param("agentNo") String agentNo, @Param("orderType") String orderType,
            @Param("actionTypeList") List<String> actionTypeList, @Param("isGive") String isGive,
            @Param("actionTypeGiveList") List<String> actionTypeGiveList,@Param("startTime") LocalDateTime startTime ,
            @Param("endTime") LocalDateTime endTime ,@Param("stockChangeType") String stockChangeType,@Param("isPkg") Integer isPkg,
                                      @Param("codeName") String codeName,@Param("excludeB1") boolean excludeB1);

    
    int getPageListCount(@Param("areaCode") String areaCode,
            @Param("companyList") List<String> companyList, @Param("goodsList") List<String> goodsList,
            @Param("orderNo") String orderNo, @Param("agentNo") String agentNo, @Param("orderType") String orderType,
            @Param("actionTypeList") List<String> actionTypeList, @Param("isGive") String isGive,
            @Param("actionTypeGiveList") List<String> actionTypeGiveList,@Param("startTime") LocalDateTime startTime ,
            @Param("endTime") LocalDateTime endTime ,@Param("stockChangeType") String stockChangeType,@Param("isPkg") Integer isPkg);
    
    List<StStockStraceDO> getPageListByYear(@Param("history") String history,@Param("areaCode") String areaCode,
    		@Param("companyList") List<String> companyList, @Param("goodsList") List<String> goodsList,
    		@Param("orderNo") String orderNo, @Param("agentNo") String agentNo, @Param("orderType") String orderType,
    		@Param("actionTypeList") List<String> actionTypeList, @Param("isGive") String isGive,
    		@Param("actionTypeGiveList") List<String> actionTypeGiveList,@Param("startTime") LocalDateTime startTime ,
    		@Param("endTime") LocalDateTime endTime ,@Param("stockChangeType") String stockChangeType,@Param("isPkg") Integer isPkg,
                                            @Param("codeName") String codeName,@Param("excludeB1") boolean excludeB1);
    
    
    int getPageListCountByYear(@Param("history") String history,@Param("areaCode") String areaCode,
    		@Param("companyList") List<String> companyList, @Param("goodsList") List<String> goodsList,
    		@Param("orderNo") String orderNo, @Param("agentNo") String agentNo, @Param("orderType") String orderType,
    		@Param("actionTypeList") List<String> actionTypeList, @Param("isGive") String isGive,
    		@Param("actionTypeGiveList") List<String> actionTypeGiveList,@Param("startTime") LocalDateTime startTime ,
    		@Param("endTime") LocalDateTime endTime ,@Param("stockChangeType") String stockChangeType,
    		@Param("isPkg") Integer isPkg);
    
    
    /** 
     * @Title: getStockHistory 
     * @Description: TODO
     * @param areaCode
     * @param companyList
     * @param endDate
     * @return
     * @return: List<StStockHistoryDO>
     */
    List<StStockHistoryDO> getStockHistory(@Param("bonusType") String bonusType,@Param("region") String region,@Param("areaCode") String areaCode,
            @Param("companyList") List<String> companyList, @Param("endDate") LocalDateTime endDate);
    /** 
     * @Title: getStockHistory 
     * @Description: TODO
     * @param areaCode
     * @param companyList
     * @param endDate
     * @return
     * @return: List<StStockHistoryDO>
     */
    List<StStockHistoryDO> getStockHistorys(@Param("bonusTypeList") List<String> bonusTypeList,@Param("region") String region,
    		@Param("areaCodeList") List<String> areaCodeList,
            @Param("companyList") List<String> companyList, @Param("endDate") LocalDateTime endDate,@Param("goodsList") List<String> goodsList);
    
    String getMaxCreatTimeByHistory();
    int insertIntoHistory();
    Long getMaxIdByHistory();
    Long getMaxIdByStrace();
    
    int updateStock(@Param("straceId") Long straceId);
    
    //同步扩展属性
    int insertStraceExtendNoOrder(@Param("actionType") String actionType,@Param("orderNo") String orderNo,@Param("straceId") Long straceId);
    int insertStraceExtendOrder(@Param("actionType") String actionType,@Param("orderNo") String orderNo,@Param("straceId") Long straceId,@Param("isPkg") int isPkg);
    int insertStraceExtendPromo(@Param("actionType") String actionType,@Param("orderNo") String orderNo,@Param("straceId") Long straceId);  
    int updateExtendPkg(@Param("straceId") Long straceId,@Param("isPkg") int isPkg,@Param("pkgCode") String pkgCode);  
    int updateExtendDel(@Param("straceId") Long straceId);
    
}