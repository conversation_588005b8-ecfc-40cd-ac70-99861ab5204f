/*
 * @ClassName PdPromoSendinfoDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 11:30:52
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class PdPromoSendinfoDO extends BaseDO {
    /**
     * @Fields id 00.ID ID
     */
    private Integer id;
    /**
     * @Fields companycode 分公司/仓库编号
     */
    private String companyCode;
    /**
     * @Fields receiptno 合并单出库单号 通过fn_sys_getno('pdso',0)获得
     */
    private String receiptno;
    //合并子单合并单号
    private String receiptnoItem;
    /**
     * @Fields orderor 创建人
     */
    private String orderor;
    /**
     * @Fields sender 发货人
     */
    private String sender;
    /**
     * @Fields receiver 收货人
     */
    private String receiver;
    /**
     * @Fields orderdate 创建日期
     */
    private LocalDateTime orderdate;
    
    /**
     * @Fields senddate 发货日期
     */
    private LocalDateTime senddate;
    /**
     * @Fields recdate 收货日期
     */
    private LocalDateTime recdate;
    /**
     * @Fields orderstatus 创建状态(bit)
         1=新建    2审核       3已发货     4收货
     */
    private Integer orderstatus;
    /**
     * @Fields sendstatus 0未发货  1已发货
     */
    private Integer sendStatus;
    /**
     * @Fields recstatus 收货状态 0未收货  1已发货
     */
    private Integer recstatus;
    /**
     * @Fields recName 收货人姓名
     */
    private String recName;
    /**
     * @Fields recAddr 收货地址
     */
    private String recAddr;
    /**
     * @Fields recPhone 收货联系电话
     */
    private String recPhone;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields requestNo 提货申请单号
     */
    private String requestNo;
    /**
     * @Fields dcsendNo 物流单号
     */
    private String dcsendNo;
    /**
     * @Fields regionCode 地区编号
     */
    private String regionCode;
    /**
     * @Fields recCode 收货人编号
     */
    private String recCode;
    /**
     * @Fields cancelCode 删除人
     */
    private String cancelCode;
    /**
     * @Fields canceldate 删除时间
     */
    private LocalDateTime canceldate;
    /**
     * @Fields isDc 分公司制单
     */
    private Integer isDc;
    /**
     * @Fields faChecker 财务审核人
     */
    private String faChecker;
    /**
     * @Fields faCheckdate 财务审核日期
     */
    private LocalDateTime faCheckdate;
    /**
     * @Fields memo 
     */
    private String memo;
    /**
     * @Fields totalCost 
     */
    private Integer totalCost;
    /**
     * @Fields comboReceiptno 合并单号
     */
    private String comboReceiptno;
    /**
     * @Fields splitReceiptno 拆分原单号
     */
    private String splitReceiptno;
    /**
     * @Fields isSplit 是否拆分单：1拆分单
     */
    private Integer isSplit;
    /**
     * @Fields isagentstock 是否增加代办处库存  1=增加
     */
    private Integer isagentstock;
    /**
     * @Fields isCombo 是否合并 0 未合并 1 已合并
     */
    private Byte isCombo;

    /**
     * @Fields activeCode 活跃编码
     */
    private String activeCode;
    /**
     * @Fields activeName 活跃编名称
     */
    private String activeName;
    /**
     * @Fields name 经销商姓名
     */
    private String name;

    private String companyCodes;

    public String getCompanyCodes() {
        return companyCodes;
    }

    public void setCompanyCodes(String companyCodes) {
        this.companyCodes = companyCodes;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getOrderor() {
        return orderor;
    }

    public void setOrderor(String orderor) {
        this.orderor = orderor;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public LocalDateTime getOrderdate() {
        return orderdate;
    }

    public void setOrderdate(LocalDateTime orderdate) {
        this.orderdate = orderdate;
    }

    public LocalDateTime getSenddate() {
        return senddate;
    }

    public void setSenddate(LocalDateTime senddate) {
        this.senddate = senddate;
    }

    public LocalDateTime getRecdate() {
        return recdate;
    }

    public void setRecdate(LocalDateTime recdate) {
        this.recdate = recdate;
    }

    public Integer getOrderstatus() {
        return orderstatus;
    }

    public void setOrderstatus(Integer orderstatus) {
        this.orderstatus = orderstatus;
    }

    public Integer getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(Integer sendStatus) {
		this.sendStatus = sendStatus;
	}

	public Integer getRecstatus() {
        return recstatus;
    }

    public void setRecstatus(Integer recstatus) {
        this.recstatus = recstatus;
    }

    public String getRecName() {
        return recName;
    }

    public void setRecName(String recName) {
        this.recName = recName;
    }

    public String getRecAddr() {
        return recAddr;
    }

    public void setRecAddr(String recAddr) {
        this.recAddr = recAddr;
    }

    public String getRecPhone() {
        return recPhone;
    }

    public void setRecPhone(String recPhone) {
        this.recPhone = recPhone;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getDcsendNo() {
        return dcsendNo;
    }

    public void setDcsendNo(String dcsendNo) {
        this.dcsendNo = dcsendNo;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRecCode() {
        return recCode;
    }

    public void setRecCode(String recCode) {
        this.recCode = recCode;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCanceldate() {
        return canceldate;
    }

    public void setCanceldate(LocalDateTime canceldate) {
        this.canceldate = canceldate;
    }

    public Integer getIsDc() {
        return isDc;
    }

    public void setIsDc(Integer isDc) {
        this.isDc = isDc;
    }

    public String getFaChecker() {
        return faChecker;
    }

    public void setFaChecker(String faChecker) {
        this.faChecker = faChecker;
    }

    public LocalDateTime getFaCheckdate() {
        return faCheckdate;
    }

    public void setFaCheckdate(LocalDateTime faCheckdate) {
        this.faCheckdate = faCheckdate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Integer totalCost) {
        this.totalCost = totalCost;
    }

    public String getComboReceiptno() {
        return comboReceiptno;
    }

    public void setComboReceiptno(String comboReceiptno) {
        this.comboReceiptno = comboReceiptno;
    }

    public String getSplitReceiptno() {
        return splitReceiptno;
    }

    public void setSplitReceiptno(String splitReceiptno) {
        this.splitReceiptno = splitReceiptno;
    }

    public Integer getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(Integer isSplit) {
        this.isSplit = isSplit;
    }

    public Integer getIsagentstock() {
        return isagentstock;
    }

    public void setIsagentstock(Integer isagentstock) {
        this.isagentstock = isagentstock;
    }

    public Byte getIsCombo() {
        return isCombo;
    }

    public void setIsCombo(Byte isCombo) {
        this.isCombo = isCombo;
    }

    public String getActiveCode() {
        return activeCode;
    }

    public void setActiveCode(String activeCode) {
        this.activeCode = activeCode;
    }

    public String getActiveName() {
        return activeName;
    }

    public void setActiveName(String activeName) {
        this.activeName = activeName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

	public String getReceiptnoItem() {
		return receiptnoItem;
	}

	public void setReceiptnoItem(String receiptnoItem) {
		this.receiptnoItem = receiptnoItem;
	}
    
    
}