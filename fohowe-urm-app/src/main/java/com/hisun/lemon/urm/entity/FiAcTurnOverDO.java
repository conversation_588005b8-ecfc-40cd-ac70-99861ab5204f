package com.hisun.lemon.urm.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class FiAcTurnOverDO extends BaseDO{
	/**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields acType 账户类型，fb=fb ， f$＝f$，fv＝fv, f0=f000，pv=活跃pv，b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，s1=全球分红，s2=凤凰大使分红
     */
    private String acType;
    /**
     * @Fields orderType 单据类型
     */
    private String orderType;
    /**
     * @Fields companyCode 公司编号 company_code
     */
    private String companyCode;
    /**
     * @Fields userCode 用户编号 user_code
     */
    private String userCode;
    /**
     * @Fields money 兑换金额 
     */
    private BigDecimal money;
    /**
     * @Fields handlingFee 手续费 handling_fee
     */
    private BigDecimal handlingFee;
    /**
     * @Fields status 状态，0:新建 1:审核
     */
    private String status;
    /**
     * @Fields creatorCode 创建人编号 creator_code
     */
    private String creatorCode;
    /**
     * @Fields creatorCode 创建人姓名  creator_name
     */
    private String creatorName;
    /**
     * @Fields checkerCode 确认人编号 checker_code
     */
    private String checkerCode;
    /**
     * @Fields checkTime 确认时间 check_time
     */
    private LocalDateTime checkTime;
    /**
     * @Fields checkerCode 复核人编号 checker_code
     */
    private String reCheckerCode;
    /**
     * @Fields checkTime 复核时间 check_time
     */
    private LocalDateTime reCheckTime;
    /**
     * @Fields checkerCode 取消人编号 checker_code
     */
    private String cancleCode;
    /**
     * @Fields checkTime 取消时间 check_time
     */
    private LocalDateTime cancleTime;
    /**
     * @Fields memo 备注(会员) 
     */
    private String memo;
    /**
     * @Fields remark 摘要(公司) 
     */
    private String remark;
    /**
     * @Fields periodWeek 兑换申请期数
     */
    private String periodWeek;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields 处理结果
     */
    private String result;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAcType() {
		return acType;
	}
	public void setAcType(String acType) {
		this.acType = acType;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public BigDecimal getMoney() {
		return money;
	}
	public void setMoney(BigDecimal money) {
		this.money = money;
	}
	public BigDecimal getHandlingFee() {
		return handlingFee;
	}
	public void setHandlingFee(BigDecimal handlingFee) {
		this.handlingFee = handlingFee;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCreatorCode() {
		return creatorCode;
	}
	public void setCreatorCode(String creatorCode) {
		this.creatorCode = creatorCode;
	}
	public String getCheckerCode() {
		return checkerCode;
	}
	public void setCheckerCode(String checkerCode) {
		this.checkerCode = checkerCode;
	}
	public LocalDateTime getCheckTime() {
		return checkTime;
	}
	public void setCheckTime(LocalDateTime checkTime) {
		this.checkTime = checkTime;
	}
	public String getReCheckerCode() {
		return reCheckerCode;
	}
	public void setReCheckerCode(String reCheckerCode) {
		this.reCheckerCode = reCheckerCode;
	}
	public LocalDateTime getReCheckTime() {
		return reCheckTime;
	}
	public void setReCheckTime(LocalDateTime reCheckTime) {
		this.reCheckTime = reCheckTime;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getPeriodWeek() {
		return periodWeek;
	}
	public void setPeriodWeek(String periodWeek) {
		this.periodWeek = periodWeek;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	public String getCancleCode() {
		return cancleCode;
	}
	public void setCancleCode(String cancleCode) {
		this.cancleCode = cancleCode;
	}
	public LocalDateTime getCancleTime() {
		return cancleTime;
	}
	public void setCancleTime(LocalDateTime cancleTime) {
		this.cancleTime = cancleTime;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	
}
