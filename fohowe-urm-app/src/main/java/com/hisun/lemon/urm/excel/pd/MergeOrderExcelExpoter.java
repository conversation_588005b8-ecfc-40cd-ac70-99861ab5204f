package com.hisun.lemon.urm.excel.pd;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.OrderType;
import com.hisun.lemon.urm.entity.pd.PdMergeOrderInfoDo;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;
import com.hisun.lemon.urm.uitls.excel.st.SendInfoOweExcelExporter;

public class MergeOrderExcelExpoter extends URMExcelExportFactory {
    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "合并订单明细表";
        String[] colNames = new String[] { "分公司","代办处","合并单号","PO订单号", "出库单号", "产品编码","数量","单价","是否已发货","订单分司","单据类型"};
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        List<PdMergeOrderInfoDo> dataList = (List<PdMergeOrderInfoDo>) obj;
        for (PdMergeOrderInfoDo o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);
            int index=0;
            //"分公司","代办处","合并单号","PO订单号", "出库单号", "产品编码","数量","金额","是否已发货","订单分司","单据类型"
            row.createCell(index++).setCellValue(o.getCompanyCode());
            row.createCell(index++).setCellValue(o.getAgentNo());
            row.createCell(index++).setCellValue(o.getComboReceiptNo());
            row.createCell(index++).setCellValue(o.getBiOrderNo());
            row.createCell(index++).setCellValue(o.getReceiptNo());
            row.createCell(index++).setCellValue(o.getGoodsCode());
            row.createCell(index++).setCellValue(o.getOrderQty());
            row.createCell(index++).setCellValue(o.getPrice().doubleValue());
            String isOutGoodStatus="否";
            if("1".equals(o.getState())){
                isOutGoodStatus="是";
            }else {
                isOutGoodStatus="否";
            }
            row.createCell(index++).setCellValue(isOutGoodStatus);
            row.createCell(index++).setCellValue(o.getOrderCompanyCode());
            Integer orderType = Integer.parseInt(o.getOrderType());
            row.createCell(index++).setCellValue(JudgeUtils.isNull(OrderType.getByCode(orderType)) ? o.getOrderType().toString() : OrderType.getByCode(orderType).getName().toString());
        }
    }

    public static SendInfoOweExcelExporter builder() {
        return new SendInfoOweExcelExporter();
    }

}
