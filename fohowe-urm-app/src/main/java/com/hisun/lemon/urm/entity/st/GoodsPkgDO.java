package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;

public class GoodsPkgDO extends BaseDO {
	/**
     * @Fields id ID
     */
    private Integer id;
    /**
     * @Fields goodsCode 套餐代码
     */
    private String pkgCode;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    /**
     * @Fields unitQty基本产品数量
     */
    private Integer unitQty;
    /**
     * @Fields goodsName 名称
     */
    private String goodsName;
    
    private String chineseName;
    
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getPkgCode() {
		return pkgCode;
	}
	public void setPkgCode(String pkgCode) {
		this.pkgCode = pkgCode;
	}
	public String getGoodsCode() {
		return goodsCode;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	public Integer getUnitQty() {
		return unitQty;
	}
	public void setUnitQty(Integer unitQty) {
		this.unitQty = unitQty;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	
	public String getChineseName() {
		return chineseName;
	}
	public void setChineseName(String chineseName) {
		this.chineseName = chineseName;
	}
	@Override
	public String toString() {
		return "GoodsPkgDO [id=" + id + ", pkgCode=" + pkgCode + ", goodsCode=" + goodsCode + ", unitQty=" + unitQty
				+ ", goodsName=" + goodsName + "]";
	}
}
