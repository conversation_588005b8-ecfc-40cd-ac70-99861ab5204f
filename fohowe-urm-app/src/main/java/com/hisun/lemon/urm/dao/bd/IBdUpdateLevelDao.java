/*
 * @ClassName IBdUpdateLevelDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-11 15:43:33
 */
package com.hisun.lemon.urm.dao.bd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.RightOrMemLevelAdjustBean;
import com.hisun.lemon.urm.dto.mi.member.RightOrMemLevelAdjustQryDTO;
import com.hisun.lemon.urm.entity.bd.BdUpdateLevelDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IBdUpdateLevelDao extends BaseDao<BdUpdateLevelDO> {

	//分页相关
		int getTotalCount(RightOrMemLevelAdjustQryDTO dto);
		List<RightOrMemLevelAdjustBean> getListByPageBreak(RightOrMemLevelAdjustQryDTO dto);
		//详情查询
		RightOrMemLevelAdjustBean getLevelAdjustDetail(@Param("id") Long id);
		
		BdUpdateLevelDO get(long parseLong);

}