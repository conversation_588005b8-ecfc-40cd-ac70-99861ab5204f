package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.fohowe.common.enums.BonusType;
import com.hisun.lemon.urm.dto.mi.member.RightUpgradeBean;
import com.hisun.lemon.urm.dto.mi.member.RightUpgradeRspVO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class RightUpgradeExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="经营权经销商升级日志";
		
		String[] colNames=new String[] {
				"奖金制度","区域","分公司","代办处编号","经营权编号",
				"会员编号","姓名","期数","旧级别","新级别",
				"升级类型"
				};
		//String[] colNames=new String[] {"","","","","",};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		RightUpgradeRspVO vo=(RightUpgradeRspVO) obj;
		List<RightUpgradeBean> dataList=vo.getDataList();
		Map<String,String> levelNameKV=vo.getLevelNameKV();
		Map<String,String> cardTypeKV=vo.getCardTypeKV();
		for(RightUpgradeBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(BonusType.getByCode(o.getBonusType()).getName());
			row.createCell(1).setCellValue(o.getAreaCode());
			row.createCell(2).setCellValue(o.getCompanyCode());
			row.createCell(3).setCellValue(o.getAgentNo());
			row.createCell(4).setCellValue(o.getRightNo()==null?"":o.getRightNo().trim());
			
			row.createCell(5).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(6).setCellValue(o.getMemberName());
			row.createCell(7).setCellValue(o.getwWeek());
			if("1".equals(o.getUpType())) { //经营权
				row.createCell(8).setCellValue(levelNameKV.get(o.getOldLevel()));
				row.createCell(9).setCellValue(levelNameKV.get(o.getNewLevel()));
				row.createCell(10).setCellValue("经营权升级");
			}else {
				row.createCell(8).setCellValue(cardTypeKV.get(o.getOldLevel()));
				row.createCell(9).setCellValue(cardTypeKV.get(o.getNewLevel()));
				row.createCell(10).setCellValue("经销商升级");
			}
		}
		
	}
	public static RightUpgradeExcelExporter builder() {
		return new RightUpgradeExcelExporter();
	}
}
