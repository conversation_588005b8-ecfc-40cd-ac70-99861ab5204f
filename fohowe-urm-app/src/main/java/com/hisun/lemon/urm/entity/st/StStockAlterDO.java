/*
 * @ClassName StStockAlterDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-12 17:43:36
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class StStockAlterDO extends BaseDO {
    /**
     * @Fields id 00.ID ID
     */
    private Long id;
    /**
     * @Fields companyCode 分公司/仓库编号
     */
    private String companyCode;
    /**
     * @Fields receiptNo 单据编号 通过fn_sys_getno('stao',0)获得
     */
    private String receiptNo;
    /**
     * @Fields receiptType 单据类型(DIC）
     */
    private String receiptType;
    /**
     * @Fields operAtor 创建人
     */
    private String operAtor;
    /**
     * @Fields operDate 创建日期
     */
    private LocalDateTime operDate;
    /**
     * @Fields status 状态,0=新增 ，1=已审核 2财务审核 3审核回退
     */
    private String status;
    /**
     * @Fields checker 审核人
     */
    private String checker;
    /**
     * @Fields checkDate 审核日期
     */
    private LocalDateTime checkDate;
    /**
     * @Fields remark 10.备注 Remark
     */
    private String remark;
    /**
     * @Fields cancelCode 作废人
     */
    private String cancelCode;
    /**
     * @Fields cancelTime 作废时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields reCheckerCode 
     */
    private String reCheckerCode;
    /**
     * @Fields reCheckTime 财务确认时间
     */
    private LocalDateTime reCheckTime;
    /**
     * @Fields confirmCheckerCode  
     */
    private String confirmCheckerCode;
    /**
     * @Fields confirmCheckTime 确认时间
     */
    private LocalDateTime confirmCheckTime;
    /**
     * @Fields fiRemark 财务确认备注
     */
    private String fiRemark;
    /**
     * @Fields isEv 是否门票库存调整 0 否  1是
     */
    private String isEv;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

    public String getOperAtor() {
        return operAtor;
    }

    public void setOperAtor(String operAtor) {
        this.operAtor = operAtor;
    }

    public LocalDateTime getOperDate() {
        return operDate;
    }

    public void setOperDate(LocalDateTime operDate) {
        this.operDate = operDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public LocalDateTime getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(LocalDateTime checkDate) {
        this.checkDate = checkDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getReCheckerCode() {
        return reCheckerCode;
    }

    public void setReCheckerCode(String reCheckerCode) {
        this.reCheckerCode = reCheckerCode;
    }

    public LocalDateTime getReCheckTime() {
        return reCheckTime;
    }

    public void setReCheckTime(LocalDateTime reCheckTime) {
        this.reCheckTime = reCheckTime;
    }

    public String getFiRemark() {
        return fiRemark;
    }

    public void setFiRemark(String fiRemark) {
        this.fiRemark = fiRemark;
    }

    public String getIsEv() {
        return isEv;
    }

    public void setIsEv(String isEv) {
        this.isEv = isEv;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

	public String getConfirmCheckerCode() {
		return confirmCheckerCode;
	}

	public void setConfirmCheckerCode(String confirmCheckerCode) {
		this.confirmCheckerCode = confirmCheckerCode;
	}

	public LocalDateTime getConfirmCheckTime() {
		return confirmCheckTime;
	}

	public void setConfirmCheckTime(LocalDateTime confirmCheckTime) {
		this.confirmCheckTime = confirmCheckTime;
	}
    
}