package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;

public class BonusFreezeStore extends BaseDO {
    private Integer id;

    private String companyCode;

    private String agentNo;

    private String memberNo;

    private BigDecimal taxRate;

    private Integer fsStatus;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public Integer getFsStatus() {
        return fsStatus;
    }

    public void setFsStatus(Integer fsStatus) {
        this.fsStatus = fsStatus;
    }
}