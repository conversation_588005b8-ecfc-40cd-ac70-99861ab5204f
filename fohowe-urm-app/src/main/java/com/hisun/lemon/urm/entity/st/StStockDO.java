/*
 * @ClassName StStockDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-08 14:49:31
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class StStockDO extends BaseDO {
    /**
     * @Fields id 00.ID ID
     */
    private Long id;
    /**
     * @Fields companyCode 分公司/仓库编号
     */
    private String companyCode;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    private String goodsName;
    /**
     * @Fields quantity 库存量
     */
    private Integer quantity;
    /**
     * @Fields onWay 订货在途
     */
    private Integer onWay;
    /**
     * @Fields donWay 调入在途
     */
    private Integer donWay;
    /**
     * @Fields lastSaleDate 最后销售日
     */
    private LocalDateTime lastSaleDate;
    /**
     * @Fields lastInDate 最后入库日（采购，调拨）
     */
    private LocalDateTime lastInDate;
    /**
     * @Fields yearSale 本年销售
     */
    private Integer yearSale;
    /**
     * @Fields lySale 上年销售,年度结转时更新
     */
    private Integer lySale;
    /**
     * @Fields lastMonth 上月销量
     */
    private Integer lastMonth;
    /**
     * @Fields thisMonth 本月销量
     */
    private Integer thisMonth;
    /**
     * @Fields saleQty 30天日均销量
     */
    private Integer saleQty;
    /**
     * @Fields validQty 可用库存量
     */
    private Integer validQty;
    /**
     * @Fields stockDays 库存天数
     */
    private Integer stockDays;
    /**
     * @Fields requestQty 采购申请量
     */
    private Integer requestQty;
    /**
     * @Fields outWay 调出在途
     */
    private Integer outWay;
    /**
     * @Fields alterQty 报损库存
     */
    private Integer alterQty;
    /**
     * @Fields saleWay 销售在途
     */
    private Integer saleWay;
    /**
     * @Fields waitQty 待发货
     */
    private Integer waitQty;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    private BigDecimal standardPrice;

    private BigDecimal standardFv;
    private BigDecimal buyPrice;
    private String orderSpec;
    private int orderUnit;
    private Integer virtualQty;
    /**
     * 待审批销售库存
     */
    private Integer saleWait;
    //是否删除
    private Integer isDel;

    private String codeName;

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getOnWay() {
        return onWay;
    }

    public void setOnWay(Integer onWay) {
        this.onWay = onWay;
    }

    public Integer getDonWay() {
        return donWay;
    }

    public void setDonWay(Integer donWay) {
        this.donWay = donWay;
    }

    public LocalDateTime getLastSaleDate() {
        return lastSaleDate;
    }

    public void setLastSaleDate(LocalDateTime lastSaleDate) {
        this.lastSaleDate = lastSaleDate;
    }

    public LocalDateTime getLastInDate() {
        return lastInDate;
    }

    public void setLastInDate(LocalDateTime lastInDate) {
        this.lastInDate = lastInDate;
    }

    public Integer getYearSale() {
        return yearSale;
    }

    public void setYearSale(Integer yearSale) {
        this.yearSale = yearSale;
    }

    public Integer getLySale() {
        return lySale;
    }

    public void setLySale(Integer lySale) {
        this.lySale = lySale;
    }

    public Integer getLastMonth() {
        return lastMonth;
    }

    public void setLastMonth(Integer lastMonth) {
        this.lastMonth = lastMonth;
    }

    public Integer getThisMonth() {
        return thisMonth;
    }

    public void setThisMonth(Integer thisMonth) {
        this.thisMonth = thisMonth;
    }

    public Integer getSaleQty() {
        return saleQty;
    }

    public void setSaleQty(Integer saleQty) {
        this.saleQty = saleQty;
    }

    public Integer getValidQty() {
        return validQty;
    }

    public void setValidQty(Integer validQty) {
        this.validQty = validQty;
    }

    public Integer getStockDays() {
        return stockDays;
    }

    public void setStockDays(Integer stockDays) {
        this.stockDays = stockDays;
    }

    public Integer getRequestQty() {
        return requestQty;
    }

    public void setRequestQty(Integer requestQty) {
        this.requestQty = requestQty;
    }

    public Integer getOutWay() {
        return outWay;
    }

    public void setOutWay(Integer outWay) {
        this.outWay = outWay;
    }

    public Integer getAlterQty() {
        return alterQty;
    }

    public void setAlterQty(Integer alterQty) {
        this.alterQty = alterQty;
    }

    public Integer getSaleWay() {
        return saleWay;
    }

    public void setSaleWay(Integer saleWay) {
        this.saleWay = saleWay;
    }

    public Integer getWaitQty() {
        return waitQty;
    }

    public void setWaitQty(Integer waitQty) {
        this.waitQty = waitQty;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public BigDecimal getStandardFv() {
        return standardFv;
    }

    public void setStandardFv(BigDecimal standardFv) {
        this.standardFv = standardFv;
    }

    public String getOrderSpec() {
        return orderSpec;
    }

    public void setOrderSpec(String orderSpec) {
        this.orderSpec = orderSpec;
    }

    public int getOrderUnit() {
        return orderUnit;
    }

    public void setOrderUnit(int orderUnit) {
        this.orderUnit = orderUnit;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getBuyPrice() {
        return buyPrice;
    }

    public void setBuyPrice(BigDecimal buyPrice) {
        this.buyPrice = buyPrice;
    }

	public Integer getVirtualQty() {
		return virtualQty;
	}

	public void setVirtualQty(Integer virtualQty) {
		this.virtualQty = virtualQty;
	}

	public Integer getSaleWait() {
		return saleWait;
	}

	public void setSaleWait(Integer saleWait) {
		this.saleWait = saleWait;
	}

	public Integer getIsDel() {
		return isDel;
	}

	public void setIsDel(Integer isDel) {
		this.isDel = isDel;
	}
	
}