/*
 * @ClassName IPdQmanifestItemsDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-08 10:23:01
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdQmanifestDO;
import com.hisun.lemon.urm.entity.pd.PdQmanifestItemRspDO;
import com.hisun.lemon.urm.entity.pd.PdQmanifestItemsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface IPdQmanifestItemsDao extends BaseDao<PdQmanifestItemsDO> {

    int insertBatchSend(@Param("maniItem") List<PdQmanifestItemsDO> maniItemDOList);

    List<PdQmanifestItemRspDO> queryGoods(Integer id);

    int deleteBySendId(Integer id);

    List<PdQmanifestItemsDO> queryById(Integer id);

    int updateSendNum(List<Integer> idList);
    
    int updateCanceSendNum(List<Integer> idList);

    int updateRecNum(Integer id);

    List<PdQmanifestItemsDO> queryByIds(List<Integer> idList);

    /**
     * 获取商品单价
     * @param itemsDOList
     * @return
     */
    List<PdQmanifestItemRspDO> queryPrice(List<PdQmanifestItemsDO> itemsDOList);

    /** 
     * @Title: queryShipGoods 
     * @param areaCode
     * @param qmanifestDO
     * @param companyCode
     * @param timeType
     * @param startDate
     * @param endDate
     * @param reqStores
     * @return
     * @return: List<T>
     */
    List<PdQmanifestItemRspDO> queryShipGoods(@Param("areaCode")String areaCode, @Param("item")PdQmanifestDO qmanifestDO
            , @Param("companyCode")String companyCode, @Param("timeType") String timeType
            , @Param("startDate")LocalDateTime startDate, @Param("endDate")LocalDateTime endDate
            , @Param("reqList")List<String> reqList);

    /** 
     * @Title: queryShipSendGoods 
     * @param qmanifestDO
     * @param companyCode
     * @param code
     * @param startDate
     * @param endDate
     * @param reqStores
     * @return
     * @return: List<T>
     */
    List<PdQmanifestItemRspDO> queryShipSendGoods(@Param("item") PdQmanifestDO qmanifestDO, @Param("companyCode") String companyCode
            , @Param("queryType") String code, @Param("startDate")LocalDateTime startDate
            , @Param("endDate")LocalDateTime endDate, @Param("reqList")List<String> reqList);
}