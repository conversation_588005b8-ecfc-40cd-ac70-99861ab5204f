/*
 * @ClassName IPdPoOrderDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-25 10:44:08
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdPoOrderDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IPdPoOrderDao extends BaseDao<PdPoOrderDO> {

    /** 
     * @Title: getPageList 
     * @param orderNo
     * @param receiptStatus
     * @param checkDate
     * @param properTypeList 
     * @param delFlag 
     * @param vendorName 
     * @param companyCode 
     * @return
     * @return: List<PdPoOrderDO>
     */
    List<PdPoOrderDO> getPageList(@Param("orderNo") String orderNo, @Param("receiptStatus") String receiptStatus,
            @Param("properTypeList") List<String> properTypeList, @Param("delFlag") String delFlag,
            @Param("vendorName") String vendorName, @Param("companyCode") String companyCode);

    /** 
     * @Title: getByOrderNo 
     * @param orderNo
     * @return
     * @return: PdPoOrderDO
     */
    PdPoOrderDO getByOrderNo(@Param("orderNo") String orderNo);

    /** 
     * @Title: get 
     * @param id
     * @return
     * @return: PdPoOrderDO
     */
    PdPoOrderDO get(@Param("id") long id);

    /** 
     * @Title: delete 
     * @param id
     * @return
     * @return: int
     */
    int delete(@Param("id") long id);

    Boolean isSlfExit(@Param("memberNo") String memberNo);
}