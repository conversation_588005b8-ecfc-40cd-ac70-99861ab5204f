package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.dto.st.StockAlterBeanDTO;
import com.hisun.lemon.urm.dto.st.StockAlterQueryListRspDTO;
import com.hisun.lemon.urm.enums.st.ReceiptStatus;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class StockAlterExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "库存调整查询列表";
        String[] colNames = new String[] { "分公司编号", "调整单编号","总货值", "调整原因", "调整单状态", "审核人", "审核时间" };
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        StockAlterQueryListRspDTO vo = (StockAlterQueryListRspDTO) obj;
        List<StockAlterBeanDTO> dataList = vo.getStockAlterList();
        Map<String, String> receiptTypeMap = vo.getReceiptTypeMap();
        for (StockAlterBeanDTO o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);

            // 分公司编号
            row.createCell(0).setCellValue(o.getCompanyCode());

            // 调整单编号
            row.createCell(1).setCellValue(o.getReceiptNo() + "");
            //总货值
            row.createCell(2).setCellValue(o.getTotalPrice() + "");

            // 调整原因
            row.createCell(3).setCellValue(receiptTypeMap.get(o.getReceiptType()));

            // 调整单状态
            row.createCell(4).setCellValue(JudgeUtils.isNull(ReceiptStatus.getByCode(o.getStatus())) ? o.getStatus()
                    : ReceiptStatus.getByCode(o.getStatus()).getName());
            
            //审核人和审核时间（需要区分不同阶段）
           if (o.getStatus().equals(ReceiptStatus.BRANCH.getCode())) {
                row.createCell(5).setCellValue(o.getChecker() + "");// 审核人
                row.createCell(6).setCellValue(JudgeUtils.isNull(o.getCheckDate()) ? "" : o.getCheckDate().format(ymdhms));// 审核时间
			}else if (o.getStatus().equals(ReceiptStatus.HEAD.getCode())){
	            row.createCell(5).setCellValue(o.getReCheckerCode() + "");// 审核人
	            row.createCell(6).setCellValue(JudgeUtils.isNull(o.getReCheckTime()) ? "" : o.getReCheckTime().format(ymdhms)); // 审核时间
			}else if (o.getStatus().equals(ReceiptStatus.REFUSE.getCode())){
	            row.createCell(5).setCellValue(o.getCancelCode() + "");// 审核人
	            row.createCell(6).setCellValue(JudgeUtils.isNull(o.getCancelTime()) ? "" : o.getCancelTime().format(ymdhms)); // 审核时间
			}else if (o.getStatus().equals(ReceiptStatus.CONFIRM.getCode())){
				row.createCell(5).setCellValue(o.getConfirmCheckerCode() + "");// 确认人
	            row.createCell(6).setCellValue(JudgeUtils.isNull(o.getConfirmCheckTime()) ? "" : o.getConfirmCheckTime().format(ymdhms)); // 确认时间
			}else {
				row.createCell(5).setCellValue("");// 审核人
	            row.createCell(6).setCellValue(""); // 审核时间
			}
        }

    }

    public static StockAlterExcelExporter builder() {
        return new StockAlterExcelExporter();
    }
}
