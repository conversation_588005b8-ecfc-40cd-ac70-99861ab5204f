package com.hisun.lemon.urm.entity.mi;

import com.hisun.lemon.framework.data.BaseDO;

public class MiMemberTeamInfo extends BaseDO {
    private Integer id;
    private String userCode;

    private String type;

    private String memo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
