/*
 * @ClassName ImpMemberCnDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class ImpMemberCnDO extends BaseDO {
    /**
     * @Fields memberNo 
     */
    private Long memberNo;
    /**
     * @Fields name 姓名
     */
    private String name;
    /**
     * @Fields recommendNo 推荐人
     */
    private String recommendNo;
    /**
     * @Fields linkNo 安置人
     */
    private String linkNo;
    /**
     * @Fields sn 顺序
     */
    private String sn;
    /**
     * @Fields idcard 身份证号
     */
    private String idcard;
    /**
     * @Fields accountBank 开户银行
     */
    private String accountBank;
    /**
     * @Fields accountName 开户名
     */
    private String accountName;
    /**
     * @Fields accountCode 银行帐号
     */
    private String accountCode;
    /**
     * @Fields memo 
     */
    private String memo;

    public Long getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(Long memberNo) {
        this.memberNo = memberNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRecommendNo() {
        return recommendNo;
    }

    public void setRecommendNo(String recommendNo) {
        this.recommendNo = recommendNo;
    }

    public String getLinkNo() {
        return linkNo;
    }

    public void setLinkNo(String linkNo) {
        this.linkNo = linkNo;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getAccountBank() {
        return accountBank;
    }

    public void setAccountBank(String accountBank) {
        this.accountBank = accountBank;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}