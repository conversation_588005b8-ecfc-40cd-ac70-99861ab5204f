package com.hisun.lemon.urm.sys.filter.impl;

import java.util.Arrays;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.UserType;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.dao.sys.ISysManagerDao;
import com.hisun.lemon.urm.dao.sys.ISysUserGrantDao;
import com.hisun.lemon.urm.dto.sys.UserBasicInfDTO;
import com.hisun.lemon.urm.entity.sys.SysManagerDO;
import com.hisun.lemon.urm.service.sys.IUserService;
import com.hisun.lemon.urm.sys.bo.PermissionBeanBO;
import com.hisun.lemon.urm.sys.filter.SysPermissionFileterService;

/**
 * 权限过滤服务实现类
 * <AUTHOR>
 * @date 2017年12月20日
 * @time 下午4:15:48
 */
@Service
public class SysPermissionFilterServiceImpl implements SysPermissionFileterService {
    private static final Logger logger = LoggerFactory.getLogger(SysPermissionFileterService.class);
    
    @Resource
    private ISysUserGrantDao userGrantDao;
    @Resource
    private IUserService userService;
    @Resource
    private ISysManagerDao managerDao;
    
    @Override
    public void filter(PermissionBeanBO beanBO) {
        String userCode = LemonUtils.getUserId();
        if (JudgeUtils.isNotBlank(userCode)) {
            //获取当前登录用户信息，进行查询结果过滤
            UserBasicInfDTO user = userService.queryUserByLoginId(userCode);
            if(user==null) return;
            String userType = user.getUserType();
            beanBO.setUserType(userType);
            logger.info("UserBasicInfDTO:"+user.toString());
            if (userType.equals(UserType.AGENT.getCode())) {
                // 代办处用户
                beanBO.setAgentNo(LemonUtils.getUserId());
                beanBO.setAreaCode(user.getAreaCode());
                beanBO.setBonusType(user.getBonusType());
                beanBO.setCompanyCode(user.getCompanyCode());
            } else if (userType.equals(UserType.MEMBER.getCode())) {
                // 经销商用户
                beanBO.setMemberNo(LemonUtils.getUserId());
                beanBO.setAreaCode(user.getAreaCode());
                beanBO.setBonusType(user.getBonusType());
                beanBO.setCompanyCode(user.getCompanyCode());
            } 
            else if (userType.equals(UserType.COMPANY.getCode())) {
                // 分公司用户
            	if(JudgeUtils.isEmpty(beanBO.getCompanyCode())) {
            		beanBO.setCompanyCode(user.getCompanyCode());
            	}
            	SysManagerDO managerDO = managerDao.get(userCode);
        		if (JudgeUtils.isNotNull(managerDO)) {
        			String managerCompany = managerDO.getManagerCompany();
        			String[] companyCodes = managerCompany.split(",");
        			beanBO.setManagerCompanys(Arrays.asList(companyCodes));
        		}
                //beanBO.setCompanyCode(user.getCompanyCode());
            } else if (userType.equals(UserType.HEAD.getCode())) {
                // 总公司用户-区域管理用户
            	if(JudgeUtils.isBlank(beanBO.getCompanyCode())) {
            		beanBO.setCompanyCode(user.getCompanyCode());
            	}
        		SysManagerDO managerDO = managerDao.get(userCode);
        		if (JudgeUtils.isNotNull(managerDO)) {
        			String managerCompany = managerDO.getManagerCompany();
        			String[] companyCodes = managerCompany.split(",");
        			beanBO.setManagerCompanys(Arrays.asList(companyCodes));
        			if (JudgeUtils.equals(user.getCompanyCode(), LemonUtils.getProperty("urm.headCompany"))) {
        				beanBO.setCompanyCode(managerCompany);
        			}
        		}
                /*if (JudgeUtils.notEquals(user.getCompanyCode(), LemonUtils.getProperty("urm.headCompany"))) {
                    beanBO.setCompanyCode(user.getCompanyCode());
                }else{
                	if(JudgeUtils.isEmpty(beanBO.getCompanyCode())) {
                		SysManagerDO managerDO = managerDao.get(userCode);
                		if (JudgeUtils.isNull(managerDO)) {
                			LemonException.throwBusinessException(MsgCd.QUERY_NO_RECORD.getMsgCd());
                		}
                		String managerCompany = managerDO.getManagerCompany();
                		beanBO.setCompanyCode(managerCompany);
                	}
                }*/
            }
        }else {
//        	LemonException.throwBusinessException(MsgCd.QUERY_NO_RECORD.getMsgCd());
        }
    }

}
