/*
 * @ClassName SysConfigValueDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:46
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysConfigValueDO extends BaseDO {
    /**
     * @Fields valueId 索引ID VALUE_ID
     */
    private long valueId;
    /**
     * @Fields KEY_ID
     */
    private long keyId;
    /**
     * @Fields companyCode 公司编码 COMPANY_CODE
     */
    private String companyCode;
    /**
     * @Fields companyName 公司名称 
     */
    private String companyName;
    /**
     * @Fields configValue 参数值 CONFIG_VALUE
     */
    private String configValue;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    private String keyDesc;
    
    private String defaultValue;
    private String configValueTwo;

    public long getKeyId() {
        return keyId;
    }

    public void setKeyId(long keyId) {
        this.keyId = keyId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public long getValueId() {
        return valueId;
    }

    public void setValueId(long valueId) {
        this.valueId = valueId;
    }

	public String getKeyDesc() {
		return keyDesc;
	}

	public void setKeyDesc(String keyDesc) {
		this.keyDesc = keyDesc;
	}

	public String getDefaultValue() {
		return defaultValue;
	}

	public void setDefaultValue(String defaultValue) {
		this.defaultValue = defaultValue;
	}

    public String getConfigValueTwo() {
        return configValueTwo;
    }

    public void setConfigValueTwo(String configValueTwo) {
        this.configValueTwo = configValueTwo;
    }
}