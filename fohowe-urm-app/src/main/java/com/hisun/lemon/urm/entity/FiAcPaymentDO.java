/*
 * @ClassName FiAcPaymentDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiAcPaymentDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields posId 
     */
    private Long posId;
    /**
     * @Fields posAuthKey 
     */
    private String posAuthKey;
    /**
     * @Fields sessionId 
     */
    private String sessionId;
    /**
     * @Fields currency 本地货币类型
     */
    private String currency;
    /**
     * @Fields amount 本地货币金额
     */
    private BigDecimal amount;
    /**
     * @Fields paymentDesc 
     */
    private String paymentDesc;
    /**
     * @Fields orderId 订单编号（充值/购物）
     */
    private String orderId;
    /**
     * @Fields firstName 
     */
    private String firstName;
    /**
     * @Fields lastName 
     */
    private String lastName;
    /**
     * @Fields street 
     */
    private String street;
    /**
     * @Fields streetHn 
     */
    private String streetHn;
    /**
     * @Fields streetAn 
     */
    private String streetAn;
    /**
     * @Fields city 
     */
    private String city;
    /**
     * @Fields postCode 
     */
    private String postCode;
    /**
     * @Fields country 
     */
    private String country;
    /**
     * @Fields email 
     */
    private String email;
    /**
     * @Fields phone 
     */
    private String phone;
    /**
     * @Fields language 
     */
    private String language;
    /**
     * @Fields clientIp 
     */
    private String clientIp;
    /**
     * @Fields js 
     */
    private String js;
    /**
     * @Fields payBackLogin 
     */
    private String payBackLogin;
    /**
     * @Fields sig 
     */
    private String sig;
    /**
     * @Fields ts 
     */
    private String ts;
    /**
     * @Fields key1 
     */
    private String key1;
    /**
     * @Fields transStatus 1:new  2:cancelled 3:rejected 4:started 5:awaiting collection 7:payment rejected 99:payment collected - finished 888:incorrect status
     */
    private String transStatus;
    /**
     * @Fields status 
     */
    private String status;
    /**
     * @Fields transId 
     */
    private String transId;
    /**
     * @Fields transPayType 
     */
    private String transPayType;
    /**
     * @Fields transPayGwName 
     */
    private String transPayGwName;
    /**
     * @Fields transCreateTime 
     */
    private LocalDateTime transCreateTime;
    /**
     * @Fields transSentTime 
     */
    private LocalDateTime transSentTime;
    /**
     * @Fields transRecvTime 
     */
    private LocalDateTime transRecvTime;
    /**
     * @Fields transCancel 
     */
    private String transCancel;
    /**
     * @Fields transAuthFraud 
     */
    private String transAuthFraud;
    /**
     * @Fields transType 交易类型:0 充值  1购货 2现金或转帐
     */
    private String transType;
    /**
     * @Fields rate 本地汇率
     */
    private BigDecimal rate;
    /**
     * @Fields payState 1付款成功  2充值成功 3取消
     */
    private String payState;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPosId() {
        return posId;
    }

    public void setPosId(Long posId) {
        this.posId = posId;
    }

    public String getPosAuthKey() {
        return posAuthKey;
    }

    public void setPosAuthKey(String posAuthKey) {
        this.posAuthKey = posAuthKey;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPaymentDesc() {
        return paymentDesc;
    }

    public void setPaymentDesc(String paymentDesc) {
        this.paymentDesc = paymentDesc;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getStreetHn() {
        return streetHn;
    }

    public void setStreetHn(String streetHn) {
        this.streetHn = streetHn;
    }

    public String getStreetAn() {
        return streetAn;
    }

    public void setStreetAn(String streetAn) {
        this.streetAn = streetAn;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getJs() {
        return js;
    }

    public void setJs(String js) {
        this.js = js;
    }

    public String getPayBackLogin() {
        return payBackLogin;
    }

    public void setPayBackLogin(String payBackLogin) {
        this.payBackLogin = payBackLogin;
    }

    public String getSig() {
        return sig;
    }

    public void setSig(String sig) {
        this.sig = sig;
    }

    public String getTs() {
        return ts;
    }

    public void setTs(String ts) {
        this.ts = ts;
    }

    public String getKey1() {
        return key1;
    }

    public void setKey1(String key1) {
        this.key1 = key1;
    }

    public String getTransStatus() {
        return transStatus;
    }

    public void setTransStatus(String transStatus) {
        this.transStatus = transStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getTransPayType() {
        return transPayType;
    }

    public void setTransPayType(String transPayType) {
        this.transPayType = transPayType;
    }

    public String getTransPayGwName() {
        return transPayGwName;
    }

    public void setTransPayGwName(String transPayGwName) {
        this.transPayGwName = transPayGwName;
    }

    public LocalDateTime getTransCreateTime() {
        return transCreateTime;
    }

    public void setTransCreateTime(LocalDateTime transCreateTime) {
        this.transCreateTime = transCreateTime;
    }

    public LocalDateTime getTransSentTime() {
        return transSentTime;
    }

    public void setTransSentTime(LocalDateTime transSentTime) {
        this.transSentTime = transSentTime;
    }

    public LocalDateTime getTransRecvTime() {
        return transRecvTime;
    }

    public void setTransRecvTime(LocalDateTime transRecvTime) {
        this.transRecvTime = transRecvTime;
    }

    public String getTransCancel() {
        return transCancel;
    }

    public void setTransCancel(String transCancel) {
        this.transCancel = transCancel;
    }

    public String getTransAuthFraud() {
        return transAuthFraud;
    }

    public void setTransAuthFraud(String transAuthFraud) {
        this.transAuthFraud = transAuthFraud;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }
}