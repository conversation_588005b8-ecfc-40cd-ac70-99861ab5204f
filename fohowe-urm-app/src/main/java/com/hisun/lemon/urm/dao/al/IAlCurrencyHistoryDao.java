/*
 * @ClassName IAlCurrencyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-06 10:45:13
 */
package com.hisun.lemon.urm.dao.al;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.al.CurrencyQueryReqDTO;
import com.hisun.lemon.urm.entity.al.AlCurrencyHistoryDO;

@Mapper
public interface IAlCurrencyHistoryDao extends BaseDao<AlCurrencyHistoryDO> {
	
	public int insertHistoryByCurrency(@Param("currencyCode") String currencyCode);
	public AlCurrencyHistoryDO getHisCurrency(@Param("currencyCode") String currencyCode,@Param("createTime") String createTime);
	public List<AlCurrencyHistoryDO> getHisList(@Param("currencyCode") String currencyCode);
    public List<AlCurrencyHistoryDO> findList(CurrencyQueryReqDTO currencyQueryReqDTO);
}