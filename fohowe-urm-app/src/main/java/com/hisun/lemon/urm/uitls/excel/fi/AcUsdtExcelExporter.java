package com.hisun.lemon.urm.uitls.excel.fi;



import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.fi.fgc.AcUsdtQueryBean;
import com.hisun.lemon.urm.dto.fi.fgc.AcUsdtVO;
import com.hisun.lemon.urm.dto.sys.UsdtQueryListRspDTO;
import com.hisun.lemon.urm.enums.fi.UsdtStatusEnums;
import com.hisun.lemon.urm.enums.fi.UsdtTypeEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcUsdtExcelExporter extends  URMExcelExportFactorys{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="USDT";
		String[] colNames=new String[] {"期数","所属分公司","代办处编号","订单号","用户编号","USDT账户","USDT数量",
				"本地货币代码","本地货币金额","汇率","F$","本地货币金额","状态","创建时间","转账时间","转账类型","转账备注","动作类型",
				"财务确认状态","财务确认时间","财务确认人","财务确认备注"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		AcUsdtVO vo=(AcUsdtVO) obj;
		List<AcUsdtQueryBean> dataList=vo.getDataList();
		Map<Object, String> dateTypeKV=EnumsUtils.EnumToMap(UsdtTypeEnums.class);
		Map<Object, String> statusKV=EnumsUtils.EnumToMap(UsdtStatusEnums.class);
		for(AcUsdtQueryBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getPeriodWeek());
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getOrderNo());
			row.createCell(index++).setCellValue(o.getUserCode());
			row.createCell(index++).setCellValue(o.getUsdtCode());
			row.createCell(index++).setCellValue(o.getTransferAmount()==null?0.00:o.getTransferAmount().doubleValue());
			row.createCell(index++).setCellValue(o.getLocalCurrency());
			row.createCell(index++).setCellValue(o.getLocalMoney()==null?0.00:o.getLocalMoney().doubleValue());
			row.createCell(index++).setCellValue(o.getRate()==null?0.00:o.getRate().doubleValue());
			row.createCell(index++).setCellValue(o.getFdMoney()==null?0.00:o.getFdMoney().doubleValue());
			BigDecimal currenyMony=BigDecimal.ZERO;
			if(o.getFdMoney()!=null && o.getRate() != null) {
				currenyMony=o.getFdMoney().multiply(o.getRate()).setScale(2,BigDecimal.ROUND_HALF_UP);
			}
			row.createCell(index++).setCellValue(currenyMony.doubleValue());
			row.createCell(index++).setCellValue(statusKV.get(o.getStatus()));
			row.createCell(index++).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getTransferTime()==null?"":o.getTransferTime().format(ymdhms));
			row.createCell(index++).setCellValue(dateTypeKV.get(o.getDataType()));
			row.createCell(index++).setCellValue(o.getComments());
			String typeName="";
			if(JudgeUtils.isNotBlank(o.getStockType())) {
				if(o.getStockType().equals("1")) {
					typeName="充值";
				}else {
					typeName="体现";
				}
			}
			row.createCell(index++).setCellValue(typeName);
			row.createCell(index++).setCellValue(o.getFiCheckStatus());
			row.createCell(index++).setCellValue(o.getFicheckeTime()==null?"":o.getFicheckeTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getFicheckerCode());
			row.createCell(index++).setCellValue(o.getMemo());
		}
	}
	/**
	 * USDT绑定账户导出
	 */
	public void export2(Object obj, HttpServletResponse response) {
		try {
			String fileName="USDT账户";
			String[] colNames=new String[] {"所属分公司","代办处编号","用户编号","用户名称","USDT账户","状态","创建时间","确认时间","备注"};
			List<List<Object>> data= addData(obj);
			super.exportActually2(data,fileName, colNames, response);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	public List<List<Object>> addData(Object obj) throws Exception {
		List<List<Object>> list = new ArrayList<List<Object>>();
		List<UsdtQueryListRspDTO> usdtList = (List<UsdtQueryListRspDTO>) obj;
		if(usdtList==null) return list;
		for (int j = 0; j < usdtList.size(); j++) {
			UsdtQueryListRspDTO o = (UsdtQueryListRspDTO)usdtList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(o.getCompanyCode());
			data.add(o.getAgentNo());
			data.add(o.getUserCode());
			data.add(o.getUserName());
			data.add(o.getUsdtCode());
			if(o.getUsdtStatus()==null) {
				o.setUsdtStatus(0);
			}
			String statusStr="";
			if(o.getUsdtStatus()==0) {
				statusStr="未确认";
			}
			if(o.getUsdtStatus()==1) {
				statusStr="已确认";
			}
			if(o.getUsdtStatus()==3) {
				statusStr="已解绑";
			}
			data.add(statusStr);
			data.add(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			data.add(o.getCheckeTime()==null?"":o.getCheckeTime().format(ymdhms));
			data.add(o.getRemark());
			list.add(data);
		}
		return list;
	}
	
	public static AcUsdtExcelExporter builder() {
		return new AcUsdtExcelExporter();
	}
}
