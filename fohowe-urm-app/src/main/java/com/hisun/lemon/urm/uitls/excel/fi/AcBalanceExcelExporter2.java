package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.fi.balance.AcBalanceQueryBean2;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceVO2;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class AcBalanceExcelExporter2 extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="账户余额统计";
		String[] colNames=new String[] {"所属分公司","用户编号","F$","FV","F000","H000","FB","活跃PV","活跃P$","旅游基金",
				"名车基金","游艇基金","住宅基金","市场发展基金","跨境积分","FP","借款余额"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AcBalanceVO2 vo=(AcBalanceVO2) obj;
		List<AcBalanceQueryBean2> dataList=vo.getAcBalances();
		for(AcBalanceQueryBean2 o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getCompanyCode()+"-"+o.getCompanyName());
			row.createCell(1).setCellValue(o.getUserCode());
			row.createCell(2).setCellValue(o.getF$()==null?new Integer(0):o.getF$().doubleValue());
			row.createCell(3).setCellValue(o.getFv()==null?new Integer(0):o.getFv().doubleValue());
			row.createCell(4).setCellValue(o.getF000()==null?new Integer(0):o.getF000().doubleValue());
			row.createCell(5).setCellValue(o.getH000()==null?new Integer(0):o.getH000().doubleValue());
			row.createCell(6).setCellValue(o.getFb()==null?new Integer(0):o.getFb().doubleValue());
			row.createCell(7).setCellValue(o.getPv()==null?new Integer(0):o.getPv().doubleValue());
			row.createCell(8).setCellValue(o.getP$()==null?new Integer(0):o.getP$().doubleValue());
			row.createCell(9).setCellValue(o.getB1()==null?new Integer(0):o.getB1().doubleValue());
			row.createCell(10).setCellValue(o.getB2()==null?new Integer(0):o.getB2().doubleValue());
			row.createCell(11).setCellValue(o.getB3()==null?new Integer(0):o.getB3().doubleValue());
			row.createCell(12).setCellValue(o.getB4()==null?new Integer(0):o.getB4().doubleValue());
			row.createCell(13).setCellValue(o.getB5()==null?new Integer(0):o.getB5().doubleValue());
			row.createCell(14).setCellValue(o.getHv()==null?new Integer(0):o.getHv().doubleValue());
			row.createCell(15).setCellValue(o.getFp()==null?new Integer(0):o.getFp().doubleValue());
			row.createCell(16).setCellValue(o.getOwtAmt()==null?new Integer(0):o.getOwtAmt().doubleValue());
		}
		
	}
	public static AcBalanceExcelExporter2 builder() {
		return new AcBalanceExcelExporter2();
	}
}
