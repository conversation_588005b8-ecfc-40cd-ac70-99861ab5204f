package com.hisun.lemon.urm.uitls;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 重新计算网络线程池配置
 * 计算网络独有线程池，请勿他用，否则将影响计算过程
 * <AUTHOR>
 * @date 2018-01-05 15:18
 */
@Configuration
@EnableAsync
public class AsyncApplication {
	private static final Logger logger = LoggerFactory.getLogger(AsyncApplication.class);

	@Value("${urm.networkPool.coreSize:20}")
	private int coreSize=20;  
	@Value("${urm.networkPool.maxSize:100}")
    private int maxSize=100;  
	@Value("${urm.networkPool.queueCapacity:500}")
    private int queueCapacity=500;

    public int getCoreSize() {
		return coreSize;
	}

	public void setCoreSize(int coreSize) {
		this.coreSize = coreSize;
	}

	public int getMaxSize() {
		return maxSize;
	}

	public void setMaxSize(int maxSize) {
		this.maxSize = maxSize;
	}

	public int getQueueCapacity() {
		return queueCapacity;
	}

	public void setQueueCapacity(int queueCapacity) {
		this.queueCapacity = queueCapacity;
	}

	/**
	 * 计算网络独有线程池，请勿他用，否则将影响计算过程
	* @return 
	* <AUTHOR>
	 */
	@Bean
    public Executor reNetworkAsyncExecutor() {
		logger.info("reNetworkAsyncExecutor properties,coreSize:"+coreSize+",maxSize:"+maxSize+",queueCapacity:"+queueCapacity);
       //depends on sql poll!! less than it
    	ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("RegenerateNework-");
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务  
        // CALLER_RUNS：不在新线程中执行任务，而是由调用者所在的线程来执行  
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


}