/*
 * @ClassName IMiAgentMemUpdateDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.agent.AgentMemUpdateBean;
import com.hisun.lemon.urm.dto.mi.agent.AgentMemUpdateDTO;
import com.hisun.lemon.urm.entity.MiAgentMemUpdateDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IMiAgentMemUpdateDao extends BaseDao<MiAgentMemUpdateDO> {

	int getTotalCount(AgentMemUpdateDTO agentMemUpdateDTO);

	List<AgentMemUpdateBean> getListByCondition(AgentMemUpdateDTO agentMemUpdateDTO);
	/**
	 * 批量删除
	 * @param idList
	 * @return
	 */
	int deleteById(@Param("idList") List<Long> idList);
	/**
	 * 通过id查找变更记录
	 * @param string
	 * @return
	 */
	AgentMemUpdateBean getById(@Param("id") Long id);

	void insertMemByAgent(@Param("id") Long id);

	void insertMemByLinknet(@Param("id") Long id);

	List<MiAgentMemUpdateDO> getItemMemById(@Param("id") Long id);

	void updateItem(MiAgentMemUpdateDO iteMemUpdateDO);
	 
}