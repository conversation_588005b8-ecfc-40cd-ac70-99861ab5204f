/**   
 * Copyright © 2017 公司名. All rights reserved.
 * @Title: ConvertUtils.java 
 * @Prject: fohowe-web-ec
 * @Package: com.hisun.lemon.fohowe.ec.utils 
 * @author: xiaozsheng   
 * @date: 2017年12月4日 上午11:28:47 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.uitls;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.CardTypes;
import com.hisun.lemon.urm.dto.al.CharacterQueryRspDTO;

/** 
 * @ClassName: ConvertUtils 
 * @Description: 转化工具
 * @author: xiaozsheng
 * @date: 2017年12月4日 上午11:28:47  
 */
public class ConvertUtils {
    
    /**
     * 
     * @Title: list2Map3 
     * @Description: 列表转map
     * @param list
     * @param keyMethodName
     * @param c
     * @return
     * @return: Map<K,V>
     */
    public static <K, V> Map<K, V> list2Map3(List<V> list, String keyMethodName,Class<V> c) {  
        Map<K, V> map = new HashMap<K, V>();  
        if (list != null) {  
            try {  
                Method methodGetKey = c.getMethod(keyMethodName);  
                for (int i = 0; i < list.size(); i++) {  
                    V value = list.get(i);  
                    @SuppressWarnings("unchecked")  
                    K key = (K) methodGetKey.invoke(list.get(i));  
                    map.put(key, value);  
                }  
            } catch (Exception e) {  
                throw new IllegalArgumentException("field can't match the key!");  
            }  
        }  
  
        return map;  
    }   
    
    public static Map<String, String> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, String> map = new HashMap<String,String>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            String value = String.valueOf(field.get(obj));
            map.put(fieldName, value);
        }
        return map;
    }
    
    public static Map<String, String> checkMap(Map<String, String> map) {
		if (map != null&& !map.isEmpty()) {
			Iterator<String> ite=map.keySet().iterator();
			while (ite.hasNext()) {
				String key = (String) ite.next();
				String value=map.get(key);
				if("sign".equals(key.toLowerCase())||JudgeUtils.equals("null", value)||JudgeUtils.isNull(value)|| JudgeUtils.isBlank(value)) {
					ite.remove();
				}else {
					 
				}
			}
		}
		
		return map;
	}
    
    public static String languageConversion(Map<String, CharacterQueryRspDTO> map, String cardType) {
		if(cardType== null|| "".equals(cardType)) return "";
		String PRE = JudgeUtils.isNull(map.get("listvalue.trainee.member")) ? "listvalue.trainee.member" : map.get("listvalue.trainee.member").getCharacterValue();
        String TYPICAL = JudgeUtils.isNull(map.get("listvalue.qualified.member")) ? "listvalue.qualified.member" : map.get("listvalue.qualified.member").getCharacterValue();
        String GREEN_STONE = JudgeUtils.isNull(map.get("listvalue.emerald.member")) ? "listvalue.emerald.member" : map.get("listvalue.emerald.member").getCharacterValue();
        String BLUE_STONE = JudgeUtils.isNull(map.get("listvalue.sapphire.member")) ? "listvalue.sapphire.member" : map.get("listvalue.sapphire.member").getCharacterValue();
        String DIAMOND = JudgeUtils.isNull(map.get("listvalue.diamond.member")) ? "listvalue.diamond.member" : map.get("listvalue.diamond.member").getCharacterValue();
        String THREE_DIAMOND = JudgeUtils.isNull(map.get("fohowe-store.san_zuan_shi_jing_xiao_shang")) ? "fohowe-store.san_zuan_shi_jing_xiao_shang" : map.get("fohowe-store.san_zuan_shi_jing_xiao_shang").getCharacterValue();
        String FIVE_DIAMOND = JudgeUtils.isNull(map.get("fohowe-store.wu_zuan_shi_jing_xiao_shang")) ? "fohowe-store.wu_zuan_shi_jing_xiao_shang" : map.get("fohowe-store.wu_zuan_shi_jing_xiao_shang").getCharacterValue();
        String SEVEN_DIAMOND = JudgeUtils.isNull(map.get("fohowe-store.qi_zuan_shi_jing_xiao_shang")) ? "fohowe-store.qi_zuan_shi_jing_xiao_shang" : map.get("fohowe-store.qi_zuan_shi_jing_xiao_shang").getCharacterValue();
        String PHOENIX = JudgeUtils.isNull(map.get("fohow.ambassador")) ? "fohow.ambassador" : map.get("fohow.ambassador").getCharacterValue();
        String DOUBLE_PHOENIX = JudgeUtils.isNull(map.get("fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang")) ? "fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang" : map.get("fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang").getCharacterValue();
       
		switch (CardTypes.getByCode(cardType)) {
		case PRE :
			cardType = PRE;
			break;
		case TYPICAL :
			cardType = TYPICAL;
			break;
		case GREEN_STONE :
			cardType = GREEN_STONE;
			break;
		case BLUE_STONE :
			cardType = BLUE_STONE;
			break;
		case DIAMOND :
			cardType = DIAMOND;
			break;
		case THREE_DIAMOND :
			cardType = THREE_DIAMOND;
			break;
		case FIVE_DIAMOND :
			cardType = FIVE_DIAMOND;
			break;
		case SEVEN_DIAMOND :
			cardType = SEVEN_DIAMOND;
			break;
		case PHOENIX :
			cardType = PHOENIX;
			break;
		case DOUBLE_PHOENIX :
			cardType = DOUBLE_PHOENIX;
			break;
		default:
			break;
		}
		return cardType;
	}
    
    public static boolean isNumber(String str) {
    	if(JudgeUtils.isBlank(str)) return false;
    	for (int i =str.length(); --i>=0;) {
			if(!Character.isDigit(str.charAt(i))) {
				return false;
			}
		}
    	return true;
    }
}
