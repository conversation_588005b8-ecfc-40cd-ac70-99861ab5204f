/*
 * @ClassName FiCompanyFeeDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;

public class FiCompanyFeeDO extends BaseDO {
    /**
     * @Fields feeId 
     */
    private Long feeId;
    /**
     * @Fields outCompanyCode 转出公司编码 out_company_code
     */
    private String outCompanyCode;
    /**
     * @Fields inCompanyCode 转入公司编码 in_company_code
     */
    private String inCompanyCode;
    /**
     * @Fields feeRate 手续费比例 fee_rate
     */
    private BigDecimal feeRate;
    /**
     * @Fields minFee 最低费用 min_fee
     */
    private BigDecimal minFee;
    /**
     * @Fields inRoleCode 转入会员群组 in_role_code
     */
    private String inRoleCode;

    public Long getFeeId() {
        return feeId;
    }

    public void setFeeId(Long feeId) {
        this.feeId = feeId;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public BigDecimal getMinFee() {
        return minFee;
    }

    public void setMinFee(BigDecimal minFee) {
        this.minFee = minFee;
    }

    public String getInRoleCode() {
        return inRoleCode;
    }

    public void setInRoleCode(String inRoleCode) {
        this.inRoleCode = inRoleCode;
    }
}