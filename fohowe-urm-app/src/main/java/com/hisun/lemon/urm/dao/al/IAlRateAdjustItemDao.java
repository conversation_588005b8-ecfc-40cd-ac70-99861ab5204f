/*
 * @ClassName IAlRateAdjustItemDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-09 14:17:22
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlRateAdjustItemDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlRateAdjustItemDao extends BaseDao<AlRateAdjustItemDO> {

    /** 
     * @Title: findBuAdjustCode 
     * @Description: 查询该批次调整的所有汇率信息
     * @param rateCode
     * @return
     * @return: List<AlRateAdjustItemDO>
     */
    public List<AlRateAdjustItemDO> findBuAdjustCode(@Param("adjustCode") String adjustCode);
}