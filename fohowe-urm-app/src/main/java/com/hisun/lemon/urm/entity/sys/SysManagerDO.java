/*
 * @ClassName SysManagerDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-09 20:39:39
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysManagerDO extends BaseDO {
    /**
     * @Fields userCode 用户帐号 USER_CODE
     */
    private String userCode;
    /**
     * @Fields userName 用户名/姓名 USER_NAME
     */
    private String userName;
    /**
     * @Fields defLang 默认语言 DEF_LANG
     */
    private String defLang;
    /**
     * @Fields suspendStatus 限制状态 0:未限制 1：已限制
     */
    private String suspendStatus;
    /**
     * @Fields departmentId 部门ID
     */
    private String departmentId;
    /**
     * @Fields email 电子信箱 EMAIL
     */
    private String email;
    /**
     * @Fields tel 联系电话 TEL
     */
    private String tel;
    /**
     * @Fields mobile 手    机 MOBILE
     */
    private String mobile;
    /**
     * @Fields fax 传   真  FAX
     */
    private String fax;
    /**
     * @Fields address 地   址  ADDRESS
     */
    private String address;
    /**
     * @Fields companyCode 公司编码 COMPANY_CODE
     */
    private String companyCode;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    private String managerCompany;

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDefLang() {
        return defLang;
    }

    public void setDefLang(String defLang) {
        this.defLang = defLang;
    }

    public String getSuspendStatus() {
        return suspendStatus;
    }

    public void setSuspendStatus(String suspendStatus) {
        this.suspendStatus = suspendStatus;
    }

    public String getManagerCompany() {
        return managerCompany;
    }

    public void setManagerCompany(String managerCompany) {
        this.managerCompany = managerCompany;
    }
    
}