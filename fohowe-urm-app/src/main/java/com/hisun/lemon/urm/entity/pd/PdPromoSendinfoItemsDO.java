/*
 * @ClassName PdPromoSendinfoItemsDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 11:30:52
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

public class PdPromoSendinfoItemsDO extends BaseDO {
    /**
     * @Fields id 01.ID ID
     */
    private Integer id;
    /**
     * @Fields receiptno 出库单号
     */
    private String receiptno;
    /**
     * @Fields goodscode 商品代码
     */
    private String goodscode;
    /**
     * @Fields goodscode 商品名称
     */
    private String goodsname;
    /**
     * @Fields orderqty 请货量
     */
    private Integer orderqty;
    /**
     * @Fields sendqty 发货量
     */
    private Integer sendqty;
    /**
     * @Fields recqty 收货量
     */
    private Integer recqty;
    /**
     * @Fields price 单价
     */
    private Integer price;
    /**
     * @Fields state 
     */
    private String state;
    /**
     * @Fields memo 备注
     */
    private String memo;
    
    private String companyCode;
    
    private String comboReceiptNo;
    
    
    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getGoodscode() {
        return goodscode;
    }

    public void setGoodscode(String goodscode) {
        this.goodscode = goodscode;
    }

    public Integer getOrderqty() {
        return orderqty;
    }

    public void setOrderqty(Integer orderqty) {
        this.orderqty = orderqty;
    }

    public Integer getSendqty() {
        return sendqty;
    }

    public void setSendqty(Integer sendqty) {
        this.sendqty = sendqty;
    }

    public Integer getRecqty() {
        return recqty;
    }

    public void setRecqty(Integer recqty) {
        this.recqty = recqty;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

	public String getComboReceiptNo() {
		return comboReceiptNo;
	}

	public void setComboReceiptNo(String comboReceiptNo) {
		this.comboReceiptNo = comboReceiptNo;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
    
}