package com.hisun.lemon.urm.excel.pd;



import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.pd.PromotionPageRspDTO;
import com.hisun.lemon.urm.dto.pd.PromotionRspDTO;
import com.hisun.lemon.urm.enums.pd.OrderStatus;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class PromotionExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="促销赠品导出"; 
		String[] colNames=new String[] {"期次","报单分公司","发货仓库","订单编号","合并单号","经销商编号","经销商姓名","代办处编号","商品编码","产品数量","活动名称","订单状态","审核时间"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		PromotionPageRspDTO vo = (PromotionPageRspDTO)obj;
		List<PromotionRspDTO> dataList=vo.getPromotionRspList();
		for(PromotionRspDTO o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getwWeek());
			row.createCell(index++).setCellValue(o.getRepoCompany());
			row.createCell(index++).setCellValue(o.getSendCompany());
			row.createCell(index++).setCellValue(o.getOrderNo());
			row.createCell(index++).setCellValue(o.getCombineNo());
			row.createCell(index++).setCellValue(o.getMemberNo());
			row.createCell(index++).setCellValue(o.getName());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getGoodsCode());
			row.createCell(index++).setCellValue(o.getQuantity());
			row.createCell(index++).setCellValue(o.getActiveName());
			String statusName =o.getOrderStatus()==null?"":OrderStatus.getByCode(o.getOrderStatus()).getName();
			row.createCell(index++).setCellValue(statusName);
			row.createCell(index++).setCellValue(o.getCheckTime()==null?"":o.getCheckTime().format(ymdhms)); 
		}
		
	}
	public static PromotionExcelExporter builder() {
		return new PromotionExcelExporter();
	}
}
