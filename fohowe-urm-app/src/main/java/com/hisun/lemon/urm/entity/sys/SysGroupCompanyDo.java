/*
 * @ClassName SysGroupCompanyDo
 * @Description 
 * @version 1.0
 * @Date 2019-02-12 11:18:35
 */
package com.hisun.lemon.urm.entity.sys;


import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;
import io.swagger.annotations.ApiModelProperty;


public class SysGroupCompanyDo extends BaseDO {
    /**
     * @Fields id 00.序号 ID
     */
    private String id;
    /**
     * @Fields groupId 区域编号
     */
    private String groupId;
    /**
     * @Fields companyCode 分公司
     */
    private String companyCode;
    /**
     * @Fields companyCodes 分公司数组
     */
    private String[] companyCodes;
    /**
     * @Fields recType 收获方式
     */
    private String recType;
    /**
     * @Fields needINvoice 发票
     */
    private String needINvoice;
    /**
     * @Fields payMethod 支付方式
     */
    private String payMethod;
    /**
     * @Fields currencyType 货币类型
     */
    private String currencyType;
    /**
     * @Fields orderType 订单类型
     */
    private String orderType;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    private String bonusType;

    private String isFbFlag;
    
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

	public String getRecType() {
		return recType;
	}

	public void setRecType(String recType) {
		this.recType = recType;
	}

	public String getNeedINvoice() {
		return needINvoice;
	}

	public void setNeedINvoice(String needINvoice) {
		this.needINvoice = needINvoice;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public String[] getCompanyCodes() {
		return companyCodes;
	}

	public void setCompanyCodes(String[] companyCodes) {
		this.companyCodes = companyCodes;
	}
	
	public String getCurrencyType() {
		return currencyType;
	}

	public void setCurrencyType(String currencyType) {
		this.currencyType = currencyType;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

    public String getIsFbFlag() {
        return isFbFlag;
    }

    public void setIsFbFlag(String isFbFlag) {
        this.isFbFlag = isFbFlag;
    }
}