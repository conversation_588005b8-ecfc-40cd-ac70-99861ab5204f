/*
 * @ClassName PdManifestDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-13 18:27:13
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PdManifestDO extends BaseDO {
    /**
     * @Fields id 00.ID ID
     */
    private Long id;
    /**
     * @Fields companyCode 订货分公司
     */
    private String companyCode;
    /**
     * @Fields vendorNo 供应商编号
     */
    private String vendorNo;
    private String vendorName;
    /**
     * @Fields orderNo 订货单编号
     */
    private String orderNo;
    /**
     * @Fields receiptNo 进货单编号
     */
    private String receiptNo;
    /**
     * @Fields receiptStatus 单据状态:0=进货,1=审核,2=删除
     */
    private String receiptStatus;
    /**
     * @Fields deliveryNo 供应商送货单号
     */
    private String deliveryNo;
    /**
     * @Fields recAmount 收货金额
     */
    private BigDecimal recAmount;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields inDate 实际收货日期,收货确认时填入
     */
    private LocalDateTime inDate;
    /**
     * @Fields checkDate 收货审核日期
     */
    private LocalDateTime checkDate;
    /**
     * @Fields lastUpDate 最后修改日期
     */
    private LocalDateTime lastUpDate;
    /**
     * @Fields recUser 收货制单人
     */
    private String recUser;
    /**
     * @Fields recChecker 收货审核人
     */
    private String recChecker;
    /**
     * @Fields totCost 总F$
     */
    private BigDecimal totCost;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getVendorNo() {
        return vendorNo;
    }

    public void setVendorNo(String vendorNo) {
        this.vendorNo = vendorNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getReceiptStatus() {
        return receiptStatus;
    }

    public void setReceiptStatus(String receiptStatus) {
        this.receiptStatus = receiptStatus;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public BigDecimal getRecAmount() {
        return recAmount;
    }

    public void setRecAmount(BigDecimal recAmount) {
        this.recAmount = recAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getInDate() {
        return inDate;
    }

    public void setInDate(LocalDateTime inDate) {
        this.inDate = inDate;
    }

    public LocalDateTime getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(LocalDateTime checkDate) {
        this.checkDate = checkDate;
    }

    public LocalDateTime getLastUpDate() {
        return lastUpDate;
    }

    public void setLastUpDate(LocalDateTime lastUpDate) {
        this.lastUpDate = lastUpDate;
    }

    public String getRecUser() {
        return recUser;
    }

    public void setRecUser(String recUser) {
        this.recUser = recUser;
    }

    public String getRecChecker() {
        return recChecker;
    }

    public void setRecChecker(String recChecker) {
        this.recChecker = recChecker;
    }

    public BigDecimal getTotCost() {
        return totCost;
    }

    public void setTotCost(BigDecimal totCost) {
        this.totCost = totCost;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }
}