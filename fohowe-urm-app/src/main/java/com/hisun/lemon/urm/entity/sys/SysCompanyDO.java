/*
 * @ClassName SysCompanyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:05:49
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysCompanyDO extends BaseDO {
    /**
     * @Fields id 00.序号 ID
     */
    private String id;
    /**
     * @Fields companyCode 编号
     */
    private String companyCode;
    /**
     * @Fields name 名称
     */
    private String name;
    /**
     * @Fields shortname 简称 
     */
    private String shortname;
    /**
     * @Fields managerName 负责人
     */
    private String managerName;
    /**
     * @Fields contacter 联系人姓名
     */
    private String contacter;
    /**
     * @Fields contacttel 联系人电话
     */
    private String contacttel;
    /**
     * @Fields tel 电话
     */
    private String tel;
    /**
     * @Fields fax 传真
     */
    private String fax;
    /**
     * @Fields email EMail地址
     */
    private String email;
    /**
     * @Fields address 地址
     */
    private String address;
    /**
     * @Fields creater 创建人
     */
    private String creater;
    /**
     * @Fields createDate 创建日期
     */
    private LocalDateTime createDate;
    /**
     * @Fields lastupDater 最后修改人
     */
    private String lastupDater;
    /**
     * @Fields lastupDate 最后更新日期
     */
    private LocalDateTime lastupDate;
    /**
     * @Fields typeFlag 类型,1=区域，2=分公司，3=物流中心
     */
    private String typeFlag;
    /**
     * @Fields langCode 默认语言编码
     */
    private String langCode;
    /**
     * @Fields isStop 停止使用，1=停止使用
     */
    private String isStop;
    /**
     * @Fields bonusType 奖金制度,1=欧洲奖金制度，2=非洲奖金制度, 3=东南亚奖金制度 ，4=中国奖金制度
     */
    private String bonusType;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    private String parentId;

    private String departmentId;

    private String departmentName;
    
    private String currencyCode;
    //财务结算默认币种
    private String currencyFin;
    
    private String regionsType;
    //是否延期发放奖金
    private Integer isDelay;

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortname() {
        return shortname;
    }

    public void setShortname(String shortname) {
        this.shortname = shortname;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getContacter() {
        return contacter;
    }

    public void setContacter(String contacter) {
        this.contacter = contacter;
    }

    public String getContacttel() {
        return contacttel;
    }

    public void setContacttel(String contacttel) {
        this.contacttel = contacttel;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public String getLastupDater() {
        return lastupDater;
    }

    public void setLastupDater(String lastupDater) {
        this.lastupDater = lastupDater;
    }

    public LocalDateTime getLastupDate() {
        return lastupDate;
    }

    public void setLastupDate(LocalDateTime lastupDate) {
        this.lastupDate = lastupDate;
    }

    public String getTypeFlag() {
        return typeFlag;
    }

    public void setTypeFlag(String typeFlag) {
        this.typeFlag = typeFlag;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getIsStop() {
        return isStop;
    }

    public void setIsStop(String isStop) {
        this.isStop = isStop;
    }

    public String getBonusType() {
        return bonusType;
    }

    public void setBonusType(String bonusType) {
        this.bonusType = bonusType;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public String getCurrencyFin() {
		return currencyFin;
	}

	public void setCurrencyFin(String currencyFin) {
		this.currencyFin = currencyFin;
	}

	public String getRegionsType() {
		return regionsType;
	}

	public void setRegionsType(String regionsType) {
		this.regionsType = regionsType;
	}

	public Integer getIsDelay() {
		return isDelay;
	}

	public void setIsDelay(Integer isDelay) {
		this.isDelay = isDelay;
	}
	
}