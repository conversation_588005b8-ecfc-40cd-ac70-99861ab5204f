/*
 * @ClassName IAlLanguageCodeDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-25 17:47:04
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlLanguageCodeDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlLanguageCodeDao extends BaseDao<AlLanguageCodeDO> {

    /** 
     * @Title: findAllRecord 
     * @Description: 查询所有记录
     * @return
     * @return: List<AlLanguageCodeDO>
     */
    List<AlLanguageCodeDO> findAllRecord();

    /** 
     * @Title: getByCode 
     * @Description: 根据语言编码获取数据
     * @param langCode
     * @return
     * @return: AlLanguageCodeDO
     */
    AlLanguageCodeDO getByCode(@Param("langCode") String langCode);

    /** 
     * @Title: get 
     * @Description: 查询
     * @param id
     * @return
     * @return: AlLanguageCodeDO
     */
    AlLanguageCodeDO get(@Param("id") Long id);

    /** 
     * @Title: delete 
     * @Description: 删除
     * @param id
     * @return
     * @return: int
     */
    int delete(@Param("id") Long id);
}