/*
 * @ClassName IPdManifestDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-25 10:44:08
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdManifestDO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface IPdManifestDao extends BaseDao<PdManifestDO> {

    List<PdManifestDO> pageManifestQuery(@Param("orderNo") String orderNo);

    Integer queryByQManiId(Integer id);

    /** 
     * @Title: getPageList 
     * @param companyCode
     * @param receiptNo
     * @param orderNo
     * @param deliveryNo
     * @param vendorNo
     * @param beginDate
     * @param endDate
     * @param receiptStatus
     * @return
     * @return: List<T>
     */
    List<PdManifestDO> getPageList(@Param("companyCode") String companyCode, @Param("receiptNo") String receiptNo,
            @Param("orderNo") String orderNo, @Param("deliveryNo") String deliveryNo,@Param("manifestId") long manifestId,
            @Param("vendorName") String vendorName, @Param("beginDate") LocalDateTime beginDate,
            @Param("endDate") LocalDateTime endDate, @Param("receiptStatus") String receiptStatus,
            @Param("delFlag") String delFlag);

    /** 
     * @Title: get 
     * @param manifestId
     * @return
     * @return: PdManifestDO
     */
    PdManifestDO get(@Param("id") long id);

    /** 
     * @Title: getByIdList 
     * @param idList
     * @param receiptStatus
     * @return
     * @return: PdManifestDO
     */
    List<PdManifestDO> getByIdList(@Param("idList") List<Long> idList, @Param("receiptStatus") String receiptStatus);

    /** 
     * @Title: updateToDelete 
     * @param idList
     * @param receiptStatus
     * @param lastUpDate
     * @return
     * @return: int
     */
    int updateToDelete(@Param("idList") List<Long> idList, @Param("receiptStatus") String receiptStatus,
            @Param("lastUpDate") LocalDateTime lastUpDate);

}