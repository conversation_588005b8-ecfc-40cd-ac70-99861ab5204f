package com.hisun.lemon.urm.bo;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.entity.fi.FiFgcDepositDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;


@ClientValidated
@ApiModel(value = "FgcDepositRspBO", description = "定存查询结果集对象")
public class FgcDepositRspBO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "分页结果集")
    PageInfo<FiFgcDepositDO> depositInfo;


    @ApiModelProperty(value = "总定存数")
    private BigDecimal countNumber;


    @ApiModelProperty(value = "总fgc数")
    private BigDecimal countFgcNum;
    
    @ApiModelProperty(value = "总金额")
    private BigDecimal payAmountTotal;

    public PageInfo<FiFgcDepositDO> getDepositInfo() {
        return depositInfo;
    }

    public void setDepositInfo(PageInfo<FiFgcDepositDO> depositInfo) {
        this.depositInfo = depositInfo;
    }

    public BigDecimal getCountNumber() {
        return countNumber;
    }

    public void setCountNumber(BigDecimal countNumber) {
        this.countNumber = countNumber;
    }

    public BigDecimal getCountFgcNum() {
        return countFgcNum;
    }

    public void setCountFgcNum(BigDecimal countFgcNum) {
        this.countFgcNum = countFgcNum;
    }

	public BigDecimal getPayAmountTotal() {
		return payAmountTotal;
	}

	public void setPayAmountTotal(BigDecimal payAmountTotal) {
		this.payAmountTotal = payAmountTotal;
	}
 
}
