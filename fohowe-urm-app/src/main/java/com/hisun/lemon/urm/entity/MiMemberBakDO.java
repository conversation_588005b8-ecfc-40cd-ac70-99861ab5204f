/*
 * @ClassName MiMemberBakDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class MiMemberBakDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields memberNo 
     */
    private String memberNo;
    /**
     * @Fields recommendNo 
     */
    private String recommendNo;
    /**
     * @Fields agentNo 
     */
    private String agentNo;
    /**
     * @Fields storeId 
     */
    private String storeId;
    /**
     * @Fields companyCode 
     */
    private String companyCode;
    /**
     * @Fields cardType 
     */
    private String cardType;
    /**
     * @Fields name 
     */
    private String name;
    /**
     * @Fields registerDate 
     */
    private LocalDateTime registerDate;
    /**
     * @Fields regionCode 
     */
    private String regionCode;
    /**
     * @Fields storeAddr 
     */
    private String storeAddr;
    /**
     * @Fields storePost 
     */
    private String storePost;
    /**
     * @Fields homeTel 
     */
    private String homeTel;
    /**
     * @Fields officeTel 
     */
    private String officeTel;
    /**
     * @Fields mobile 
     */
    private String mobile;
    /**
     * @Fields fax 
     */
    private String fax;
    /**
     * @Fields accountBank 
     */
    private String accountBank;
    /**
     * @Fields accountCode 
     */
    private String accountCode;
    /**
     * @Fields accountName 
     */
    private String accountName;
    /**
     * @Fields email 
     */
    private String email;
    /**
     * @Fields webAddr 
     */
    private String webAddr;
    /**
     * @Fields remark 
     */
    private String remark;
    /**
     * @Fields petName 
     */
    private String petName;
    /**
     * @Fields sex 
     */
    private String sex;
    /**
     * @Fields birthday 
     */
    private LocalDateTime birthday;
    /**
     * @Fields paperType 
     */
    private String paperType;
    /**
     * @Fields paperNo 
     */
    private String paperNo;
    /**
     * @Fields healthy 
     */
    private String healthy;
    /**
     * @Fields knowWay 
     */
    private String knowWay;
    /**
     * @Fields memType 
     */
    private String memType;
    /**
     * @Fields disType 
     */
    private String disType;
    /**
     * @Fields activeTime 
     */
    private LocalDateTime activeTime;
    /**
     * @Fields exitStatus 
     */
    private String exitStatus;
    /**
     * @Fields exitTime 
     */
    private LocalDateTime exitTime;
    /**
     * @Fields exitUser 
     */
    private String exitUser;
    /**
     * @Fields activeWeek 
     */
    private String activeWeek;
    /**
     * @Fields periodWeek 
     */
    private String periodWeek;
    /**
     * @Fields isMall 
     */
    private String isMall;
    /**
     * @Fields isFree 
     */
    private String isFree;
    /**
     * @Fields isAgcon 
     */
    private String isAgcon;
    /**
     * @Fields isProm 
     */
    private String isProm;
    /**
     * @Fields promdate 
     */
    private LocalDateTime promdate;
    /**
     * @Fields isUnlock 
     */
    private String isUnlock;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getRecommendNo() {
        return recommendNo;
    }

    public void setRecommendNo(String recommendNo) {
        this.recommendNo = recommendNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(LocalDateTime registerDate) {
        this.registerDate = registerDate;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getStoreAddr() {
        return storeAddr;
    }

    public void setStoreAddr(String storeAddr) {
        this.storeAddr = storeAddr;
    }

    public String getStorePost() {
        return storePost;
    }

    public void setStorePost(String storePost) {
        this.storePost = storePost;
    }

    public String getHomeTel() {
        return homeTel;
    }

    public void setHomeTel(String homeTel) {
        this.homeTel = homeTel;
    }

    public String getOfficeTel() {
        return officeTel;
    }

    public void setOfficeTel(String officeTel) {
        this.officeTel = officeTel;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getAccountBank() {
        return accountBank;
    }

    public void setAccountBank(String accountBank) {
        this.accountBank = accountBank;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebAddr() {
        return webAddr;
    }

    public void setWebAddr(String webAddr) {
        this.webAddr = webAddr;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPetName() {
        return petName;
    }

    public void setPetName(String petName) {
        this.petName = petName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public LocalDateTime getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDateTime birthday) {
        this.birthday = birthday;
    }

    public String getPaperType() {
        return paperType;
    }

    public void setPaperType(String paperType) {
        this.paperType = paperType;
    }

    public String getPaperNo() {
        return paperNo;
    }

    public void setPaperNo(String paperNo) {
        this.paperNo = paperNo;
    }

    public String getHealthy() {
        return healthy;
    }

    public void setHealthy(String healthy) {
        this.healthy = healthy;
    }

    public String getKnowWay() {
        return knowWay;
    }

    public void setKnowWay(String knowWay) {
        this.knowWay = knowWay;
    }

    public String getMemType() {
        return memType;
    }

    public void setMemType(String memType) {
        this.memType = memType;
    }

    public String getDisType() {
        return disType;
    }

    public void setDisType(String disType) {
        this.disType = disType;
    }

    public LocalDateTime getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(LocalDateTime activeTime) {
        this.activeTime = activeTime;
    }

    public String getExitStatus() {
        return exitStatus;
    }

    public void setExitStatus(String exitStatus) {
        this.exitStatus = exitStatus;
    }

    public LocalDateTime getExitTime() {
        return exitTime;
    }

    public void setExitTime(LocalDateTime exitTime) {
        this.exitTime = exitTime;
    }

    public String getExitUser() {
        return exitUser;
    }

    public void setExitUser(String exitUser) {
        this.exitUser = exitUser;
    }

    public String getActiveWeek() {
        return activeWeek;
    }

    public void setActiveWeek(String activeWeek) {
        this.activeWeek = activeWeek;
    }

    public String getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }

    public String getIsMall() {
        return isMall;
    }

    public void setIsMall(String isMall) {
        this.isMall = isMall;
    }

    public String getIsFree() {
        return isFree;
    }

    public void setIsFree(String isFree) {
        this.isFree = isFree;
    }

    public String getIsAgcon() {
        return isAgcon;
    }

    public void setIsAgcon(String isAgcon) {
        this.isAgcon = isAgcon;
    }

    public String getIsProm() {
        return isProm;
    }

    public void setIsProm(String isProm) {
        this.isProm = isProm;
    }

    public LocalDateTime getPromdate() {
        return promdate;
    }

    public void setPromdate(LocalDateTime promdate) {
        this.promdate = promdate;
    }

    public String getIsUnlock() {
        return isUnlock;
    }

    public void setIsUnlock(String isUnlock) {
        this.isUnlock = isUnlock;
    }
}