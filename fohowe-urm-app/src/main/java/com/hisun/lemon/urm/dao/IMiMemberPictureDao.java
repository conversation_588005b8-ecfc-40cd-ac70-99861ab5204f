/*
 * @ClassName IMiMemberPictureDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-17 20:53:27
 */
package com.hisun.lemon.urm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.MemberPictureBean;
import com.hisun.lemon.urm.dto.mi.member.MemberPictureDTO;
import com.hisun.lemon.urm.entity.MiMemberPictureDO;

@Mapper
public interface IMiMemberPictureDao extends BaseDao<MiMemberPictureDO> {
	
	
	MiMemberPictureDO getItem(@Param("memberNo") String memberNo);
	int deleteByNo(@Param("memberNo") String memberNo);

	int getTotalCount(MemberPictureDTO dto);
	List<MemberPictureBean> getListByPage(MemberPictureDTO dto);
}