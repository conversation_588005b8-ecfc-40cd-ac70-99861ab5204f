package com.hisun.lemon.urm.dao.bd;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.bd.BdTourismPromQueryDTO;
import com.hisun.lemon.urm.dto.bd.BdTourismPromotionBean;
import com.hisun.lemon.urm.entity.bd.BdTourismPromotionDO;


@Mapper
public interface IBdTourismPromotionDao extends BaseDao<BdTourismPromotionDO> {
    int deleteByPrimaryKey(@Param("id") Long id);

    int insertSelective(BdTourismPromotionDO row);

    BdTourismPromotionDO selectByPrimaryKey(Long id);
    List<BdTourismPromotionDO> selectBysalesPromotion(BdTourismPromotionBean reqDTO);

    int updateByPrimaryKeySelective(BdTourismPromotionDO row);

    int updateByPrimaryKey(BdTourismPromotionDO row);
    List<BdTourismPromotionDO> selectInformation(BdTourismPromotionBean reqDTO);

    int updateByMoney(BdTourismPromotionDO reqDTO);

    int updateByRetrue(BdTourismPromotionDO reqDTO);
    List<BdTourismPromotionDO> selectByStatus(BdTourismPromotionBean reqDTO);

	List<BdTourismPromotionDO> queryInformation(BdTourismPromQueryDTO reqDTO);
}