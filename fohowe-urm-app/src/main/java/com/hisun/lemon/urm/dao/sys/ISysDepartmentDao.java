/*
 * @ClassName ISysDepartmentDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-09 20:39:39
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysDepartmentDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysDepartmentDao extends BaseDao<SysDepartmentDO> {

    /** 
     * @Title: findList 
     * @Description: 查询部门列表
     * @param companyCode
     * @param userCode
     * @return
     * @return: List<SysDepartmentDO>
     */
    public List<SysDepartmentDO> findList(@Param("companyCode") String companyCode, @Param("userCode") String userCode);

    /**
     * 
     * @param companyCode 
     * @param departmentId 
     * @param object 
     * @param companyCode 
     * @Title: findOrganizerList 
     * @Description: 查询部门组织列表
     * @param companyCode
     * @param userCode
     * @param departmentId
     * @return
     * @return: List<SysDepartmentDO>
     */
    public List<SysDepartmentDO> findOrganizerList(@Param("companyCode") String companyCode);

    /**
     * 
     * @Title: findPageList 
     * @Description: 分页查询部门信息
     * @param companyCode
     * @param departmentId
     * @return
     * @return: List<SysDepartmentDO>
     */
    public List<SysDepartmentDO> findPageList(@Param("companyCode") String companyCode,
            @Param("departmentId") String departmentId);

    /** 
     * @Title: getByCompany 
     * @param companyCode
     * @return
     * @return: SysDepartmentDO
     */
    public SysDepartmentDO getByCompany(@Param("companyCode") String companyCode);
}