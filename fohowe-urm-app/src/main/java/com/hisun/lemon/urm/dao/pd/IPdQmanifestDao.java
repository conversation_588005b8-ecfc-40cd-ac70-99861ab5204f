/*
 * @ClassName IPdQmanifestDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-08 10:23:01
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.pd.PdManifestGoodQueryRspDTO;
import com.hisun.lemon.urm.entity.pd.PdQmanifestDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface IPdQmanifestDao extends BaseDao<PdQmanifestDO> {

    List<PdQmanifestDO> pageQuery(@Param("areaCode") String areaCode, @Param("item") PdQmanifestDO qmanifestDO
               , @Param("startDate")LocalDateTime startDate, @Param("endDate")LocalDateTime endDate
            , @Param("companyCode") String companyCode, @Param("reqList") List<String> reqStores);

    PdQmanifestDO queryByReceipNo(String receipNo);

    int updateSend(PdQmanifestDO itemsDO);

    int deleteById(Integer id);

    int checkSend(@Param("id") Integer id, @Param("userId")String userId,@Param("now") LocalDateTime now);

    int checkCenter(@Param("id") Integer id, @Param("userId")String userId,@Param("now") LocalDateTime now);

    int finanCheck(@Param("id") Integer id, @Param("userId")String userId,@Param("now") LocalDateTime now);

    int confirmDelivery(@Param("idList") List<Integer> idList, @Param("userId")String userId,@Param("now") LocalDateTime now);
    
    int canceDelivery(@Param("idList") List<Integer> idList, @Param("userId")String userId,@Param("now") LocalDateTime now);

    PdQmanifestDO queryById(Integer id);

    int cancelSend(@Param("id") Integer id, @Param("userId")String userId,@Param("now") LocalDateTime now);

    List<PdQmanifestDO> pageSendQuery(@Param("item") PdQmanifestDO qmanifestDO, @Param("startDate")LocalDateTime startDate
            , @Param("endDate")LocalDateTime endDate, @Param("companyCode") String companyCode, @Param("reqList")List<String> reqStore);

    List<PdQmanifestDO> pageRecQuery(@Param("item") PdQmanifestDO qmanifestDO, @Param("startDate")LocalDateTime startDate
            , @Param("endDate")LocalDateTime endDate, @Param("companyCode") String companyCode, @Param("reqList")List<String> reqStore);


    int recUpdate(@Param("id") Integer id, @Param("userId")String userId,@Param("now") LocalDateTime now);

    List<PdQmanifestDO> shipPageQuery(@Param("areaCode")String areaCode, @Param("item")PdQmanifestDO qmanifestDO
            , @Param("companyCode")String companyCode, @Param("timeType") String timeType
            , @Param("startDate")LocalDateTime startDate, @Param("endDate")LocalDateTime endDate
            , @Param("reqList")List<String> reqList);

    String queryCurrency(Integer id);

    List<PdQmanifestDO> shipFlowPageQuery(@Param("item") PdQmanifestDO qmanifestDO, @Param("companyCode") String companyCode
            , @Param("queryType") String code, @Param("startDate")LocalDateTime startDate
            , @Param("endDate")LocalDateTime endDate, @Param("reqList")List<String> reqList);

    List<PdQmanifestDO> queryByIds(List<Integer> idList);

	List<PdManifestGoodQueryRspDTO> pageGoodSendQuery(@Param("item") PdQmanifestDO qmanifestDO,@Param("startDate") LocalDateTime startDate,@Param("endDate") LocalDateTime endDate,
			@Param("companyCode")String companyCode,@Param("reqList") List<String> reqStores);

	List<PdManifestGoodQueryRspDTO> pageGoodRecQuery(@Param("item")PdQmanifestDO qmanifestDO,@Param("startDate") LocalDateTime startDate,@Param("endDate") LocalDateTime endDate,
			@Param("companyCode")String companyCode,@Param("reqList") List<String> reqStores,@Param("queryType") String queryType);

	List<PdManifestGoodQueryRspDTO> pageGoodRecQuery(@Param("areaCode")String areaCode, @Param("item")PdQmanifestDO qmanifestDO,@Param("startDate") LocalDateTime startDate,@Param("endDate") LocalDateTime endDate,
			@Param("companyCode")String companyCode,@Param("reqList") List<String> reqStores,@Param("queryType") String queryType);

}