/*
 * @ClassName IAlCurrencyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-06 10:45:13
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlCurrencyDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlCurrencyDao extends BaseDao<AlCurrencyDO> {
    public List<AlCurrencyDO> findList(@Param("currencyCode") String currencyCode,
            @Param("currencyName") String currencyName, @Param("companyCode") String companyCode,
            @Param("companyCodes") String[] companyCodes);
    
    public List<AlCurrencyDO> findAllList(@Param("currencyCode") String currencyCode, @Param("currencyName") String currencyName);
    
    public List<AlCurrencyDO> findByUsdt(@Param("currencyUsdt") String currencyUsdt,
    		                             @Param("agentNo") String agentNo);
}