/*
 * @ClassName IIcFileDownloadDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-20 09:55:33
 */
package com.hisun.lemon.urm.dao.ic;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.ic.IcFileDownloadDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IIcFileDownloadDao extends BaseDao<IcFileDownloadDO> {

    /** 
     * @Title: get 
     * @Description: TODO
     * @param dfileId
     * @return
     * @return: IcFileDownloadDO
     */
    IcFileDownloadDO get(@Param("dfileId") long dfileId);
    
    void deleteById(@Param("dfileId") long dfileId);

    /** 
     * @Title: getByList 
     * @Description: TODO
     * @param dfileList
     * @return
     * @return: IcFileDownloadDO
     */
    List<IcFileDownloadDO> getByImgUrl(@Param("imgUrl") String imgUrl);
    
    List<IcFileDownloadDO> getByList(@Param("dfileList") List<Long> dfileList);
}