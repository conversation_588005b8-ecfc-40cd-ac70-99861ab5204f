package com.hisun.lemon.urm.uitls.excel.fi;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.bns.enums.MsgInfo;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.entity.ticket.TicketsVoucherDO;
import com.hisun.lemon.urm.uitls.EncryptionUtils;

public class TicketsVoucherExcelImport extends BaseService{
	private Logger logger = LoggerFactory.getLogger(TicketsVoucherExcelImport.class);
	
	EncryptionUtils encryptionUtil;
	
	public final static Integer  MAX_ABLE_CNT=100000;
	
	public List<TicketsVoucherDO> importBatchPreAdd(InputStream in) {
		List<TicketsVoucherDO> list = new ArrayList<TicketsVoucherDO>();
		Workbook book = null;
		try {
			book = WorkbookFactory.create(in);
		} catch (Exception e) {
			LemonException.throwBusinessException(MsgInfo.IS_NOT_FILE.getMsgCd());
		}  
    	if (book == null) {
			LemonException.throwBusinessException(MsgInfo.IS_NOT_FILE.getMsgCd());
		}
    	Sheet xssfSheet = book.getSheetAt(0);
    	int rowNum = xssfSheet.getLastRowNum();
    	//忽略表头
    	for (int i = 0; i <= rowNum; i++) {
    		if(i==0){
				logger.debug("跳过表头");
				continue;
			}
			if(rowNum>MAX_ABLE_CNT){
				logger.debug("导入数量大于{}条，忽略后续数据",MAX_ABLE_CNT);
				LemonException.throwBusinessException(MsgInfo.OVER_THEMAXIMUMNUMBER.getMsgCd());
				break;
			}
			TicketsVoucherDO importDO = new TicketsVoucherDO(); 
    		Row row = xssfSheet.getRow(i);
    		try {
    			//期次	分公司	代办处	经销商编号	经销商姓名	轮次	经营权编号	当前目标	
    			//当前目标开始期次	当前目标限期	剩余期数	当前目标推荐人数	实发奋斗奖金 
    			int index=0;
    			String ticketsNo = this.getCellValue(row.getCell(index++));//代金券编号
    			String serialStr = this.getCellValue(row.getCell(index++));//序号
    			String ticketsPwd = this.getCellValue(row.getCell(index++));//代金券秘钥
        		String amountStr = this.getCellValue(row.getCell(index++));//面值
//        		String teacher = this.getCellValue(row.getCell(index++));//老师
//        		String meetingNo = this.getCellValue(row.getCell(index++));//会议编号
        		String memo = this.getNameCellValue(row.getCell(index++));//备注
        		BigDecimal amount =BigDecimal.ZERO;
        		if(JudgeUtils.isNotBlank(amountStr)) {
        			amount = new BigDecimal(amountStr);
        		}
        		Integer serial=(i+1);
        		if(JudgeUtils.isNotBlank(serialStr)) {
        			serial=Integer.parseInt(serialStr);
        		}
        		importDO.setTicketsNo(ticketsNo);
        		importDO.setSerial(serial);
        		String password=null;
        		if(JudgeUtils.isNotBlank(ticketsPwd)) {
        			ticketsPwd=ticketsPwd.toUpperCase();
        			password = encryptionUtil.encrypt(ticketsPwd);
				}
        		importDO.setTicketsPwd(password);
        		importDO.setAmount(amount);
//        		importDO.setTeacher(teacher);
//        		importDO.setMeetingNo(meetingNo);
        		importDO.setMemo(memo);
        		importDO.setTicketsStatus(0);
        		importDO.setVcStatus(0);
        		importDO.setCreaterCode(LemonUtils.getUserId());
        		importDO.setCreateTime(LocalDateTime.now());
			} catch (Exception e) {
				logger.info("多少行"+i+"报错"+"编号:"+this.getCellValue(row.getCell(0)));
				e.printStackTrace();
				LemonException.throwBusinessException(MsgInfo.IMPORT_FILE_FAILED.getMsgCd());
			}
    		if(JudgeUtils.isNotBlank(importDO.getTicketsNo())) {
    			list.add(importDO);
    		}
		}
		return list;
	}
	
	 public String getCellValue(Cell cell){
			if(cell==null){
				return null;
			}
			String str = null;
			if (cell.getCellTypeEnum() == CellType.BOOLEAN){
				return null;
			}else if (cell.getCellTypeEnum() == CellType.NUMERIC){
				short format = cell.getCellStyle().getDataFormat();
	            SimpleDateFormat sdf = null;
	            if (format == 14 || format == 31 || format == 57 || format == 58  
	                    || (176<=format && format<=178) || (182<=format && format<=196) 
	                    || (210<=format && format<=213) || (208==format ) ) { // 日期
	                sdf = new SimpleDateFormat("yyyy-MM-dd");
	                double value = cell.getNumericCellValue();
	                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
	                if(date!=null){
	                	str = sdf.format(date);
	                }else {
	                	str ="0";
	                }
	            } else if (format == 20 || format == 32 || format==183 || (200<=format && format<=209) ) { // 时间
	                sdf = new SimpleDateFormat("HH:mm");
	                double value = cell.getNumericCellValue();
	                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
	                if(date!=null){
	                	str = sdf.format(date);
	                }else {
	                	str ="0";
	                }
	            } else { // 不是日期格式
	            	DecimalFormat df = new DecimalFormat("0.##"); 
	    			str=df.format(cell.getNumericCellValue());
	            }
			}else if (cell.getCellTypeEnum() == CellType.STRING){
				str=cell.getStringCellValue();	
			}else{
				return null;
			}
			return str.replaceAll("[\\s\\?]", "").replace("　", "");
		}
	
	/**
     * 保留空格
     * @param cell
     * @return
     */
    public String getNameCellValue(Cell cell){
		if(cell==null){
			return null;
		}
		String str = null;

		if (cell.getCellTypeEnum() == CellType.BOOLEAN){
			return null;
		}else if (cell.getCellTypeEnum() == CellType.NUMERIC){
			short format = cell.getCellStyle().getDataFormat();
            SimpleDateFormat sdf = null;
            if (format == 14 || format == 31 || format == 57 || format == 58  
                    || (176<=format && format<=178) || (182<=format && format<=196) 
                    || (210<=format && format<=213) || (208==format ) ) { // 日期
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                	str = sdf.format(date);
                }else {
                	str ="0";
                }
            } else if (format == 20 || format == 32 || format==183 || (200<=format && format<=209) ) { // 时间
                sdf = new SimpleDateFormat("HH:mm");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                	str = sdf.format(date);
                }else {
                	str ="0";
                }
            } else { // 不是日期格式
            	DecimalFormat df = new DecimalFormat("0.##"); 
    			str=df.format(cell.getNumericCellValue());
            }
		}else if (cell.getCellTypeEnum() == CellType.STRING){
			str=cell.getStringCellValue();	
		}else{
			return null;
		}
		return str.replaceAll("[\\s\\?]", "");
	}

	public EncryptionUtils getEncryptionUtil() {
		return encryptionUtil;
	}

	public void setEncryptionUtil(EncryptionUtils encryptionUtil) {
		this.encryptionUtil = encryptionUtil;
	}
}
