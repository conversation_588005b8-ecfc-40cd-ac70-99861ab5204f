/*
 * @ClassName IMiLtvAuthorizeDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-24 11:25:44
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.LtvAuthorizeDTO;
import com.hisun.lemon.urm.entity.MiLtvAuthorizeDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IMiLtvAuthorizeDao extends BaseDao<MiLtvAuthorizeDO> {

	MiLtvAuthorizeDO search(LtvAuthorizeDTO ltvAuthorizeDTO);
}