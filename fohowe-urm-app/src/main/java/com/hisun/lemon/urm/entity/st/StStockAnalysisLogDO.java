package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;

public class StStockAnalysisLogDO extends BaseDO {
	private Long id;
	private int wWeek;
	private int calcType;
    private String regionCode;
    private String goodsCode;
    private String userCode;
    private int calcStatus;
    private String msg;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public int getwWeek() {
		return wWeek;
	}
	public void setwWeek(int wWeek) {
		this.wWeek = wWeek;
	}
	public int getCalcType() {
		return calcType;
	}
	public void setCalcType(int calcType) {
		this.calcType = calcType;
	}
	public String getRegionCode() {
		return regionCode;
	}
	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}
	public String getGoodsCode() {
		return goodsCode;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public int getCalcStatus() {
		return calcStatus;
	}
	public void setCalcStatus(int calcStatus) {
		this.calcStatus = calcStatus;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
 
	 
    
}	