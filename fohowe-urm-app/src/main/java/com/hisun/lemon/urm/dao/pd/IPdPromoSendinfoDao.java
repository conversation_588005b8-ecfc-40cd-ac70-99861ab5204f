/*
 * @ClassName IPdPromoSendinfoDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 11:30:52
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.GoodsCompanyDO;
import com.hisun.lemon.urm.entity.pd.PdPromoSendinfoDO;
import com.hisun.lemon.urm.entity.pd.PdPromoSendinfoRspDO;
import com.hisun.lemon.urm.entity.pd.SendInforExcelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface IPdPromoSendinfoDao extends BaseDao<PdPromoSendinfoDO> {

    int insertBatch(List<PdPromoSendinfoDO> sendinfoList);

    List<PdPromoSendinfoDO> getByRequestNos(List<String> orderList);
    
    PdPromoSendinfoDO getByRequestNo(@Param("requestNo")String requestNo);

    int mergeUpdate(List<PdPromoSendinfoDO> sendinfoDOS);

    int deleteByRecips(List<String> reciepList);

    List<PdPromoSendinfoRspDO> sendPageQuery(@Param("bonusType") String bonusType,@Param("biOrderNo") String biOrderNo
            , @Param("wWeek") String wWeek, @Param("companyCodes") List<String> companyCodes, @Param("sendinfo") PdPromoSendinfoDO sendinfoDO);

    List<SendInforExcelDO> promoSendInfoExcelList(@Param("bonusType") String bonusType, @Param("biOrderNo") String biOrderNo
            , @Param("wWeek") String wWeek, @Param("companyCodes") List<String> companyCodes, @Param("sendinfo") PdPromoSendinfoDO sendinfoDO);

    int doSendAct(PdPromoSendinfoDO sendinfoDO);

    PdPromoSendinfoDO get(Integer id);

    List<PdPromoSendinfoDO> comboPageQuery(@Param("sendInfo") PdPromoSendinfoDO sendInfoDO, @Param("startTime") LocalDateTime startTime, @Param("endTime")LocalDateTime endTime);
    List<SendInforExcelDO> SendComboInfoExcel(@Param("comboReceiptno") String comboReceiptno,@Param("RECEIPTNO") String RECEIPTNO);
    int doSplit(@Param("receiptno") String receiptno, @Param("comboReceiptno") String comboReceiptno);

    int delete(Integer id);

    List<PdPromoSendinfoDO> getComboItems(Integer id);

    GoodsCompanyDO queryPrice(String goodsCode, String companyCode);

	PdPromoSendinfoDO getComboByNo(@Param("comboReceiptNo") String comboReceiptno);
}