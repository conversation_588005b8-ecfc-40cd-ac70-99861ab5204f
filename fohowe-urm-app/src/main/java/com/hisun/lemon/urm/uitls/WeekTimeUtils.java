/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: DateTimeUtils.java 
 * @Prject: fohowe-urm-app
 * @Package: com.hisun.lemon.urm.uitls 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月14日 下午5:15:13 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.uitls;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.hisun.lemon.common.utils.DateTimeUtils;

/** 
 * @ClassName: DateTimeUtils 
 * @Description: TODO
 * @author: tian
 * @date: 2017年11月14日 下午5:15:13  
 */
@Component
public class WeekTimeUtils {
    /**
     * 
     * @Title: getWeek 
     * @Description: 获取当天星期数
     * @return
     * @return: String
     */
    public static Integer getWeek() {
        Map<String, Integer> weekMap = new HashMap<>();
        weekMap.put("星期一", 1);
        weekMap.put("星期二", 2);
        weekMap.put("星期三", 3);
        weekMap.put("星期四", 4);
        weekMap.put("星期五", 5);
        weekMap.put("星期六", 6);
        weekMap.put("星期日", 7);
        weekMap.put("Monday", 1);
        weekMap.put("Tuesday", 2);
        weekMap.put("Wednesday", 3);
        weekMap.put("Thursday", 4);
        weekMap.put("Friday", 5);
        weekMap.put("Saturday", 6);
        weekMap.put("Sunday", 7);
        String nowWeek = DateTimeUtils.getCurrentDateStr("EEEE");
        return weekMap.get(nowWeek);
    }

    /**
     * 
     * @Title: isBetween 
     * @Description: 判断当前日期是否在两个日期范围内
     * @param beginDate
     * @param endDate
     * @param nowHour 
     * @param endTime 
     * @param beginTime 
     * @return
     * @return: boolean
     */
    public static boolean isBetween(int beginDate, int endDate, int nowDate, Integer beginTime, Integer endTime,
            int nowHour) {
        if (beginDate < endDate) {
            if (beginDate < nowDate && endDate > nowDate) {
                return true;
            } else if (beginDate == nowDate) {
                if (beginTime >= nowHour) {
                    return true;
                }
            } else if (beginDate < nowDate) {
                if (endTime < nowHour) {
                    return true;
                }
            } else {
                return false;
            }
        } else if (beginDate > endDate) {
            if (endDate > nowDate || nowDate > beginDate) {
                return true;
            } else if (beginDate == nowDate) {
                if (beginTime <= nowHour) {
                    return true;
                }
            } else if (endDate == nowDate) {
                if (endTime > nowHour) {
                    return true;
                }
            } else {
                return false;
            }
        } else {
            if (beginDate == nowDate) {
                if (beginTime < endTime) {
                    if (beginTime <= nowHour && endTime > nowHour) {
                        return true;
                    }
                } else if (beginTime > endTime) {
                    if (nowHour >= beginTime || nowHour < endTime ) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}
