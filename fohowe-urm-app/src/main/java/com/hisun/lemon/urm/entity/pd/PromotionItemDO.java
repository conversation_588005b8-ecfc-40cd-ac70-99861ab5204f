package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;

public class PromotionItemDO extends BaseDO{
    /**id标识*/
    private Integer id;
    /**商品编码*/
    private String goodsCode;
    /**商品名称*/
    private String goodsName;
    /**数量*/
    private Integer quantity;
    /**单价*/
    private BigDecimal price;
    /**订单编号*/
    private String orderNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
