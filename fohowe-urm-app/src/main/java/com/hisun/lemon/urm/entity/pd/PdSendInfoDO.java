/*
 * @ClassName PdSendInfoDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 16:19:40
 */
package com.hisun.lemon.urm.entity.pd;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.hisun.lemon.framework.data.BaseDO;
import com.hisun.lemon.urm.dto.pd.SendInfoItemsBeanDTO;

import io.swagger.annotations.ApiModelProperty;

public class PdSendInfoDO extends BaseDO {
    /**
     * @Fields id 00.ID ID
     */
    private Long id;
    /**
     * @Fields companyCode 分公司/仓库编号
     */
    private String companyCode;
    /**
     * @Fields receiptNo 出库单号 pdso
     */
    private String receiptNo;
    /**
     * @Fields orderor 创建人
     */
    private String orderor;
    /**
     * @Fields sender 发货人
     */
    private String sender;
    /**
     * @Fields receiver 收货人
     */
    private String receiver;
    /**
     * @Fields orderDate 创建日期
     */
    private LocalDateTime orderDate;
    /**
     * @Fields sendDate 发货日期
     */
    private LocalDateTime sendDate;
    /**
     * @Fields recDate 收货日期
     */
    private LocalDateTime recDate;
    /**
     * @Fields orderStatus 创建状态 1=审核 2财务确认 3删除
     */
    private String orderStatus;
    /**
     * @Fields sendStatus 发货状态1=审核
     */
    private String sendStatus;
    /**
     * @Fields recStatus 收货状态1=审核
     */
    private String recStatus;
    /**
     * @Fields recName 收货人姓名
     */
    private String recName;
    /**
     * @Fields recAddr 收货地址
     */
    private String recAddr;
    /**
     * @Fields recPhone 收货联系电话
     */
    private String recPhone;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields requestNo 提货申请单号
     */
    private String requestNo;
    /**
     * @Fields dcsendNo 物流单号
     */
    private String dcsendNo;
    /**
     * @Fields regionCode 地区编号
     */
    private String regionCode;
    /**
     * @Fields recCode 收货人编号
     */
    private String recCode;
    /**
     * @Fields cancelCode 删除人
     */
    private String cancelCode;
    /**
     * @Fields cancelDate 删除时间
     */
    private LocalDateTime cancelDate;
    /**
     * @Fields isDc 分公司制单
     */
    private String isDc;
    /**
     * @Fields faChecker 财务审核人
     */
    private String faChecker;
    /**
     * @Fields faCheckDate 财务审核日期
     */
    private LocalDateTime faCheckDate;
    
    /**
     * @Fields MEMO_ORDERER  备注修改人 
     */
    private String memoOrderer;
    /**
     * @Fields MEMO_DATETIME  备注修改时间
     */
    private LocalDateTime memoDatetime;
    /**
     * @Fields memo_old  旧备注
     */
    private String memoOld;
    /**
     * @Fields memo 
     */
    private String memo;
    /**
     * @Fields totalCost 
     */
    private BigDecimal totalCost;
    /**
     * @Fields comboReceiptNo 合并单号
     */
    private String comboReceiptNo;
    /**
     * @Fields splitReceiptNo 拆分原单号
     */
    private String splitReceiptNo;
    /**
     * @Fields isSplit 是否拆分单：1拆分单
     */
    private String isSplit;
    /**
     * @Fields isMergeHead 是否合并单：1拆分单
     */
    private String isMergeHead;
    /**
     * @Fields isAgentStock 是否增加代办处库存  1=增加
     */
    private String isAgentStock;
    /**
     * @Description: 支付的F$
     */
    private BigDecimal payAmount;
    
    //运费
    private BigDecimal sendFee ;
    @ApiModelProperty(value = "收货人Email")
    private String recEmail;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * 是否申请发货
     */
    private Integer isApplySend;
    
    private String orderCompanyCode;
    private String orderType;
    private String recType;
    private BigDecimal totalAmount;
    private BigDecimal payCash;
    private BigDecimal payFb;
    private BigDecimal totalHV;
    private String goodsKind;
    private String biOrderNo;
    private String payMethod;
    private String needInvoice;
    private String memberName;
    private String remark;
    private BigDecimal euRate;
    private String orderDateStr;
    
    List<SendInfoItemsBeanDTO> goodsItemsList = new ArrayList<SendInfoItemsBeanDTO>();
    
    public BigDecimal getSendFee() {
		return sendFee;
	}

	public void setSendFee(BigDecimal sendFee) {
		this.sendFee = sendFee;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getOrderor() {
        return orderor;
    }

    public void setOrderor(String orderor) {
        this.orderor = orderor;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public LocalDateTime getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDateTime orderDate) {
        this.orderDate = orderDate;
    }

    public LocalDateTime getSendDate() {
        return sendDate;
    }

    public void setSendDate(LocalDateTime sendDate) {
        this.sendDate = sendDate;
    }

    public LocalDateTime getRecDate() {
        return recDate;
    }

    public void setRecDate(LocalDateTime recDate) {
        this.recDate = recDate;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getRecStatus() {
        return recStatus;
    }

    public void setRecStatus(String recStatus) {
        this.recStatus = recStatus;
    }

    public String getRecName() {
        return recName;
    }

    public void setRecName(String recName) {
        this.recName = recName;
    }

    public String getRecAddr() {
        return recAddr;
    }

    public void setRecAddr(String recAddr) {
        this.recAddr = recAddr;
    }

    public String getRecPhone() {
        return recPhone;
    }

    public void setRecPhone(String recPhone) {
        this.recPhone = recPhone;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getDcsendNo() {
        return dcsendNo;
    }

    public void setDcsendNo(String dcsendNo) {
        this.dcsendNo = dcsendNo;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRecCode() {
        return recCode;
    }

    public void setRecCode(String recCode) {
        this.recCode = recCode;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getIsDc() {
        return isDc;
    }

    public void setIsDc(String isDc) {
        this.isDc = isDc;
    }

    public String getFaChecker() {
        return faChecker;
    }

    public void setFaChecker(String faChecker) {
        this.faChecker = faChecker;
    }

    public LocalDateTime getFaCheckDate() {
        return faCheckDate;
    }

    public void setFaCheckDate(LocalDateTime faCheckDate) {
        this.faCheckDate = faCheckDate;
    }

    public String getMemo() {
        return memo;
    }
    
    public String getMemoOrderer() {
		return memoOrderer;
	}

	public void setMemoOrderer(String memoOrderer) {
		this.memoOrderer = memoOrderer;
	}

	public LocalDateTime getMemoDatetime() {
		return memoDatetime;
	}

	public void setMemoDatetime(LocalDateTime memoDatetime) {
		this.memoDatetime = memoDatetime;
	}

	public String getMemoOld() {
		return memoOld;
	}

	public void setMemoOld(String memoOld) {
		this.memoOld = memoOld;
	}

	public void setMemo(String memo) {
        this.memo = memo;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public String getComboReceiptNo() {
        return comboReceiptNo;
    }

    public void setComboReceiptNo(String comboReceiptNo) {
        this.comboReceiptNo = comboReceiptNo;
    }

    public String getSplitReceiptNo() {
        return splitReceiptNo;
    }

    public void setSplitReceiptNo(String splitReceiptNo) {
        this.splitReceiptNo = splitReceiptNo;
    }

    public String getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(String isSplit) {
        this.isSplit = isSplit;
    }

    public String getIsAgentStock() {
        return isAgentStock;
    }

    public void setIsAgentStock(String isAgentStock) {
        this.isAgentStock = isAgentStock;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getIsMergeHead() {
        return isMergeHead;
    }

    public void setIsMergeHead(String isMergeHead) {
        this.isMergeHead = isMergeHead;
    }

    public String getOrderCompanyCode() {
        return orderCompanyCode;
    }

    public void setOrderCompanyCode(String orderCompanyCode) {
        this.orderCompanyCode = orderCompanyCode;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getRecType() {
        return recType;
    }

    public void setRecType(String recType) {
        this.recType = recType;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPayFb() {
        return payFb;
    }

    public void setPayFb(BigDecimal payFb) {
        this.payFb = payFb;
    }

    public BigDecimal getTotalHV() {
        return totalHV;
    }

    public void setTotalHV(BigDecimal totalHV) {
        this.totalHV = totalHV;
    }

    public String getGoodsKind() {
        return goodsKind;
    }

    public void setGoodsKind(String goodsKind) {
        this.goodsKind = goodsKind;
    }

    public String getBiOrderNo() {
        return biOrderNo;
    }

    public void setBiOrderNo(String biOrderNo) {
        this.biOrderNo = biOrderNo;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getNeedInvoice() {
        return needInvoice;
    }

    public void setNeedInvoice(String needInvoice) {
        this.needInvoice = needInvoice;
    }

    public BigDecimal getPayCash() {
        return payCash;
    }

    public void setPayCash(BigDecimal payCash) {
        this.payCash = payCash;
    }

	public String getRecEmail() {
		return recEmail;
	}

	public void setRecEmail(String recEmail) {
		this.recEmail = recEmail;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public Integer getIsApplySend() {
		return isApplySend;
	}

	public void setIsApplySend(Integer isApplySend) {
		this.isApplySend = isApplySend;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public BigDecimal getEuRate() {
		return euRate;
	}

	public void setEuRate(BigDecimal euRate) {
		this.euRate = euRate;
	}

	public String getOrderDateStr() {
		return orderDateStr;
	}

	public void setOrderDateStr(String orderDateStr) {
		this.orderDateStr = orderDateStr;
	}

	public List<SendInfoItemsBeanDTO> getGoodsItemsList() {
		return goodsItemsList;
	}

	public void setGoodsItemsList(List<SendInfoItemsBeanDTO> goodsItemsList) {
		this.goodsItemsList = goodsItemsList;
	}
	
}