/*
 * @ClassName ISysVerifyCodeDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-04 14:50:17
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysVerifyCodeDO;

import feign.Param;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ISysVerifyCodeDao extends BaseDao<SysVerifyCodeDO> {

    /** 
     * @Title: findCondition 
     * @Description: 
     * @param verifyCodeDO
     * @return
     * @return: SysVerifyCodeDO
     */
    SysVerifyCodeDO findCondition(@Param("verifyCodeDO") SysVerifyCodeDO verifyCodeDO);
}