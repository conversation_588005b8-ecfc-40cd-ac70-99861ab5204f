/*
 * @ClassName ImpPoOrderCnDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;

public class ImpPoOrderCnDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields goodscode 商品编码
     */
    private String goodscode;
    /**
     * @Fields qty 数量
     */
    private String qty;
    /**
     * @Fields totalmoney F$金额
     */
    private BigDecimal totalmoney;
    /**
     * @Fields isCon 是否重消 0=首购，1=重消
     */
    private BigDecimal isCon;
    /**
     * @Fields errorMsg 错误信息
     */
    private String errorMsg;
    /**
     * @Fields totalfv FV金额
     */
    private BigDecimal totalfv;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getGoodscode() {
        return goodscode;
    }

    public void setGoodscode(String goodscode) {
        this.goodscode = goodscode;
    }

    public String getQty() {
        return qty;
    }

    public void setQty(String qty) {
        this.qty = qty;
    }

    public BigDecimal getTotalmoney() {
        return totalmoney;
    }

    public void setTotalmoney(BigDecimal totalmoney) {
        this.totalmoney = totalmoney;
    }

    public BigDecimal getIsCon() {
        return isCon;
    }

    public void setIsCon(BigDecimal isCon) {
        this.isCon = isCon;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public BigDecimal getTotalfv() {
        return totalfv;
    }

    public void setTotalfv(BigDecimal totalfv) {
        this.totalfv = totalfv;
    }
}