/*
 * @ClassName IIcAnnounceLookDao
 * @Description 
 * @version 1.0
 * @Date 2018-02-05 11:07:12
 */
package com.hisun.lemon.urm.dao.ic;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.ic.IcAnnounceLookDO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IIcAnnounceLookDao extends BaseDao<IcAnnounceLookDO> {

    /** 
     * @Title: getByUser 
     * @param announceId
     * @param userCode
     * @return
     * @return: IcAnnounceLookDO
     */
    IcAnnounceLookDO getByUser(@Param("announceId") String announceId, @Param("userCode") String userCode);
    
    /** 
     * @Title: inserts 
     * @param announceId
     * @param userCode
     * @return: int
     */
    int inserts(@Param("userCode") String userCode,@Param("targetTerminal") String targetTerminal,@Param("type") String type);
}