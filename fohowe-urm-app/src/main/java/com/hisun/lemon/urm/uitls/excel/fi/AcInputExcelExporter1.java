package com.hisun.lemon.urm.uitls.excel.fi;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.urm.dto.fi.input.AcInputQueryDetailBean;
import com.hisun.lemon.urm.dto.fi.input.AcInputVO;
import com.hisun.lemon.urm.enums.fi.AcInputExporterEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcInputExcelExporter1 extends URMExcelExportFactorys {
    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName="申购";
        String[] colNames=new String[] {"欧部公账期数","欧部公账所属分公司","订单号","用户编号","欧部公账账户名称","欧部公账金额",
                "欧部公账币种","汇率","欧部公账明细状态","欧部公账入账时间","备注"};
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(Sheet sheet, CellStyle style, Object obj, int beginRow) throws Exception{
        AcInputVO vo=(AcInputVO) obj;
        Map<Object, String> tranTypeKV= EnumsUtils.EnumToMap(AcInputExporterEnums.class);
        List<AcInputQueryDetailBean> detailList=vo.getDataDetailList();
        for(AcInputQueryDetailBean o:detailList) {
            Row row = sheet.createRow(beginRow++);
            int index=0;
            row.createCell(index++).setCellValue(o.getPeriodWeek());
            row.createCell(index++).setCellValue(o.getCompanyCode());
            row.createCell(index++).setCellValue(o.getInputNo());
            row.createCell(index++).setCellValue(o.getUserCode());
            row.createCell(index++).setCellValue(o.getAccountName());
            row.createCell(index++).setCellValue(o.getLocalMoneyStr());
            row.createCell(index++).setCellValue(o.getLocalCurrency());
            row.createCell(index++).setCellValue(o.getRate().doubleValue());
            row.createCell(index++).setCellValue(tranTypeKV.get(o.getStatuse()));
            row.createCell(index++).setCellValue(o.getCreateTime().format(ymdhms));
            row.createCell(index++).setCellValue(o.getFiCheckMemo());
        }
    }

    public static AcInputExcelExporter1 builder() {
        return new AcInputExcelExporter1();
    }
}
