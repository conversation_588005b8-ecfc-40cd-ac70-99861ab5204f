package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.urm.dto.fi.input.AcInputQueryBean;
import com.hisun.lemon.urm.dto.fi.input.AcInputVO;
import com.hisun.lemon.urm.enums.fi.StatusEnums;
import com.hisun.lemon.urm.enums.fi.TranTypeEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcInputExcelExporter extends  URMExcelExportFactorys{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="申购";
		String[] colNames=new String[] {"期数","所属分公司","代办处编号","订单号","用户编号","名称","充值方式","金额",
				"本地货币代码","本地货币金额","汇率","状态","创建时间","入账时间","财务确定状态",
				"备注","财务确认备注","分公司审核人","总公司审核人","支付平台","支付金额"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		AcInputVO vo=(AcInputVO) obj;
		List<AcInputQueryBean> dataList=vo.getDataList();
		Map<Object, String> tranTypeKV=EnumsUtils.EnumToMap(TranTypeEnums.class);
		Map<Object, String> statusKV=EnumsUtils.EnumToMap(StatusEnums.class);
		for(AcInputQueryBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getPeriodWeek());
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getInputNo());
			row.createCell(index++).setCellValue(o.getUserCode());
			row.createCell(index++).setCellValue(o.getUserName());
			row.createCell(index++).setCellValue(tranTypeKV.get(o.getTranType()));
			row.createCell(index++).setCellValue(o.getMoney().doubleValue());
			row.createCell(index++).setCellValue(o.getLocalCurrency());
			row.createCell(index++).setCellValue(o.getLocalMoney().doubleValue());
			row.createCell(index++).setCellValue(o.getRate().doubleValue());
			row.createCell(index++).setCellValue(statusKV.get(o.getStatus()));
			row.createCell(index++).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getRecheckeTime()==null?"":o.getRecheckeTime().format(ymdhms));
			row.createCell(index++).setCellValue(statusKV.get(o.getFiCheckStatus()));
			row.createCell(index++).setCellValue(o.getMemo());
			row.createCell(index++).setCellValue(o.getFiCheckMemo());
			row.createCell(index++).setCellValue(o.getCheckerCode()==null?"":o.getCheckerCode());
			row.createCell(index++).setCellValue(o.getRecheckerCode()==null?"":o.getRecheckerCode());
			row.createCell(index++).setCellValue(o.getPayPlat()==null?"":o.getPayPlat());
			row.createCell(index++).setCellValue(o.getPayAmount()==null?0:o.getPayAmount().doubleValue());
		}
		
	}
	public static AcInputExcelExporter builder() {
		return new AcInputExcelExporter();
	}
}
