package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.urm.dto.fi.appl.AcApplQueryBean;
import com.hisun.lemon.urm.dto.fi.appl.AcApplVO;
import com.hisun.lemon.urm.enums.fi.StatusEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcApplExcelExporter extends  URMExcelExportFactorys{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="申领";
		String[] colNames=new String[] {"申请单号","用户编号","申请金额","实发金额","手续费","币种",
				"本地货币金额","汇率","申请时间","申请状态","分公司审核","总部审核","期数","财务确认","财务确认状态","财务确认备注"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		AcApplVO vo=(AcApplVO) obj;
		List<AcApplQueryBean> dataList=vo.getAcAppls();
		Map<Object, String> statusKV=EnumsUtils.EnumToMap(StatusEnums.class);
//		Map<Object, String> mallTypeKV=EnumsUtils.EnumToMap(MallTypeEnums.class);
		for(AcApplQueryBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getApplNo());
			row.createCell(index++).setCellValue(o.getUserCode());
			row.createCell(index++).setCellValue(o.getAmount().doubleValue());
			row.createCell(index++).setCellValue(o.getSendAmt().doubleValue());
			row.createCell(index++).setCellValue(o.getFees().doubleValue());
			row.createCell(index++).setCellValue(o.getLocalCurrency());
			row.createCell(index++).setCellValue(o.getLocalMoney().doubleValue());
			row.createCell(index++).setCellValue(o.getRate().doubleValue());
			row.createCell(index++).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(index++).setCellValue(statusKV.get(o.getStatus()));
			row.createCell(index++).setCellValue(o.getCheckTime()==null?"":o.getCheckerCode()+"-"+o.getCheckerName()+"/"+o.getCheckTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getReCheckTime()==null?"":o.getReCheckerCode()+"-"+o.getReCheckerName()+"/"+o.getReCheckTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getPeriodWeek());
			row.createCell(index++).setCellValue(o.getFiCheckTime()==null?"":o.getFiCheckerCode()+"-"+o.getFiCheckerName()+"/"+o.getFiCheckTime().format(ymdhms));
			row.createCell(index++).setCellValue(statusKV.get(o.getFiCheckStatus()));
			row.createCell(index++).setCellValue(o.getFiCheckMemo());
		}
		
	}
	public static AcApplExcelExporter builder() {
		return new AcApplExcelExporter();
	}
}
