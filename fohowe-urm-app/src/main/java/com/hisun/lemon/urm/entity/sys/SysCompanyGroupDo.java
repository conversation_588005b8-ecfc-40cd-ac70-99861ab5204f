/*
 * @ClassName SysCompanyGroupDo
 * @Description 
 * @version 1.0
 * @Date 2019-02-12 17:01:51
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysCompanyGroupDo extends BaseDO {
    /**
     * @Fields id 群组ID group_id
     */
    private String id;
    /**
     * @Fields orderType 单据类型：
			1=注册新经销商，
			2=购买新经营权，
			3=活跃订单，
			4=经营权促销，
			5=资格单，
			6=周转货单，
			8=提货申请单，
			9=零售订单,
			11=H000换货, 
			25=重消分配单
     */
    private String orderType;
    /**
     * @Fields currencyType 货币类型（商品货币）比如：$
     */
    private String currencyType;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}