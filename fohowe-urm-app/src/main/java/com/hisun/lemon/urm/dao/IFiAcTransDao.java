/*
 * @ClassName IFiAcTransDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.trans.AcTransBean;
import com.hisun.lemon.urm.dto.fi.trans.AcTransDTO;
import com.hisun.lemon.urm.entity.FiAcTransDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IFiAcTransDao extends BaseDao<FiAcTransDO> {

	List<AcTransBean> getListByCondition(AcTransDTO acTransDTO);
	FiAcTransDO getTrans(@Param("id") long id);
	int getTotalCount(AcTransDTO acTransDTO);
}