/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: EncryptionUtils.java 
 * @Prject: fohowe-urm-app
 * @Package: com.hisun.lemon.urm.uitls 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年12月4日 下午7:39:44 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.uitls;

import java.security.Key;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import org.springframework.stereotype.Component;
import org.apache.commons.codec.binary.Base64;
import com.hisun.lemon.framework.utils.LemonUtils;

/** 
 * @ClassName: EncryptionUtils 
 * @Description: TODO
 * @author: tian
 * @date: 2017年12月4日 下午7:39:44  
 */
@Component
public class EncryptionUtils {
    /** 
     * 
     * @Title: encrypt 
     * @Description: 密码加密
     * @param data
     * @param key
     * @return
     * @return: String
     */
    public String encrypt(String pwd) {
        String DESkey = LemonUtils.getProperty("urm.password.key");
        //pwd = pwd.toLowerCase();
        if (DESkey.length() != 24) {
            return pwd;
        }
        try {
            Key deskey = null;
            byte[] data = pwd.getBytes("UTF-8");
            DESedeKeySpec spec = new DESedeKeySpec(DESkey.getBytes("utf-8"));

            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
            deskey = keyfactory.generateSecret(spec);

            Cipher cipher = Cipher.getInstance("desede/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, deskey);

            byte[] pwdCipherByte = cipher.doFinal(data);
//            return new sun.misc.BASE64Encoder().encode(pwdCipherByte);
            return Base64.encodeBase64String(pwdCipherByte);
        } catch (Exception e) {

        }
        return "";
    }

    /** 
     * @Title: decrypt 
     * @Description: 密码解密
     * @param data
     * @param key
     * @return
     * @return: String
     */
    public String decrypt(String pwdCipher) {
        String DESkey = LemonUtils.getProperty("urm.password.key");
        try {
//            byte[] pwdCipherByte = new sun.misc.BASE64Decoder().decodeBuffer(pwdCipher);
            byte[] pwdCipherByte =Base64.decodeBase64(pwdCipher);
            Cipher cipher = Cipher.getInstance("desede" + "/ECB/PKCS5Padding");
            DESedeKeySpec spec = new DESedeKeySpec(DESkey.getBytes("utf-8"));

            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
            Key deskey = keyfactory.generateSecret(spec);
            cipher.init(Cipher.DECRYPT_MODE, deskey);
            byte[] pwdByte = cipher.doFinal(pwdCipherByte);
            return new String(pwdByte, "UTF-8");
        } catch (Exception e) {
        }
        return "";
    }
}
