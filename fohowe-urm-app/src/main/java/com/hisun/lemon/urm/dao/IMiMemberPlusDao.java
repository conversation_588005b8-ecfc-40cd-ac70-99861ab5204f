package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.MiMemberPlusDO;
import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceDTO;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceVO;
import com.hisun.lemon.urm.dto.mi.member.*;
import com.hisun.lemon.urm.dto.mi.member.ActiveBean;
import com.hisun.lemon.urm.dto.mi.member.ActiveDTO;
import com.hisun.lemon.urm.dto.mi.member.MIBaseInfo;
import com.hisun.lemon.urm.dto.mi.member.MemAgentQueryDTO;
import com.hisun.lemon.urm.dto.mi.member.MemberBeanDO;
import com.hisun.lemon.urm.dto.mi.member.MemberCoreInfoVO;
import com.hisun.lemon.urm.dto.mi.member.MemberDTO;
import com.hisun.lemon.urm.dto.mi.member.MemberDetailBeanDO;
import com.hisun.lemon.urm.dto.mi.member.MemberQueryBean;
import com.hisun.lemon.urm.dto.mi.member.SubMember;
import com.hisun.lemon.urm.dto.mi.member.SubMemberDO;
import com.hisun.lemon.urm.entity.MiMemberDO;
import com.hisun.lemon.urm.entity.MiMemberPlusDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface IMiMemberPlusDao extends BaseDao<MiMemberPlusDO> {
	
	/**
     * 通过经销商编号查询经销商
     *
     * @param memberNo
     * @return
     */
	MiMemberPlusDO getByMemberNo(String memberNo);
	/**
     * 通过代办处编号查询代办处
     *
     * @param agentNo
     * @return
     */
	MiMemberPlusDO getByAgentNo(String agentNo);
	/**
     * 通过分公司编号查询分公司
     *
     * @param companyCode
     * @return
     */
	MiMemberPlusDO getByCompanyCode(String companyCode);
	
//	List<MiMemPlusRspDO> findMemPlus(MiMemPlusQueryDO memPlusQueryDO);
}
