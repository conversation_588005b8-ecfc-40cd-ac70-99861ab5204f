/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: FileUtil.java 
 * @Prject: fohowe-urm-app
 * @Package: com.hisun.lemon.urm.uitls 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2017年11月17日 下午5:49:34 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.uitls;

import java.awt.Image;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.common.MsgCd;

/** 
 * @ClassName: FileUtil 
 * @Description: 文件处理类
 * @author: tian
 * @date: 2017年11月17日 下午5:49:34  
 */
@Component
public class FileUtils {

    /**
     * 
     * @Title: uploadFile 
     * @Description: 文件上传
     * @param file
     * @param filePath
     * @param fileName
     * @throws Exception
     * @return: void
     */
    public void uploadFile(byte[] file, String filePath, String fileName) throws Exception {
        File targetFile = new File(filePath);
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
        FileOutputStream out = new FileOutputStream(filePath + "/" + fileName);
        out.write(file);
        out.flush();
        out.close();
    }
    /**
     * 删除文件
     * @param filePath
     * @param fileName
     */
    public void DeleteFile(String filePath, String fileName) {
		File file = new File(filePath + "/" + fileName);
		if (file.exists()) {
			file.delete();
		}
	}

    /** 
     * @Title: downloadFile 
     * @Description: 文件下载
     * @return: void
     * @throws IOException 
     */
    public void downloadFile(String filePath, String fileName, HttpServletResponse response) throws IOException {
        File file = new File(filePath + "/" + fileName);
        if (file.exists()) {
            byte[] buffer = new byte[1024];
            FileInputStream fis = new FileInputStream(file);
            BufferedInputStream bis = new BufferedInputStream(fis);
            response.reset();
            OutputStream os = response.getOutputStream();
            response.setContentType("application/force-download");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentLength((int) file.length());

            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
            bis.close();
        } else {
            LemonException.throwBusinessException(MsgCd.FILE_IS_NOT_EXISIS.getMsgCd());
        }
    }

    /** 
      * 通过读取文件并获取其width及height的方式，来判断判断当前文件是否图片，这是一种非常简单的方式。 
      *  
      * @param imageFile 
      * @return 
      */
    public boolean isImage(MultipartFile imageFile) {
        Image img = null;
        boolean isImg=false;
        try {
            img = ImageIO.read(imageFile.getInputStream());
            if (img == null || img.getWidth(null) <= 0 || img.getHeight(null) <= 0) {
            	isImg= false;
            }else {
            	isImg= true;
            }
            if(!isImg) {
            	// 获取文件的ContentType
            	String contentType = imageFile.getContentType();
            	if(JudgeUtils.isNotBlank(contentType)) {
            		// 判断ContentType是否为图片类型
            		for (String imageContentType : new String[]{"image/gif", "image/jpeg", "image/png", "image/jpg"}) {
            			if (imageContentType.equalsIgnoreCase(contentType)) {
            				isImg= true;
            			}
            		}
            	}
            }
            if(!isImg) {
            	isImg=checkIsImg(imageFile.getOriginalFilename());
            }
        } catch (Exception e) {
            return false;
        } finally {
            img = null;
        }
        return isImg;
    }

    /**
     * 判断是否为允许的上传文件类型,true表示允许
     */
    public boolean checkFile(String fileName) {
        //设置允许上传文件类型
        String suffixList = "jpg,gif,png,ico,bmp,jpeg,doc,docx,xls,xlsx,txt,pdf,zip,rar,apk,app,ipa,deb,pxl,bin,tar,gz";
        // 获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        if (suffixList.contains(suffix.trim().toLowerCase())) {
            return true;
        }
        return false;
    }
    
    public boolean checkIsImg(String fileName) {
    	//设置允许上传文件类型
    	String suffixList = "jpg,gif,png,ico,bmp,jpeg";
    	// 获取文件后缀
    	String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
    	if (suffixList.contains(suffix.trim().toLowerCase())) {
    		return true;
    	}
    	return false;
    }
}
