package com.hisun.lemon.urm.dao.ticket;

import com.hisun.lemon.fohowe.ec.dto.OrderBean;
import com.hisun.lemon.fohowe.ec.dto.OrderDetailsBean;
import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsRegistrationMemberBean;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsRegistrationMemberDTO;
import com.hisun.lemon.urm.entity.ticket.TicketsRegistrationMemberDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ITicketsRegistrationMemberDao extends BaseDao<TicketsRegistrationMemberDO> {
    int deleteByPrimaryKey(Long id);
    int insertSelective(TicketsRegistrationMemberDO DO);
    int updateByPrimaryKeySelective(TicketsRegistrationMemberDO reqDTO);
    List<TicketsRegistrationMemberBean> queryList(TicketsRegistrationMemberDTO bean);
    int getTotalCount(TicketsRegistrationMemberDTO bean);
    int batchUpdate(@Param("list") List<TicketsRegistrationMemberDO> list);

    TicketsRegistrationMemberDO getTicketsRegistration(@Param("id") String id);

    int isExitByMeetingNo(@Param("meetingNo") String meetingNo,@Param("memberNo") String memberNo);

    List<TicketsRegistrationMemberBean> queryRegistrationList(TicketsRegistrationMemberDTO bean);

    int getRegistrationListCount(TicketsRegistrationMemberDTO bean);
    

    Integer isExitSLF300(@Param("memberNo") String memberNo);

    List<TicketsRegistrationMemberBean> isRegistration(@Param("orderDetailsBeanList")List<OrderDetailsBean> orderDetailsBeanList);

    List<OrderDetailsBean> queryOrders(OrderDetailsBean bean);


    OrderBean getOrderByOrderNo(@Param("memberNo") String memberNo,@Param("orderNo") String orderNo);

    int disassociate(TicketsRegistrationMemberDTO bean);

    List<TicketsRegistrationMemberBean> queryAllRegistrationList();
}
