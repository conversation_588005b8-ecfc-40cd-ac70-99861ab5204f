package com.hisun.lemon.urm.entity.ticket;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDateTime;

public class TicketsRegistrationMemberDO extends BaseDO {
    private Long id;

    private String meetingNo;

    private String memberNo;

    private String memberName;

    private String agentNo;

    private String companyCode;

    private Integer status;

    private LocalDateTime fiCheckTime;

    private String orderNo;


    private String goodsCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getFiCheckTime() {
        return fiCheckTime;
    }

    public void setFiCheckTime(LocalDateTime fiCheckTime) {
        this.fiCheckTime = fiCheckTime;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }


    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }
}
