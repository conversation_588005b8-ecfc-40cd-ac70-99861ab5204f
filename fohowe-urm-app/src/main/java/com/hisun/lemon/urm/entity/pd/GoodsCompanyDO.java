/*
 * @ClassName GoodsCompanyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-04 10:00:58
 */
package com.hisun.lemon.urm.entity.pd;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.BaseDO;

public class GoodsCompanyDO extends BaseDO {
	
    /**
     * @Fields id ID
     */
    private Integer id;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    /**
     * @Fields goodsName 商品名称
     */
    private String goodsName;
    /**
     * @Fields saleType 上线类型 1-销售 2-积分专区 3-
     */
    private String saleType;
    /**
     * @Fields companyCode 分公司编号
     */
    private String companyCode;
    /**
     * @Fields companyName 分公司名称
     */
    private String companyName;
    /**
     * @Fields standardPrice 普通F$价
     */
    private BigDecimal standardPrice;
    /**
     * @Fields standardFv 普通价积分
     */
    private BigDecimal standardFv;
    /**
     * @Fields proPrice 优惠F$价
     */
    private BigDecimal proPrice;
    /**
     * @Fields proFv 优惠价积分
     */
    private BigDecimal proFv;
    /**
     * @Fields mallPrice 会员价
     */
    private BigDecimal mallPrice;
    /**
     * @Fields mallFv 会员Fv
     */
    private BigDecimal mallFv;
   
    /**
     * @Fields integralPrice 积分专区F$
     */
    private BigDecimal integralPrice;
    /**
     * @Fields integralFv 积分专区FV
     */
    private BigDecimal integralFv;
    /**
     * @Fields integralHv 积分专区Hv
     */
    private BigDecimal integralHv;
    /**
     * @Fields stopSale 停止销售，1-是 0-否
     */
    private String stopSale;
    /**
     * @Fields h000Sale H000换货:1-是 0-否
     */
    private String h000Sale;
    /**
     * @Fields isAgentSale 是否在代办处销售:1-是 0-否
     */
    private String isAgentSale;
    /**
     * @Fields saleQuantity 销量
     */
    private Integer saleQuantity;
    /**
     * @Fields createCode 创建者
     */
    private String createCode;
    /**
     * @Fields updateCode 更新人
     */
    private String updateCode;
    /**
     * @Fields delFlag 是否删除，1-是
     */
    private String delFlag;
    /**
     * 普通H000价格
     */
    private BigDecimal standardH000;
    /**
     * 会员H000价格
     */
    private BigDecimal mallH000;
    /**
     * 商品中文名称
     */
    private String chineseName;
    
	public GoodsCompanyDO() {
	}
	
	public GoodsCompanyDO(Integer id, String goodsCode, String goodsName, String saleType, String companyCode,
			String companyName, BigDecimal standardPrice, BigDecimal standardFv, BigDecimal proPrice, BigDecimal proFv,
			BigDecimal mallPrice, BigDecimal mallFv, BigDecimal integralPrice, BigDecimal integralFv,
			BigDecimal integralHv, String stopSale, String h000Sale, String isAgentSale, Integer saleQuantity,
			String createCode, String updateCode, String delFlag, BigDecimal standardH000, BigDecimal mallH000,
			String chineseName) {
		super();
		this.id = id;
		this.goodsCode = goodsCode;
		this.goodsName = goodsName;
		this.saleType = saleType;
		this.companyCode = companyCode;
		this.companyName = companyName;
		this.standardPrice = standardPrice;
		this.standardFv = standardFv;
		this.proPrice = proPrice;
		this.proFv = proFv;
		this.mallPrice = mallPrice;
		this.mallFv = mallFv;
		this.integralPrice = integralPrice;
		this.integralFv = integralFv;
		this.integralHv = integralHv;
		this.stopSale = stopSale;
		this.h000Sale = h000Sale;
		this.isAgentSale = isAgentSale;
		this.saleQuantity = saleQuantity;
		this.createCode = createCode;
		this.updateCode = updateCode;
		this.delFlag = delFlag;
		this.standardH000 = standardH000;
		this.mallH000 = mallH000;
		this.chineseName = chineseName;
	}

	public GoodsCompanyDO(String goodsCode, String saleType, String companyCode) {
		this.goodsCode = goodsCode;
		this.saleType = saleType;
		this.companyCode = companyCode;
	}

	public String getIsAgentSale() {
        return isAgentSale;
    }

    public void setIsAgentSale(String isAgentSale) {
        this.isAgentSale = isAgentSale;
    }

    public String getChineseName() {
		return chineseName;
	}

	public void setChineseName(String chineseName) {
		this.chineseName = chineseName;
	}

	public BigDecimal getStandardH000() {
		return standardH000;
	}

	public void setStandardH000(BigDecimal standardH000) {
		this.standardH000 = standardH000;
	}

	public BigDecimal getMallH000() {
		return mallH000;
	}

	public void setMallH000(BigDecimal mallH000) {
		this.mallH000 = mallH000;
	}

	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }
    
    public String getSaleType() {
		return saleType;
	}

	public void setSaleType(String saleType) {
		this.saleType = saleType;
	}

	public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public BigDecimal getStandardFv() {
        return standardFv;
    }

    public void setStandardFv(BigDecimal standardFv) {
        this.standardFv = standardFv;
    }

   
    public BigDecimal getProPrice() {
        return proPrice;
    }

    public void setProPrice(BigDecimal proPrice) {
        this.proPrice = proPrice;
    }

    public BigDecimal getProFv() {
        return proFv;
    }

    public void setProFv(BigDecimal proFv) {
        this.proFv = proFv;
    }

    public BigDecimal getMallPrice() {
        return mallPrice;
    }

    public void setMallPrice(BigDecimal mallPrice) {
        this.mallPrice = mallPrice;
    }

    public BigDecimal getMallFv() {
        return mallFv;
    }

    public void setMallFv(BigDecimal mallFv) {
        this.mallFv = mallFv;
    }

    public BigDecimal getIntegralPrice() {
        return integralPrice;
    }

    public void setIntegralPrice(BigDecimal integralPrice) {
        this.integralPrice = integralPrice;
    }

    public BigDecimal getIntegralFv() {
        return integralFv;
    }

    public void setIntegralFv(BigDecimal integralFv) {
        this.integralFv = integralFv;
    }

    public BigDecimal getIntegralHv() {
        return integralHv;
    }

    public void setIntegralHv(BigDecimal integralHv) {
        this.integralHv = integralHv;
    }

    public String getStopSale() {
        return stopSale;
    }

    public void setStopSale(String stopSale) {
        this.stopSale = stopSale;
    }

    public String getH000Sale() {
        return h000Sale;
    }

    public void setH000Sale(String h000Sale) {
        this.h000Sale = h000Sale;
    }

    public Integer getSaleQuantity() {
		return saleQuantity;
	}

	public void setSaleQuantity(Integer saleQuantity) {
		this.saleQuantity = saleQuantity;
	}

	public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public String getUpdateCode() {
        return updateCode;
    }

    public void setUpdateCode(String updateCode) {
        this.updateCode = updateCode;
    }

	public String getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(String delFlag) {
		this.delFlag = delFlag;
	}

   
}