/*
 * @ClassName ISysIdDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-14 11:41:12
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysIdDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysIdDao extends BaseDao<SysIdDO> {

    /** 
     * @Title: pageFind 
     * @Description: 分页模糊查询
     * @param idCode
     * @param idName
     * @return
     * @return: List<T>
     */
    public List<SysIdDO> pageFind(@Param("idCode") String idCode, @Param("idName") String idName);

    /** 
     * @Title: delete 
     * @Description: 删除序号生成规则
     * @param id
     * @return
     * @return: int
     */
    public int delete(@Param("id") long id);
}