/*
 * @ClassName IAlCharacterValueDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-25 17:47:04
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlCharacterValueDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlCharacterValueDao extends BaseDao<AlCharacterValueDO> {

    /**
     * 
     * @Title: deleteByLang 
     * @Description: 删除该语言对应的所有记录
     * @param id
     * @return
     * @return: int
     */
    int deleteByLang(@Param("langId") Long id);

    /**
     * 新增语言编码时初始化一条记录
     * 
     * @param langCode
     * @return
     */
    public int insertByLangId(@Param("langId") long langId);

    /** 
     * @Title: insertAllLang 
     * @param keyId
     * @param keyDesc
     * @return
     * @return: int
     */
    int insertAllLang(@Param("keyId") long keyId, @Param("keyDesc") String keyDesc);

    /** 
     * @Title: insertByLangCode 
     * @Description: 新增语言编码时相应新增数据
     * @param langId
     * @return
     * @return: int
     */
    int insertByLangCode(@Param("langId") long langId);

    /** 
     * @Title: findByKeyId 
     * @Description: 根据keyId查询value值列表
     * @param id
     * @return
     * @return: List<T>
     */
    List<AlCharacterValueDO> findByKeyId(@Param("keyId") long keyId);

    /** 
     * @Title: deleteById 
     * @Description: 根据keyId删除
     * @param idList
     * @return
     * @return: int
     */
    int deleteById(@Param("keyId") Long keyId);

    /** 
     * @Title: findByKeyLang 
     * @param keyList
     * @param langCode
     * @return
     * @return: List<AlCharacterValueDO>
     */
    List<AlCharacterValueDO> findByKeyLang(@Param("keyList") List<String> keyList,
            @Param("langCode") String langCode ,@Param("groupId") String groupId);

    /** 
     * @Title: findByKey 
     * @param characterKey
     * @return
     * @return: List<AlCharacterValueDO>
     */
    List<AlCharacterValueDO> findByKey(@Param("characterKey") String characterKey);
    
    /** 
     * @Title: findByKey 
     * @param characterKey
     * @param groupId
     * @param langCode
     * @return
     * @return: List<AlCharacterValueDO>
     */
    List<AlCharacterValueDO> findByKeys(@Param("characterKey") String characterKey,@Param("groupId") String groupId,@Param("langCode") String langCode);

    /** 
     * @Title: delete 
     * @param valueOf
     * @return
     * @return: int
     */
    int delete(@Param("id") Long id);

    /** 
     * @Title: findRecordExists 
     * @param id
     * @param keyId
     * @return
     * @return: AlCharacterValueDO
     */
    AlCharacterValueDO findRecordExists(@Param("langId") long langId, @Param("keyId") long keyId);
    
    /** 
     * @Title: findRecordExist 
     * @param id
     * @param keyId
     * @param groupId
     * @return
     * @return: AlCharacterValueDO
     */
    AlCharacterValueDO findRecordExist(@Param("langId") long langId, @Param("keyId") long keyId, @Param("groupId") String groupId);
}