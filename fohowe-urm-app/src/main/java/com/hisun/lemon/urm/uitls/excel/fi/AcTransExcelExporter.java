package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.fi.trans.AcTransBean;
import com.hisun.lemon.urm.dto.fi.trans.AcTransVO;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class AcTransExcelExporter extends  URMExcelExportFactory{
	private ILanguageService languageService;
	public AcTransExcelExporter(ILanguageService languageService) {
		this.languageService=languageService;
	}
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="转账查询";
		// 转出方编号、转出方姓名、转出方所属代办处、转入方编号、转入方姓名、转入方所属代办处、提交日期、确认日期、转账金额、转出方备注、代办处备注、签字（签字那一列留空白就行）
		String[] colNames=new String[] {"转出方编号","转出方姓名","转出金额类型","转入方编号","转入方姓名","提交日期","确认日期","转账金额","签字"};
		String multiLangFileName="column.memDataInfo";
		String[] multiLangColNames=new String[] {
				"column.payUser","column.payUserName","column.zhang_hu_lei_xing","column.reciveUserNo","column.reciveUserName","column.createData","column.okTime","column.transeMoney","common.sign"
		};
		Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
		if(result!=null) {
			colNames=(String[]) result.get("titles");
			fileName=(String) result.get("fileName");
		}
		super.exportActually(fileName, colNames, obj, response);
	}

	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AcTransVO vo=(AcTransVO) obj;
		List<AcTransBean> dataList=vo.getDataList();
//		Map<Object, String> tranTypeKV=EnumsUtils.EnumToMap(TranTypeEnums.class);
//		Map<String,String> acTypeKV=vo.getAcTypeKV();
		for(AcTransBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getOutUserCode());
			row.createCell(index++).setCellValue(o.getOutUserName());
//			row.createCell(index++).setCellValue(o.getOutAgentNo());
//			row.createCell(index++).setCellValue(acTypeKV.get(o.getAcType()));
			row.createCell(index++).setCellValue(o.getAcType());
			row.createCell(index++).setCellValue(o.getInUserCode());
			row.createCell(index++).setCellValue(o.getInUserName());
//			row.createCell(index++).setCellValue(o.getInAgentNo());
			row.createCell(index++).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getCheckTime()==null?"":o.getCheckTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getOutMoney()==null? 0 :o.getOutMoney().doubleValue());
//			row.createCell(index++).setCellValue(o.getMemo());
//			row.createCell(index++).setCellValue(o.getAgentMemo());
			row.createCell(index++).setCellValue("");
		}

	}
}
