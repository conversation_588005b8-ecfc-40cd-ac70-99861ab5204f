/*
 * @ClassName AlLanguageCodeDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-27 10:16:06
 */
package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AlLanguageCodeDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields langCode 语言编码
     */
    private String langCode;
    /**
     * @Fields langName 语言名称
     */
    private String langName;
    /**
     * @Fields allowedUser 管理人员
     */
    private String allowedUser;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getLangName() {
        return langName;
    }

    public void setLangName(String langName) {
        this.langName = langName;
    }

    public String getAllowedUser() {
        return allowedUser;
    }

    public void setAllowedUser(String allowedUser) {
        this.allowedUser = allowedUser;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}