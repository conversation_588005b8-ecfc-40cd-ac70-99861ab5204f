package com.hisun.lemon.urm.uitls.excel.mi;



import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.fohowe.common.enums.CardTypes;
import com.hisun.lemon.fohowe.common.enums.HealthyTypeEnums;
import com.hisun.lemon.fohowe.common.enums.KnowWayTypeEnums;
import com.hisun.lemon.fohowe.common.enums.PaperTypeEnums;
import com.hisun.lemon.fohowe.common.enums.RightDisplayWayEnums;
import com.hisun.lemon.fohowe.common.enums.Sex;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.mi.member.MemberQueryBean;
import com.hisun.lemon.urm.dto.mi.member.MemberVO;
import com.hisun.lemon.urm.enums.mi.MemStatusEnums;
import com.hisun.lemon.urm.enums.mi.MemTypeEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class MemberExcelExporter extends  URMExcelExportFactorys{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="经销商基本信息";
		String[] colNames=new String[] {
				"经销商编号","所属分公司","代办处编号","经销商姓名","经销商级别","推荐人编号",
				"安置编号","注册时间","加入时间","加入期数","用户状态","活跃期数","昵称","性别","生日","证件类型","证件号","详细地址",
				"邮政编码","开户银行","户名","账号","办公电话","移动电话","传真","健康状态","知晓方式","种类","注册时布局",
				"推荐人姓名","推荐人代办处","推荐人分公司","经营权数量","促销资格","邮箱"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	public void export2(List<MemberQueryBean> dataList, HttpServletResponse response) {
		try {
			String fileName="经销商基本信息";
			String[] colNames=new String[] {// "推荐人编号","安置编号",
					"经销商编号","所属分公司","代办处编号","经销商姓名","经销商级别","注册时间","加入时间","加入期数","用户状态","活跃期数","昵称","性别","生日","证件类型","证件号","详细地址",
					"邮政编码","开户银行","户名","账号","办公电话","移动电话","传真","健康状态","知晓方式","种类","注册时布局",
					"经营权数量","促销资格","邮箱"};//"推荐人姓名","推荐人代办处","推荐人分公司",
			response.reset();
			response.setHeader("Content-disposition", "attachment; filename=" +fileName+ ".xlsx");
	        response.setContentType("application/msexcel;charset=UTF-8");//设置类型
	        response.setHeader("Pragma", "No-cache");//设置头
	        response.setHeader("Cache-Control", "no-cache");//设置头
	        response.setDateHeader("Expires", 0);//设置日期头
	        List<List<String>> head = new ArrayList<List<String>>();
	        for (int i = 0; i < colNames.length; i++) {
	        	List<String> head0 = new ArrayList<String>();
	        	head0.add(colNames[i]);
	        	head.add(head0);
			}
			List<List<String>> data = new ArrayList<List<String>>();
			data = addData(dataList);
			WriteWorkbook workBook = new WriteWorkbook();
			workBook.setExcelType(ExcelTypeEnum.XLSX);
			workBook.setOutputStream(response.getOutputStream());
			workBook.setNeedHead(true);
			WriteSheet sheet= new WriteSheet();
			sheet.setSheetNo(0);
			sheet.setSheetName(fileName); 
			sheet.setHead(head);
			ExcelWriter write = new ExcelWriter(workBook);
			write.write(data, sheet); 
			write.finish();
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		MemberVO vo=(MemberVO) obj;
		List<MemberQueryBean> dataList=vo.getDataList();
		Map<Object, String> memStatusKV=EnumsUtils.EnumToMap(MemStatusEnums.class);
		Map<Object, String> sexKV=EnumsUtils.EnumToMap(Sex.class);
		Map<Object, String> paperTypeKV = EnumsUtils.EnumToMap(PaperTypeEnums.class);
		Map<Object, String> healthyKV = EnumsUtils.EnumToMap(HealthyTypeEnums.class);
		Map<Object, String> knowWayKV = EnumsUtils.EnumToMap(KnowWayTypeEnums.class);
		Map<Object, String> disTypeKV = EnumsUtils.EnumToMap(RightDisplayWayEnums.class);
		Map<Object, String> memTypeKV = EnumsUtils.EnumToMap(MemTypeEnums.class);
		Map<Object, String> carTypeKV = EnumsUtils.EnumToMap(CardTypes.class);
//		Map<Object, String> promQualLevel = EnumsUtils.EnumToMap(PromQualEnums.class);
		for(MemberQueryBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(1).setCellValue(o.getCompanyCode());
			row.createCell(2).setCellValue(o.getAgentNo()==null?"":o.getAgentNo().trim());
			row.createCell(3).setCellValue(o.getMemberName());
			row.createCell(4).setCellValue(carTypeKV.get(o.getCardType()));
			row.createCell(5).setCellValue(o.getRecommendNo()==null?"":o.getRecommendNo().trim());
			row.createCell(6).setCellValue(o.getLinkNo()==null?"":o.getLinkNo().trim());
			row.createCell(7).setCellValue(o.getRegisterDate()==null?"":o.getRegisterDate().format(ymdhms));
			row.createCell(8).setCellValue(o.getActiveTime()==null?"":o.getActiveTime().format(ymdhms));
			row.createCell(9).setCellValue(o.getPeriodWeek());
			row.createCell(10).setCellValue(memStatusKV.get(o.getStatus()));
			row.createCell(11).setCellValue(o.getActiveWeek());
			row.createCell(12).setCellValue(o.getPetName());
			row.createCell(13).setCellValue(sexKV.get(o.getSex()));
//			SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
			row.createCell(14).setCellValue(o.getBirthday()==null?"":o.getBirthday().format(yms));
			row.createCell(15).setCellValue(paperTypeKV.get(o.getPaperType()));
			row.createCell(16).setCellValue(o.getPaperNo());
			row.createCell(17).setCellValue(o.getStoreAddr());
			row.createCell(18).setCellValue(o.getStorePost());
			row.createCell(19).setCellValue(o.getAccountBank());
			row.createCell(20).setCellValue(o.getAccountName());
			row.createCell(21).setCellValue(o.getAccountCode());
			row.createCell(22).setCellValue(o.getOfficeTel());
			row.createCell(23).setCellValue(o.getMobile());
			row.createCell(24).setCellValue(o.getFax());
			row.createCell(25).setCellValue(healthyKV.get(o.getHealthy()));
			row.createCell(26).setCellValue(knowWayKV.get(o.getKnowWay()));
			row.createCell(27).setCellValue(memTypeKV.get(o.getMemType()));
			row.createCell(28).setCellValue(disTypeKV.get(o.getDisType()));
			row.createCell(29).setCellValue(o.getRecommendName());
			row.createCell(30).setCellValue(o.getRecAgentNo());
			row.createCell(31).setCellValue(o.getRecCompanyCode());
			row.createCell(32).setCellValue(o.getRightNumber());
			row.createCell(33).setCellValue(o.getPromQual()==1?"VIP":"无");
			row.createCell(34).setCellValue(o.getEmail());
		}
	}
	
	public List<List<String>> addData(List<MemberQueryBean> dataList) throws Exception {
		List<List<String>> list = new ArrayList<List<String>>();
		Map<Object, String> memStatusKV=EnumsUtils.EnumToMap(MemStatusEnums.class);
		Map<Object, String> sexKV=EnumsUtils.EnumToMap(Sex.class);
		Map<Object, String> paperTypeKV = EnumsUtils.EnumToMap(PaperTypeEnums.class);
		Map<Object, String> healthyKV = EnumsUtils.EnumToMap(HealthyTypeEnums.class);
		Map<Object, String> knowWayKV = EnumsUtils.EnumToMap(KnowWayTypeEnums.class);
		Map<Object, String> disTypeKV = EnumsUtils.EnumToMap(RightDisplayWayEnums.class);
		Map<Object, String> memTypeKV = EnumsUtils.EnumToMap(MemTypeEnums.class);
		Map<Object, String> carTypeKV = EnumsUtils.EnumToMap(CardTypes.class);
//		Map<Object, String> promQualLevel = EnumsUtils.EnumToMap(PromQualEnums.class);
		for(MemberQueryBean o:dataList) {
			List<String> data = new ArrayList<String>();
			data.add(o.getMemberNo()==null?"":o.getMemberNo().trim());
			data.add(o.getCompanyCode());
			data.add(o.getAgentNo()==null?"":o.getAgentNo().trim());
			data.add(o.getMemberName());
			data.add(carTypeKV.get(o.getCardType()));
//			data.add(o.getRecommendNo()==null?"":o.getRecommendNo().trim());
//			data.add(o.getLinkNo()==null?"":o.getLinkNo().trim());
			data.add(o.getRegisterDate()==null?"":o.getRegisterDate().format(ymdhms));
			data.add(o.getActiveTime()==null?"":o.getActiveTime().format(ymdhms));
			data.add(o.getPeriodWeek());
			data.add(memStatusKV.get(o.getStatus()));
			data.add(o.getActiveWeek());
			data.add(o.getPetName());
			data.add(sexKV.get(o.getSex()));
			//SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
			data.add(o.getBirthday()==null?"":o.getBirthday().format(yms));
			data.add(paperTypeKV.get(o.getPaperType()));
			data.add(o.getPaperNo());
			data.add(o.getStoreAddr());
			data.add(o.getStorePost());
			data.add(o.getAccountBank());
			data.add(o.getAccountName());
			data.add(o.getAccountCode());
			data.add(o.getOfficeTel());
			data.add(o.getMobile());
			data.add(o.getFax());
			data.add(healthyKV.get(o.getHealthy()));
			data.add(knowWayKV.get(o.getKnowWay()));
			data.add(memTypeKV.get(o.getMemType()));
			data.add(disTypeKV.get(o.getDisType()));
//			data.add(o.getRecommendName());
//			data.add(o.getRecAgentNo());
//			data.add(o.getRecCompanyCode());
			data.add(o.getRightNumber());
//			String promQual = o.getPromQual() + "";
			data.add(o.getPromQual()==1?"VIP":"无");
			data.add(o.getEmail());
			list.add(data);
		}
		return list;
	}
	public static MemberExcelExporter builder() {
		return new MemberExcelExporter();
	}
}
