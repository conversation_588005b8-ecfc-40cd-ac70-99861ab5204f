/*
 * @ClassName IAlLanguageCodeDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-25 17:47:04
 */
package com.hisun.lemon.urm.dao.al;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.ALMsgInfoDO;

@Mapper
public interface IAlMsgInfoDao extends BaseDao<ALMsgInfoDO> {

    /** 
     * @Title: findAllRecord 
     * @Description: 查询所有记录
     * @return
     * @return: List<ALMsgInfoDO>
     */
    List<ALMsgInfoDO> getRecordList(@Param("msgCd") String msgCd,@Param("msgInfo") String msgInfo);
    
    List<ALMsgInfoDO> findAll();
    
    List<ALMsgInfoDO> getMsgList(@Param("msgCd") String msgCd);
    
    List<ALMsgInfoDO> getLanguageList();

    /** 
     * @Description: 根据语言编码获取数据
     * @return
     * @return: ALMsgInfoDO
     */
    ALMsgInfoDO getByCode(@Param("msgCd") String msgCd,@Param("language") String language);

    /**
     * 
     * @param msgCd
     * @param language
     * @return
     */
    ALMsgInfoDO get(@Param("msgCd") String msgCd,@Param("language") String language);

    /** 
     * @Title: delete 
     * @Description: 删除
     * @param id
     * @return
     * @return: int
     */
    int delete(@Param("msgCd") Long msgCd);
    
    int insertAllLang(@Param("msgCd") String msgCd, @Param("msgInfo") String msgInfo);
    
}