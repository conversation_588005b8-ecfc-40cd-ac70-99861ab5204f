/*
 * @ClassName SysConfigKeyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:45
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysConfigKeyDO extends BaseDO {
    /**
     * @Fields keyId 索引ID KEY_ID
     */
    private long keyId;
    /**
     * @Fields configCode 参数编码 CONFIG_CODE
     */
    private String configCode;
    /**
     * @Fields keyDesc 参数说明 KEY_DESC
     */
    private String keyDesc;
    /**
     * @Fields defaultValue 默认值 DEFAULT_VALUE
     */
    private String defaultValue;
    private String defaultValueTwo;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    private String configType;

    public long getKeyId() {
        return keyId;
    }

    public void setKeyId(long keyId) {
        this.keyId = keyId;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getKeyDesc() {
        return keyDesc;
    }

    public void setKeyDesc(String keyDesc) {
        this.keyDesc = keyDesc;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getDefaultValueTwo() {
        return defaultValueTwo;
    }

    public void setDefaultValueTwo(String defaultValueTwo) {
        this.defaultValueTwo = defaultValueTwo;
    }
}