package com.hisun.lemon.urm.uitls.excel.fi;



import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.urm.entity.fi.FiFgcDepositDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcFgcDepositExcelExporter extends  URMExcelExportFactorys{
	/**
	 * fgc定存导出
	 * @param fileName
	 * @param colNames
	 * @param obj
	 * @param response
	 */
	public void export(String fileName,Object obj, HttpServletResponse response) { 
		String[] colNames= {"分公司","代办处","经销商编号","经销商姓名","定存数量","定存时长","总金额","黄金单价","订单状态","创建时间","审核期数",
				"审核时间","结束时间","取消期数","取消时间","利率"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		PageInfo<FiFgcDepositDO> info =(PageInfo<FiFgcDepositDO>)obj;
		List<FiFgcDepositDO> dataList=info.getList();
		for(FiFgcDepositDO fgc:dataList) {
			Row row = sheet.createRow(beginRow++);
		    String memberNo =fgc.getMemberNo();///经销商编号
		    String agentNo =fgc.getAgentNo();//代办处编号
		    String companyCode =fgc.getCompanyCode();//公司编码
		    String name =fgc.getName();//经销商姓名
		    BigDecimal number =fgc.getNumber()==null?BigDecimal.ZERO:fgc.getNumber();//数量
		    String depositLength =fgc.getDepositLength();//定存时间 单位(月)
		    BigDecimal goldPrive =fgc.getGoldPrive()==null?BigDecimal.ZERO:fgc.getGoldPrive();//黄金单价
		    BigDecimal payAmount =number.multiply(goldPrive).setScale(4, BigDecimal.ROUND_HALF_UP);//总金额
		    Integer status =fgc.getStatus();//状态 1:新建 2:定存中 3:已取消 4:已结束
		    String statusStr="新建";
		    if (status==1) {
		    	statusStr="新建";
			}else if (status==2) {
				statusStr="定存中";
			}else if (status==3) {
				statusStr="已取消";
			}else {
				statusStr="已结束";
			}
		    LocalDateTime createTime =fgc.getCreateTime();//创建时间
		    Integer auditPeriod = fgc.getAuditPeriod()==null?0:fgc.getAuditPeriod();
		    LocalDateTime auditTime = fgc.getAuditTime();
		    LocalDateTime depositTime =fgc.getDepositTime();//到期时间
		    Integer cancelPeriod =fgc.getCancelPeriod()==null?0:fgc.getCancelPeriod();
		    LocalDateTime cancelTime = fgc.getCancelTime(); 
		    BigDecimal fRate =fgc.getRate()==null?BigDecimal.ZERO:fgc.getRate();//利率 
		    
		    
			row.createCell(0).setCellValue(companyCode);
			row.createCell(1).setCellValue(agentNo);
			row.createCell(2).setCellValue(memberNo);
			row.createCell(3).setCellValue(name);
			row.createCell(4).setCellValue(number.doubleValue());
			row.createCell(5).setCellValue(depositLength);
			row.createCell(6).setCellValue(payAmount.doubleValue());
			row.createCell(7).setCellValue(goldPrive.doubleValue());
			row.createCell(8).setCellValue(statusStr);
			row.createCell(9).setCellValue(createTime==null?"":createTime.format(ymdhms));
			row.createCell(10).setCellValue(auditPeriod);
			row.createCell(11).setCellValue(auditTime==null?"":auditTime.format(ymdhms));
			row.createCell(12).setCellValue(depositTime==null?"":depositTime.format(ymdhms));
			row.createCell(13).setCellValue(cancelPeriod);
			row.createCell(14).setCellValue(cancelTime==null?"":cancelTime.format(ymdhms));
			row.createCell(15).setCellValue(fRate.doubleValue());
		}
		
	}
	public static AcFgcDepositExcelExporter builder() {
		return new AcFgcDepositExcelExporter();
	}
}
