/*
 * @ClassName AlRegionDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:31:42
 */
package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AlRegionDO extends BaseDO {
    /**
     * @Fields regionId 地区ID
     */
    private String regionId;
    /**
     * @Fields parentRegionId 上级地区ID
     */
    private String parentRegionId;
    /**
     * @Fields regionCode 国家/地区编码
     */
    private String regionCode;
    /**
     * @Fields regionName 国家/地区名称(保存字符编码键值)
     */
    private String regionName;
    private String regionNameEn;
    /**
     * @Fields isCross 是否为跨境电商区域
     */
    private String isCross;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    private String picUrl;
    
    private String bonusType;
    
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getParentRegionId() {
        return parentRegionId;
    }

    public void setParentRegionId(String parentRegionId) {
        this.parentRegionId = parentRegionId;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public String getIsCross() {
        return isCross;
    }

    public void setIsCross(String isCross) {
        this.isCross = isCross;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getRegionNameEn() {
        return regionNameEn;
    }

    public void setRegionNameEn(String regionNameEn) {
        this.regionNameEn = regionNameEn;
    }

	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}
    
    
}