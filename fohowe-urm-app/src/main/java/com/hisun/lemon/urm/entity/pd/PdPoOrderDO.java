/*
 * @ClassName PdPoOrderDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-25 10:44:08
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class PdPoOrderDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields companyCode 订货区域/分公司 
     */
    private String companyCode;
    /**
     * @Fields vendorNo 供应商编号
     */
    private String vendorNo;
    /**
     * 供应商名称
     */
    private String vendorName;
    /**
     * @Fields orderNo 订货单编号,pdpo
     */
    private String orderNo;
    /**
     * @Fields receiptStatus 单据状态:0=订货,1=审核,2=删除
     */
    private String receiptStatus;
    /**
     * @Fields orderAmount 订货金额
     */
    private BigDecimal orderAmount;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields orderDate 订货日期，订货时自动写入
     */
    private LocalDate orderDate;
    /**
     * @Fields expectDate 预期到货日期，缺省=OrderDate+供应商送货周期
     */
    private LocalDate expectDate;
    /**
     * @Fields cancelDate 已审核订单删除日期
     */
    private LocalDateTime cancelDate;
    /**
     * @Fields orderCheckDate 订单审核日期
     */
    private LocalDateTime orderCheckDate;
    /**
     * @Fields lastUpDate 最后修改日期
     */
    private LocalDateTime lastUpDate;
    /**
     * @Fields orderUser 订单制作人
     */
    private String orderUser;
    /**
     * @Fields orderChecker 订单审核人
     */
    private String orderChecker;
    /**
     * @Fields delUser 作废人
     */
    private String delUser;
    /**
     * @Fields properTyp 单据属性 :1=已开始收货 2收货完毕
     */
    private String properTyp;
    /**
     * @Fields planId 单据头序号,PD_PO_PLAN.ID
     */
    private String planId;
    /**
     * @Fields supplyNo 供货单号
     */
    private String supplyNo;
    /**
     * @Fields deptName 下单部门
     */
    private String deptName;
    /**
     * @Fields chargePerson 负责人
     */
    private String chargePerson;
    /**
     * @Fields printDate 打码日期
     */
    private LocalDate printDate;
    /**
     * @Fields packReq 包装要求 1：俄文 2：立陶宛英文 3：加纳英文
     */
    private String packReq;
    /**
     * @Fields productStand 生产标准
     */
    private String productStand;
    /**
     * @Fields transPlace 交货地点 1：珠海御品堂 2：天津和治友德 3:珠海惟拓浦 4:其他
     */
    private String transPlace;
    /**
     * @Fields exportForm 出口于
     */
    private String exportForm;
    /**
     * @Fields isTape 产品箱内是否放8个胶带 1：是   0：否
     */
    private String isTape;
    /**
     * @Fields packType 打包方式 1：正式打包 2：简易打包
     */
    private String packType;
    /**
     * @Fields isWeight 外箱标注重量 1:是    0：否
     */
    private String isWeight;
    /**
     * @Fields markType 打码方式 1：立陶宛 方式 2：非洲方式 3：阿塞拜疆方式 4：一般方式
     */
    private String markType;
    /**
     * @Fields isOnfile 是否称重拍照存档 1：是   0：否
     */
    private String isOnfile;
    /**
     * @Fields weigh 发运前称重
     */
    private String weigh;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getVendorNo() {
        return vendorNo;
    }

    public void setVendorNo(String vendorNo) {
        this.vendorNo = vendorNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReceiptStatus() {
        return receiptStatus;
    }

    public void setReceiptStatus(String receiptStatus) {
        this.receiptStatus = receiptStatus;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDate getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDate orderDate) {
        this.orderDate = orderDate;
    }

    public LocalDate getExpectDate() {
        return expectDate;
    }

    public void setExpectDate(LocalDate expectDate) {
        this.expectDate = expectDate;
    }

    public LocalDateTime getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(LocalDateTime cancelDate) {
        this.cancelDate = cancelDate;
    }

    public LocalDateTime getOrderCheckDate() {
        return orderCheckDate;
    }

    public void setOrderCheckDate(LocalDateTime orderCheckDate) {
        this.orderCheckDate = orderCheckDate;
    }

    public LocalDateTime getLastUpDate() {
        return lastUpDate;
    }

    public void setLastUpDate(LocalDateTime lastUpDate) {
        this.lastUpDate = lastUpDate;
    }

    public String getOrderUser() {
        return orderUser;
    }

    public void setOrderUser(String orderUser) {
        this.orderUser = orderUser;
    }

    public String getOrderChecker() {
        return orderChecker;
    }

    public void setOrderChecker(String orderChecker) {
        this.orderChecker = orderChecker;
    }

    public String getDelUser() {
        return delUser;
    }

    public void setDelUser(String delUser) {
        this.delUser = delUser;
    }

    public String getProperTyp() {
        return properTyp;
    }

    public void setProperTyp(String properTyp) {
        this.properTyp = properTyp;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getSupplyNo() {
        return supplyNo;
    }

    public void setSupplyNo(String supplyNo) {
        this.supplyNo = supplyNo;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getChargePerson() {
        return chargePerson;
    }

    public void setChargePerson(String chargePerson) {
        this.chargePerson = chargePerson;
    }

    public LocalDate getPrintDate() {
        return printDate;
    }

    public void setPrintDate(LocalDate printDate) {
        this.printDate = printDate;
    }

    public String getPackReq() {
        return packReq;
    }

    public void setPackReq(String packReq) {
        this.packReq = packReq;
    }

    public String getProductStand() {
        return productStand;
    }

    public void setProductStand(String productStand) {
        this.productStand = productStand;
    }

    public String getTransPlace() {
        return transPlace;
    }

    public void setTransPlace(String transPlace) {
        this.transPlace = transPlace;
    }

    public String getExportForm() {
        return exportForm;
    }

    public void setExportForm(String exportForm) {
        this.exportForm = exportForm;
    }

    public String getIsTape() {
        return isTape;
    }

    public void setIsTape(String isTape) {
        this.isTape = isTape;
    }

    public String getPackType() {
        return packType;
    }

    public void setPackType(String packType) {
        this.packType = packType;
    }

    public String getIsWeight() {
        return isWeight;
    }

    public void setIsWeight(String isWeight) {
        this.isWeight = isWeight;
    }

    public String getMarkType() {
        return markType;
    }

    public void setMarkType(String markType) {
        this.markType = markType;
    }

    public String getIsOnfile() {
        return isOnfile;
    }

    public void setIsOnfile(String isOnfile) {
        this.isOnfile = isOnfile;
    }

    public String getWeigh() {
        return weigh;
    }

    public void setWeigh(String weigh) {
        this.weigh = weigh;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }
}