package com.hisun.lemon.urm.entity.st;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.BaseDO;

public class StStockAnalysisItemDO extends BaseDO {
    private String regionCode;
    private String goodsCode;
    private BigDecimal qty;
    private BigDecimal donWay;
    private BigDecimal outWay;
 
	public String getRegionCode() {
		return regionCode;
	}
	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}
	public String getGoodsCode() {
		return goodsCode;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	public BigDecimal getQty() {
		return qty;
	}
	public void setQty(BigDecimal qty) {
		this.qty = qty;
	}
	public BigDecimal getDonWay() {
		return donWay;
	}
	public void setDonWay(BigDecimal donWay) {
		this.donWay = donWay;
	}
	public BigDecimal getOutWay() {
		return outWay;
	}
	public void setOutWay(BigDecimal outWay) {
		this.outWay = outWay;
	} 
    
    
    
}	