/*
 * @ClassName SysUserDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:09:01
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysUserUsdtDO extends BaseDO {
	private Long id;
    private String companyCode;
    private String agentNo;
    private String userCode;
    private String userName;
    private String usdtCode;
    private Integer usdtStatus;
    private String userType;
    private String defCurreny;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime checkeTime;
    private String usdtId;
    private String nickName;
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getUserCode() {
		return userCode;
	}
	public void setUserCode(String userCode) {
		this.userCode = userCode;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUsdtCode() {
		return usdtCode;
	}
	public void setUsdtCode(String usdtCode) {
		this.usdtCode = usdtCode;
	}
	public Integer getUsdtStatus() {
		return usdtStatus;
	}
	public void setUsdtStatus(Integer usdtStatus) {
		this.usdtStatus = usdtStatus;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getDefCurreny() {
		return defCurreny;
	}
	public void setDefCurreny(String defCurreny) {
		this.defCurreny = defCurreny;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public LocalDateTime getCreateTime() {
		return createTime;
	}
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
	public LocalDateTime getCheckeTime() {
		return checkeTime;
	}
	public void setCheckeTime(LocalDateTime checkeTime) {
		this.checkeTime = checkeTime;
	}
	public String getUsdtId() {
		return usdtId;
	}
	public void setUsdtId(String usdtId) {
		this.usdtId = usdtId;
	}
	public String getNickName() {
		return nickName;
	}
	public void setNickName(String nickName) {
		this.nickName = nickName;
	}
}