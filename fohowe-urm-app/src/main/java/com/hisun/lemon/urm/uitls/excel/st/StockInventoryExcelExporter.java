package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.entity.st.StStockInventoryDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class StockInventoryExcelExporter extends URMExcelExportFactory {

    @Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName = "实际库存出入库明细";
		String[] colNames = new String[] { "期数", "分公司", "产品编码", "期初实际库存", "常规出库", "调整出库", "常规入库", "调整入库", "期末实际库存" };
		super.exportActually(fileName, colNames, obj, response);
	}

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        List<StStockInventoryDO> dataList = (List<StStockInventoryDO>) obj;
        for (StStockInventoryDO o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);
            int index=0;
            //"期数", "分公司", "产品编码", "期初实际库存", "常规出库", "调整出库", "常规入库", "调整入库", "期末实际库存"           
            row.createCell(index++).setCellValue(o.getwWeek());
            row.createCell(index++).setCellValue(o.getCompanyCode());
            row.createCell(index++).setCellValue(o.getGoodsCode());
            row.createCell(index++).setCellValue(o.getInitialQty());
            row.createCell(index++).setCellValue(o.getOutQty());
            row.createCell(index++).setCellValue(o.getAlterOut());
            row.createCell(index++).setCellValue(o.getInQty());
            row.createCell(index++).setCellValue(o.getAlterIn());
            row.createCell(index++).setCellValue(o.getFinalQty()); 
        }
    }

    public static StockInventoryExcelExporter builder() {
        return new StockInventoryExcelExporter();
    }
}
