/*
 * @ClassName MiChgLogDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class MiChgLogDO extends BaseDO {
    /**
     * @Fields seqId 
     */
    private Long seqId;
    /**
     * @Fields createDate 
     */
    private LocalDateTime createDate;
    /**
     * @Fields type --操作类型 5修改推荐人 6修改接点人
     */
    private String type;
    /**
     * @Fields memberNo 
     */
    private String memberNo;
    /**
     * @Fields recommendNo 
     */
    private String recommendNo;
    /**
     * @Fields linkNo 
     */
    private String linkNo;
    /**
     * @Fields updater 
     */
    private String updater;
    /**
     * @Fields status 
     */
    private String status;
    /**
     * @Fields errMsg 
     */
    private String errMsg;
    /**
     * @Fields remark 
     */
    private String remark;
    /**
     * @Fields rightNo 经营权编号
     */
    private String rightNo;
    /**
     * @Fields oldRecommendNo 原推荐人
     */
    private String oldRecommendNo;
    /**
     * @Fields oldLinkNo 原节点人
     */
    private String oldLinkNo;

    public Long getSeqId() {
        return seqId;
    }

    public void setSeqId(Long seqId) {
        this.seqId = seqId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getRecommendNo() {
        return recommendNo;
    }

    public void setRecommendNo(String recommendNo) {
        this.recommendNo = recommendNo;
    }

    public String getLinkNo() {
        return linkNo;
    }

    public void setLinkNo(String linkNo) {
        this.linkNo = linkNo;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRightNo() {
        return rightNo;
    }

    public void setRightNo(String rightNo) {
        this.rightNo = rightNo;
    }

    public String getOldRecommendNo() {
        return oldRecommendNo;
    }

    public void setOldRecommendNo(String oldRecommendNo) {
        this.oldRecommendNo = oldRecommendNo;
    }

    public String getOldLinkNo() {
        return oldLinkNo;
    }

    public void setOldLinkNo(String oldLinkNo) {
        this.oldLinkNo = oldLinkNo;
    }
}