package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.ic.AppQueryReqDTO;
import com.hisun.lemon.urm.dto.ic.AppVersionBean;
import com.hisun.lemon.urm.entity.sys.SysAppVersion;

@Mapper
public interface ISysAppVersionDao  extends BaseDao<SysAppVersion>{
 
    int deleteByPrimaryKey(Integer id);
 
    int insertSelective(SysAppVersion record);

    SysAppVersion selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysAppVersion record);
    
    Integer getMaxVersion(String platform);

	List<AppVersionBean> findList(AppQueryReqDTO reqDTO);
	List<SysAppVersion> getItemsList(AppQueryReqDTO reqDTO);
 
}