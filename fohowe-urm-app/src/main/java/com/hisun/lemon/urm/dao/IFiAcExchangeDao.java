/*
 * @ClassName IFiAcExchangeDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.exchange.AcExChangeDTO;
import com.hisun.lemon.urm.dto.fi.exchange.AcExchangeBean;
import com.hisun.lemon.urm.entity.FiAcExchangeDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IFiAcExchangeDao extends BaseDao<FiAcExchangeDO> {

	List<AcExchangeBean> getListByCondition(AcExChangeDTO acExChangeDTO);
	
	AcExchangeBean getListByConditionTotalF$(AcExChangeDTO acExChangeDTO);

	int getTotalCount(AcExChangeDTO acExChangeDTO);
	
	AcExchangeBean getByNo(@Param("exchangeNo") String exchangeNo);

	void callCancelExchanges(Long id);
	void insertACExchangeDO(AcExchangeBean acExchangeDO);
}