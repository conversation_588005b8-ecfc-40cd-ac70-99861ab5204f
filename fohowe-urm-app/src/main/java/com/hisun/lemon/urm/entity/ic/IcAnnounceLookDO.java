/*
 * @ClassName IcAnnounceLookDO
 * @Description 
 * @version 1.0
 * @Date 2018-02-05 11:07:12
 */
package com.hisun.lemon.urm.entity.ic;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class IcAnnounceLookDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields announceId 公告ID
     */
    private String announceId;
    /**
     * @Fields userCode 用户编号
     */
    private String userCode;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAnnounceId() {
        return announceId;
    }

    public void setAnnounceId(String announceId) {
        this.announceId = announceId;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}