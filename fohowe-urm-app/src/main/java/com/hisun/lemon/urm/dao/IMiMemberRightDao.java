/*
 * @ClassName IMiMemberRightDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
import com.hisun.lemon.urm.dto.mi.member.MemMainTree;
import com.hisun.lemon.urm.dto.mi.member.MemRightBean;
import com.hisun.lemon.urm.dto.mi.member.MemRightDTO;
import com.hisun.lemon.urm.dto.mi.member.NetWorkTO;
import com.hisun.lemon.urm.dto.mi.member.RightCaculateBean;
import com.hisun.lemon.urm.dto.mi.member.RightGoldsBean;
import com.hisun.lemon.urm.dto.mi.member.RightIndivShortBean;
import com.hisun.lemon.urm.dto.mi.member.RightNetworkBean;
import com.hisun.lemon.urm.entity.MiMemberRightDO;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IMiMemberRightDao extends BaseDao<MiMemberRightDO> {
	/**
	 * 分页查询
	* @param dto
	* @return 
	* <AUTHOR>
	 */
	List<MemRightBean> getListByPageBreak(MemRightDTO dto);

	int getTotalCount(MemRightDTO dto);
	
	/**
	* @param rightNO
	* @return 
	* <AUTHOR>
	 */
	MiMemberRightDO getByRightNO(@Param("rightNO")String rightNO);
	MiMemberRightDO getByRightNoForUpdate(@Param("rightNO")String rightNO);
	/**
	 * 根据MemNo获取奖金制度
	* @param memberNo
	* @return 
	* <AUTHOR>
	 */
	String getBonusTypeByMemNo(@Param("memberNo") String memberNo);
	
	/**
	 * get扩展的rightDo
	* @param rightNO
	* @return 
	* <AUTHOR>
	 */
	MemRightBean getExtensionDetailByIdOrRightNo(@Param("id") Long id, @Param("rightNO")  String rightNo);

	/**
	 * 安置网络查询
	* @param to
	* @return 
	* <AUTHOR>
	 */
	List<RightNetworkBean> queryRightNetworkBean(@Param("networkTO") NetWorkTO to);
	List<RightCaculateBean> querySimpleRightNetworkBean(@Param("networkTO") NetWorkTO to);;
	/**
	 * 查询经销商的经营权列表
	* @param memNo
	* @param levelType
	* @param isPrimay
	* @return 
	* <AUTHOR>
	 */
	List<MiMemberRightDO> getRightDOListByRightNo(@Param("memNo") String memNo,@Param("levelType")String levelType, @Param("isPrimay")String isPrimay);
	/**
	 * 查询经销商的经营权列表 增加标记条件
	 * @param memNo
	 * @param levelType
	 * @param isPrimay
	 * @return
	 */
	List<MiMemberRightDO> getRightDOListByRightNoQual(@Param("memNo") String memNo,
													@Param("levelType")String levelType, 
													@Param("isPrimay")String isPrimay,
													@Param("promQual") Integer promQual
													);
	
	int checkIsUsePromoteFlag(@Param("bonusType")String bonusType);
	Integer getRightNumByMemNo(String memNo);

	/**
	 * 改变经营权到另外一个经销商
	* @param fromMemberNo
	* @param toMemberNo
	* @return 
	* <AUTHOR>
	 */
	int changeRightNoToAnotherByMem(@Param("fromMemberNo")String fromMemberNo, @Param("toMemberNo")String toMemberNo);

	void updateaByRightNo(MiMemberRightDO rdo);
	/**
	 * 获取金点
	* @param memberNo
	* @return 
	* <AUTHOR>
	 */
	MiMemberRightDO getGoldRightByMemberNo(@Param("memberNo") String memberNo,@Param("goldType") String goldType);

	int selectGoldsTotalCount(GenericDTO<MIBaseQueryInfo> dto);

	List<RightGoldsBean> getGoldsListByPageBreak(GenericDTO<MIBaseQueryInfo> dto);

	MiMemberRightDO getPrimRightByMemberNo(@Param("memberNo") String memberNo);

	List<RightIndivShortBean> queryRightIndivShortBean(@Param("networkTO") NetWorkTO to);

	MiMemberRightDO get(@Param("id") Long id);
	/**
	 * 当该用户没有下线的时候，删除用户同时删除安置网络信息
	 * @param memberNo
	 */
	void deleteByMemNo(String memberNo);

	/**
	 * 重算网络相关 --start
	 */
	int countWhereMemberNoIsNull();

	int countWhereRightNoIsNull();

	int countWhereLinkNoIsNull();

	int countWhereRightEQLinkNo();

	int countWhereIndexOrLayerIsNull();
	
	int countWhereFMYPromQual(@Param("memberNo") String memberNo);

	void initCaculateFlag();

	int updateTopNodeNetwork(@Param("rightNo") String tOP_RECOMMEND_MEMBERNO, @Param("layer") int layer, @Param("index") String index,@Param("tableSuffix")String suffix);

	List<RightCaculateBean> selectListByLayerAndIndex(@Param("layer")int layer,@Param("index") String index);

	List<RightCaculateBean> selectListByLinkNo(@Param("linkNo")String linkNo);

	void updateSimpleBean(@Param("sub") RightCaculateBean sub);

	/**
	 * 重算网络相关 --end
	 */
	RightCaculateBean selectSimpleRightByRightNo(@Param("rightNo") String rightNO);
	RightCaculateBean selectSimpleRightByMemberNo(@Param("memberNo") String memberNo);

	int getTotalCountByLinkIndexAndLayer(@Param("linkIndex")String linkIndex, @Param("linkLayer") Integer linkLayer);

	int getTotalCountByGTLinkIndexAndLayer(@Param("linkIndex")String linkIndex, @Param("linkLayer") Integer linkLayer);

	List<MemMainTree> getMemberMainTree(@Param("networkTO") NetWorkTO to);

	void updateActiveDateNull(@Param("rightNo") String rightNo);
	//增加凤凰金点订单
	int callGoldRightOrderInfo(@Param("orderNo") String orderNo,@Param("memberNo") String memberNo,@Param("linkNo") String linkNo,@Param("wWeek") String wWeek);


	List<MiMemberRightDO> batchVerifyByRightNO(@Param("rightNoSet") Set<String> rightNoSet);
}