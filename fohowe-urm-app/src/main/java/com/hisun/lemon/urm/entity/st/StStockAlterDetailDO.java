/*
 * @ClassName StStockAlterDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-12 17:43:36
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class StStockAlterDetailDO extends BaseDO {
	/**
	 * 库存调整明细导出
	 */  
    /**
     * @Fields companyCode 分公司
     */
    private String companyCode;
    /**
     * @Fields receiptNo 单据编号
     */
    private String receiptNo;
    /**
     * @Fields receiptType 调整类型
     */
    private String receiptType;
    /**
     * 调整原因
     */
    private String receiptReason;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    /**
     * @Fields alterQty 调整数量
     */
    private Integer alterQty;
    /**
     * @Fields operDate 创建日期
     */
    private LocalDateTime operDate;
    /**
     * @Fields checkDate 审核日期
     */
    private LocalDateTime checkDate;
    /**
             * 确认时间
     */
    private LocalDateTime confirmCheckDate;
    /**
     * @Fields remark 10.备注 Remark
     */
    private String remark;
    /**
     * @Fields status 状态,0=新增 ，1=分公司审核 2总部已审核 3 已作废
     */
    private String status;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getGoodsCode() {
		return goodsCode;
	}

	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}

	public Integer getAlterQty() {
		return alterQty;
	}

	public void setAlterQty(Integer alterQty) {
		this.alterQty = alterQty;
	}

	public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

	public String getReceiptReason() {
		return receiptReason;
	}

	public void setReceiptReason(String receiptReason) {
		this.receiptReason = receiptReason;
	}

	public LocalDateTime getOperDate() {
        return operDate;
    }

    public void setOperDate(LocalDateTime operDate) {
        this.operDate = operDate;
    }

    public LocalDateTime getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(LocalDateTime checkDate) {
        this.checkDate = checkDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

	public LocalDateTime getConfirmCheckDate() {
		return confirmCheckDate;
	}

	public void setConfirmCheckDate(LocalDateTime confirmCheckDate) {
		this.confirmCheckDate = confirmCheckDate;
	}
    
}