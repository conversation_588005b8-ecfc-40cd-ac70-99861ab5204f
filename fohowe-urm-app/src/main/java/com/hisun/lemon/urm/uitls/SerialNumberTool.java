package com.hisun.lemon.urm.uitls;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 获取当日序列号，按天重置，从一递增
 * <AUTHOR>
 * @time 2018年1月2日
 */
public	class SerialNumberTool {
	  /* @Autowired
	   private RedisTemplate<String, Integer> redisTemplate;*/

	    private static SerialNumberTool serialNumberTool = null;
	    private static SimpleDateFormat yMd = new SimpleDateFormat("yyyyMMdd");
	    //用于自增长的标识
	    private int flag;
	    //用于记录日期
	    private Date date;


	    private SerialNumberTool() {
	        if(date == null || !(yMd.format(new Date()).equals(yMd.format(date)))){
	            date = new Date();
	            flag = 0;
	        }
	    }

	    /**
	     * 判断是否改变
	     */
	    private  void checkChangeDay(){
	        if(date == null || !(yMd.format(new Date()).equals(yMd.format(date)))){
	            date = new Date();
	            flag = 0;
	           // redisTemplate.opsForValue().set("haha", 0);
	        }
	    }


	    /**
	     * 取得PrimaryGenerater的单例实现
	     *
	     * @return
	     */
	    public static SerialNumberTool getInstance() {
	        if (serialNumberTool == null) {
	            synchronized (SerialNumberTool.class) {
	                if (serialNumberTool == null) {
	                    serialNumberTool = new SerialNumberTool();
	                }
	            }
	        }
	        return serialNumberTool;
	    }


	    /**
	     * 生成下一个编号,前缀自动补全
	     *
	     */
	    public synchronized String generaterNextNumber() {
	        checkChangeDay();
	        flag++;
//	        redisTemplate.opsForValue().increment("haha", 1);
//	        redisTemplate.opsForValue().get("haha");
	        return flag+"";
	    }

	    /**
	     * 避免当断网后重连或者服务器重启时导致标识flag清0，可以手动设置flag的开始点
	     * @param args
	     */
	    public void operateFlag(int position){
	        this.flag = position;
	    }

	    public static void main(String[] args) {
	        System.out.println(SerialNumberTool.getInstance().generaterNextNumber());
	    }
	}
