/*
 * @ClassName AlCharacterValueDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-25 17:47:04
 */
package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AlCharacterValueDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields keyId
     */
    private Long keyId;
    /**
     * @Fields langId 字符编码
     */
    private Long langId;
    /**
     * @Fields langCode 语言编码
     */
    private String langCode;
    private String langName;
    /**
     * @Fields characterKey 字符键值
     */
    private String characterKey;
    /**
     * @Fields characterValue 字符值
     */
    private String characterValue;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields groupId 群组编号
     */
    private String groupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getKeyId() {
        return keyId;
    }

    public void setKeyId(Long keyId) {
        this.keyId = keyId;
    }

    public Long getLangId() {
        return langId;
    }

    public void setLangId(Long langId) {
        this.langId = langId;
    }

    public String getCharacterValue() {
        return characterValue;
    }

    public void setCharacterValue(String characterValue) {
        this.characterValue = characterValue;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getCharacterKey() {
        return characterKey;
    }

    public void setCharacterKey(String characterKey) {
        this.characterKey = characterKey;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getLangName() {
        return langName;
    }

    public void setLangName(String langName) {
        this.langName = langName;
    }

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
    
}