/*
 * @ClassName FiAcInputDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class FiAcInputDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields companyCode 公司编码
     */
    private String companyCode;
    /**
     * @Fields userCode 用户编号
     */
    private String userCode;
    /**
     * @Fields serial 当日交易序号
     */
    private String serial;
    /**
     * @Fields orderType 单据类型,0=充值单，1=借款单，2=还款单
     */
    private String orderType;
    /**
     * @Fields acType 账户类型，f$＝f$，f0=f000
     */
    private String acType;
    /**
     * @Fields tranType 方式,0=转账，1=在线支付
     */
    private String tranType;
    /**
     * @Fields money 金额
     */
    private BigDecimal money;
    /**
     * @Fields localCurrency 本地货币代码 local_currency
     */
    private String localCurrency;
    /**
     * @Fields localMoney 本地货币金额 local_money
     */
    private BigDecimal localMoney;
    /**
     * @Fields rate 汇率 rate
     */
    private BigDecimal rate;
    /**
     * @Fields memo 备注
     */
    private String memo;
    /**
     * @Fields status 状态:1:新单 2:分公司已确认 3:总公司已确认 4：财务已确认   
     */
    private String status;
    /**
     * @Fields createrCode 建档人
     */
    private String createrCode;
    /**
     * @Fields createrName 建档人名称
     */
    private String createrName;
    /**
     * @Fields checkeTime 建档时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields checkerCode 审核人(分公司)
     */
    private String checkerCode;
    /**
     * @Fields checkerName 审核人名称
     */
    private String checkerName;
    /**
     * @Fields checkeTime 审核时间
     */
    private LocalDateTime checkeTime;
    /**
     * @Fields checkType 核准方式 0:手工 1:自动
     */
    private String checkType;
    /**
     * @Fields dealDate 交易日期
     */
    private LocalDateTime dealDate;
    /**
     * @Fields checkMsg 处理结果
     */
    private String checkMsg;
    /**
     * @Fields createNo 建立码
     */
    private String createNo;
    /**
     * @Fields remark 摘要
     */
    private String remark;
    /**
     * @Fields counterCode 柜台(所属公司/柜台)
     */
    private String counterCode;
    /**
     * @Fields recheckeTime 入账时间
     */
    private LocalDateTime recheckeTime;
    /**
     * @Fields recheckerName 入账人名称
     */
    private String recheckerName;
    /**
     * @Fields recheckerCode 入账人
     */
    private String recheckerCode;
    /**
     * @Fields mobile 联系电话
     */
    private String mobile;
    /**
     * @Fields transferDate 汇款/转帐日期
     */
    private LocalDateTime transferDate;
    /**
     * @Fields payOrderNo 银行汇款单号
     */
    private String payOrderNo;
    /**
     * @Fields payNum 支付人汇款账号
     */
    private String payNum;
    /**
     * @Fields payBank 支付人汇款银行
     */
    private String payBank;
    /**
     * @Fields accNum 收款账号
     */
    private String accNum;
    /**
     * @Fields recAmount 已财务确认金额
     */
    private BigDecimal recAmount;
    /**
     * @Fields fiCheckStatus 财务确认状态 0:未确认;1:部分确认;2:已确认
     */
    private String fiCheckStatus;
    /**
     * @Fields checkCompanyCode 审核分公司编码
     */
    private String checkCompanyCode;
    /**
     * @Fields cancelTime 作废时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields cancelCode 作废人
     */
    private String cancelCode;
    /**
     * @Fields periodWeek 充值申请期数
     */
    private String periodWeek;
    /**
     * @Fields inputNo 充值单号
     */
    private String inputNo;
    /**
     * @Fields cancelInputNo 充值取消单号
     */
    private String cancelInputNo;
    /**
     * @Fields modifyMemo 分公司修改备注
     */
    private String modifyMemo;
    /**
     * @Fields modifyMemo 财务修改备注
     */
    private String modifyMemoConfirm;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields orderNo 购货单号
     */
    private String orderNo;
    /**
     * @Fields handingFee 手续费
     */
    private BigDecimal handingFee;
    /**
     * @Fields attachmentids 附件，关联sys_attachment的id，多个附件逗号隔开
     */
    private String attachmentids;
    /**
     * @Fields accId 银行账号id
     */
    private Long accId;
    /**
     * @Fields ficheckeTime 财务确认时间
     */
    private LocalDateTime ficheckeTime;
    /**
     * @Fields ficheckerCode 财务确认人
     */
    private String ficheckerCode;
    /**
     * @Fields 申购类型0代办处 1在线支付 2促销单
     */
    private String inputType;
    /**
     * eas确认状态
     */
	private String easStatus;
    
	private Integer finStatus;
	
	private Integer finNum;
	
	private BigDecimal localFinMoney;
	//在线支付平台
	private String payPlat;
	//在线支付金额
	private BigDecimal payAmount;

    public String getSubjectNo() {
        return subjectNo;
    }

    public void setSubjectNo(String subjectNo) {
        this.subjectNo = subjectNo;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    private String subjectNo;
    private String accountId;
    private Integer isHandFee;
	
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public String getLocalCurrency() {
        return localCurrency;
    }

    public void setLocalCurrency(String localCurrency) {
        this.localCurrency = localCurrency;
    }

    public BigDecimal getLocalMoney() {
        return localMoney;
    }

    public void setLocalMoney(BigDecimal localMoney) {
        this.localMoney = localMoney;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public String getCheckerName() {
        return checkerName;
    }

    public void setCheckerName(String checkerName) {
        this.checkerName = checkerName;
    }

    public LocalDateTime getCheckeTime() {
        return checkeTime;
    }

    public void setCheckeTime(LocalDateTime checkeTime) {
        this.checkeTime = checkeTime;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public LocalDateTime getDealDate() {
        return dealDate;
    }

    public void setDealDate(LocalDateTime dealDate) {
        this.dealDate = dealDate;
    }

    public String getCheckMsg() {
        return checkMsg;
    }

    public void setCheckMsg(String checkMsg) {
        this.checkMsg = checkMsg;
    }

    public String getCreateNo() {
        return createNo;
    }

    public void setCreateNo(String createNo) {
        this.createNo = createNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCounterCode() {
        return counterCode;
    }

    public void setCounterCode(String counterCode) {
        this.counterCode = counterCode;
    }

    public LocalDateTime getRecheckeTime() {
        return recheckeTime;
    }

    public void setRecheckeTime(LocalDateTime recheckeTime) {
        this.recheckeTime = recheckeTime;
    }

    public String getRecheckerName() {
        return recheckerName;
    }

    public void setRecheckerName(String recheckerName) {
        this.recheckerName = recheckerName;
    }

    public String getRecheckerCode() {
        return recheckerCode;
    }

    public void setRecheckerCode(String recheckerCode) {
        this.recheckerCode = recheckerCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public LocalDateTime getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(LocalDateTime transferDate) {
        this.transferDate = transferDate;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public String getPayNum() {
        return payNum;
    }

    public void setPayNum(String payNum) {
        this.payNum = payNum;
    }

    public String getPayBank() {
        return payBank;
    }

    public void setPayBank(String payBank) {
        this.payBank = payBank;
    }

    public String getAccNum() {
        return accNum;
    }

    public void setAccNum(String accNum) {
        this.accNum = accNum;
    }

    public BigDecimal getRecAmount() {
        return recAmount;
    }

    public void setRecAmount(BigDecimal recAmount) {
        this.recAmount = recAmount;
    }

    public String getFiCheckStatus() {
        return fiCheckStatus;
    }

    public void setFiCheckStatus(String fiCheckStatus) {
        this.fiCheckStatus = fiCheckStatus;
    }

    public String getCheckCompanyCode() {
        return checkCompanyCode;
    }

    public void setCheckCompanyCode(String checkCompanyCode) {
        this.checkCompanyCode = checkCompanyCode;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public String getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }

    public String getInputNo() {
        return inputNo;
    }

    public void setInputNo(String inputNo) {
        this.inputNo = inputNo;
    }

    public String getCancelInputNo() {
        return cancelInputNo;
    }

    public void setCancelInputNo(String cancelInputNo) {
        this.cancelInputNo = cancelInputNo;
    }

    public String getModifyMemo() {
        return modifyMemo;
    }

    public void setModifyMemo(String modifyMemo) {
        this.modifyMemo = modifyMemo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getHandingFee() {
        return handingFee;
    }

    public void setHandingFee(BigDecimal handingFee) {
        this.handingFee = handingFee;
    }

    public String getAttachmentids() {
        return attachmentids;
    }

    public void setAttachmentids(String attachmentids) {
        this.attachmentids = attachmentids;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public LocalDateTime getFicheckeTime() {
        return ficheckeTime;
    }

    public void setFicheckeTime(LocalDateTime ficheckeTime) {
        this.ficheckeTime = ficheckeTime;
    }

    public String getFicheckerCode() {
        return ficheckerCode;
    }

    public void setFicheckerCode(String ficheckerCode) {
        this.ficheckerCode = ficheckerCode;
    }

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getModifyMemoConfirm() {
		return modifyMemoConfirm;
	}

	public void setModifyMemoConfirm(String modifyMemoConfirm) {
		this.modifyMemoConfirm = modifyMemoConfirm;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getTranType() {
		return tranType;
	}

	public void setTranType(String tranType) {
		this.tranType = tranType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getInputType() {
		return inputType;
	}

	public void setInputType(String inputType) {
		this.inputType = inputType;
	}

	public String getEasStatus() {
		return easStatus;
	}

	public void setEasStatus(String easStatus) {
		this.easStatus = easStatus;
	}

	public Integer getFinStatus() {
		return finStatus;
	}

	public void setFinStatus(Integer finStatus) {
		this.finStatus = finStatus;
	}

	public BigDecimal getLocalFinMoney() {
		return localFinMoney;
	}

	public void setLocalFinMoney(BigDecimal localFinMoney) {
		this.localFinMoney = localFinMoney;
	}

	public Integer getFinNum() {
		return finNum;
	}

	public void setFinNum(Integer finNum) {
		this.finNum = finNum;
	}

	public String getPayPlat() {
		return payPlat;
	}

	public void setPayPlat(String payPlat) {
		this.payPlat = payPlat;
	}

	public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

    public Integer getIsHandFee() {
        return isHandFee;
    }

    public void setIsHandFee(Integer isHandFee) {
        this.isHandFee = isHandFee;
    }
}