/*
 * @ClassName SysOrderTimeDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-14 15:45:01
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysOrderTimeDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private String id;
    /**
     * @Fields closeStatus 系统关闭状态，0-打开 1-关闭
     */
    private String closeStatus;
    /**
     * @Fields closeWeekday 系统关闭日期，周一--周七
     */
    private Integer closeWeekday;
    /**
     * @Fields closeTime 系统关闭时间，HH24
     */
    private Integer closeTime;
    /**
     * @Fields openWeekday 系统开放日期，周一---周七
     */
    private Integer openWeekday;
    /**
     * @Fields openTime 系统开放时间，HH24
     */
    private Integer openTime;
    /**
     * @Fields doTime 系统最后一次执行时间
     */
    private LocalDateTime doTime;
    
    private String companyCodes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCloseStatus() {
        return closeStatus;
    }

    public void setCloseStatus(String closeStatus) {
        this.closeStatus = closeStatus;
    }

    public Integer getCloseWeekday() {
        return closeWeekday;
    }

    public void setCloseWeekday(Integer closeWeekday) {
        this.closeWeekday = closeWeekday;
    }

    public Integer getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Integer closeTime) {
        this.closeTime = closeTime;
    }

    public Integer getOpenWeekday() {
        return openWeekday;
    }

    public void setOpenWeekday(Integer openWeekday) {
        this.openWeekday = openWeekday;
    }

    public Integer getOpenTime() {
        return openTime;
    }

    public void setOpenTime(Integer openTime) {
        this.openTime = openTime;
    }

    public LocalDateTime getDoTime() {
        return doTime;
    }

    public void setDoTime(LocalDateTime doTime) {
        this.doTime = doTime;
    }

	public String getCompanyCodes() {
		return companyCodes;
	}

	public void setCompanyCodes(String companyCodes) {
		this.companyCodes = companyCodes;
	}
    
}