/*
 * @ClassName FiAcAuthorizeDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiAcAuthorizeDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields userCode 用户编号
     */
    private String userCode;
    /**
     * @Fields currencyCode 货币编码
     */
    private String currencyCode;
    /**
     * @Fields rate 货币汇率
     */
    private BigDecimal rate;
    /**
     * @Fields amount 订单金额
     */
    private BigDecimal amount;
    /**
     * @Fields localAmount 本位币金额
     */
    private BigDecimal localAmount;
    /**
     * @Fields orderNo 单号
     */
    private String orderNo;
    /**
     * @Fields clientIp 用户ip
     */
    private String clientIp;
    /**
     * @Fields status 状态
     */
    private String status;
    /**
     * @Fields xLogin 
     */
    private String xLogin;
    /**
     * @Fields xTranKey 
     */
    private String xTranKey;
    /**
     * @Fields xFpTimestamp 
     */
    private String xFpTimestamp;
    /**
     * @Fields xFpSequence 
     */
    private String xFpSequence;
    /**
     * @Fields xFpHash 
     */
    private String xFpHash;
    /**
     * @Fields xAmount 
     */
    private BigDecimal xAmount;
    /**
     * @Fields xCurrencyCode 
     */
    private String xCurrencyCode;
    /**
     * @Fields xResponseCode 
     */
    private String xResponseCode;
    /**
     * @Fields xResponseReasonCode 
     */
    private String xResponseReasonCode;
    /**
     * @Fields xResponseReasonText 
     */
    private String xResponseReasonText;
    /**
     * @Fields xAuthCode 
     */
    private String xAuthCode;
    /**
     * @Fields xTransId 
     */
    private String xTransId;
    /**
     * @Fields xInvoiceNum 
     */
    private String xInvoiceNum;
    /**
     * @Fields xDescription 
     */
    private String xDescription;
    /**
     * @Fields xAccountNumber 
     */
    private String xAccountNumber;
    /**
     * @Fields xCardType 
     */
    private String xCardType;
    /**
     * @Fields xCustId 
     */
    private String xCustId;
    /**
     * @Fields xFirstName 
     */
    private String xFirstName;
    /**
     * @Fields xLastName 
     */
    private String xLastName;
    /**
     * @Fields xPhone 
     */
    private String xPhone;
    /**
     * @Fields xEmail 
     */
    private String xEmail;
    /**
     * @Fields checkTime 
     */
    private LocalDateTime checkTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getLocalAmount() {
        return localAmount;
    }

    public void setLocalAmount(BigDecimal localAmount) {
        this.localAmount = localAmount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getxLogin() {
        return xLogin;
    }

    public void setxLogin(String xLogin) {
        this.xLogin = xLogin;
    }

    public String getxTranKey() {
        return xTranKey;
    }

    public void setxTranKey(String xTranKey) {
        this.xTranKey = xTranKey;
    }

    public String getxFpTimestamp() {
        return xFpTimestamp;
    }

    public void setxFpTimestamp(String xFpTimestamp) {
        this.xFpTimestamp = xFpTimestamp;
    }

    public String getxFpSequence() {
        return xFpSequence;
    }

    public void setxFpSequence(String xFpSequence) {
        this.xFpSequence = xFpSequence;
    }

    public String getxFpHash() {
        return xFpHash;
    }

    public void setxFpHash(String xFpHash) {
        this.xFpHash = xFpHash;
    }

    public BigDecimal getxAmount() {
        return xAmount;
    }

    public void setxAmount(BigDecimal xAmount) {
        this.xAmount = xAmount;
    }

    public String getxCurrencyCode() {
        return xCurrencyCode;
    }

    public void setxCurrencyCode(String xCurrencyCode) {
        this.xCurrencyCode = xCurrencyCode;
    }

    public String getxResponseCode() {
        return xResponseCode;
    }

    public void setxResponseCode(String xResponseCode) {
        this.xResponseCode = xResponseCode;
    }

    public String getxResponseReasonCode() {
        return xResponseReasonCode;
    }

    public void setxResponseReasonCode(String xResponseReasonCode) {
        this.xResponseReasonCode = xResponseReasonCode;
    }

    public String getxResponseReasonText() {
        return xResponseReasonText;
    }

    public void setxResponseReasonText(String xResponseReasonText) {
        this.xResponseReasonText = xResponseReasonText;
    }

    public String getxAuthCode() {
        return xAuthCode;
    }

    public void setxAuthCode(String xAuthCode) {
        this.xAuthCode = xAuthCode;
    }

    public String getxTransId() {
        return xTransId;
    }

    public void setxTransId(String xTransId) {
        this.xTransId = xTransId;
    }

    public String getxInvoiceNum() {
        return xInvoiceNum;
    }

    public void setxInvoiceNum(String xInvoiceNum) {
        this.xInvoiceNum = xInvoiceNum;
    }

    public String getxDescription() {
        return xDescription;
    }

    public void setxDescription(String xDescription) {
        this.xDescription = xDescription;
    }

    public String getxAccountNumber() {
        return xAccountNumber;
    }

    public void setxAccountNumber(String xAccountNumber) {
        this.xAccountNumber = xAccountNumber;
    }

    public String getxCardType() {
        return xCardType;
    }

    public void setxCardType(String xCardType) {
        this.xCardType = xCardType;
    }

    public String getxCustId() {
        return xCustId;
    }

    public void setxCustId(String xCustId) {
        this.xCustId = xCustId;
    }

    public String getxFirstName() {
        return xFirstName;
    }

    public void setxFirstName(String xFirstName) {
        this.xFirstName = xFirstName;
    }

    public String getxLastName() {
        return xLastName;
    }

    public void setxLastName(String xLastName) {
        this.xLastName = xLastName;
    }

    public String getxPhone() {
        return xPhone;
    }

    public void setxPhone(String xPhone) {
        this.xPhone = xPhone;
    }

    public String getxEmail() {
        return xEmail;
    }

    public void setxEmail(String xEmail) {
        this.xEmail = xEmail;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }
}