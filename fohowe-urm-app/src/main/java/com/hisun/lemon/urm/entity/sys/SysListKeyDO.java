/*
 * @ClassName SysListKeyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:46
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysList<PERSON>eyDO extends BaseDO {
    /**
     * @Fields keyId 列表ID KEY_ID
     */
    private long keyId;
    /**
     * @Fields listCode 列表编码 LIST_CODE
     */
    private String listCode;
    /**
     * @Fields listName 列表说明描述
     */
    private String listName;
    /**
     * @Fields remark 备注 REMARK
     */
    private String remark;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public long getKeyId() {
        return keyId;
    }

    public void setKeyId(long keyId) {
        this.keyId = keyId;
    }

    public String getListCode() {
        return listCode;
    }

    public void setListCode(String listCode) {
        this.listCode = listCode;
    }

    public String getListName() {
        return listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}