/*
 * @ClassName StGoodAlterDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-15 11:11:00
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class StGoodAlterDO extends BaseDO {
    /**
     * @Fields id 00.ID ID
     */
    private Long id;
    /**
     * @Fields stockAlterId 单据编号,ST_StockAlter.ID
     */
    private Long stockAlterId;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    private String goodsName;
    /**
     * @Fields alterQty 调整数量
     */
    private Integer alterQty;
    /**
     * @Fields price 售价
     */
    private BigDecimal price;
    /**
     * @Fields stockPrice 库存平均价
     */
    private BigDecimal stockPrice;
    /**
     * @Fields state 
     */
    private String state;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    private Integer quantity;
    private Integer validQty;
    private BigDecimal standardPrice;
    private BigDecimal standardFv;

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getValidQty() {
        return validQty;
    }

    public void setValidQty(Integer validQty) {
        this.validQty = validQty;
    }

    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public BigDecimal getStandardFv() {
        return standardFv;
    }

    public void setStandardFv(BigDecimal standardFv) {
        this.standardFv = standardFv;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStockAlterId() {
        return stockAlterId;
    }

    public void setStockAlterId(Long stockAlterId) {
        this.stockAlterId = stockAlterId;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public Integer getAlterQty() {
        return alterQty;
    }

    public void setAlterQty(Integer alterQty) {
        this.alterQty = alterQty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getStockPrice() {
        return stockPrice;
    }

    public void setStockPrice(BigDecimal stockPrice) {
        this.stockPrice = stockPrice;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }
}