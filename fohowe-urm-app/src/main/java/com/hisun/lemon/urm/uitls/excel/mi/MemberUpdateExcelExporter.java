package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.mi.agent.AgentMemUpdateBean;
import com.hisun.lemon.urm.dto.mi.agent.AgentMemUpdateVO;
import com.hisun.lemon.urm.enums.fi.StatusEnums;
import com.hisun.lemon.urm.enums.mi.ReceiptTypeEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class MemberUpdateExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="所属代办处重置";
		String[] colNames=new String[] {
				"所属分公司","经销商编号","原代办处编号","目标代办处编号","重置类型",
				"审核状态","创建人","审核人","审核时间","转移时可用FB/F$余额","旅游基金","名车基金",
				"游艇基金","住宅基金","全球分红","大使分红","F000","H000","FGC","FP"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AgentMemUpdateVO vo=(AgentMemUpdateVO) obj;
		List<AgentMemUpdateBean> dataList=vo.getDataList();
		Map<Object, String> receiptstatusKV=EnumsUtils.EnumToMap(StatusEnums.class);
		Map<Object, String> receipttypeKV=EnumsUtils.EnumToMap(ReceiptTypeEnums.class);
		for(AgentMemUpdateBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getCompanycode());
			row.createCell(index++).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getToAgentNo());
			row.createCell(index++).setCellValue(receipttypeKV.get(o.getReceipttype().getCode()));
			row.createCell(index++).setCellValue(receiptstatusKV.get(o.getReceiptstatus().getCode()));
			row.createCell(index++).setCellValue(o.getOrderUser());
			row.createCell(index++).setCellValue(o.getOrderChecker());
			row.createCell(index++).setCellValue(o.getOrdercheckdate()==null?"":o.getOrdercheckdate().format(ymdhms));
			row.createCell(index++).setCellValue(o.getFdTotal()==null?0:o.getFdTotal().doubleValue());
			row.createCell(index++).setCellValue(o.getFund1()==null?0:o.getFund1().doubleValue());
			row.createCell(index++).setCellValue(o.getFund2()==null?0:o.getFund2().doubleValue());
			row.createCell(index++).setCellValue(o.getFund3()==null?0:o.getFund3().doubleValue());
			row.createCell(index++).setCellValue(o.getFund4()==null?0:o.getFund4().doubleValue());
			row.createCell(index++).setCellValue(o.getShare1()==null?0:o.getShare1().doubleValue());
			row.createCell(index++).setCellValue(o.getShare2()==null?0:o.getShare2().doubleValue());
			row.createCell(index++).setCellValue(o.getF000()==null?0:o.getF000().doubleValue());
			row.createCell(index++).setCellValue(o.getH000()==null?0:o.getH000().doubleValue());
			row.createCell(index++).setCellValue(o.getFgc()==null?0:o.getFgc().doubleValue());
			row.createCell(index++).setCellValue(o.getFp()==null?0:o.getFp().doubleValue());
		}
		
	}
	public static MemberUpdateExcelExporter builder() {
		return new MemberUpdateExcelExporter();
	}
}
