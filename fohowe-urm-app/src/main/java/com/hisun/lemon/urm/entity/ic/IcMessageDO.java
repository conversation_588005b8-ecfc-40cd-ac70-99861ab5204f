/*
 * @ClassName IcMessageDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 13:00:36
 */
package com.hisun.lemon.urm.entity.ic;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class IcMessageDO extends BaseDO {
    /**
     * @Fields messageId 消息ID MESSAGE_ID
     */
    private String messageId;
    /**
     * @Fields firstName 名
     */
    private String firstName;
    /**
     * @Fields lastName 姓
     */
    private String lastName;
    /**
     * @Fields sex 性别
     */
    private String sex;
    /**
     * @Fields phoneNumber 联系电话
     */
    private String phoneNumber;
    /**
     * @Fields eMail 
     */
    private String eMail;
    /**
     * @Fields skype 
     */
    private String skype;
    /**
     * @Fields viber 
     */
    private String viber;
    /**
     * @Fields facebook 
     */
    private String facebook;
    /**
     * @Fields address 居住地址
     */
    private String address;
    /**
     * @Fields job 职业
     */
    private String job;
    /**
     * @Fields refereeCode 推荐人编号
     */
    private String refereeCode;
    /**
     * @Fields messageContent 留言咨询
     */
    private String messageContent;
    /**
     * @Fields status 状态 0-未读 1-已读
     */
    private String status;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String geteMail() {
        return eMail;
    }

    public void seteMail(String eMail) {
        this.eMail = eMail;
    }

    public String getSkype() {
        return skype;
    }

    public void setSkype(String skype) {
        this.skype = skype;
    }

    public String getViber() {
        return viber;
    }

    public void setViber(String viber) {
        this.viber = viber;
    }

    public String getFacebook() {
        return facebook;
    }

    public void setFacebook(String facebook) {
        this.facebook = facebook;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getRefereeCode() {
        return refereeCode;
    }

    public void setRefereeCode(String refereeCode) {
        this.refereeCode = refereeCode;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}