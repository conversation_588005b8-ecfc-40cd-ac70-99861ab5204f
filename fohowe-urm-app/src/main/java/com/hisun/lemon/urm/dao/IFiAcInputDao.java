/*
 * @ClassName IFiAcInputDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.urm.dto.fi.input.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.FiAcInputDO;

@Mapper
public interface IFiAcInputDao extends BaseDao<FiAcInputDO> {
	//申购查询
	List<AcInputQueryBean> getListByCondition(AcInputDTO acInputDTO);
	//申购查询汇总
	AcInputQueryBean getListByConditionTotalF$(AcInputDTO acInputDTO);
	//申购查询总页数
	int getTotalCount(AcInputDTO acInputDTO);
	
	//申购明细查询
	List<AcInputQueryDetailBean> getListByConditionDetail(AcInputDTO acInputDTO);
	AcInputQueryDetailBean getListByConditionTotalF$Detail(AcInputDTO acInputDTO);
	int getTotalCountDetail(AcInputDTO acInputDTO);
	/**
	 * 删除记录
	 * @param ids
	 * @return
	 */
	int deleteById( @Param("ids") String[] ids);
	
	AcInputDO getById(String string);
	/**
	 * 根据订单编号删除申购单
	 * @param orderNo
	 */
	void deleteByOrderNo(String orderNo);
	
	FiAcInputDO getByOrderNo(String orderNo);
	/**
	 * 用于回退
	 * @param fiAcInputDO
	 * @return
	 */
	 int updates(FiAcInputDO fiAcInputDO);
	 /**
	  * 查询订单包含商品
	  * @param inputNo
	  * @return
	  */
	List<AcInputGood> getGoodsByOrderNo(String inputNo);
	
	List<Map<String, Object>> getRightNosByOrderNo(String inputNo);
	List<Map<String, Object>> getPayFbByOrderNo(@Param("inputNo") String inputNo);
	
	FiAcInputDO getOnlinePayInfo(@Param("orderNo") String orderNo);
	
	String getMaxDateByOrderNo(@Param("inputNo") String inputNo);

	int updateOrderReq(@Param("orderNo") String orderNo);
	
	int updateOrderCancel(@Param("orderNo") String orderNo);
	
	int updateFinStatus(@Param("id") Long id,@Param("finStatus") int finStatus,@Param("addFinMoney") BigDecimal addFinMoney);
}