package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.fohowe.common.enums.CardTypes;
import com.hisun.lemon.urm.entity.mi.MiMemberTeams;
import com.hisun.lemon.urm.entity.mi.MiMemberTeamsItems;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class MemberTeamsExcelExporter extends  URMExcelExportFactory{
    private ILanguageService languageService;
	public MemberTeamsExcelExporter(ILanguageService languageService) {
		// TODO Auto-generated constructor stub
		this.languageService=languageService;
	}
	public MemberTeamsExcelExporter() {
		// TODO Auto-generated constructor stub
	}
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="奋斗杯团队队员表";
		//队伍编号	队名	代办处	分公司	队长编号	队长姓名	成员编号	成员姓名	成员级别	是否奋斗经销商	最高完成目标	是否合格	队伍人数
		String[] colNames=new String[] {
				"团队编号","队名","代办处","分公司","队长编号","队长姓名","成员编号","成员姓名","成员级别",
				"是否奋斗经销商","合格期数","成员身份","加入时间","队伍人数"
				};
		
		String multiLangFileName="menu.vBonusMemTeams";
		String[] multiLangColNames=new String[] {
				"column.teams_no","column.teams_name","column.agencyNo","column.company","column.memNo","column.memName","column.teams_itemNo",
				"common.ming_cheng","column.memType",
				"column.teams_fendou","column.teams_passeFlag","成员身份","加入时间","column.teams_num"
		};
		
		Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
				
		if(result!=null) {
			colNames=(String[]) result.get("titles");
			fileName=(String) result.get("fileName");
		}
		
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		List<MiMemberTeams> dataList=(List<MiMemberTeams>)obj;
		Map<String,String> cardTypeKV= new HashMap<String, String>();
		CardTypes[] array = CardTypes.values();
		for (CardTypes bd : array) {
			cardTypeKV.put(bd.getCode(), bd.getName());
        }
		
		for(MiMemberTeams o:dataList) {
			if(o.getItems() !=null&& o.getItems().size()>0) {
				for (int i = 0; i < o.getItems().size(); i++) {
					MiMemberTeamsItems item = o.getItems().get(i);
					XSSFRow row = sheet.createRow(beginRow++);
					int index=0;
					row.createCell(index++).setCellValue(o.getTeamNo()==null?"":o.getTeamNo());
					row.createCell(index++).setCellValue(o.getTeamName()==null?"":o.getTeamName());
					row.createCell(index++).setCellValue(o.getAgentNo()==null?"":o.getAgentNo());
					row.createCell(index++).setCellValue(o.getCompanyCode()==null?"":o.getCompanyCode());
					row.createCell(index++).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
					row.createCell(index++).setCellValue(o.getMemberName()==null?"":o.getMemberName());
					row.createCell(index++).setCellValue(item.getMemberNo()==null?"":item.getMemberNo());
					row.createCell(index++).setCellValue(item.getMemberName()==null?"":item.getMemberName());
					row.createCell(index++).setCellValue(item.getCardType()==null?"":cardTypeKV.get(item.getCardType()));
					row.createCell(index++).setCellValue(item.getFendou()==null?0:item.getFendou());
					row.createCell(index++).setCellValue(item.getPasseFlag()==null?0:item.getPasseFlag());
					String leader="";
					if(item.getLeader()!=null&&item.getLeader()==2) {
						leader="队长";
					}else if(item.getLeader()!=null&&item.getLeader()==1) {
						leader="初始队员";
					}else {
						leader="新增队员";
					}
					row.createCell(index++).setCellValue(leader);
					row.createCell(index++).setCellValue(item.getCreateTime()==null?"":item.getCreateTime().format(ymdhms));
					row.createCell(index++).setCellValue(o.getTeamNum()==null?0:o.getTeamNum());
				}
			}else {
				XSSFRow row = sheet.createRow(beginRow++);
				int index=0;
				row.createCell(index++).setCellValue(o.getTeamNo());
				row.createCell(index++).setCellValue(o.getTeamName());
				row.createCell(index++).setCellValue(o.getAgentNo());
				row.createCell(index++).setCellValue(o.getCompanyCode());
				row.createCell(index++).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
				row.createCell(index++).setCellValue(o.getMemberName());
				row.createCell(index++).setCellValue("");
				row.createCell(index++).setCellValue("");
				row.createCell(index++).setCellValue("");
				row.createCell(index++).setCellValue("");
				row.createCell(index++).setCellValue("");
				row.createCell(index++).setCellValue("");
				row.createCell(index++).setCellValue(o.getTeamNum());
			}
			
//			
//			row.createCell(5).setCellValue(o.getActiveWeek());
//			row.createCell(6).setCellValue(currentWeek);
//			row.createCell(7).setCellValue(o.getRemainWeek()==null?"":o.getRemainWeek()+"");
//			row.createCell(8).setCellValue(o.getRegDate()==null?"":o.getRegDate().format(ymdhms));
		
		}
		
	}
	public static MemberTeamsExcelExporter builder() {
		return new MemberTeamsExcelExporter();
	}
}
