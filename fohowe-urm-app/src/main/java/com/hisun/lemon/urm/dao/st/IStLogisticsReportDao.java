package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.st.StLogisticsQueryReqDTO;
import com.hisun.lemon.urm.entity.st.StLogisticsReport;

@Mapper
public interface IStLogisticsReportDao  extends BaseDao<StLogisticsReport> {

    void callInsertReport(@Param("wWeek") int wWeek);

    List<StLogisticsReport> queryList(StLogisticsQueryReqDTO report);
    List<String> getLogisticsNos();

    int updateRecentUseYear(@Param("logisticsNo")String logisticsNo);
}