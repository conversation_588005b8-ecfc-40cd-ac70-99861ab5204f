package com.hisun.lemon.urm.uitls.pdf;

import java.io.BufferedInputStream;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring4.SpringTemplateEngine;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import com.github.jhonnymertz.wkhtmltopdf.wrapper.Pdf;
import com.github.jhonnymertz.wkhtmltopdf.wrapper.configurations.WrapperConfig;
import com.hisun.lemon.framework.utils.LemonUtils; 


public class PdfUtil {
	private static final Logger logger = LoggerFactory.getLogger(PdfUtil.class);
    /**
     * @param os
     * @param pdfFileName  pdf文件名称(不包含pdf后缀)
     * @param templateName 模板名称
     * @param variables    模板变量
     */
    public void buildPdf(OutputStream os, String pdfFileName, String templateName, Map<String, Object> variables) throws Exception {
    	String rootDir = LemonUtils.getProperty("urm.filePath.dir")+File.separator+"pdftemplates";
    	//构造模板引擎
        ClassLoaderTemplateResolver resolver = new ClassLoaderTemplateResolver();
        resolver.setPrefix("templates/");//模板所在目录，相对于当前classloader的classpath。
        resolver.setSuffix(".html");//模板文件后缀
        resolver.setCharacterEncoding(StandardCharsets.UTF_8.name());
        SpringTemplateEngine templateEngine = new SpringTemplateEngine();
        templateEngine.setTemplateResolver(resolver);
        //构造上下文(Model)
        Context context = new Context();
        context.setVariable("templateName", templateName);
        context.setVariable("pdfFileName", pdfFileName);
        context.setVariables(variables);
        //渲染模板
        String example = templateEngine.process(templateName, context);
        //logger.info(example);
        logger.info(templateName);
        String executable = WrapperConfig.findExecutable();
        Pdf pdf = new Pdf(new WrapperConfig(executable));
        pdf.addPageFromString(example);
        Random random = new Random();
        String filename = System.currentTimeMillis() + random.nextInt(1000) + ".pdf";
        pdf.saveAs(rootDir+File.separator+filename);
        File file = new File(rootDir+File.separator+filename);
        if (file.exists()) {
            byte[] buffer = new byte[1024];
            FileInputStream fis = new FileInputStream(file);
            BufferedInputStream bis = new BufferedInputStream(fis);
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
            bis.close();
        }
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(os,"UTF-8"));
        //PrintWriter writer = new PrintWriter(new OutputStreamWriter(os));
        writer.flush();
        writer.close();

    }
} 