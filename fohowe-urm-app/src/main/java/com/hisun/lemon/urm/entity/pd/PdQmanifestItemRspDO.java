package com.hisun.lemon.urm.entity.pd;

import java.math.BigDecimal;
import java.time.LocalDateTime;


public class PdQmanifestItemRspDO {
    /**
     * @Fields id 01.ID ID
     */
    private Integer id;
    /**
     * @Fields receiptno 02.调拨单号 ReceiptNO
     */
    private String receiptno;
    /**
     * @Fields goodscode 商品代码
     */
    private String goodscode;
    /**
     * @Fields sendqty 07.发货量 SendQty
     */
    private Integer sendqty;
    /**
     * @Fields recQty 已收货数量
     */
    private Integer recQty;
    /**
     * @Fields boxqty 箱数（手动填，没有逻辑意义）
     */
    private Integer boxqty;
    /**
     * @Fields standardPrice 标准F$
     */
    private BigDecimal standardPrice;
    /**
     * @Fields standardFv 标准FV
     */
    private BigDecimal standardFv;
    /**
     * @Fields standardFv 商品名称
     */
    private String goodsName;
    private String currencyName;
    
    /**
     * 供货方
     */
    private String fromstore;
    /**
     * 调拨申请分公司
     */
    private String reqstore;
    /**
     * 入库单日期
     */
    private LocalDateTime inHouseDate;
    /**
     * 发货日期
     */
    private LocalDateTime senddate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getGoodscode() {
        return goodscode;
    }

    public void setGoodscode(String goodscode) {
        this.goodscode = goodscode;
    }

    public Integer getSendqty() {
        return sendqty;
    }

    public void setSendqty(Integer sendqty) {
        this.sendqty = sendqty;
    }

    public Integer getRecQty() {
        return recQty;
    }

    public void setRecQty(Integer recQty) {
        this.recQty = recQty;
    }

    public Integer getBoxqty() {
        return boxqty;
    }

    public void setBoxqty(Integer boxqty) {
        this.boxqty = boxqty;
    }

    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public BigDecimal getStandardFv() {
        return standardFv;
    }

    public void setStandardFv(BigDecimal standardFv) {
        this.standardFv = standardFv;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

	public String getFromstore() {
		return fromstore;
	}

	public void setFromstore(String fromstore) {
		this.fromstore = fromstore;
	}

	public String getReqstore() {
		return reqstore;
	}

	public void setReqstore(String reqstore) {
		this.reqstore = reqstore;
	}

	public LocalDateTime getInHouseDate() {
		return inHouseDate;
	}

	public void setInHouseDate(LocalDateTime inHouseDate) {
		this.inHouseDate = inHouseDate;
	}

	public LocalDateTime getSenddate() {
		return senddate;
	}

	public void setSenddate(LocalDateTime senddate) {
		this.senddate = senddate;
	}
    
}
