/*
 * @ClassName IPdQmanifestRecitemsDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-08 10:23:01
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdQmanifestItemRspDO;
import com.hisun.lemon.urm.entity.pd.PdQmanifestRecitemsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IPdQmanifestRecitemsDao extends BaseDao<PdQmanifestRecitemsDO> {
    List<PdQmanifestItemRspDO> queryRecGoods(Integer id);

    int insertBatch(List<PdQmanifestRecitemsDO> recItems);
    
    int deleteBatch(List<Integer> idList);

    int updateToStore(@Param("id") Integer id, @Param("curCompany") String curCompany);
}