package com.hisun.lemon.urm.entity.st;

import java.math.BigDecimal;
import java.util.List;

import com.hisun.lemon.framework.data.BaseDO;

public class StLogisticsReport extends BaseDO{
    private Long id;

    private String logisticsNo;

    private String logisticsName;

    private String companyCode;

    private String goodsCode;

    private String goodsName;

    private BigDecimal standardPrice;

    private BigDecimal validQty;

    private BigDecimal donWay;

    private BigDecimal totalSales;

    private BigDecimal totalSales3;

    private BigDecimal averageSales;

    private BigDecimal averageSales3;

    private BigDecimal expectWeek;

    private BigDecimal donWayHead;

    private BigDecimal butSalesWeek;

    private BigDecimal orderNum;

    private BigDecimal totalSalesWeek;

    private BigDecimal butSalesYear;

    private BigDecimal hasSales;

    private BigDecimal preSales;

    private Integer recentUseYear;
    
    private Integer recentUseMonth3;

    List<String> goodsList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo == null ? null : logisticsNo.trim();
    }

    public String getLogisticsName() {
        return logisticsName;
    }

    public void setLogisticsName(String logisticsName) {
        this.logisticsName = logisticsName == null ? null : logisticsName.trim();
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode == null ? null : goodsCode.trim();
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public BigDecimal getValidQty() {
        return validQty;
    }

    public void setValidQty(BigDecimal validQty) {
        this.validQty = validQty;
    }

    public BigDecimal getDonWay() {
        return donWay;
    }

    public void setDonWay(BigDecimal donWay) {
        this.donWay = donWay;
    }

    public BigDecimal getTotalSales() {
        return totalSales;
    }

    public void setTotalSales(BigDecimal totalSales) {
        this.totalSales = totalSales;
    }

    public BigDecimal getTotalSales3() {
        return totalSales3;
    }

    public void setTotalSales3(BigDecimal totalSales3) {
        this.totalSales3 = totalSales3;
    }

    public BigDecimal getAverageSales() {
        return averageSales;
    }

    public void setAverageSales(BigDecimal averageSales) {
        this.averageSales = averageSales;
    }

    public BigDecimal getAverageSales3() {
        return averageSales3;
    }

    public void setAverageSales3(BigDecimal averageSales3) {
        this.averageSales3 = averageSales3;
    }

    public BigDecimal getExpectWeek() {
        return expectWeek;
    }

    public void setExpectWeek(BigDecimal expectWeek) {
        this.expectWeek = expectWeek;
    }

    public BigDecimal getDonWayHead() {
        return donWayHead;
    }

    public void setDonWayHead(BigDecimal donWayHead) {
        this.donWayHead = donWayHead;
    }

    public BigDecimal getButSalesWeek() {
        return butSalesWeek;
    }

    public void setButSalesWeek(BigDecimal butSalesWeek) {
        this.butSalesWeek = butSalesWeek;
    }

    public BigDecimal getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(BigDecimal orderNum) {
        this.orderNum = orderNum;
    }

    public BigDecimal getTotalSalesWeek() {
        return totalSalesWeek;
    }

    public void setTotalSalesWeek(BigDecimal totalSalesWeek) {
        this.totalSalesWeek = totalSalesWeek;
    }

    public BigDecimal getButSalesYear() {
        return butSalesYear;
    }

    public void setButSalesYear(BigDecimal butSalesYear) {
        this.butSalesYear = butSalesYear;
    }

    public BigDecimal getHasSales() {
        return hasSales;
    }

    public void setHasSales(BigDecimal hasSales) {
        this.hasSales = hasSales;
    }

    public BigDecimal getPreSales() {
        return preSales;
    }

    public void setPreSales(BigDecimal preSales) {
        this.preSales = preSales;
    }

	public List<String> getGoodsList() {
		return goodsList;
	}

	public void setGoodsList(List<String> goodsList) {
		this.goodsList = goodsList;
	}

    public Integer getRecentUseYear() {
        return recentUseYear;
    }

    public void setRecentUseYear(Integer recentUseYear) {
        this.recentUseYear = recentUseYear;
    }

	public Integer getRecentUseMonth3() {
		return recentUseMonth3;
	}

	public void setRecentUseMonth3(Integer recentUseMonth3) {
		this.recentUseMonth3 = recentUseMonth3;
	}
    
    
}