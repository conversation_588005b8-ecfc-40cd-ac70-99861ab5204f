/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: EntityGranExcel.java 
 * @Prject: fohowe-web-ec
 * @Package: com.hisun.lemon.fohowe.ec.excel 
 * @author: liubao   
 * @date: 2017年12月6日 上午10:46:28 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.excel.st;

import java.util.HashMap;
import java.util.Map;

import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.excel.ExcelExportFactory;
import com.hisun.lemon.fohowe.ec.dto.EntityGrantExcelErrorRes;
import com.hisun.lemon.urm.common.MsgCd;

/**
 * @ClassName: SendInfoGrantExcel 
 * @author: tian
 * @date: 2018年3月1日 下午5:54:21 
 * @param <T>
 */
public class SendInfoGrantExcel<T extends Map<String, String>> extends ExcelExportFactory<T> {

    private static final Logger logger = LoggerFactory.getLogger(SendInfoGrantExcel.class);

    @Override
    public Object parseExcel(XSSFSheet sheet) throws LemonException {
        //缓存经销商或代办处
        Map<String, String> sendInfoMap = new HashMap<>();
        logger.info("实物导入开始============>");
        // 正文内容应该从第二行开始,第一、二行为表头的标题
        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            XSSFRow hssfRow = sheet.getRow(rowNum);
            if (hssfRow != null) {
                EntityGrantExcelErrorRes errorRes = new EntityGrantExcelErrorRes();
                errorRes.setIdNo("第" + rowNum + "行");

                // 订单编号
                String requestNo = getCellValue(hssfRow.getCell(0));
                // 物流单号
                String dcsendNo = getCellValue(hssfRow.getCell(1));
                if (JudgeUtils.isBlankAny(requestNo, dcsendNo)) {
                    LemonException.throwBusinessException(MsgCd.ID_IS_NOT_EMPTY.getMsgCd());
                }
                sendInfoMap.put(requestNo, dcsendNo);
            }
        }

        logger.info("实物导入结束============>");

        return sendInfoMap;
    }

    private String getCellValue(XSSFCell cell) {
        Object obj = "";
        switch (cell.getCellTypeEnum()) {
        case STRING:
            obj = cell.getStringCellValue();
            break;
        case NUMERIC:
            obj = cell.getNumericCellValue();
            break;
        case FORMULA:
            obj = cell.getCellFormula();
            break;
        case ERROR:
            obj = cell.getErrorCellValue();
            break;
        case BOOLEAN:
            obj = cell.getBooleanCellValue();
            break;
        case BLANK:
            break;
		case _NONE:
			break;
		default:
			break;
        }
        return String.valueOf(obj).trim();
    }
}
