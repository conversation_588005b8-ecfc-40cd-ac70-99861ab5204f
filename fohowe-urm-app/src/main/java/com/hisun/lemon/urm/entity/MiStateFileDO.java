/*
 * @ClassName MiStateFileDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class MiStateFileDO extends BaseDO {
    /**
     * @Fields fileId 
     */
    private Long fileId;
    /**
     * @Fields adjustCode 调整单编号 ADJUST_CODE
     */
    private String adjustCode;
    /**
     * @Fields fileName 文件名称 FILE_NAME
     */
    private String fileName;
    /**
     * @Fields originalName 原文件名称 ORIGINAL_NAME
     */
    private String originalName;
    /**
     * @Fields fileUrl 文件地址 FILE_URL
     */
    private String fileUrl;
    /**
     * @Fields fileSize 文件大小 FILE_SIZE
     */
    private String fileSize;
    /**
     * @Fields suffixName 后缀名 SUFFIX_NAME
     */
    private String suffixName;
    /**
     * @Fields createrCode 上传人编号 CREATER_CODE
     */
    private String createrCode;

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public String getAdjustCode() {
        return adjustCode;
    }

    public void setAdjustCode(String adjustCode) {
        this.adjustCode = adjustCode;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getSuffixName() {
        return suffixName;
    }

    public void setSuffixName(String suffixName) {
        this.suffixName = suffixName;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }
}