/*
 * @ClassName IFiFgcDepositDao
 * @Description 
 * @version 1.0
 * @Date 2019-10-30 14:43:27
 */
package com.hisun.lemon.urm.dao.fi;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.bo.FgcDepositRspBO;
import com.hisun.lemon.urm.dto.fi.fgc.FgcReportReqDTO;
import com.hisun.lemon.urm.entity.fi.FiFgcDepositDO;
import com.hisun.lemon.urm.entity.fi.FiFgcReportDO;

@Mapper
public interface IFiFgcDepositDao extends BaseDao<FiFgcDepositDO> {

    List<FiFgcDepositDO> findDepositTheEnd(FiFgcDepositDO fiFgcDepositDO);

    FiFgcDepositDO findDepositLock(FiFgcDepositDO fiFgcDepositDO);

    public List<FiFgcDepositDO> finds(FiFgcDepositDO fiFgcDepositDO);

    public FgcDepositRspBO fingTotalResult(FiFgcDepositDO fiFgcDepositDO);
    
    public List<FiFgcReportDO> getFiFgcReportByCompany(FgcReportReqDTO fgcReportReqDTO); 
    
    public List<FiFgcReportDO> getFiFgcReportByAgent(FgcReportReqDTO fgcReportReqDTO); 
    
    public List<FiFgcReportDO> getFiFgcReportByMem(FgcReportReqDTO fgcReportReqDTO); 
    
    public List<FiFgcReportDO> InsertFiFgcReportByMem(FgcReportReqDTO fgcReportReqDTO); 
}