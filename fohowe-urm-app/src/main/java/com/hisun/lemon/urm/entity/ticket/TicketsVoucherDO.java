package com.hisun.lemon.urm.entity.ticket;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class TicketsVoucherDO extends BaseDO {
    private Long id;

    private String ticketsNo;
    
    private Integer serial;

    private BigDecimal amount;

    private String ticketsPwd;
    
    private Integer ticketsStatus;

    private String companyCode;

    private String departmentCode;

    private String meetingNo;

    private String teacher;

    private Integer startWeek;

    private Integer endWeek;

    private Integer vcStatus;

    private String orderNo;
    
    private String memberNo;

    private String memo;

    private String remark;

    private LocalDateTime fiCheckTime;

    private String fiCheckCode;

    private String ficheckeStatus;

    private LocalDateTime ficheckeTime;

    private String ficheckeMemo;
    
    private String ficheckerCode;

    private String createrCode;
    
    private Integer finStatus;

    private BigDecimal localFinMoney;

    private Integer finNum;
    
    private Integer periodWeek;
    
    private String orderCompany;
    
    private String orderAgent;
    
    private String meetingTitle;
    private String currency;
    private BigDecimal localAmount;

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getLocalAmount() {
        return localAmount;
    }

    public void setLocalAmount(BigDecimal localAmount) {
        this.localAmount = localAmount;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTicketsNo() {
        return ticketsNo;
    }

    public void setTicketsNo(String ticketsNo) {
        this.ticketsNo = ticketsNo == null ? null : ticketsNo.trim();
    }

    public Integer getSerial() {
		return serial;
	}

	public void setSerial(Integer serial) {
		this.serial = serial;
	}

	public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTicketsPwd() {
        return ticketsPwd;
    }

    public void setTicketsPwd(String ticketsPwd) {
        this.ticketsPwd = ticketsPwd == null ? null : ticketsPwd.trim();
    }

    public Integer getTicketsStatus() {
		return ticketsStatus;
	}

	public void setTicketsStatus(Integer ticketsStatus) {
		this.ticketsStatus = ticketsStatus;
	}

	public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getMeetingNo() {
        return meetingNo;
    }

    public void setMeetingNo(String meetingNo) {
        this.meetingNo = meetingNo == null ? null : meetingNo.trim();
    }

    public String getTeacher() {
        return teacher;
    }

    public void setTeacher(String teacher) {
        this.teacher = teacher == null ? null : teacher.trim();
    }

    public Integer getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(Integer startWeek) {
        this.startWeek = startWeek;
    }

    public Integer getEndWeek() {
        return endWeek;
    }

    public void setEndWeek(Integer endWeek) {
        this.endWeek = endWeek;
    }

    public Integer getVcStatus() {
        return vcStatus;
    }

    public void setVcStatus(Integer vcStatus) {
        this.vcStatus = vcStatus;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public LocalDateTime getFiCheckTime() {
        return fiCheckTime;
    }

    public void setFiCheckTime(LocalDateTime fiCheckTime) {
        this.fiCheckTime = fiCheckTime;
    }

    public String getFiCheckCode() {
        return fiCheckCode;
    }

    public void setFiCheckCode(String fiCheckCode) {
        this.fiCheckCode = fiCheckCode == null ? null : fiCheckCode.trim();
    }

    public String getFicheckeStatus() {
        return ficheckeStatus;
    }

    public void setFicheckeStatus(String ficheckeStatus) {
        this.ficheckeStatus = ficheckeStatus == null ? null : ficheckeStatus.trim();
    }

    public LocalDateTime getFicheckeTime() {
        return ficheckeTime;
    }

    public void setFicheckeTime(LocalDateTime ficheckeTime) {
        this.ficheckeTime = ficheckeTime;
    }

    public String getFicheckeMemo() {
		return ficheckeMemo;
	}

	public void setFicheckeMemo(String ficheckeMemo) {
		this.ficheckeMemo = ficheckeMemo;
	}

	public String getFicheckerCode() {
        return ficheckerCode;
    }

    public void setFicheckerCode(String ficheckerCode) {
        this.ficheckerCode = ficheckerCode == null ? null : ficheckerCode.trim();
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode == null ? null : createrCode.trim();
    }

	public Integer getFinStatus() {
		return finStatus;
	}

	public void setFinStatus(Integer finStatus) {
		this.finStatus = finStatus;
	}

	public BigDecimal getLocalFinMoney() {
		return localFinMoney;
	}

	public void setLocalFinMoney(BigDecimal localFinMoney) {
		this.localFinMoney = localFinMoney;
	}

	public Integer getFinNum() {
		return finNum;
	}

	public void setFinNum(Integer finNum) {
		this.finNum = finNum;
	}

	public Integer getPeriodWeek() {
		return periodWeek;
	}

	public void setPeriodWeek(Integer periodWeek) {
		this.periodWeek = periodWeek;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getOrderCompany() {
		return orderCompany;
	}

	public void setOrderCompany(String orderCompany) {
		this.orderCompany = orderCompany;
	}

	public String getOrderAgent() {
		return orderAgent;
	}

	public void setOrderAgent(String orderAgent) {
		this.orderAgent = orderAgent;
	}

	public String getMeetingTitle() {
		return meetingTitle;
	}

	public void setMeetingTitle(String meetingTitle) {
		this.meetingTitle = meetingTitle;
	}

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }
}