package com.hisun.lemon.urm.entity.fi;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.BaseDO;


public class FiFgcReportDO extends BaseDO {
	//期次 按范围选择
	private String startWeek;
	private String endWeek;
	//分公司
	private String companyCode;
	//代办处
	private String agentNo;
	//经销商
	private String memberNo;
	//期初汇率 所选期次startWeek第一天汇率
	private BigDecimal initialRate;
	//期初（个）所选期次之前的FGC余额
	private BigDecimal initialFgcNum;
	//本期购入（个）所选期购买的FGC数量
	private BigDecimal buyFgcNum;
	//购入价 平均值=汇总购买总金额/(本期购入（个）)
	private BigDecimal buyFgcPrice;
	//购入总额 所选期购买的总额
	private BigDecimal buyFgcAmount;
	//本期出售  所选期出售的FGC数量
	private BigDecimal sellFgcNum;
	//卖出价 平均值=汇总购买总金额/(本期出售)
	private BigDecimal sellFgcPrice;
	//卖出总额  所选期出售的总额
	private BigDecimal sellFgcAmount;
	//可用数额（个） 期初（个）+本期购入（个）+定存到期利息-出售	
	private BigDecimal avaFgcNum;
	//可用余额  可用数额（个）*卖出价
	private BigDecimal avaFgcAmount;
	//定存转入（个） 所选期定存中的数量 
	private BigDecimal depInFgcNum;
	//定存转入金额  汇总所选期定存中的总金额
	private BigDecimal depInFgcAmount;
	//定存转出（个）本期定存到期总FGC个数+定存取消个数
	private BigDecimal depOutFgcNum;
	//利息（个） 汇总所选期定存到期利息个数
	private BigDecimal depOutFgcInterest;
	//定存转出金额  定存转出（个）*卖出价
	private BigDecimal depOutFgcAmount;
	//转入十年定存（个）汇总所选期十年定存的数量
	private BigDecimal depTenFgcNum;
	//转入十年定存金额  汇总所选期十年定存中的总金额
	private BigDecimal depTenFgcAmount;
	//十年定存取消（个）汇总所选期十年定存取消的数量
	private BigDecimal depTenCancelFgcNum;
	//十年定存取消金额  汇总所选期十年定存中取消的总金额
	private BigDecimal depTenCancelFgcAmount;
	//实际可用数量（个）期初个数+本期买入-本期卖出-转入十年定存（不要把转入普通定存算进去）+十年定存取消
	private BigDecimal actFgcNum;
	//期末汇率 所选期次最后一天汇率
	private BigDecimal closingRate;
	//实际可用余额   实际可用数量（个）*卖出价
	private BigDecimal actFgcAmount;
	
	public String getStartWeek() {
		return startWeek;
	}
	public void setStartWeek(String startWeek) {
		this.startWeek = startWeek;
	}
	public String getEndWeek() {
		return endWeek;
	}
	public void setEndWeek(String endWeek) {
		this.endWeek = endWeek;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getAgentNo() {
		return agentNo;
	}
	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}
	public String getMemberNo() {
		return memberNo;
	}
	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}
	public BigDecimal getInitialRate() {
		return initialRate;
	}
	public void setInitialRate(BigDecimal initialRate) {
		this.initialRate = initialRate;
	}
	public BigDecimal getInitialFgcNum() {
		return initialFgcNum;
	}
	public void setInitialFgcNum(BigDecimal initialFgcNum) {
		this.initialFgcNum = initialFgcNum;
	}
	public BigDecimal getBuyFgcNum() {
		return buyFgcNum;
	}
	public void setBuyFgcNum(BigDecimal buyFgcNum) {
		this.buyFgcNum = buyFgcNum;
	}
	public BigDecimal getBuyFgcPrice() {
		return buyFgcPrice;
	}
	public void setBuyFgcPrice(BigDecimal buyFgcPrice) {
		this.buyFgcPrice = buyFgcPrice;
	}
	public BigDecimal getBuyFgcAmount() {
		return buyFgcAmount;
	}
	public void setBuyFgcAmount(BigDecimal buyFgcAmount) {
		this.buyFgcAmount = buyFgcAmount;
	}
	public BigDecimal getSellFgcNum() {
		return sellFgcNum;
	}
	public void setSellFgcNum(BigDecimal sellFgcNum) {
		this.sellFgcNum = sellFgcNum;
	}
	public BigDecimal getSellFgcPrice() {
		return sellFgcPrice;
	}
	public void setSellFgcPrice(BigDecimal sellFgcPrice) {
		this.sellFgcPrice = sellFgcPrice;
	}
	public BigDecimal getSellFgcAmount() {
		return sellFgcAmount;
	}
	public void setSellFgcAmount(BigDecimal sellFgcAmount) {
		this.sellFgcAmount = sellFgcAmount;
	}
	public BigDecimal getAvaFgcNum() {
		return avaFgcNum;
	}
	public void setAvaFgcNum(BigDecimal avaFgcNum) {
		this.avaFgcNum = avaFgcNum;
	}
	public BigDecimal getAvaFgcAmount() {
		return avaFgcAmount;
	}
	public void setAvaFgcAmount(BigDecimal avaFgcAmount) {
		this.avaFgcAmount = avaFgcAmount;
	}
	public BigDecimal getDepInFgcNum() {
		return depInFgcNum;
	}
	public void setDepInFgcNum(BigDecimal depInFgcNum) {
		this.depInFgcNum = depInFgcNum;
	}
	public BigDecimal getDepInFgcAmount() {
		return depInFgcAmount;
	}
	public void setDepInFgcAmount(BigDecimal depInFgcAmount) {
		this.depInFgcAmount = depInFgcAmount;
	}
	public BigDecimal getDepOutFgcNum() {
		return depOutFgcNum;
	}
	public void setDepOutFgcNum(BigDecimal depOutFgcNum) {
		this.depOutFgcNum = depOutFgcNum;
	}
	public BigDecimal getDepOutFgcInterest() {
		return depOutFgcInterest;
	}
	public void setDepOutFgcInterest(BigDecimal depOutFgcInterest) {
		this.depOutFgcInterest = depOutFgcInterest;
	}
	public BigDecimal getDepOutFgcAmount() {
		return depOutFgcAmount;
	}
	public void setDepOutFgcAmount(BigDecimal depOutFgcAmount) {
		this.depOutFgcAmount = depOutFgcAmount;
	}
	public BigDecimal getDepTenFgcNum() {
		return depTenFgcNum;
	}
	public void setDepTenFgcNum(BigDecimal depTenFgcNum) {
		this.depTenFgcNum = depTenFgcNum;
	}
	public BigDecimal getDepTenFgcAmount() {
		return depTenFgcAmount;
	}
	public void setDepTenFgcAmount(BigDecimal depTenFgcAmount) {
		this.depTenFgcAmount = depTenFgcAmount;
	}
	public BigDecimal getDepTenCancelFgcNum() {
		return depTenCancelFgcNum;
	}
	public void setDepTenCancelFgcNum(BigDecimal depTenCancelFgcNum) {
		this.depTenCancelFgcNum = depTenCancelFgcNum;
	}
	public BigDecimal getDepTenCancelFgcAmount() {
		return depTenCancelFgcAmount;
	}
	public void setDepTenCancelFgcAmount(BigDecimal depTenCancelFgcAmount) {
		this.depTenCancelFgcAmount = depTenCancelFgcAmount;
	}
	public BigDecimal getActFgcNum() {
		return actFgcNum;
	}
	public void setActFgcNum(BigDecimal actFgcNum) {
		this.actFgcNum = actFgcNum;
	}
	public BigDecimal getClosingRate() {
		return closingRate;
	}
	public void setClosingRate(BigDecimal closingRate) {
		this.closingRate = closingRate;
	}
	public BigDecimal getActFgcAmount() {
		return actFgcAmount;
	}
	public void setActFgcAmount(BigDecimal actFgcAmount) {
		this.actFgcAmount = actFgcAmount;
	}
	
}