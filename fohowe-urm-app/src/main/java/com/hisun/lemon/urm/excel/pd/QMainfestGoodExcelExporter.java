package com.hisun.lemon.urm.excel.pd;



import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.pd.PdManifestGoodQueryRspDTO;
import com.hisun.lemon.urm.dto.pd.PdQmanifestGoodPageRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class QMainfestGoodExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="成品发运单(商品)";
		String[] colNames=new String[] {"订单编号","成品分货单","商品编号","商品名称","供货方",
				"调拨申请分公司","入库单日期","发货日期","单价","分值","数量","单品金额小计","单品分值小计","币种","批号","数量"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		PdQmanifestGoodPageRspDTO vo = (PdQmanifestGoodPageRspDTO)obj;
		List<PdManifestGoodQueryRspDTO> dataList=vo.getQmanifestGoodRspList();
		for(PdManifestGoodQueryRspDTO o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getReceiptno());
			row.createCell(index++).setCellValue(o.getSendNo());
			row.createCell(index++).setCellValue(o.getGoodsCode());
			row.createCell(index++).setCellValue(o.getGoodsName());
			row.createCell(index++).setCellValue(o.getFromstore());
			row.createCell(index++).setCellValue(o.getReqstore());
			row.createCell(index++).setCellValue(o.getInHouseDate()==null?"":o.getInHouseDate().format(ymdhms));
			row.createCell(index++).setCellValue(o.getSenddate()==null?"":o.getSenddate().format(ymdhms));
			row.createCell(index++).setCellValue(o.getStandardPrice()+"");
			row.createCell(index++).setCellValue(o.getStandardFv()+"");
			row.createCell(index++).setCellValue(o.getSendqty());
			row.createCell(index++).setCellValue(o.getTotalCost()+"");
			row.createCell(index++).setCellValue(o.getTotalPv()+"");
			row.createCell(index++).setCellValue(o.getCurrencyCode()==null?"":o.getCurrencyCode());
			row.createCell(index++).setCellValue(o.getNo()==null?"":o.getNo());
			row.createCell(index++).setCellValue(o.getBoxqty()==null?"":o.getBoxqty()+"");
		}
		
	}
	public static QMainfestGoodExcelExporter builder() {
		return new QMainfestGoodExcelExporter();
	}
}
