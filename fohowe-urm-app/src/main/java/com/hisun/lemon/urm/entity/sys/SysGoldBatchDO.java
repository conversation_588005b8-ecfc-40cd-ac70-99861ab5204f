/*
 * @ClassName SysGoldBatchDO
 * @Description 
 * @version 1.0
 * @Date 2019-12-02 10:17:44
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class SysGoldBatchDO extends BaseDO {
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields batchNo 批次号，yyyymmdd
     */
    private String batchNo;
    /**
     * @Fields price 当日黄金单价
     */
    private BigDecimal price;
    /**
     * @Fields remark 备注 
     */
    private String remark;
    /**
     * @Fields remark01 冗余字段01
     */
    private String remark01;
    /**
     * @Fields remark02 冗余字段02
     */
    private String remark02;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark01() {
        return remark01;
    }

    public void setRemark01(String remark01) {
        this.remark01 = remark01;
    }

    public String getRemark02() {
        return remark02;
    }

    public void setRemark02(String remark02) {
        this.remark02 = remark02;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
}