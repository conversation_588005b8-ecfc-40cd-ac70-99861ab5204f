/*
 * @ClassName IPdSendInfoDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 16:19:40
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdSendInfoDO;
import com.hisun.lemon.urm.service.pd.bo.SendInfoCntQueryBO;
import com.hisun.lemon.urm.service.pd.bo.SendInfoReportBO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IPdSendInfoReportDao extends BaseDao<PdSendInfoDO> {

    /** 
     * @Title: getPageList 
     * @param areaCode
     * @param companyCode
     * @param orderCompanyCode
     * @param receiptNo
     * @param requestNo
     * @param memberNo
     * @param agentNo
     * @param comboRedeiptNo
     * @param sendStatus
     * @param recStatus
     * @param isSplit
     * @param isMergeHead
     * @param orderType
     * @param orderStatus
     * @param dateType
     * @param beginDate
     * @param endDate
     * @param isMall
     * @param recType
     * @param dcsendNo 
     * @param biOrderNo 
     * @return
     * @return: List<T>
     */
    List<PdSendInfoDO> getPageList(@Param("areaCode") String areaCode, @Param("companyCode") String companyCode,
            @Param("orderCompanyCode") String orderCompanyCode, @Param("receiptNo") String receiptNo,
            @Param("requestNo") String requestNo, @Param("memberNo") String memberNo, @Param("agentNo") String agentNo,
            @Param("comboReceiptNo") String comboReceiptNo, @Param("sendStatus") String sendStatus,
            @Param("recStatus") String recStatus, @Param("isSplit") String isSplit,
            @Param("isMergeHead") String isMergeHead, @Param("orderType") String orderType,
            @Param("orderStatus") String orderStatus, @Param("dateType") String dateType,
            @Param("beginDate") LocalDateTime beginDate, @Param("endDate") LocalDateTime endDate,
            @Param("isMall") String isMall, @Param("recType") String recType, @Param("dcsendNo") String dcsendNo,
            @Param("biOrderNo") String biOrderNo);

    /** 
     * @Title: get 
     * @param id
     * @return
     * @return: PdSendInfoDO
     */
    PdSendInfoDO get(@Param("id") long id);

    /** 
     * @Title: getListById 
     * @param idList
     * @return
     * @return: List<PdSendInfoDO>
     */
    List<PdSendInfoDO> getListById(@Param("idList") List<Long> idList);

    /** 
     * @Title: updateMergeHead 
     * @param orderStatus
     * @param comboReceiptNo
     * @param string 
     * @return
     * @return: int
     */
    int updateMergeHead(@Param("newStatus") String newStatus, @Param("oldStatus") String oldStatus,
            @Param("comboReceiptNo") String comboReceiptNo, @Param("dcsendNo") String dcsendNo);

    /** 
     * @Title: getMergePageList 
     * @param receiptNo
     * @param requestNo
     * @param memberNo
     * @param agentNo
     * @param comboReceiptNo
     * @param sendStatus
     * @param orderStatus
     * @param dateType
     * @param beginDate
     * @param endDate
     * @param companyCode 
     * @return
     * @return: List<T>
     */
    List<PdSendInfoDO> getMergePageList(@Param("receiptNo") String receiptNo, @Param("requestNo") String requestNo,
            @Param("memberNo") String memberNo, @Param("agentNo") String agentNo,
            @Param("comboReceiptNo") String comboReceiptNo, @Param("sendStatus") String sendStatus,
            @Param("orderStatus") String orderStatus, @Param("dateType") String dateType,
            @Param("beginDate") LocalDateTime beginDate, @Param("endDate") LocalDateTime endDate,
            @Param("isMergeHead") String isMergeHead, @Param("companyCode") String companyCode);

    /** 
     * @Title: updateById 
     * @param idList
     * @param receiver
     * @param recDate
     * @param recStatus
     * @param orderStatus 
     * @param sendStatus 
     * @return
     * @return: int
     */
    int updateById(@Param("idList") List<Long> idList, @Param("receiver") String receiver,
            @Param("recDate") LocalDateTime recDate, @Param("recStatus") String recStatus);

    /** 
     * @Title: getByRequestNo 
     * @param requestNo
     * @return
     * @return: PdSendInfoDO
     */
    List<PdSendInfoDO> getByRequestNo(@Param("requestNo") String requestNo);

    List<PdSendInfoDO> getByOrderNo(@Param("orderNo") String orderNo,
            @Param("orderStatusList") List<String> orderStatusList);

    /** 
     * @Title: getMergeList 
     * @Description: TODO
     * @param comboReceiptNo
     * @param orderStatus
     * @return
     * @return: List<PdSendInfoDO>
     */
    List<PdSendInfoDO> getMergeList(@Param("comboReceiptNo") String comboReceiptNo,
            @Param("orderStatus") String orderStatus);

    /** 
     * @Title: getByReceiptNo 
     * @param receiptNo
     * @return
     * @return: PdSendInfoDO
     */
    PdSendInfoDO getByReceiptNo(@Param("receiptNo") String receiptNo);

    /** 
     * @Title: updateComboOrder 
     * @Description: 更新已合并订单订单状态
     * @param sendInfoDO
     * @param orderStatus
     * @return
     * @return: int
     */
    int updateComboOrder(@Param("sendInfoDO") PdSendInfoDO sendInfoDO, @Param("orderStatus") String orderStatus);

    /** 
     * @Title: getByComboReceiptNo 
     * @param comboReceiptNo
     * @param code
     * @return
     * @return: PdSendInfoDO
     */
    List<PdSendInfoDO> getByComboReceiptNo(@Param("comboReceiptNo") String comboReceiptNo);

    /** 
     * @Title: getCntList 
     * @param idList
     * @param companyCode
     * @param memberNo
     * @param agentNo
     * @param orderStatus 
     * @param isMergeHead 
     * @param pageSize 
     * @return
     * @return: List<T>
     */
    List<SendInfoCntQueryBO> getCntList(@Param("idList") List<Long> idList, @Param("memberNo") String memberNo,
            @Param("agentNo") String agentNo, @Param("isMergeHead") String isMergeHead,
            @Param("orderStatus") String orderStatus, @Param("pageSize") int pageSize);

    /** 
     * @Title: updateDcSendNo 
     * @param pdSendInfoDO
     * @return
     * @return: int
     */
    int updateDcSendNo(@Param("pdSendInfoDO") PdSendInfoDO pdSendInfoDO);

    /** 
     * @Title: getByRequestList 
     * @param sendInfoMap
     * @return
     * @return: List<PdSendInfoDO>
     */
    List<PdSendInfoDO> getByRequestList(@Param("sendInfoMap") Map<String, String> sendInfoMap);

    /** 
     * @Title: getRowTranColumn 
     * @param reportBO
     * @return
     * @return: String
     */
    Map<String, String> getGoodsTotalColumn(SendInfoReportBO reportBO);

    /** 
     * @Title: findGoodsTotal 
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> findGoodsTotal(SendInfoReportBO reportBO);

    /** 
     * @Title: getGoodsSendColumn 
     * @Description: TODO
     * @param reportBO
     * @return
     * @return: String
     */
    Map<String, String> getGoodsSendColumn(SendInfoReportBO reportBO);

    /** 
     * @Title: findGoodsTotalSend 
     * @Description: TODO
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> findGoodsTotalSend(SendInfoReportBO reportBO);

    /** 
     * @Title: getSaleTotalColumn 
     * @Description: TODO
     * @param reportBO
     * @return
     * @return: Map<String,String>
     */
    Map<String, String> getSaleTotalColumn(SendInfoReportBO reportBO);

    /** 
     * @Title: findSaleTotal 
     * @Description: TODO
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> findSaleTotal(SendInfoReportBO reportBO);

	List<Map<String, Object>> findSaleTotalForProm(SendInfoReportBO reportBO);

	Map<String, String> getSaleTotalColumnForProm(SendInfoReportBO reportBO);

	Map<String, String> getGoodsTotalColumnForProm(SendInfoReportBO reportBO);

	List<Map<String, Object>> findGoodsTotalForProm(SendInfoReportBO reportBO);

	Map<String, String> getGoodsSendColumnForProm(SendInfoReportBO reportBO);

	List<Map<String, Object>> findGoodsTotalSendForProm(SendInfoReportBO reportBO);

    String findGoodsNameByGoodsCode(String goodsCode);
}