/*
 * @ClassName IFiAcBalanceItemDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceItemDTO;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceItemQueryBean;
import com.hisun.lemon.urm.dto.fi.balance.AcHistoryBalanceItemBo;
import com.hisun.lemon.urm.dto.fi.balance.AcHistoryBalanceItemDO;
import com.hisun.lemon.urm.dto.fi.balance.NetCashReportRspBean;
import com.hisun.lemon.urm.entity.FiAcBalanceItemDO;

@Mapper
public interface IFiAcBalanceItemDao extends BaseDao<FiAcBalanceItemDO> {

	List<AcBalanceItemQueryBean> getListByCondition(AcBalanceItemDTO acBalanceItemDTO);
	List<AcBalanceItemQueryBean> getListByConditionByYear(AcBalanceItemDTO acBalanceItemDTO);
	
	List<AcBalanceItemQueryBean> getListByOrderSum(AcBalanceItemDTO acBalanceItemDTO);
	
	List<AcHistoryBalanceItemDO> getAcHistoryBalanceItem(AcHistoryBalanceItemBo bo);
	Integer getPeriodWeekByDate(@Param("endCheckTime") LocalDateTime endCheckTime);
	Integer getHisWeek(@Param("wWeek") Integer wWeek);
    /**
     * 查询总页数
     * @param acBalanceItemDTO
     * @return
     */
	int getTotalCount(AcBalanceItemDTO acBalanceItemDTO);
	int getTotalCountByYear(AcBalanceItemDTO acBalanceItemDTO);
	String getMaxCreatTimeByHistory();
    int insertIntoHistory();
	List<NetCashReportRspBean> netCashReport(@Param("type")String type,@Param("bonusType")String bonusType, 
			@Param("areaCode")String areaCode,@Param("companyCode") String companyCode,@Param("agentNo") String agentNo, 
			@Param("period") Integer period,@Param("startDate") LocalDateTime startDate);
	
	Double getAmountByOrderNO(@Param("orderType")String orderType,@Param("acType")String acType,@Param("orderNo")String orderNo);
}