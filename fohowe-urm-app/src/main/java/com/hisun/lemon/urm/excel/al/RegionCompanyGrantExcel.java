/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: EntityGranExcel.java 
 * @Prject: fohowe-web-ec
 * @Package: com.hisun.lemon.fohowe.ec.excel 
 * @author: liubao   
 * @date: 2017年12月6日 上午10:46:28 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.excel.al;

import java.util.List;

import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.entity.al.AlRegionCompanyDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

/**
 * @ClassName: RegionCompanyGrantExcel 
 * @author: tian
 * @date: 2018年2月24日 下午3:29:50 
 * @param <T>
 */
public class RegionCompanyGrantExcel extends URMExcelExportFactory {

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style,Object obj,int beginRow) throws Exception {
    	List<AlRegionCompanyDO> dataList = (List<AlRegionCompanyDO>) obj;
        if (dataList != null && dataList.size() > 0) {
            for (int i = 0; i < dataList.size(); i++) {
                AlRegionCompanyDO regionCompanyDO = dataList.get(i);
                // 创建所需的行数
                XSSFRow row = sheet.createRow(i + beginRow);
                XSSFCell cell = null;

                //国家/地区编码
                cell = row.createCell(0, CellType.STRING);
                cell.setCellValue(regionCompanyDO.getRegionCode());
                cell.setCellStyle(style);

                //国家/地区名称
                cell = row.createCell(1, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(regionCompanyDO.getRegionName()) ? "" : regionCompanyDO.getRegionName());
                cell.setCellStyle(style);

                //公司编号
                cell = row.createCell(2, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(regionCompanyDO.getCompanyCode()) ? "" : regionCompanyDO.getCompanyCode());
                cell.setCellStyle(style);

                //公司名称
                cell = row.createCell(3, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(regionCompanyDO.getCompanyName()) ? "" : regionCompanyDO.getCompanyName());
                cell.setCellStyle(style);
            }
        }
    }

}
