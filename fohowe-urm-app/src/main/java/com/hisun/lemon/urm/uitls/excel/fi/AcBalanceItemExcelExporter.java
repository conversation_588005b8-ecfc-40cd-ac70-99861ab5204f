package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceItemQueryBean;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceItemVO;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcBalanceItemExcelExporter extends  URMExcelExportFactorys{
	private ILanguageService languageService;
	public AcBalanceItemExcelExporter(ILanguageService languageService) {
		this.languageService=languageService;
	}
	public AcBalanceItemExcelExporter() {
		
	}
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="账户查询";
		String[] colNames=new String[] {"所属分公司","代办处","账户类型","用户编号","交易类别",
				"存入","取出","可余额","借款余额","创建时间","备注","历史代办处","期次"};
		String multiLangFileName="common.zhang_hu_cha_xun";
		String[] multiLangColNames=new String[] {"column.toCompany",
				"column.agencyNo","column.accountType","column.memNo","column.tradeType","column.inmoney","column.outmoney"
				,"column.validBalance","column.oweAmt","column.createData","column.memo","column.oriAgencyNO","column.period"
		};
		

		Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
				
		if(result!=null) {
			colNames=(String[]) result.get("titles");
			fileName=(String) result.get("fileName");
		}
		super.exportActually(fileName, colNames, obj, response);
	}
	@Override
	public void export2(Object obj, HttpServletResponse response) {
		try {
			String fileName="账户查询";
			String[] colNames=new String[] {"所属分公司","代办处","账户类型","用户编号","交易类别",
					"存入","取出","可余额","借款余额","创建时间","备注","历史代办处","单据编号","期次"};
			String multiLangFileName="common.zhang_hu_cha_xun";
			String[] multiLangColNames=new String[] {"column.toCompany",
					"column.agencyNo","column.accountType","column.memNo","column.tradeType","column.inmoney","column.outmoney"
					,"column.validBalance","column.oweAmt","column.createData","column.memo","column.oriAgencyNO","common.dan_ju_bian_hao","column.period"
			};
			
			Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
			if(result!=null) {
				colNames=(String[]) result.get("titles");
				fileName=(String) result.get("fileName");
			}
			super.exportActually2(addData(obj),fileName,colNames,response);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		AcBalanceItemVO vo=(AcBalanceItemVO) obj;
		List<AcBalanceItemQueryBean> dataList=vo.getAcBalances();
		Map<String,String> orderTypeKV=vo.getOrderTypeKV();
		Map<String,String> acTypeKV=vo.getAcTypeKV();
		for(AcBalanceItemQueryBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getCompanyCode());
			row.createCell(1).setCellValue(o.getAgentNo());
			row.createCell(2).setCellValue(acTypeKV.get(o.getAcType()));
			row.createCell(3).setCellValue(o.getUserCode());
			row.createCell(4).setCellValue(orderTypeKV.get(o.getOrderType()));
			row.createCell(5).setCellValue(o.getInMoney()+"");
			row.createCell(6).setCellValue(o.getOutMoney()+"");
			row.createCell(7).setCellValue(o.getValidBalance());
			row.createCell(8).setCellValue(o.getOweBalance());
			row.createCell(9).setCellValue(o.getDealDate()==null?"":o.getDealDate().format(ymdhms));
			row.createCell(10).setCellValue(o.getMemo());
			if (o.getUserAgentNo() !=null || !"".equals(o.getUserAgentNo())) {
				row.createCell(11).setCellValue(o.getUserAgentNo());
			}else {
				row.createCell(11).setCellValue(o.getAgentNo());
			}
			row.createCell(12).setCellValue(o.getwWeek());
		}
		
	}
	public List<List<Object>> addData(Object obj) throws Exception {
		List<List<Object>> list = new ArrayList<List<Object>>();
		AcBalanceItemVO voObj = (AcBalanceItemVO) obj;
		List<AcBalanceItemQueryBean> dataList = (List<AcBalanceItemQueryBean>) voObj.getAcBalances();
		Map<String,String> orderTypeKV=voObj.getOrderTypeKV();
		Map<String,String> acTypeKV=voObj.getAcTypeKV();
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			AcBalanceItemQueryBean o = (AcBalanceItemQueryBean)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(o.getCompanyCode());
			data.add(o.getAgentNo());
			data.add(acTypeKV.get(o.getAcType()));
			data.add(o.getUserCode());
			data.add(orderTypeKV.get(o.getOrderType()));
			data.add(o.getInMoney()+"");
			data.add(o.getOutMoney()+"");
			data.add(o.getValidBalance());
			data.add(o.getOweBalance());
			data.add(o.getDealDate()==null?"":o.getDealDate().format(ymdhms));
			data.add(o.getMemo());
			if (o.getUserAgentNo() !=null || !"".equals(o.getUserAgentNo())) {
				data.add(o.getUserAgentNo());
			}else {
				data.add(o.getAgentNo());
			}
			data.add(o.getOrderNo());
			data.add(o.getwWeek());
			list.add(data);
		}
		return list;
	}
	public static AcBalanceItemExcelExporter builder() {
		return new AcBalanceItemExcelExporter();
	}
}
