package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.YesNoStatus;
import com.hisun.lemon.urm.dto.pd.PdQmanifestPageRspDTO;
import com.hisun.lemon.urm.dto.pd.PdQmanifestRspDTO;
import com.hisun.lemon.urm.enums.pd.QManiStatus;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class ShipOrderQueryExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "成品分货单统计列表";
        String[] colNames = new String[] { "单据编号", "订单状态", "是否发货", "是否收货", "入库单日期", "发货日期", "收货日期", "F$", "FV", "供货方",
                "调拨申请分公司", "调拨发货人", "创建人", "备注" };
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        PdQmanifestPageRspDTO vo = (PdQmanifestPageRspDTO) obj;
        if (JudgeUtils.isEmpty(vo.getQmanifestRspList())) {
            return;
        }
        List<PdQmanifestRspDTO> dataList = vo.getQmanifestRspList();
        for (PdQmanifestRspDTO o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);

            row.createCell(0).setCellValue(o.getReceiptno());// 单据编号
            row.createCell(1).setCellValue(JudgeUtils.isNull(o.getOrderstatus()) ? ""
                    : QManiStatus.getByCode(o.getOrderstatus().toString()).getName());// 订单状态
            row.createCell(2).setCellValue(JudgeUtils.isNull(o.getSendstatus()) ? ""
                    : YesNoStatus.getByCode(o.getSendstatus().toString()).getName());// 是否发货
            row.createCell(3).setCellValue(JudgeUtils.isNull(o.getRecstatus()) ? ""
                    : YesNoStatus.getByCode(o.getRecstatus().toString()).getName());// 是否收货
            row.createCell(4)
                    .setCellValue(JudgeUtils.isNull(o.getInHouseDate()) ? "" : o.getInHouseDate().format(ymdhms));// 入库单日期
            row.createCell(5).setCellValue(JudgeUtils.isNull(o.getSenddate()) ? "" : o.getSenddate().format(ymdhms));// 发货日期
            row.createCell(6).setCellValue(JudgeUtils.isNull(o.getRecdate()) ? "" : o.getRecdate().format(ymdhms));// 收货日期
            row.createCell(7).setCellValue(JudgeUtils.isNull(o.getTotalCost()) ? "" : o.getTotalCost().toString());// F$
            row.createCell(8).setCellValue(JudgeUtils.isNull(o.getTotalPv()) ? "" : o.getTotalPv().toString());// FV
            row.createCell(9).setCellValue(JudgeUtils.isNull(o.getFromstore()) ? "" : o.getFromstore());// 供货方
            row.createCell(10).setCellValue(JudgeUtils.isNull(o.getReqstore()) ? "" : o.getReqstore());// 调拨申请分公司
            row.createCell(11).setCellValue(JudgeUtils.isNull(o.getSender()) ? "" : o.getSender());// 调拨发货人
            row.createCell(12).setCellValue(JudgeUtils.isNull(o.getOrderor()) ? "" : o.getOrderor());// 创建人
            row.createCell(13).setCellValue(JudgeUtils.isNull(o.getMemo()) ? "" : o.getMemo());// 备注
        }

    }

    public static ShipOrderQueryExcelExporter builder() {
        return new ShipOrderQueryExcelExporter();
    }
}
