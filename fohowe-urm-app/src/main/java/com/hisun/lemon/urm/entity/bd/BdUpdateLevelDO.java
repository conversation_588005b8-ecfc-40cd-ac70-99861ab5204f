/*
 * @ClassName BdUpdateLevelDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-11 15:43:33
 */
package com.hisun.lemon.urm.entity.bd;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class BdUpdateLevelDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields wWeek 根据ID序号设置
     */
    private String wWeek;
    /**
     * @Fields ruleType 类型,0=调整经销商级别，1=调整经营权级别
     */
    private String ruleType;
    /**
     * @Fields rightNo 经营权编号
     */
    private String rightNo;
    /**
     * @Fields memberNo 会员编号
     */
    private String memberNo;
    /**
     * @Fields status 状态，1=审核，2=取消，3=锁定
     */
    private String status;
    /**
     * @Fields cardType 经销商级别，1=合格经销商，2=绿宝石经销商，3=蓝宝石经销商，4=钻石经销商，5=三钻石经销商，6=五钻石经销商，7=  七钻石经销商，8=凤凰大使经销商
     */
    private String cardType;
    /**
     * @Fields levelType 经营权级别，1=准经营权，2=合格经营权，3=绿宝石经营权，4=蓝宝石经营权，5=钻石经营权
            
     */
    private String levelType;
    /**
     * @Fields creatorCode 建单人
     */
    private String creatorCode;
    /**
     * @Fields checkCode 审核人
     */
    private String checkCode;
    /**
     * @Fields checkTime 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * @Fields cancelCode 取消人
     */
    private String cancelCode;
    /**
     * @Fields cancelTime 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields oldLevelType 旧的级别，新增字段lc
     */
    private String oldLevelType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getwWeek() {
        return wWeek;
    }

    public void setwWeek(String wWeek) {
        this.wWeek = wWeek;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getRightNo() {
        return rightNo;
    }

    public void setRightNo(String rightNo) {
        this.rightNo = rightNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getLevelType() {
        return levelType;
    }

    public void setLevelType(String levelType) {
        this.levelType = levelType;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getOldLevelType() {
        return oldLevelType;
    }

    public void setOldLevelType(String oldLevelType) {
        this.oldLevelType = oldLevelType;
    }
}