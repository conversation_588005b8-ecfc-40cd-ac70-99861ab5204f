package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.st.StockAnalysisLogDO;
import com.hisun.lemon.urm.dto.st.StockAnalysisReqDTO;
import com.hisun.lemon.urm.entity.st.StStockAnalysisLogDO;

@Mapper
public interface IStStockAnalysisLogDao extends BaseDao<StStockAnalysisLogDO> {
	// 获取最新前5条
	List<StStockAnalysisLogDO> getLogs(); 
	List<StStockAnalysisLogDO> getCalcLog(); 
	List<StStockAnalysisLogDO> getCalcLogByType(@Param("calcType") int calcType); 
	List<StockAnalysisLogDO> queryPage(StockAnalysisReqDTO reqDTO); 
}
