package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StStockMonthConfigDO;
import com.hisun.lemon.urm.entity.st.StStockMonthSummaryDO;

@Mapper
public interface IStStockMonthDao extends BaseDao<StStockMonthConfigDO> {
	StStockMonthConfigDO getConfig(@Param("id") Long id);
	StStockMonthConfigDO getConfigByCode(@Param("goodsCode") String goodsCode);
	int deleteConfigById(@Param("id") Long id);
	int deleteConfig(StStockMonthConfigDO configDO);
	int insertConfig(StStockMonthConfigDO configDO);
	List<StStockMonthConfigDO> getConfigList(@Param("bonusTypeList") List<String> bonusTypeList,
			@Param("areaCodeList") List<String> areaCodeList, @Param("companyList") List<String> companyList,
			@Param("goodsList") List<String> goodsList);
	
	List<StStockMonthSummaryDO> getSummaryHeadList(@Param("bonusTypeList") List<String> bonusTypeList,
			@Param("areaCodeList") List<String> areaCodeList, @Param("companyList") List<String> companyList,
			@Param("goodsList") List<String> goodsList, @Param("typeFlag") String typeFlag,
			@Param("startYear") Integer startYear,@Param("startMonth") Integer startMonth,
			@Param("endYear") Integer endYear,@Param("endMonth") Integer endMonth);
	
	
	List<StStockMonthSummaryDO> getSummaryAreaList(@Param("bonusTypeList") List<String> bonusTypeList,
			@Param("areaCodeList") List<String> areaCodeList, @Param("companyList") List<String> companyList,
			@Param("goodsList") List<String> goodsList, @Param("typeFlag") String typeFlag,
			@Param("startYear") Integer startYear,@Param("startMonth") Integer startMonth,
			@Param("endYear") Integer endYear,@Param("endMonth") Integer endMonth);
	
	List<StStockMonthSummaryDO> getSummaryCompanyList(@Param("bonusTypeList") List<String> bonusTypeList,
			@Param("areaCodeList") List<String> areaCodeList, @Param("companyList") List<String> companyList,
			@Param("goodsList") List<String> goodsList, @Param("typeFlag") String typeFlag,
			@Param("startYear") Integer startYear,@Param("startMonth") Integer startMonth,
			@Param("endYear") Integer endYear,@Param("endMonth") Integer endMonth);
	
	List<StStockMonthSummaryDO> getSummaryList(@Param("bonusTypeList") List<String> bonusTypeList,
			@Param("areaCodeList") List<String> areaCodeList, @Param("companyList") List<String> companyList,
			@Param("goodsList") List<String> goodsList, @Param("typeFlag") String typeFlag,
			@Param("startYear") Integer startYear,@Param("startMonth") Integer startMonth,
			@Param("endYear") Integer endYear,@Param("endMonth") Integer endMonth);
	
	
}
