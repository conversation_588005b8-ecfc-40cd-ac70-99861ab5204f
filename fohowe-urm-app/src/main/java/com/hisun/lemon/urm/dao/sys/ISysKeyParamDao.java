/*
 * @ClassName ISysKeyParamDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-01 19:03:28
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysKeyParamDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ISysKeyParamDao extends BaseDao<SysKeyParamDO> {

    /** 
     * @Title: getByCondition 
     * @Description: TODO
     * @param qryInKeyParamDO
     * @return
     * @return: SysKeyParamDO
     */
    SysKeyParamDO getByCondition(SysKeyParamDO qryInKeyParamDO);
}