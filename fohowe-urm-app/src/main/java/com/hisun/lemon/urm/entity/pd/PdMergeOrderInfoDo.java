package com.hisun.lemon.urm.entity.pd;

import java.math.BigDecimal;

public class PdMergeOrderInfoDo {
    private Long id;
    private String companyCode;
    private String agentNo;
    private String comboReceiptNo;
    private String biOrderNo;
    private String receiptNo;
    private String goodsCode;
    private int orderQty;
    private BigDecimal price;
    private String state;
    private String orderCompanyCode;
    private String orderType;

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getComboReceiptNo() {
        return comboReceiptNo;
    }

    public void setComboReceiptNo(String comboReceiptNo) {
        this.comboReceiptNo = comboReceiptNo;
    }

    public String getBiOrderNo() {
        return biOrderNo;
    }

    public void setBiOrderNo(String biOrderNo) {
        this.biOrderNo = biOrderNo;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public int getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(int orderQty) {
        this.orderQty = orderQty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getOrderCompanyCode() {
        return orderCompanyCode;
    }

    public void setOrderCompanyCode(String orderCompanyCode) {
        this.orderCompanyCode = orderCompanyCode;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
}
