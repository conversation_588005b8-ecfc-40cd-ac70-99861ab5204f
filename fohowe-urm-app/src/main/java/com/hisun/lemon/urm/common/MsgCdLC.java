package com.hisun.lemon.urm.common;


/**
 * 错误码
 * <AUTHOR>
 * @date 2017-11-11 15:50
 */
public enum MsgCdLC {
	
	RECINDEX_OVERFLOW("URM47001", "推荐索引超过了最大值999！"),
		
	QUERY_NO_MEMBER("URM70002", "查询无用户记录！"),
	QUERY_NO_RIGHTNO("URM70003", "查询无经营权记录！"),
	
	QUERY_NO_RECORD1("URM70004", "查询无记录！"),
	QUERY_NO_RECORD2("URM70005", "查询无记录！"),
	ENUM_TYPE_ERROR("URM70006", "枚举值错误！"),
	INSERT_RECORD_FAILED("URM70007", "新增记录失败！"),
	UPDATE_RECORD_FAILED("URM70008", "修改记录失败！"),
	DELETE_RECORD_FAILED("URM70009", "删除记录失败！"),    
	RECORD_ALREADY_EXISTS("URM70010", "记录已存在！"),    
	UPDATE_RECORD_FORBIT("URM70011", "记录不允许修改！"),   
	
	
	NETWORK_QUERY_FAILED("URM71000", "网络查询失败！"),
	
	RIGHT_ADD_FAILED("URM72000", "新增经营权失败！"),
	RIGHT_ADD_PV_ERROR("URM72001", "新增经营权失败-加入pv不符合规则！"),
	RIGHT_ADD_FULL_ERROR("URM72002", "新增经营权失败-接点权已满！"),
	RIGHT_ADD_WAY_ERROR("URM47003", "新增经营权失败-不支持的加入方式！"),
	
	
	RIGHT_ADD_LINK_ERROR1("URM72101", "操作失败-推荐人没有主经营权！"),
	RIGHT_ADD_LINK_ERROR2("URM72102", "操作失败-接点权不在推荐人网络之内！"),
	RIGHT_ADD_LINK_ERROR3("URM72103", "操作失败-接点权不在经销商网络之内！"),
	RIGHT_ADD_LINK_USA_ERROR("URM72104", "操作失败-美国制度不能与其他制度互接！"),
	RIGHT_ADD_NUM_USA_ERROR("URM72105", "操作失败-美国制度最大经营权个数超限！"),
	RIGHT_ADD_PROMT_ERROR1("URM72108", "操作失败-违反促销规则[新节点必须安置在经销商主经营权之下]"),
	RIGHT_ADD_PROMT_ERROR2("URM72109", "操作失败-违反促销规则[经销商主经营权必须没有下线]"),
	
	
	RIGHT_NOTEXIST("URM72004", "经营权不存在"),
	RIGHT_MERGE_NOEXIST1("URM72005", "被合并经销商不存在,或者该经销商没有经营权"),
	RIGHT_MERGE_NOEXIST2("URM72006", "目标经销商不存在,或者该经销商没有经营权"),
	RIGHT_MERGE_RELATION_ERROR("URM72007", "被合并经营权不在目标经营权的下属网络"),
	RIGHT_MERGE_PARAM_ERR1("URM72008", "源经销商与目标经销商不能相同"),
	RIGHT_GOLDS_EXIST("URM72009", "经销商已经存在了金点，不允许再添加"),
	RIGHT_GOLDS_PV_LOW("URM72010", "购买金点PV值不足"),
	GOLDS_EUROPE_LIMIT("URM72011", "只有欧洲用户才能添加金点"),
	RIGHT_UPD_BELONG_ERROR1("URM72014", "更改的经营权不能是主经营权"),
	RIGHT_UPD_BELONG_ERROR2("URM72015", "被修改经营权不在经销商网络之下"),
	
	
	
	
	
	
	
	RIGHT_UPD_BELONG_ERROR3("URM72016", "修改不能跨制度"),
	RELATION_ERROR1("URM72017", "该节点不属于当前代办处"),
	RELATION_ERROR2("URM72018", "该节点不属于当前用户"),
	RELATION_ERROR3("URM72019", "该节点不属于该当前公司"),
	PV_UPD_ERROR1("URM72020", "PV超过了最大值"),
	
	
	NETWORK_ERROR1("URM72100", "操作失败，网络关系不符合要求"),
	OPERATION_BUSY_FAIL("URM79005", "正在处理中,请稍后再试"),
	OPERATION_FAIL_NOCHANGE("URM79006", "请求参数没有变化"),
	
	MEMBER_NOTEXIST("URM73000", "经销商不存在"),
	
	PARAM_NOT_NULL("URM78000", "参数不能为空！"),
	PARAM_CONFIG_ERROR("URM78001", "系统参数未配置或配置错误！"),
	
	OPERATION_FAIL("URM79000", "操作失败！"),
	OPERATION_STATUS_ERROR("URM79001", "操作失败,数据状态不符合操作条件"),
	EXPORT_ERROR("URM79002", "导出失败，请稍后再试"),
	GET_PERIOD_ERROR("URM79003", "获取期数失败"),
	GET_LOGINUSER_FAIL("URM79004", "获取登陆用户信息失败！"),
	OPERATION_RATE_ERD("URM79008", "USDT汇率差大于公司允许的比例！"),
;	
    
    private String msgCd;
    private String msgInfo;
    private MsgCdLC(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }
    public String getMsgCd() {
        return msgCd;
    }
    public String getMsgInfo() {
        return msgInfo;
    }
    
   /* public static void main(String[] args) {
    	MsgCdYJJ[] array = MsgCdYJJ.values();
    	for (MsgCdYJJ e : array) {
            System.out.println(e.getMsgCd());
        }
    	for (MsgCdYJJ e : array) {
            System.out.println(e.getMsgInfo());
        }
	}*/
}
