package com.hisun.lemon.urm.entity.st;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.BaseDO;

public class StStockMonthSummaryDO extends BaseDO {

	private Long id;
	private Integer stYear;//年
	private Integer stMonth;//月
	private String areaCode;//区域
	private String companyCode;//分公司/仓库
	private String goodsCode;//商品代码
	private String goodsName;//商品名称（中文）
	private Integer quantity;//数量（可用库存、出库数量）
	private BigDecimal stockPrice;//库存平均价(标准价格+会员价格的平均单价)
	private BigDecimal totalPrice;//总货额
	private String typeFlag;//类型：1 可用，2 实际
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getStYear() {
		return stYear;
	}
	public void setStYear(Integer stYear) {
		this.stYear = stYear;
	}
	public Integer getStMonth() {
		return stMonth;
	}
	public void setStMonth(Integer stMonth) {
		this.stMonth = stMonth;
	}
	public String getAreaCode() {
		return areaCode;
	}
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	public String getGoodsCode() {
		return goodsCode;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Integer getQuantity() {
		return quantity;
	}
	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}
	public BigDecimal getStockPrice() {
		return stockPrice;
	}
	public void setStockPrice(BigDecimal stockPrice) {
		this.stockPrice = stockPrice;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}
	public String getTypeFlag() {
		return typeFlag;
	}
	public void setTypeFlag(String typeFlag) {
		this.typeFlag = typeFlag;
	}
}
