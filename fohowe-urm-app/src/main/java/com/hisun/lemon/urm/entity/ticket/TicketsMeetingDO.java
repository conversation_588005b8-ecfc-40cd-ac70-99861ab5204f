package com.hisun.lemon.urm.entity.ticket;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class TicketsMeetingDO extends BaseDO {
	private Long id;

	private String meetingNo;

	private String meetingTitle;

	private String areaCode;

	private String companyCode;

	private String city;

	private String planFirstType;
	
	private String planMeetingType;

	private LocalDateTime meetingDateStart;

	private LocalDateTime meetingDateEnd;

	private Integer planNum;

	private String planManager;

	private BigDecimal planAmount;

	private String planCurrency;

	private String planLecturer;

	private String planTeacher;

	private String planTranslator;
	
	private String planTransPart;
	
	private Integer planDealQty;

	private String actalFirstType;
	
	private String actalMeetingType;

	private LocalDateTime heldDateStart;

	private LocalDateTime heldDateEnd;

	private Integer actualNum;

	private String actualManager;

	private BigDecimal actualAmount;

	private String actualCurrency;

	private String actualLecturer;

	private String actualTeacher;

	private String actualTranslator;
	
	private String actualTransPart;

	private Integer dealQty;

	private BigDecimal deposit;

	private Integer vcStatus;

	private LocalDateTime auditTime;

	private String memo;

	private String remark;

	private String createrCode;

	private String createrName;

	private LocalDateTime createTime;

	private LocalDateTime modifyTime;

	private Integer checkeFlag;

	private String checkeCode;

	private LocalDateTime checkeTime;

	private String reCheckeCode;

	private LocalDateTime reCheckeTime;

	private Integer reCheckeFlag;
	
	private String planGuests;

    private String actualGuests;
    
    private String reviewFirstType;

    private String reviewMeetingType;

    private LocalDateTime reviewDateStart;

    private LocalDateTime reviewDateEnd;

    private Integer reviewNum;

    private String reviewManager;

    private String reviewGuests;

    private BigDecimal reviewAmount;

    private String reviewCurrency;

    private String reviewLecturer;

    private String reviewTeacher;

    private String reviewTranslator;

    private String reviewTransPart;

    private Integer reviewDealQty;

    private BigDecimal reviewDeposit;
    
    private String reviewRemark;

	private String planLecturerName;
	private String planTranslatorName;
	private String actualLecturerName;
	private String actualTranslatorName;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMeetingNo() {
		return meetingNo;
	}

	public void setMeetingNo(String meetingNo) {
		this.meetingNo = meetingNo;
	}

	public String getMeetingTitle() {
		return meetingTitle;
	}

	public void setMeetingTitle(String meetingTitle) {
		this.meetingTitle = meetingTitle;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getPlanMeetingType() {
		return planMeetingType;
	}

	public void setPlanMeetingType(String planMeetingType) {
		this.planMeetingType = planMeetingType;
	}



	public Integer getPlanNum() {
		return planNum;
	}

	public void setPlanNum(Integer planNum) {
		this.planNum = planNum;
	}

	public String getPlanManager() {
		return planManager;
	}

	public void setPlanManager(String planManager) {
		this.planManager = planManager;
	}

	public BigDecimal getPlanAmount() {
		return planAmount;
	}

	public void setPlanAmount(BigDecimal planAmount) {
		this.planAmount = planAmount;
	}

	public String getPlanCurrency() {
		return planCurrency;
	}

	public void setPlanCurrency(String planCurrency) {
		this.planCurrency = planCurrency;
	}

	public String getPlanLecturer() {
		return planLecturer;
	}

	public void setPlanLecturer(String planLecturer) {
		this.planLecturer = planLecturer;
	}

	public String getPlanTeacher() {
		return planTeacher;
	}

	public void setPlanTeacher(String planTeacher) {
		this.planTeacher = planTeacher;
	}

	public String getPlanTranslator() {
		return planTranslator;
	}

	public void setPlanTranslator(String planTranslator) {
		this.planTranslator = planTranslator;
	}

	public Integer getPlanDealQty() {
		return planDealQty;
	}

	public void setPlanDealQty(Integer planDealQty) {
		this.planDealQty = planDealQty;
	}

	public String getActalMeetingType() {
		return actalMeetingType;
	}

	public void setActalMeetingType(String actalMeetingType) {
		this.actalMeetingType = actalMeetingType;
	}

	public Integer getActualNum() {
		return actualNum;
	}

	public void setActualNum(Integer actualNum) {
		this.actualNum = actualNum;
	}

	public String getActualManager() {
		return actualManager;
	}

	public void setActualManager(String actualManager) {
		this.actualManager = actualManager;
	}

	public BigDecimal getActualAmount() {
		return actualAmount;
	}

	public void setActualAmount(BigDecimal actualAmount) {
		this.actualAmount = actualAmount;
	}

	public String getActualCurrency() {
		return actualCurrency;
	}

	public void setActualCurrency(String actualCurrency) {
		this.actualCurrency = actualCurrency;
	}

	public String getActualLecturer() {
		return actualLecturer;
	}

	public void setActualLecturer(String actualLecturer) {
		this.actualLecturer = actualLecturer;
	}

	public String getActualTeacher() {
		return actualTeacher;
	}

	public void setActualTeacher(String actualTeacher) {
		this.actualTeacher = actualTeacher;
	}

	public String getActualTranslator() {
		return actualTranslator;
	}

	public void setActualTranslator(String actualTranslator) {
		this.actualTranslator = actualTranslator;
	}

	public Integer getDealQty() {
		return dealQty;
	}

	public void setDealQty(Integer dealQty) {
		this.dealQty = dealQty;
	}

	public BigDecimal getDeposit() {
		return deposit;
	}

	public void setDeposit(BigDecimal deposit) {
		this.deposit = deposit;
	}

	public Integer getVcStatus() {
		return vcStatus;
	}

	public void setVcStatus(Integer vcStatus) {
		this.vcStatus = vcStatus;
	}



	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreaterCode() {
		return createrCode;
	}

	public void setCreaterCode(String createrCode) {
		this.createrCode = createrCode;
	}

	public String getCreaterName() {
		return createrName;
	}

	public void setCreaterName(String createrName) {
		this.createrName = createrName;
	}



	public Integer getCheckeFlag() {
		return checkeFlag;
	}

	public void setCheckeFlag(Integer checkeFlag) {
		this.checkeFlag = checkeFlag;
	}

	public String getCheckeCode() {
		return checkeCode;
	}

	public void setCheckeCode(String checkeCode) {
		this.checkeCode = checkeCode;
	}

	public LocalDateTime getMeetingDateStart() {
		return meetingDateStart;
	}

	public void setMeetingDateStart(LocalDateTime meetingDateStart) {
		this.meetingDateStart = meetingDateStart;
	}

	public LocalDateTime getMeetingDateEnd() {
		return meetingDateEnd;
	}

	public void setMeetingDateEnd(LocalDateTime meetingDateEnd) {
		this.meetingDateEnd = meetingDateEnd;
	}

	public LocalDateTime getHeldDateStart() {
		return heldDateStart;
	}

	public void setHeldDateStart(LocalDateTime heldDateStart) {
		this.heldDateStart = heldDateStart;
	}

	public LocalDateTime getHeldDateEnd() {
		return heldDateEnd;
	}

	public void setHeldDateEnd(LocalDateTime heldDateEnd) {
		this.heldDateEnd = heldDateEnd;
	}

	public LocalDateTime getAuditTime() {
		return auditTime;
	}

	public void setAuditTime(LocalDateTime auditTime) {
		this.auditTime = auditTime;
	}

	@Override
	public LocalDateTime getCreateTime() {
		return createTime;
	}

	@Override
	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	@Override
	public LocalDateTime getModifyTime() {
		return modifyTime;
	}

	@Override
	public void setModifyTime(LocalDateTime modifyTime) {
		this.modifyTime = modifyTime;
	}

	public LocalDateTime getCheckeTime() {
		return checkeTime;
	}

	public void setCheckeTime(LocalDateTime checkeTime) {
		this.checkeTime = checkeTime;
	}

	public LocalDateTime getReCheckeTime() {
		return reCheckeTime;
	}

	public void setReCheckeTime(LocalDateTime reCheckeTime) {
		this.reCheckeTime = reCheckeTime;
	}

	public String getReCheckeCode() {
		return reCheckeCode;
	}

	public void setReCheckeCode(String reCheckeCode) {
		this.reCheckeCode = reCheckeCode;
	}


	public Integer getReCheckeFlag() {
		return reCheckeFlag;
	}

	public void setReCheckeFlag(Integer reCheckeFlag) {
		this.reCheckeFlag = reCheckeFlag;
	}

	public String getPlanLecturerName() {
		return planLecturerName;
	}

	public void setPlanLecturerName(String planLecturerName) {
		this.planLecturerName = planLecturerName;
	}

	public String getPlanTranslatorName() {
		return planTranslatorName;
	}

	public void setPlanTranslatorName(String planTranslatorName) {
		this.planTranslatorName = planTranslatorName;
	}

	public String getActualLecturerName() {
		return actualLecturerName;
	}

	public void setActualLecturerName(String actualLecturerName) {
		this.actualLecturerName = actualLecturerName;
	}

	public String getActualTranslatorName() {
		return actualTranslatorName;
	}

	public void setActualTranslatorName(String actualTranslatorName) {
		this.actualTranslatorName = actualTranslatorName;
	}

	public String getPlanTransPart() {
		return planTransPart;
	}

	public void setPlanTransPart(String planTransPart) {
		this.planTransPart = planTransPart;
	}

	public String getActualTransPart() {
		return actualTransPart;
	}

	public void setActualTransPart(String actualTransPart) {
		this.actualTransPart = actualTransPart;
	}

	public String getPlanFirstType() {
		return planFirstType;
	}

	public void setPlanFirstType(String planFirstType) {
		this.planFirstType = planFirstType;
	}

	public String getActalFirstType() {
		return actalFirstType;
	}

	public void setActalFirstType(String actalFirstType) {
		this.actalFirstType = actalFirstType;
	}

	public String getPlanGuests() {
		return planGuests;
	}

	public void setPlanGuests(String planGuests) {
		this.planGuests = planGuests;
	}

	public String getActualGuests() {
		return actualGuests;
	}

	public void setActualGuests(String actualGuests) {
		this.actualGuests = actualGuests;
	}

	public String getReviewFirstType() {
		return reviewFirstType;
	}

	public void setReviewFirstType(String reviewFirstType) {
		this.reviewFirstType = reviewFirstType;
	}

	public String getReviewMeetingType() {
		return reviewMeetingType;
	}

	public void setReviewMeetingType(String reviewMeetingType) {
		this.reviewMeetingType = reviewMeetingType;
	}

	public LocalDateTime getReviewDateStart() {
		return reviewDateStart;
	}

	public void setReviewDateStart(LocalDateTime reviewDateStart) {
		this.reviewDateStart = reviewDateStart;
	}

	public LocalDateTime getReviewDateEnd() {
		return reviewDateEnd;
	}

	public void setReviewDateEnd(LocalDateTime reviewDateEnd) {
		this.reviewDateEnd = reviewDateEnd;
	}

	public Integer getReviewNum() {
		return reviewNum;
	}

	public void setReviewNum(Integer reviewNum) {
		this.reviewNum = reviewNum;
	}

	public String getReviewManager() {
		return reviewManager;
	}

	public void setReviewManager(String reviewManager) {
		this.reviewManager = reviewManager;
	}

	public String getReviewGuests() {
		return reviewGuests;
	}

	public void setReviewGuests(String reviewGuests) {
		this.reviewGuests = reviewGuests;
	}

	public BigDecimal getReviewAmount() {
		return reviewAmount;
	}

	public void setReviewAmount(BigDecimal reviewAmount) {
		this.reviewAmount = reviewAmount;
	}

	public String getReviewCurrency() {
		return reviewCurrency;
	}

	public void setReviewCurrency(String reviewCurrency) {
		this.reviewCurrency = reviewCurrency;
	}

	public String getReviewLecturer() {
		return reviewLecturer;
	}

	public void setReviewLecturer(String reviewLecturer) {
		this.reviewLecturer = reviewLecturer;
	}

	public String getReviewTeacher() {
		return reviewTeacher;
	}

	public void setReviewTeacher(String reviewTeacher) {
		this.reviewTeacher = reviewTeacher;
	}

	public String getReviewTranslator() {
		return reviewTranslator;
	}

	public void setReviewTranslator(String reviewTranslator) {
		this.reviewTranslator = reviewTranslator;
	}

	public String getReviewTransPart() {
		return reviewTransPart;
	}

	public void setReviewTransPart(String reviewTransPart) {
		this.reviewTransPart = reviewTransPart;
	}

	public Integer getReviewDealQty() {
		return reviewDealQty;
	}

	public void setReviewDealQty(Integer reviewDealQty) {
		this.reviewDealQty = reviewDealQty;
	}

	public BigDecimal getReviewDeposit() {
		return reviewDeposit;
	}

	public void setReviewDeposit(BigDecimal reviewDeposit) {
		this.reviewDeposit = reviewDeposit;
	}

	public String getReviewRemark() {
		return reviewRemark;
	}

	public void setReviewRemark(String reviewRemark) {
		this.reviewRemark = reviewRemark;
	}
	
}