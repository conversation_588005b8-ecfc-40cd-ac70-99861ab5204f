package com.hisun.lemon.urm.excel.st;

import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.bns.enums.MsgInfo;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.entity.st.StStockMonthConfigDO;

public class StockConfigExcelImport extends BaseService{
	private Logger logger = LoggerFactory.getLogger(StockConfigExcelImport.class);
	
	public final static Integer  MAX_ABLE_CNT=10000;
	
	public List<StStockMonthConfigDO> importBatchPreAdd(InputStream in) {
		List<StStockMonthConfigDO> list = new ArrayList<StStockMonthConfigDO>();
		Workbook book = null;
		try {
			book = WorkbookFactory.create(in);
		} catch (Exception e) {
			LemonException.throwBusinessException(MsgInfo.IS_NOT_FILE.getMsgCd());
		}  
    	if (book == null) {
    		LemonException.throwBusinessException(MsgInfo.IS_NOT_FILE.getMsgCd());
		}
    	Sheet xssfSheet = book.getSheetAt(0);
    	int rowNum = xssfSheet.getLastRowNum();
    	//忽略表头
    	for (int i = 0; i <= rowNum; i++) {
    		if(i==0){
				logger.debug("跳过表头");
				continue;
    		}
			if(rowNum>MAX_ABLE_CNT){
				logger.debug("导入数量大于{}条，忽略后续数据",MAX_ABLE_CNT);
				LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
				break;
			}
    		
			StStockMonthConfigDO importDO = new StStockMonthConfigDO(); 
    		Row row = xssfSheet.getRow(i);
    		try {
    			String companyCode = getCellValue(row.getCell(0));//分公司
        		String goodsCode = getCellValue(row.getCell(1));//产品编号
        		String state = getCellValue(row.getCell(2));//操作 0，新增(有则替换,默认) ,1 删除
        		if(state==null|| "".equals(state)) {
        			state="0"; 
        		}
        		
        		importDO.setCompanyCode(companyCode);
        		importDO.setGoodsCode(goodsCode);
        		importDO.setState(Integer.parseInt(state));
			} catch (Exception e) {
				logger.info("多少行"+i+"报错"+"经销商"+getCellValue(row.getCell(1)));
				e.printStackTrace();
				LemonException.throwBusinessException(MsgInfo.IMPORT_FILE_FAILED.getMsgCd());
			}
    		list.add(importDO);
		}
		return list;
	}
	
	private String getCellValue(Cell cell){
		if(cell==null){
			return null;
		}
		String str = null;

		if (cell == null || "".equals(cell)){
			return null;
		}else if (cell.getCellTypeEnum() == CellType.BOOLEAN){
			return null;
		}else if (cell.getCellTypeEnum() == CellType.NUMERIC){
			short format = cell.getCellStyle().getDataFormat();
            SimpleDateFormat sdf = null;
            if (format == 14 || format == 31 || format == 57 || format == 58  
                    || (176<=format && format<=178) || (182<=format && format<=196) 
                    || (210<=format && format<=213) || (208==format ) ) { // 日期
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                	str = sdf.format(date);
                }else {
                	str ="0";
                }
            } else if (format == 20 || format == 32 || format==183 || (200<=format && format<=209) ) { // 时间
                sdf = new SimpleDateFormat("HH:mm");
                double value = cell.getNumericCellValue();
                Date date = org.apache.poi.ss.usermodel.DateUtil.getJavaDate(value);
                if(date!=null){
                	str = sdf.format(date);
                }else {
                	str ="0";
                }
            } else { // 不是日期格式
            	DecimalFormat df = new DecimalFormat("0.##"); 
    			str=df.format(cell.getNumericCellValue());
            }
		}else if (cell.getCellTypeEnum() == CellType.STRING){
			str=cell.getStringCellValue();	
		}else{
			return null;
		}
		return str.replaceAll("[\\s\\?]", "").replace("　", "");
	}
	
}
