package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.CardTypes;
import com.hisun.lemon.fohowe.common.enums.PromQualEnums;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.dto.al.CharacterQueryListRspDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryReqDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryRspDTO;
import com.hisun.lemon.urm.dto.mi.member.MemberQueryBean;
import com.hisun.lemon.urm.dto.mi.member.MemberVO;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.ConvertUtils;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class MemberExcelExporter2 extends  URMExcelExportFactory{
	private ILanguageService languageService;
	public MemberExcelExporter2(ILanguageService languageService) {
		this.languageService=languageService;
	}

	public MemberExcelExporter2() {
		// TODO Auto-generated constructor stub
	}
	@Override
	public void export(Object obj, HttpServletResponse response) {
		      
		String fileName="经销商资料";
		String[] colNames=new String[] {
				"代办处编号","经销商编号","经销商姓名","经销商级别","加入期数","促销资格"};
		
		String multiLangFileName="column.memDataInfo";
		String[] multiLangColNames=new String[] {
				"column.agencyNo","column.memNo","column.memName","column.memType","column.addPeriod","common.cu_xiao_zi_ge"
		};
		
		Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
				
		if(result!=null) {
			colNames=(String[]) result.get("titles");
			fileName=(String) result.get("fileName");
		}
		
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		
		//多语言处理
        CharacterQueryReqDTO reqDTO = new CharacterQueryReqDTO();
        String charKey = "listvalue.trainee.member,listvalue.qualified.member,listvalue.emerald.member,"
        		+ "listvalue.sapphire.member,listvalue.diamond.member,fohowe-store.san_zuan_shi_jing_xiao_shang,"
        		+ "fohowe-store.wu_zuan_shi_jing_xiao_shang,fohowe-store.qi_zuan_shi_jing_xiao_shang,"
        		+ "fohow.ambassador,fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang,common.wuwuwu";
        String langCode = LemonUtils.getLocale().getLanguage() + "-" + LemonUtils.getLocale().getCountry();
        reqDTO.setCharacterKey(charKey);
        reqDTO.setLangCode(langCode);
        CharacterQueryListRspDTO resultDto = languageService.languageQueryList(reqDTO);
        
        if (resultDto==null||resultDto.getCharValueList()==null||resultDto.getCharValueList().size()==0) {
        	
        }
        
        List<CharacterQueryRspDTO> valueList = resultDto.getCharValueList();
        
    	//将经营权列表转化为Map
        Map<String, CharacterQueryRspDTO> map = ConvertUtils.list2Map3(valueList, "getCharacterKey", CharacterQueryRspDTO.class);
		
		MemberVO vo=(MemberVO) obj;
		List<MemberQueryBean> dataList=vo.getDataList();
//		Map<Object, String> carTypeKV = EnumsUtils.EnumToMap(CardTypes.class);
		Map<Object, String> promQualLevel = EnumsUtils.EnumToMap(PromQualEnums.class);
		for(MemberQueryBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			String cardType = languageConversion(map, o);
			String promQualStr = o.getPromQual()==0 ? JudgeUtils.isNull(map.get("common.wuwuwu")) ? "无" : map.get("common.wuwuwu").getCharacterValue():promQualLevel.get(o.getPromQual());
			row.createCell(0).setCellValue(o.getAgentNo());
			row.createCell(1).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(2).setCellValue(o.getMemberName());
			row.createCell(3).setCellValue(cardType);
			row.createCell(4).setCellValue(o.getPeriodWeek());
			row.createCell(5).setCellValue(promQualStr);
		}
		
	}
	
	private String languageConversion(Map<String, CharacterQueryRspDTO> map, MemberQueryBean orderDO) {
		
		String PRE = JudgeUtils.isNull(map.get("listvalue.trainee.member")) ? "listvalue.trainee.member" : map.get("listvalue.trainee.member").getCharacterValue();
        String TYPICAL = JudgeUtils.isNull(map.get("listvalue.qualified.member")) ? "listvalue.qualified.member" : map.get("listvalue.qualified.member").getCharacterValue();
        String GREEN_STONE = JudgeUtils.isNull(map.get("listvalue.emerald.member")) ? "listvalue.emerald.member" : map.get("listvalue.emerald.member").getCharacterValue();
        String BLUE_STONE = JudgeUtils.isNull(map.get("listvalue.sapphire.member")) ? "listvalue.sapphire.member" : map.get("listvalue.sapphire.member").getCharacterValue();
        String DIAMOND = JudgeUtils.isNull(map.get("listvalue.diamond.member")) ? "listvalue.diamond.member" : map.get("listvalue.diamond.member").getCharacterValue();
        String THREE_DIAMOND = JudgeUtils.isNull(map.get("fohowe-store.san_zuan_shi_jing_xiao_shang")) ? "fohowe-store.san_zuan_shi_jing_xiao_shang" : map.get("fohowe-store.san_zuan_shi_jing_xiao_shang").getCharacterValue();
        String FIVE_DIAMOND = JudgeUtils.isNull(map.get("fohowe-store.wu_zuan_shi_jing_xiao_shang")) ? "fohowe-store.wu_zuan_shi_jing_xiao_shang" : map.get("fohowe-store.wu_zuan_shi_jing_xiao_shang").getCharacterValue();
        String SEVEN_DIAMOND = JudgeUtils.isNull(map.get("fohowe-store.qi_zuan_shi_jing_xiao_shang")) ? "fohowe-store.qi_zuan_shi_jing_xiao_shang" : map.get("fohowe-store.qi_zuan_shi_jing_xiao_shang").getCharacterValue();
        String PHOENIX = JudgeUtils.isNull(map.get("fohow.ambassador")) ? "fohow.ambassador" : map.get("fohow.ambassador").getCharacterValue();
        String DOUBLE_PHOENIX = JudgeUtils.isNull(map.get("fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang")) ? "fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang" : map.get("fohowe-store.shuang_feng_huang_da_shi_jing_xiao_shang").getCharacterValue();
       
		String cardType = null;
		switch (CardTypes.getByCode(orderDO.getCardType())) {
		case PRE :
			cardType = PRE;
			break;
		case TYPICAL :
			cardType = TYPICAL;
			break;
		case GREEN_STONE :
			cardType = GREEN_STONE;
			break;
		case BLUE_STONE :
			cardType = BLUE_STONE;
			break;
		case DIAMOND :
			cardType = DIAMOND;
			break;
		case THREE_DIAMOND :
			cardType = THREE_DIAMOND;
			break;
		case FIVE_DIAMOND :
			cardType = FIVE_DIAMOND;
			break;
		case SEVEN_DIAMOND :
			cardType = SEVEN_DIAMOND;
			break;
		case PHOENIX :
			cardType = PHOENIX;
			break;
		case DOUBLE_PHOENIX :
			cardType = DOUBLE_PHOENIX;
			break;
		default:
			break;
		}
		return cardType;
	}
}
