/*
 * @ClassName PdSendInfoItemsDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 16:19:40
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class PdSendInfoItemsDO extends BaseDO {
    /**
     * @Fields id 01.ID ID
     */
    private Long id;
    /**
     * @Fields receiptNo 出库单号
     */
    private String receiptNo;
    private String requestNo;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    private String goodsName;
    /**
     * @Fields orderQty 请货量
     */
    private Integer orderQty;
    /**
     * @Fields sendQty 发货量
     */
    private Integer sendQty;
    /**
     * @Fields recQty 收货量
     */
    private Integer recQty;
    /**
     * @Fields price 单价
     */
    private BigDecimal price;
    /**
     * @Fields state 
     */
    private String state;
    /**
     * @Fields memo 备注
     */
    private String memo;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields comboReceiptNo 合并单号
     */
    private String comboReceiptNo;
    
    private int quantity;
    private String spec;
    
    private List<Long> ids;
    
    private Integer isPkg;//是否套餐
    
    private String pkgCode;//套餐编号
    
    private BigDecimal unitHv;
    
    private String companyCode;
    private String orderType;
    private String recType;
    
    public List<Long> getIds() {
		return ids;
	}

	public void setIds(List<Long> ids) {
		this.ids = ids;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public Integer getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Integer orderQty) {
        this.orderQty = orderQty;
    }

    public Integer getSendQty() {
        return sendQty;
    }

    public void setSendQty(Integer sendQty) {
        this.sendQty = sendQty;
    }

    public Integer getRecQty() {
        return recQty;
    }

    public void setRecQty(Integer recQty) {
        this.recQty = recQty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

	public String getComboReceiptNo() {
		return comboReceiptNo;
	}

	public void setComboReceiptNo(String comboReceiptNo) {
		this.comboReceiptNo = comboReceiptNo;
	}

	public Integer getIsPkg() {
		return isPkg;
	}

	public void setIsPkg(Integer isPkg) {
		this.isPkg = isPkg;
	}

	public String getPkgCode() {
		return pkgCode;
	}

	public void setPkgCode(String pkgCode) {
		this.pkgCode = pkgCode;
	}

	public BigDecimal getUnitHv() {
		return unitHv;
	}

	public void setUnitHv(BigDecimal unitHv) {
		this.unitHv = unitHv;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getRecType() {
		return recType;
	}

	public void setRecType(String recType) {
		this.recType = recType;
	}
	
}