package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.fi.turnover.AcTurnOverBean;
import com.hisun.lemon.urm.dto.fi.turnover.AcTurnOverVO;
import com.hisun.lemon.urm.enums.fi.OrderTypeSimpleEnums;
import com.hisun.lemon.urm.enums.fi.StatusEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class AcTurnOverExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="申领";
		String[] colNames=new String[] {"公司名称","代办处编号","单据类型","资金类型","状态",
				"金额","创建人","创建时间","备注"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AcTurnOverVO vo=(AcTurnOverVO) obj;
		List<AcTurnOverBean> dataList=vo.getDataList();
		Map<Object, String> statusKV=EnumsUtils.EnumToMap(StatusEnums.class);
		Map<Object, String> orderTypeSimpleKV=EnumsUtils.EnumToMap(OrderTypeSimpleEnums.class);
		Map<String,String> acTypeKV=vo.getAcTypeKV();
		for(AcTurnOverBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getCompanyName());
			row.createCell(1).setCellValue(o.getUserCode());
			row.createCell(2).setCellValue(orderTypeSimpleKV.get(o.getOrderType()));
			row.createCell(3).setCellValue(acTypeKV.get(o.getAcType()));
			row.createCell(4).setCellValue(statusKV.get(o.getStatus()));
			row.createCell(5).setCellValue(o.getMoney()+"");
			row.createCell(6).setCellValue(o.getCreatorName());
			row.createCell(7).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(8).setCellValue(o.getMemo());
		}
		
	}
	public static AcTurnOverExcelExporter builder() {
		return new AcTurnOverExcelExporter();
	}
}
