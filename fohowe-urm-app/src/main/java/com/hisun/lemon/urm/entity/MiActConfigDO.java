/*
 * @ClassName MiActConfigDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class MiActConfigDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields cardType 经销商级别
     */
    private String cardType;
    /**
     * @Fields actNum 获得次数
     */
    private String actNum;
    /**
     * @Fields actYear 有效年限
     */
    private String actYear;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getActNum() {
        return actNum;
    }

    public void setActNum(String actNum) {
        this.actNum = actNum;
    }

    public String getActYear() {
        return actYear;
    }

    public void setActYear(String actYear) {
        this.actYear = actYear;
    }
}