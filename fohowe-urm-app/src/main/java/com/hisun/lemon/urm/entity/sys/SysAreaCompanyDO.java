/*
 * @ClassName SysAreaCompanyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-25 11:13:55
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysAreaCompanyDO extends BaseDO {
    /**
     * @Fields id 00.序号 ID
     */
    private String id;
    /**
     * @Fields areaCode 区域编号
     */
    private String areaCode;
    /**
     * @Fields companyCode 分公司/仓库编号
     */
    private String companyCode;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}