/*
 * @ClassName MsgDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-26 16:30:30
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class MsgDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields msgType 消息类型  1文本  2图片  3语音  4视频 5图文
     */
    private String msgType;
    /**
     * @Fields mediaId 多媒体id
     */
    private String mediaId;
    /**
     * @Fields content 文本消息内容
     */
    private String content;
    /**
     * @Fields available 0停用 1正常
     */
    private String available;
    /**
     * @Fields createTsmp 创建时间
     */
    private String createTsmp;
    /**
     * @Fields effTsmp 生效时间戳
     */
    private String effTsmp;
    /**
     * @Fields ineffTsmp 失效时间戳
     */
    private String ineffTsmp;
    /**
     * @Fields mediaPath 多媒体路径
     */
    private String mediaPath;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAvailable() {
        return available;
    }

    public void setAvailable(String available) {
        this.available = available;
    }

    public String getCreateTsmp() {
        return createTsmp;
    }

    public void setCreateTsmp(String createTsmp) {
        this.createTsmp = createTsmp;
    }

    public String getEffTsmp() {
        return effTsmp;
    }

    public void setEffTsmp(String effTsmp) {
        this.effTsmp = effTsmp;
    }

    public String getIneffTsmp() {
        return ineffTsmp;
    }

    public void setIneffTsmp(String ineffTsmp) {
        this.ineffTsmp = ineffTsmp;
    }

    public String getMediaPath() {
        return mediaPath;
    }

    public void setMediaPath(String mediaPath) {
        this.mediaPath = mediaPath;
    }
}