package com.hisun.lemon.urm.entity.ticket;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MeetingExportVO {
	List<TicketsMeetingDO> dataList;
	
	Map<String, String> statusMap = new HashMap<String, String>();

	public List<TicketsMeetingDO> getDataList() {
		return dataList;
	}

	public void setDataList(List<TicketsMeetingDO> dataList) {
		this.dataList = dataList;
	}

	public Map<String, String> getStatusMap() {
		return statusMap;
	}

	public void setStatusMap(Map<String, String> statusMap) {
		this.statusMap = statusMap;
	}
	
}
