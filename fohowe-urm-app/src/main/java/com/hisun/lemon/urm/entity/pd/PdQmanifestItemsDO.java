/*
 * @ClassName PdQmanifestItemsDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-08 10:23:01
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

public class PdQmanifestItemsDO extends BaseDO {
    /**
     * @Fields id 01.ID ID
     */
    private Integer id;
    /**
     * @Fields receiptno 02.调拨单号 ReceiptNO
     */
    private String receiptno;
    /**
     * @Fields goodscode 商品代码
     */
    private String goodscode;
    /**
     * @Fields orderqty 06.请货量 OrderQty
     */
    private Integer orderqty;
    /**
     * @Fields sendqty 07.发货量 SendQty
     */
    private Integer sendqty;
    /**
     * @Fields recqty NR:
     */
    private Integer recqty;
    /**
     * @Fields dcPrice 配送价格，用于分摊关税、运费
     */
    private Integer dcPrice;
    /**
     * @Fields recQty 已收货数量
     */
    private Integer recQty;
    /**
     * @Fields state 批号前缀和数量拼接字符串
     */
    private String state;
    /**
     * @Fields boxqty 箱数（手动填，没有逻辑意义）
     */
    private Integer boxqty;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getGoodscode() {
        return goodscode;
    }

    public void setGoodscode(String goodscode) {
        this.goodscode = goodscode;
    }

    public Integer getOrderqty() {
        return orderqty;
    }

    public void setOrderqty(Integer orderqty) {
        this.orderqty = orderqty;
    }

    public Integer getSendqty() {
        return sendqty;
    }

    public void setSendqty(Integer sendqty) {
        this.sendqty = sendqty;
    }

    public Integer getRecqty() {
        return recqty;
    }

    public void setRecqty(Integer recqty) {
        this.recqty = recqty;
    }

    public Integer getDcPrice() {
        return dcPrice;
    }

    public void setDcPrice(Integer dcPrice) {
        this.dcPrice = dcPrice;
    }

    public Integer getRecQty() {
        return recQty;
    }

    public void setRecQty(Integer recQty) {
        this.recQty = recQty;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getBoxqty() {
        return boxqty;
    }

    public void setBoxqty(Integer boxqty) {
        this.boxqty = boxqty;
    }
}