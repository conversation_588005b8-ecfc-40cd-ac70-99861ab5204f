/*
 * @ClassName IAlRateAdjustDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:31:42
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlRateAdjustDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlRateAdjustDao extends BaseDao<AlRateAdjustDO> {

    /** 
     * @Title: getPageList 
     * @Description: TODO
     * @param rateCode
     * @param status
     * @return
     * @return: List<T>
     */
    List<AlRateAdjustDO> getPageList(@Param("adjustCode") String adjustCode, @Param("status") String status);
}