/*
 * @ClassName IBiAreaSendfeeDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-19 19:36:06
 */
package com.hisun.lemon.urm.dao.st;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.BiAreaSendfeeDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IBiAreaSendfeeDao extends BaseDao<BiAreaSendfeeDO> {

    /** 
     * @Title: getPageList 
     * @Description: TODO
     * @param bonusType
     * @param companyCode
     * @param areaCode
     * @return
     * @return: List<BiAreaSendfeeDO>
     */
    List<BiAreaSendfeeDO> getPageList(@Param("bonusType") String bonusType, @Param("companyCode") String companyCode,
            @Param("areaCode") String areaCode);

    /** 
     * @Title: getByCompanyArea 
     * @Description: TODO
     * @param companyCode
     * @param areaCode
     * @return
     * @return: BiAreaSendfeeDO
     */
    BiAreaSendfeeDO getByCompanyArea(@Param("companyCode") String companyCode, @Param("areaCode") String areaCode);

    /** 
     * @Title: get 
     * @Description: TODO
     * @param id
     * @return
     * @return: BiAreaSendfeeDO
     */
    BiAreaSendfeeDO get(@Param("id") int id);

    /** 
     * @Title: delete 
     * @Description: TODO
     * @param id
     * @return
     * @return: int
     */
    int delete(@Param("id") int id);
}