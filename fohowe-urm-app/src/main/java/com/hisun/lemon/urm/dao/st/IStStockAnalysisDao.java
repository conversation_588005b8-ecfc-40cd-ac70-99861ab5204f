package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StStockAnalysisDO;
import com.hisun.lemon.urm.entity.st.StStockAnalysisItemDO;

@Mapper
public interface IStStockAnalysisDao extends BaseDao<StStockAnalysisDO> {
	List<StStockAnalysisItemDO> getGoodsRowTranColumn(@Param("regionCode") String regionCode,@Param("wWeek") int wWeek,@Param("goodsList") List<String> goodsList);
	List<StStockAnalysisItemDO> getRegionRowTranColumn(@Param("goodsCode") String goodsCode,@Param("wWeek") int wWeek);
	List<StStockAnalysisItemDO> countGoodsPreTotal(@Param("regionCode") String regionCode);
	List<StStockAnalysisItemDO> countRegionPreTotal(@Param("goodsCode") String goodsCode);
	Integer getReduceQty(@Param("regionCode") String regionCode,@Param("goodsCode") String goodsCode,@Param("wWeek") int wWeek);
	Integer getPerWeek(@Param("wWeek") int wWeek);
	Integer callImportAnalysisByCompany(@Param("wWeek") int wWeek,@Param("regionCode") String regionCode);
	Integer callImportAnalysisByGoods(@Param("wWeek") int wWeek,@Param("goodsCode") String goodsCode);
	List<StStockAnalysisItemDO> getGoodsListByWeek(@Param("regionCode") String regionCode,@Param("wWeek") int wWeek,@Param("goodsList") List<String> goodsList);
	List<StStockAnalysisItemDO> getRegionListByWeek(@Param("goodsCode") String goodsCode,@Param("wWeek") int wWeek);
}
