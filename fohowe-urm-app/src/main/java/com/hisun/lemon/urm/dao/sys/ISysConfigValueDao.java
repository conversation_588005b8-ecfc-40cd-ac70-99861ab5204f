/*
 * @ClassName ISysConfigValueDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:46
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysConfigValueDO;

import java.util.List;

import com.hisun.lemon.urm.entity.ticket.TicketsRegistrationMemberDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysConfigValueDao extends BaseDao<SysConfigValueDO> {

    /** 
     * @Title: findValueList 
     * @Description: 查询参数值列表
     * @param keyId
     * @param configCode
     * @param companyCode
     * @return
     * @return: List<SysConfigValueDO>
     */
    public List<SysConfigValueDO> findValueList(@Param("keyId") String keyId, @Param("configCode") String configCode,
            @Param("companyCode") String companyCode);

    /** 
     * @Title: findExists 
     * @Description: 根据KeyId查询是否存在不为空的参数值
     * @param keyId
     * @return
     * @return: SysConfigValueDO
     */
    public SysConfigValueDO findExists(@Param("keyId") long keyId);

    /** 
     * @Title: deleteByKeyId 
     * @Description: 根据KeyId删除记录
     * @param keyId
     * @return
     * @return: int
     */
    public int deleteByKeyId(@Param("keyId") long keyId);

    /** 
     * @Title: findValue 
     * @param configCode
     * @param companyCode
     * @return
     * @return: SysConfigValueDO
     */
    public SysConfigValueDO findValue(@Param("configCode") String configCode, @Param("companyCode") String companyCode);

    /** 
     * @Title: insertByBonus 
     * @param keyId
     * @param companyCode
     * @param defaultValue 
     * @return
     * @return: int
     */
    public int insertByBonus(@Param("keyId") long keyId, @Param("companyCode") String companyCode, @Param("configValueTwo") String configValueTwo);

}