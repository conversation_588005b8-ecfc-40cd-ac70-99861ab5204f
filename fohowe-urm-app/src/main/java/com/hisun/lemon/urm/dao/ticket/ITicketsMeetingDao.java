package com.hisun.lemon.urm.dao.ticket;

import java.time.LocalDateTime;
import java.util.List;

import com.hisun.lemon.urm.dto.fi.tickets.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.urm.entity.ticket.TicketsMeetingDO;

@Mapper
public interface ITicketsMeetingDao {
	
	List<TicketsMeetingDO> getListByCondition(TicketsMeetingDTO queryDTO);
	
	int getTotalCount(TicketsMeetingDTO queryDTO);
	
	TicketsBaseInfo getTotalSumCount(TicketsMeetingDTO queryDTO);
	
	List<TicketsMeetingDO> queryList(TicketsMeetingDTO query);

	TicketsMeetingDO selectByNo(@Param("meetingNo") String meetingNo);
 
    List<TicketsMeetingDO> selectByFutureInformation(TicketsMeetingReqDTO reqDTO);
    List<TicketsMeetingDO> selectByPaymentInformation(TicketsMeetingPaymentReqDTO reqDTO);
    List<TicketsMeetingDO> selectByActualInformation(TicketsMeetingActualReqDTO reqDTO);
    
    List<TicketsMeetingDO> selectByMeeting(TicketsFinishMeetingReqDTO reqDTO);
    

    
    int deleteByPrimaryKey(Long id);

    int insertSelective(TicketsMeetingDO record);

    TicketsMeetingDO selectByPrimaryKey(Long id);
    
    int updateStatusByIds(@Param("vcStatus") int vcStatus,@Param("auditTime") LocalDateTime auditTime ,@Param("idsList") List<Long> ids); 

    int updateByPrimaryKeySelective(TicketsMeetingDO record);


    void insertMeetingList(@Param("list") List<TicketsMeetingDO> importBeanList);
}