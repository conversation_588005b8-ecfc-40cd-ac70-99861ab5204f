/*
 * @ClassName SysUserDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:09:01
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysUserDO extends BaseDO {
    /**
     * @Fields userCode 登录帐号 USER_CODE
     */
    private String userCode;
    /**
     * @Fields userName 用户名/姓名 USER_NAME
     */
    private String userName;
    /**
     * @Fields password 6至20个字符
     */
    private String password;
    /**
     * @Fields advPassword 高级密码，默认与u_password相同
     */
    private String advPassword;
    /**
     * @Fields suspendStatus 限制状态 0:未限制 1：已限制
     */
    private String suspendStatus;
    /**
     * @Fields companyCode 公司编码 COMPANY_CODE
     */
    private String companyCode;
    /**
     * @Fields userType Q,代理商 P:配送中心 M:会员 C:公司用户 A:管理中心 S:商城用户 I:双轨商城用户
     */
    private String userType;
    /**
     * @Fields defLang 默认语言 DEF_LANG
     */
    private String defLang;
    /**
     * @Fields freezeStatus 冻结状态 0:未冻结 1：已冻结 2:锁定发放H0
     */
    private String freezeStatus;
    /**
     * @Fields isActive 活跃状态 0-不活跃 1-活跃
     */
    private String isActive;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields upPwdTime  修改密码时间
     */
    private LocalDateTime upPwdTime;
    /**
     * @Fields upPwdTime  创建时间
     */
    private LocalDateTime createTime;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 部门编号
     */
    private String departmentId;
    private String bonusType;
    private String areaCode;
    
    
    public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public LocalDateTime getUpPwdTime() {
		return upPwdTime;
	}

	public void setUpPwdTime(LocalDateTime upPwdTime) {
		this.upPwdTime = upPwdTime;
	}

	public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAdvPassword() {
        return advPassword;
    }

    public void setAdvPassword(String advPassword) {
        this.advPassword = advPassword;
    }

    public String getSuspendStatus() {
        return suspendStatus;
    }

    public void setSuspendStatus(String suspendStatus) {
        this.suspendStatus = suspendStatus;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getDefLang() {
        return defLang;
    }

    public void setDefLang(String defLang) {
        this.defLang = defLang;
    }

    public String getFreezeStatus() {
        return freezeStatus;
    }

    public void setFreezeStatus(String freezeStatus) {
        this.freezeStatus = freezeStatus;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getBonusType() {
        return bonusType;
    }

    public void setBonusType(String bonusType) {
        this.bonusType = bonusType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

	@Override
	public String toString() {
		return "SysUserDO [userCode=" + userCode + ", userName=" + userName + ", password=" + password
				+ ", advPassword=" + advPassword + ", suspendStatus=" + suspendStatus + ", companyCode=" + companyCode
				+ ", userType=" + userType + ", defLang=" + defLang + ", freezeStatus=" + freezeStatus + ", isActive="
				+ isActive + ", tmSmp=" + tmSmp + ", upPwdTime=" + upPwdTime + ", createTime=" + createTime
				+ ", roleName=" + roleName + ", departmentId=" + departmentId + ", bonusType=" + bonusType
				+ ", areaCode=" + areaCode + "]";
	}

}