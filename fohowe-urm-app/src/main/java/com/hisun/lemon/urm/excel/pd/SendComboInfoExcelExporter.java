package com.hisun.lemon.urm.excel.pd;

import com.hisun.lemon.urm.entity.pd.SendInforExcelDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public class SendComboInfoExcelExporter extends URMExcelExportFactory {
    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName="促销-赠品单合并发货";
        String[] colNames=new String[] {"分公司","代办处","合并单号","出库单号","产品编号","数量","金额","发货状态"};
        super.exportActually(fileName, colNames, obj, response);
    }



    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        List<SendInforExcelDO> vo = (List<SendInforExcelDO>)obj;
        List<SendInforExcelDO> dataList=vo;
        for(SendInforExcelDO o:dataList) {
            XSSFRow row = sheet.createRow(beginRow++);
            int index=0;
            row.createCell(index++).setCellValue(o.getCompanyCode());
            row.createCell(index++).setCellValue(o.getAgentNo());
            row.createCell(index++).setCellValue(o.getComboReceiptno());
            row.createCell(index++).setCellValue(o.getReceiptno());
            row.createCell(index++).setCellValue(o.getGoodScode());
            row.createCell(index++).setCellValue(o.getOrderQty().toString());
            if (o.getStandardPrice()==null)
            {
                row.createCell(index++).setCellValue(0);
            }
            else
            {row.createCell(index++).setCellValue(o.getStandardPrice().toString());}
            String statusName ="";
            if (o.getStatue()==0){
                statusName = "未发货";
            }
            if (o.getStatue()==1){
                statusName = "已发货";
            }
            row.createCell(index++).setCellValue(statusName);
        }
    }

    public static SendComboInfoExcelExporter builder() {
        return new SendComboInfoExcelExporter();
    }
}
