package com.hisun.lemon.urm.excel.st;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.excel.ExcelExportFactorys;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.st.StStockMonthSummaryDTO;

public class StockMonthSummaryExcel<T extends StStockMonthSummaryDTO> extends ExcelExportFactorys<T> {
	
	public void export2(List<StStockMonthSummaryDTO> dataList, String fileName,String[] colNames,HttpServletResponse response) {
		try {
			response.reset();
			response.setHeader("Content-disposition", "attachment; filename=" +fileName+ ".xlsx");
            response.setContentType("application/msexcel;charset=UTF-8");//设置类型
            response.setHeader("Pragma", "No-cache");//设置头
            response.setHeader("Cache-Control", "no-cache");//设置头
            response.setDateHeader("Expires", 0);//设置日期头
            List<List<String>> head = new ArrayList<List<String>>();
            for (int i = 0; i < colNames.length; i++) {
            	List<String> head0 = new ArrayList<String>();
            	head0.add(colNames[i]);
            	head.add(head0);
    		}
			WriteWorkbook workBook = new WriteWorkbook();
			workBook.setExcelType(ExcelTypeEnum.XLSX);
			workBook.setOutputStream(response.getOutputStream());
			workBook.setNeedHead(true);
			WriteSheet sheet= new WriteSheet();
			sheet.setSheetNo(0);
			sheet.setSheetName(fileName); 
			sheet.setHead(head);
			ExcelWriter write = new ExcelWriter(workBook);
			write.write(addData(dataList), sheet); 
			write.finish();
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
    @Override
    public void addCell(Sheet sheet, CellStyle style, List<T> dataList) throws Exception {
//        if (dataList != null && dataList.size() > 0) {
//            for (int i = 0; i < dataList.size(); i++) {
//                
//            }
//        }
    }
    public List<List<Object>> addData(List<StStockMonthSummaryDTO> dataList) throws Exception {
		List<List<Object>> list = new ArrayList<List<Object>>();
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			StStockMonthSummaryDTO stockDO = (StStockMonthSummaryDTO)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(stockDO.getStYear());
			data.add(stockDO.getStMonth());
			data.add(JudgeUtils.isNull(stockDO.getAreaCode()) ? "" :stockDO.getAreaCode());
			data.add(JudgeUtils.isNull(stockDO.getCompanyCode()) ? "" :stockDO.getCompanyCode());
			data.add(JudgeUtils.isNull(stockDO.getGoodsCode()) ? "" : stockDO.getGoodsCode()); // 商品代码
			data.add(JudgeUtils.isNull(stockDO.getGoodsName()) ? "" : stockDO.getGoodsName());// 商品名称
			data.add(stockDO.getQuantity()==null?0:stockDO.getQuantity());
			data.add(stockDO.getStockPrice()==null?0:stockDO.getStockPrice());
			data.add(stockDO.getTotalPrice()==null?0:stockDO.getTotalPrice());
			list.add(data);
		}
		return list;
	}
}
