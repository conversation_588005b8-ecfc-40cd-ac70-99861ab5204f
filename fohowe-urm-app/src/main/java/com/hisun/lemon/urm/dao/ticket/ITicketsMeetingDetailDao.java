package com.hisun.lemon.urm.dao.ticket;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.ticket.TicketsMeetingDetailDO;

@Mapper
public interface ITicketsMeetingDetailDao extends BaseDao<TicketsMeetingDetailDO> {
    int deleteByPrimaryKey(Long id);
    
    int deleteByMeeting(@Param("meetingId") Long meetingId,@Param("itemFlag") Integer itemFlag);

    int insert(TicketsMeetingDetailDO row);

    int insertSelective(TicketsMeetingDetailDO row);

    TicketsMeetingDetailDO selectByPrimaryKey(Long id);
    
    List<TicketsMeetingDetailDO> selectByMeeting(@Param("meetingId") Long meetingId);

    int updateByPrimaryKeySelective(TicketsMeetingDetailDO row);

    int updateByPrimaryKey(TicketsMeetingDetailDO row);
}