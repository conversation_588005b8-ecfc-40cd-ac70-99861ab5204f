package com.hisun.lemon.urm.entity.bd;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

public class BdTourismInformationBO {

	private Long id;
	
	private Integer vcStatus;

    private Integer fiCheckeCode;

    private String memberNo;
    
    private String memberName;

    private String agentNo;

    private String companyCode;

    private String memberType;

    private String memberLastName;

    private String menberFirstName;

    private String passportsLastName;

    private String passportsFirstName;

    private String sex;

    private String menberNationality;

    private String menberBirthday;

    private String passportsNo;

    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
	private LocalDate passportsData;

    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
	private LocalDate passportsEffectiveData;

    private Integer fiVcStatus;

    private Integer fiHusbandPeer;

    private String wifePassport;

    private Integer fiChildren;

    private String childrenPassportNo;

    private String prssportPicPath;

    private String visaPrssportPicPath;
    
    private Integer finishVcStatus;
    
    private String areaName;

    private Long tourId;
    
    private String tourCompanyCode;

    private String tourAgentNo;

    private String tourMemberNo;

    private String tourMemberName;

    private BigDecimal totalAmount;

    private BigDecimal repairAmount;

    private BigDecimal payAmount;

    private String salesPromotion;

	private LocalDateTime payTime;

    private Integer payWeek;

    private Integer quotaNum;

    private Integer isTraveled;

    private String remark;

	private LocalDateTime ficheckeTime;

    private String ficheckerCode;

    private Integer ficheckeStatus;

	private LocalDateTime createTime;

    private String payerCode;
    private String lineStatus;

    private Integer agFicheckeStatus;
    
    private Integer isOutPay;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getVcStatus() {
		return vcStatus;
	}

	public void setVcStatus(Integer vcStatus) {
		this.vcStatus = vcStatus;
	}

	public Integer getFiCheckeCode() {
		return fiCheckeCode;
	}

	public void setFiCheckeCode(Integer fiCheckeCode) {
		this.fiCheckeCode = fiCheckeCode;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getMemberType() {
		return memberType;
	}

	public void setMemberType(String memberType) {
		this.memberType = memberType;
	}

	public String getMemberLastName() {
		return memberLastName;
	}

	public void setMemberLastName(String memberLastName) {
		this.memberLastName = memberLastName;
	}

	public String getMenberFirstName() {
		return menberFirstName;
	}

	public void setMenberFirstName(String menberFirstName) {
		this.menberFirstName = menberFirstName;
	}

	public String getPassportsLastName() {
		return passportsLastName;
	}

	public void setPassportsLastName(String passportsLastName) {
		this.passportsLastName = passportsLastName;
	}

	public String getPassportsFirstName() {
		return passportsFirstName;
	}

	public void setPassportsFirstName(String passportsFirstName) {
		this.passportsFirstName = passportsFirstName;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getMenberNationality() {
		return menberNationality;
	}

	public void setMenberNationality(String menberNationality) {
		this.menberNationality = menberNationality;
	}

	public String getMenberBirthday() {
		return menberBirthday;
	}

	public void setMenberBirthday(String menberBirthday) {
		this.menberBirthday = menberBirthday;
	}

	public String getPassportsNo() {
		return passportsNo;
	}

	public void setPassportsNo(String passportsNo) {
		this.passportsNo = passportsNo;
	}

	public LocalDate getPassportsData() {
		return passportsData;
	}

	public void setPassportsData(LocalDate passportsData) {
		this.passportsData = passportsData;
	}

	public LocalDate getPassportsEffectiveData() {
		return passportsEffectiveData;
	}

	public void setPassportsEffectiveData(LocalDate passportsEffectiveData) {
		this.passportsEffectiveData = passportsEffectiveData;
	}

	public Integer getFiVcStatus() {
		return fiVcStatus;
	}

	public void setFiVcStatus(Integer fiVcStatus) {
		this.fiVcStatus = fiVcStatus;
	}

	public Integer getFiHusbandPeer() {
		return fiHusbandPeer;
	}

	public void setFiHusbandPeer(Integer fiHusbandPeer) {
		this.fiHusbandPeer = fiHusbandPeer;
	}

	public String getWifePassport() {
		return wifePassport;
	}

	public void setWifePassport(String wifePassport) {
		this.wifePassport = wifePassport;
	}

	public Integer getFiChildren() {
		return fiChildren;
	}

	public void setFiChildren(Integer fiChildren) {
		this.fiChildren = fiChildren;
	}

	public String getChildrenPassportNo() {
		return childrenPassportNo;
	}

	public void setChildrenPassportNo(String childrenPassportNo) {
		this.childrenPassportNo = childrenPassportNo;
	}

	public String getPrssportPicPath() {
		return prssportPicPath;
	}

	public void setPrssportPicPath(String prssportPicPath) {
		this.prssportPicPath = prssportPicPath;
	}

	public String getVisaPrssportPicPath() {
		return visaPrssportPicPath;
	}

	public void setVisaPrssportPicPath(String visaPrssportPicPath) {
		this.visaPrssportPicPath = visaPrssportPicPath;
	}

	public Integer getFinishVcStatus() {
		return finishVcStatus;
	}

	public void setFinishVcStatus(Integer finishVcStatus) {
		this.finishVcStatus = finishVcStatus;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getTourCompanyCode() {
		return tourCompanyCode;
	}

	public void setTourCompanyCode(String tourCompanyCode) {
		this.tourCompanyCode = tourCompanyCode;
	}

	public String getTourAgentNo() {
		return tourAgentNo;
	}

	public void setTourAgentNo(String tourAgentNo) {
		this.tourAgentNo = tourAgentNo;
	}

	public String getTourMemberNo() {
		return tourMemberNo;
	}

	public void setTourMemberNo(String tourMemberNo) {
		this.tourMemberNo = tourMemberNo;
	}

	public String getTourMemberName() {
		return tourMemberName;
	}

	public void setTourMemberName(String tourMemberName) {
		this.tourMemberName = tourMemberName;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getRepairAmount() {
		return repairAmount;
	}

	public void setRepairAmount(BigDecimal repairAmount) {
		this.repairAmount = repairAmount;
	}

	public BigDecimal getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(BigDecimal payAmount) {
		this.payAmount = payAmount;
	}

	public String getSalesPromotion() {
		return salesPromotion;
	}

	public void setSalesPromotion(String salesPromotion) {
		this.salesPromotion = salesPromotion;
	}

	public LocalDateTime getPayTime() {
		return payTime;
	}

	public void setPayTime(LocalDateTime payTime) {
		this.payTime = payTime;
	}

	public Integer getPayWeek() {
		return payWeek;
	}

	public void setPayWeek(Integer payWeek) {
		this.payWeek = payWeek;
	}

	public Integer getQuotaNum() {
		return quotaNum;
	}

	public void setQuotaNum(Integer quotaNum) {
		this.quotaNum = quotaNum;
	}

	public Integer getIsTraveled() {
		return isTraveled;
	}

	public void setIsTraveled(Integer isTraveled) {
		this.isTraveled = isTraveled;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public LocalDateTime getFicheckeTime() {
		return ficheckeTime;
	}

	public void setFicheckeTime(LocalDateTime ficheckeTime) {
		this.ficheckeTime = ficheckeTime;
	}

	public String getFicheckerCode() {
		return ficheckerCode;
	}

	public void setFicheckerCode(String ficheckerCode) {
		this.ficheckerCode = ficheckerCode;
	}

	public Integer getFicheckeStatus() {
		return ficheckeStatus;
	}

	public void setFicheckeStatus(Integer ficheckeStatus) {
		this.ficheckeStatus = ficheckeStatus;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getPayerCode() {
		return payerCode;
	}

	public void setPayerCode(String payerCode) {
		this.payerCode = payerCode;
	}

	public Integer getAgFicheckeStatus() {
		return agFicheckeStatus;
	}

	public void setAgFicheckeStatus(Integer agFicheckeStatus) {
		this.agFicheckeStatus = agFicheckeStatus;
	}

	public Integer getIsOutPay() {
		return isOutPay;
	}

	public void setIsOutPay(Integer isOutPay) {
		this.isOutPay = isOutPay;
	}

	public String getLineStatus() {
		return lineStatus;
	}

	public void setLineStatus(String lineStatus) {
		this.lineStatus = lineStatus;
	}

	public Long getTourId() {
		return tourId;
	}

	public void setTourId(Long tourId) {
		this.tourId = tourId;
	}

	@Override
	public String toString() {
		return "BdTourismInformationBO{" +
				"id=" + id +
				", vcStatus=" + vcStatus +
				", fiCheckeCode=" + fiCheckeCode +
				", memberNo='" + memberNo + '\'' +
				", memberName='" + memberName + '\'' +
				", agentNo='" + agentNo + '\'' +
				", companyCode='" + companyCode + '\'' +
				", memberType='" + memberType + '\'' +
				", memberLastName='" + memberLastName + '\'' +
				", menberFirstName='" + menberFirstName + '\'' +
				", passportsLastName='" + passportsLastName + '\'' +
				", passportsFirstName='" + passportsFirstName + '\'' +
				", sex='" + sex + '\'' +
				", menberNationality='" + menberNationality + '\'' +
				", menberBirthday='" + menberBirthday + '\'' +
				", passportsNo='" + passportsNo + '\'' +
				", passportsData=" + passportsData +
				", passportsEffectiveData=" + passportsEffectiveData +
				", fiVcStatus=" + fiVcStatus +
				", fiHusbandPeer=" + fiHusbandPeer +
				", wifePassport='" + wifePassport + '\'' +
				", fiChildren=" + fiChildren +
				", childrenPassportNo='" + childrenPassportNo + '\'' +
				", prssportPicPath='" + prssportPicPath + '\'' +
				", visaPrssportPicPath='" + visaPrssportPicPath + '\'' +
				", finishVcStatus=" + finishVcStatus +
				", areaName='" + areaName + '\'' +
				", tourCompanyCode='" + tourCompanyCode + '\'' +
				", tourAgentNo='" + tourAgentNo + '\'' +
				", tourMemberNo='" + tourMemberNo + '\'' +
				", tourMemberName='" + tourMemberName + '\'' +
				", totalAmount=" + totalAmount +
				", repairAmount=" + repairAmount +
				", payAmount=" + payAmount +
				", salesPromotion='" + salesPromotion + '\'' +
				", payTime=" + payTime +
				", payWeek=" + payWeek +
				", quotaNum=" + quotaNum +
				", isTraveled=" + isTraveled +
				", remark='" + remark + '\'' +
				", ficheckeTime=" + ficheckeTime +
				", ficheckerCode='" + ficheckerCode + '\'' +
				", ficheckeStatus=" + ficheckeStatus +
				", createTime=" + createTime +
				", payerCode='" + payerCode + '\'' +
				", LineStatus='" + lineStatus + '\'' +
				", agFicheckeStatus=" + agFicheckeStatus +
				", isOutPay=" + isOutPay +
				'}';
	}
}
