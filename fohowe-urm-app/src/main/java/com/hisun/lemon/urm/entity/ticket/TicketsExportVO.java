package com.hisun.lemon.urm.entity.ticket;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TicketsExportVO {

	List<TicketsVoucherDO> dataList;
	
	Map<String, String> statusMap = new HashMap<String, String>();

	public List<TicketsVoucherDO> getDataList() {
		return dataList;
	}

	public void setDataList(List<TicketsVoucherDO> dataList) {
		this.dataList = dataList;
	}

	public Map<String, String> getStatusMap() {
		return statusMap;
	}

	public void setStatusMap(Map<String, String> statusMap) {
		this.statusMap = statusMap;
	}
	
}
