/*
 * @ClassName SysCompanyCurrencyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-04 20:08:11
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysCompanyCurrencyDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private String id;
    /**
     * @Fields companyCode 公司编码 COMPANY_CODE
     */
    private String companyCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * @Fields currencyCode 货币编码 CURRENCY_CODE
     */
    private String currencyCode;
    /**
     * @Fields isDefault 是否默认 0-否 1-是
     */
    private String isDefault;
    /**
     * 是否可用
     */
    private String isUsable;
    /**
     * @Fields tmSmp
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getIsUsable() {
        return isUsable;
    }

    public void setIsUsable(String isUsable) {
        this.isUsable = isUsable;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
}