/*
 * @ClassName AlRateAdjustItemDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-09 14:17:22
 */
package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AlRateAdjustItemDO extends BaseDO {
    /**
     * @Fields itemId 明细ID ITEM_ID
     */
    private String itemId;
    /**
     * @Fields adjustCode 调整单号 ADJUST_CODE
     */
    private String adjustCode;
    /**
     * @Fields currencyCode 货币编号 CURRENCY_CODE
     */
    private String currencyCode;
    private String currencyName;
    /**
     * @Fields rateToUsd 对美元汇率
     */
    private BigDecimal rateToUsd;
    /**
     * @Fields rateToEur 对欧元汇率
     */
    private BigDecimal rateToEur;
    /**
     * @Fields rateToInput 对凤凰币充值汇率
     */
    private BigDecimal rateToInput;
    /**
     * @Fields rateToApple 对凤凰币提现汇率
     */
    private BigDecimal rateToApple;
    /**
     * @Fields oldRateToUsd 原来对美元汇率
     */
    private BigDecimal oldRateToUsd;
    /**
     * @Fields oldRateToEur 原来对欧元汇率
     */
    private BigDecimal oldRateToEur;
    /**
     * @Fields oldRateToInput 原来对凤凰币充值汇率
     */
    private BigDecimal oldRateToInput;
    /**
     * @Fields oldRateToApple 原来对凤凰币提现汇率
     */
    private BigDecimal oldRateToApple;
    /**
     * USDT汇率
     */
    private BigDecimal rateToUsdt;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getAdjustCode() {
        return adjustCode;
    }

    public void setAdjustCode(String adjustCode) {
        this.adjustCode = adjustCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getRateToUsd() {
        return rateToUsd;
    }

    public void setRateToUsd(BigDecimal rateToUsd) {
        this.rateToUsd = rateToUsd;
    }

    public BigDecimal getRateToEur() {
        return rateToEur;
    }

    public void setRateToEur(BigDecimal rateToEur) {
        this.rateToEur = rateToEur;
    }

    public BigDecimal getRateToInput() {
        return rateToInput;
    }

    public void setRateToInput(BigDecimal rateToInput) {
        this.rateToInput = rateToInput;
    }

    public BigDecimal getRateToApple() {
        return rateToApple;
    }

    public void setRateToApple(BigDecimal rateToApple) {
        this.rateToApple = rateToApple;
    }

    public BigDecimal getOldRateToUsd() {
        return oldRateToUsd;
    }

    public void setOldRateToUsd(BigDecimal oldRateToUsd) {
        this.oldRateToUsd = oldRateToUsd;
    }

    public BigDecimal getOldRateToEur() {
        return oldRateToEur;
    }

    public void setOldRateToEur(BigDecimal oldRateToEur) {
        this.oldRateToEur = oldRateToEur;
    }

    public BigDecimal getOldRateToInput() {
        return oldRateToInput;
    }

    public void setOldRateToInput(BigDecimal oldRateToInput) {
        this.oldRateToInput = oldRateToInput;
    }

    public BigDecimal getOldRateToApple() {
        return oldRateToApple;
    }

    public void setOldRateToApple(BigDecimal oldRateToApple) {
        this.oldRateToApple = oldRateToApple;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

	public BigDecimal getRateToUsdt() {
		return rateToUsdt;
	}

	public void setRateToUsdt(BigDecimal rateToUsdt) {
		this.rateToUsdt = rateToUsdt;
	}
}