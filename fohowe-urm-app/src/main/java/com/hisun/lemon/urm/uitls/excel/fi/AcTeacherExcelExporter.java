package com.hisun.lemon.urm.uitls.excel.fi;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.entity.ticket.TicketsTeacherDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;

public class AcTeacherExcelExporter extends URMExcelExportFactorys {
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		try {
			String fileName="老师";
			String[] colNames=new String[] {"身份","编号","名称","组长编号","是否组长","状态","备注"};
			List<List<Object>> data= addData(obj);
			super.exportActually2(data,fileName,colNames, response);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	public List<List<Object>> addData(Object obj) throws Exception {
		List<TicketsTeacherDO> dataList=(List<TicketsTeacherDO>)obj;
		List<List<Object>> list = new ArrayList<List<Object>>();
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			TicketsTeacherDO teacher = (TicketsTeacherDO)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			//"身份","编号","名称","组长编号","是否组长","状态","备注"
			if(teacher.getTeacherType() !=null && teacher.getTeacherType()==1) {
				data.add("翻译");
			}else {
				data.add("老师");
			}
			data.add(teacher.getTeacherNo());
			data.add(teacher.getTeacherName());
			data.add(teacher.getTeamNo());
			data.add(teacher.getIsTeam() ==null?0:teacher.getIsTeam());
			String statusStr="";
			if(teacher.getStatus() != null && teacher.getStatus() == 1) {
				statusStr="禁用";
			}else {
				statusStr="启用";
			}
			data.add(statusStr);
			data.add(teacher.getMemo());
			list.add(data);
		}
		return list;
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		List<TicketsTeacherDO> dataList=(List<TicketsTeacherDO>)obj;
		for(TicketsTeacherDO teacher:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			String str="";
			if(teacher.getTeacherType() !=null && teacher.getTeacherType()==1) {
				str = "翻译";
			}else {
				str = "老师";
			}
			row.createCell(index++).setCellValue(str);
			row.createCell(index++).setCellValue(teacher.getTeacherNo());
			row.createCell(index++).setCellValue(teacher.getTeacherName());
			row.createCell(index++).setCellValue(teacher.getTeamNo());
			row.createCell(index++).setCellValue(teacher.getIsTeam() ==null?0:teacher.getIsTeam());
			String statusStr="";
			if(teacher.getStatus() != null && teacher.getStatus() == 1) {
				statusStr="禁用";
			}else {
				statusStr="启用";
			}
			row.createCell(index++).setCellValue(statusStr);
			row.createCell(index++).setCellValue(teacher.getMemo());
		}
		
	}
	public static AcTeacherExcelExporter builder() {
		return new AcTeacherExcelExporter();
	}
}
