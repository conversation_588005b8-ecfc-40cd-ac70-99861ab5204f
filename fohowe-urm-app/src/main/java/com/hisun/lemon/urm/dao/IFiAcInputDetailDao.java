/*
 * @ClassName IFiAcInputDetailDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.input.AcInputDetailBean;
import com.hisun.lemon.urm.dto.fi.input.AcInputDetailDTO;
import com.hisun.lemon.urm.entity.FiAcInputDetailDO;

@Mapper
public interface IFiAcInputDetailDao extends BaseDao<FiAcInputDetailDO> {

	List<AcInputDetailBean> getListByCondition(FiAcInputDetailDO acInputDetail);
	List<FiAcInputDetailDO> getListByEAS(FiAcInputDetailDO acInputDetail);
	List<FiAcInputDetailDO> getId(AcInputDetailDTO acInputDetail);

	int getTotalCount(FiAcInputDetailDO acInputDetail);
	
	int deleteInputId(FiAcInputDetailDO acInputDetail);
	
	int deleteByEASInputId(FiAcInputDetailDO acInputDetail);
	int update(FiAcInputDetailDO DO);
	
	FiAcInputDetailDO getCashAccountByNo(@Param("cashNo") String cashNo);
	
	FiAcInputDetailDO getCashAccountById(@Param("accountId") Integer accountId);
}