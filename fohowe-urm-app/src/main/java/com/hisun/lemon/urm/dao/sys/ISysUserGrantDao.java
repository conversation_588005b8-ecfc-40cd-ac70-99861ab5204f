package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysUserGrantDO;

@Mapper
public interface ISysUserGrantDao extends BaseDao<SysUserGrantDO> {
	
	List<SysUserGrantDO> getList(String userCord);
	
    int updateByPrimaryKeySelective(SysUserGrantDO record);

    int updateByPrimaryKey(SysUserGrantDO record);
}