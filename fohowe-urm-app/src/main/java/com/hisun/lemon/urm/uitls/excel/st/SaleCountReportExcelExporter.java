package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.hisun.lemon.urm.service.pd.impl.SendInfoImpl;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.dto.pd.ReportRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SaleCountReportExcelExporter extends URMExcelExportFactory {

    private static final Logger logger = LoggerFactory.getLogger(SaleCountReportExcelExporter.class);
    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "产品报单销售汇总报表";
        ReportRspDTO vo = (ReportRspDTO) obj;
        String[] colNames = new String[vo.getColumns().size()];
        colNames[0] = "商品编号";
        colNames[1] = "合计";
        colNames[2] = "商品名称";
        int colNums = 3;
        for (int i = 0; i < vo.getColumns().size(); i++) {
            if (JudgeUtils.equalsAny(vo.getColumns().get(i), "GOODS_CODE")) {
                continue;
            }
            if (JudgeUtils.equalsAny(vo.getColumns().get(i), "total")) {
            	continue;
            }
            if (JudgeUtils.equalsAny(vo.getColumns().get(i), "GOODS_NAME")) {
                continue;
            }
            colNames[colNums] = vo.getColumns().get(i);
            colNums++;
        }
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        ReportRspDTO vo = (ReportRspDTO) obj;
        List<Map<String, Object>> dataList = vo.getList();
        for (Map<String, Object> o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);

            // 出库->订货分公司
            row.createCell(0).setCellValue(o.get("GOODS_CODE").toString());
            row.createCell(1).setCellValue(o.get("total").toString());
            row.createCell(2).setCellValue(o.get("GOODS_NAME").toString());

            int colNums = 3;
            for (int i = 0; i < vo.getColumns().size(); i++) {
                if (JudgeUtils.equalsAny(vo.getColumns().get(i), "GOODS_CODE")) {
                    continue;
                }
                if (JudgeUtils.equalsAny(vo.getColumns().get(i), "total")) {
                	continue;
                }
                if (JudgeUtils.equalsAny(vo.getColumns().get(i), "GOODS_NAME")) {
                    continue;
                }
                String cellValue = vo.getColumns().get(i);
                row.createCell(colNums).setCellValue(o.get(cellValue).toString());
                colNums++;
            }
        }

    }

    public static SaleCountReportExcelExporter builder() {
        return new SaleCountReportExcelExporter();
    }
}
