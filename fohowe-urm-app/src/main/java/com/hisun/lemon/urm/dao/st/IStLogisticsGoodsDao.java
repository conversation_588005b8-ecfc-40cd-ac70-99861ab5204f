package com.hisun.lemon.urm.dao.st;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StLogisticsGoods;

@Mapper
public interface IStLogisticsGoodsDao extends BaseDao<StLogisticsGoods>  {
    int deleteByPrimaryKey(Integer id);

    int insert(StLogisticsGoods record);

    int insertSelective(StLogisticsGoods record);

    StLogisticsGoods selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(StLogisticsGoods record);

    int updateByPrimaryKey(StLogisticsGoods record);

	void deleteByLogisticsNo(String logisticsNo);
}