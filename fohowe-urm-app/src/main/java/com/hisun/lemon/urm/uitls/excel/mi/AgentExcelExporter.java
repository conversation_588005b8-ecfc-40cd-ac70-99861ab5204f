package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.mi.agent.AgentQueryBean;
import com.hisun.lemon.urm.dto.mi.agent.AgentVO;
import com.hisun.lemon.urm.enums.sys.SuspendStatus;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class AgentExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="代办处基本信息";
		String[] colNames=new String[] {
				"代办处编号","区域","所属分公司","代办处名称","加入期数","登录状态"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AgentVO vo=(AgentVO) obj;
		List<AgentQueryBean> dataList=vo.getDataList();
		Map<Object, String> suspendStatus = new HashMap<Object, String>();
		suspendStatus.put(SuspendStatus.YES.getCode(), "禁止登录");
		suspendStatus.put(SuspendStatus.NO.getCode(), "允许登录");
		for(AgentQueryBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getAgentNo());
			row.createCell(1).setCellValue(o.getAreaCode());
			row.createCell(2).setCellValue(o.getCompanyCode());
			row.createCell(3).setCellValue(o.getName());
			row.createCell(4).setCellValue(o.getStartWeek());
			row.createCell(5).setCellValue(suspendStatus.get(o.getLoginStatus()));
		}
		
	}
	public static AgentExcelExporter builder() {
		return new AgentExcelExporter();
	}
}
