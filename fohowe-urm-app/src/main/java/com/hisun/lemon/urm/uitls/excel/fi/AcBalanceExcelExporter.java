package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceQueryBean;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceVO;
import com.hisun.lemon.urm.enums.fi.AccountStatusEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class AcBalanceExcelExporter extends URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="账户余额统计";
		String[] colNames=new String[] {"所属分公司","代办处编号","账户类型","用户编号","余额","可用余额","借款余额","状态"};
		super.exportActually(fileName, colNames, obj, response);
	}
	@Override
	public void export2(Object obj, HttpServletResponse response) {
		try {
			String fileName="账户余额统计";
			String[] colNames=new String[] {"所属分公司","代办处编号","账户类型","用户编号","用户名称","余额","可用余额","借款余额","状态"};
			super.exportActually2(addData(obj),fileName,colNames,response);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AcBalanceVO vo=(AcBalanceVO) obj;
		List<AcBalanceQueryBean> dataList=vo.getAcBalances();
		Map<Object, String> statusKV=EnumsUtils.EnumToMap(AccountStatusEnums.class);
		Map<String,String> acTypeKV=vo.getAcTypeKV();
		for(AcBalanceQueryBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(acTypeKV.get(o.getAcType()));
			row.createCell(index).setCellValue(o.getUserCode());
			row.createCell(index++, CellType.NUMERIC);
			row.createCell(index).setCellValue(o.getBalance().doubleValue());
			row.createCell(index++, CellType.NUMERIC);
			row.createCell(index).setCellValue(o.getValidBalance().doubleValue());
			row.createCell(index++, CellType.NUMERIC);
			row.createCell(index++).setCellValue(o.getOweAmt().doubleValue());
			row.createCell(index++).setCellValue(statusKV.get(o.getStatus()));
		}
		
	}
	public List<List<Object>> addData(Object obj) throws Exception {
		List<List<Object>> list = new ArrayList<List<Object>>();
		Map<Object, String> statusKV=EnumsUtils.EnumToMap(AccountStatusEnums.class);
		Map<Object,String> acTypeKV=EnumsUtils.EnumToMap(AcTypeEnums.class);
		List<AcBalanceQueryBean> dataList = (List<AcBalanceQueryBean>) obj;
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			AcBalanceQueryBean o = (AcBalanceQueryBean)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(o.getCompanyCode());
			data.add(o.getAgentNo());
			data.add(acTypeKV.get(o.getAcType()));
			data.add(o.getUserCode());
			data.add(o.getUserName());
			data.add(o.getBalance().doubleValue());
			data.add(o.getValidBalance().doubleValue());
			data.add(o.getOweAmt().doubleValue());
			data.add(statusKV.get(o.getStatus()));
			list.add(data);
		}
		return list;
	}
	public static AcBalanceExcelExporter builder() {
		return new AcBalanceExcelExporter();
	}
}
