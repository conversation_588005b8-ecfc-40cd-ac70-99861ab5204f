package com.hisun.lemon.urm.dao.st;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StStockOldsysVirtualDO;

import feign.Param;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IStStockOldsysVirtualDao extends BaseDao<StStockOldsysVirtualDO> {

    /** 
     * @Title: deleteByCompany 
     * @Description: TODO
     * @param companyCode
     * @return
     * @return: int
     */
    int deleteByCompany(@Param("companyCode") String companyCode);

    /** 
     * @Title: getByCompany 
     * @Description: TODO
     * @param companyCode
     * @return
     * @return: StStockOldsysVirtualDO
     */
    StStockOldsysVirtualDO getByCompany(@Param("companyCode") String companyCode);

    /** 
     * @Title: getByGoods
     * @Description: TODO
     * @param goodsCode
     * @return
     * @return: StStockOldsysVirtualDO
     */
    List<StStockOldsysVirtualDO> getByGoods(@Param("goodsCode") String goodsCode);
}