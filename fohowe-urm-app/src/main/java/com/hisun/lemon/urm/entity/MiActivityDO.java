/*
 * @ClassName MiActivityDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class MiActivityDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields actName 活动 名称
     */
    private String actName;
    /**
     * @Fields actDate 活动时间
     */
    private LocalDateTime actDate;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields operCode 操作人
     */
    private String operCode;
    /**
     * @Fields operDate 操作时间
     */
    private LocalDateTime operDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public LocalDateTime getActDate() {
        return actDate;
    }

    public void setActDate(LocalDateTime actDate) {
        this.actDate = actDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperCode() {
        return operCode;
    }

    public void setOperCode(String operCode) {
        this.operCode = operCode;
    }

    public LocalDateTime getOperDate() {
        return operDate;
    }

    public void setOperDate(LocalDateTime operDate) {
        this.operDate = operDate;
    }
}