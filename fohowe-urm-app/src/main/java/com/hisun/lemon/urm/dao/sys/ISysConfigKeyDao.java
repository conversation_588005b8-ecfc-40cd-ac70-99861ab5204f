/*
 * @ClassName ISysConfigKeyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:45
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysConfigKeyDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysConfigKeyDao extends BaseDao<SysConfigKeyDO> {

    /** 
     * @Title: pageFind 
     * @Description: 分页模糊查询
     * @param configCode
     * @param keyDesc
     * @return
     * @return: List<T>
     */
    public List<SysConfigKeyDO> pageFind(@Param("configCode") String configCode, @Param("keyDesc") String keyDesc);

    /** 
     * @Title: getByCode 
     * @Description: 根据参数编码查询记录
     * @param configCode
     * @return
     * @return: SysConfigKeyDO
     */
    public SysConfigKeyDO getByCode(@Param("configCode") String configCode);

    /** 
     * @Title: delete 
     * @Description: 删除记录
     * @param keyId
     * @return
     * @return: int
     */
    public int delete(@Param("keyId") long keyId);

    SysConfigKeyDO getByKeyId(@Param("keyId") long keyId);
}