/*
 * @ClassName IsysUserPwdLogDao
 * @Description 
 * @version 1.0
 * @Date 2018-02-08 18:10:52
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.sys.UserPwdInfoRspDTO;
import com.hisun.lemon.urm.dto.sys.UserPwdLogDTO;
import com.hisun.lemon.urm.entity.sys.sysUserPwdLogDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IsysUserPwdLogDao extends BaseDao<sysUserPwdLogDO> {

	List<UserPwdInfoRspDTO> pageQueryRecord(UserPwdLogDTO userPwdLogDTO);
}