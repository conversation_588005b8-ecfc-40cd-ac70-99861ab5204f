/**   
 * Copyright © 2018 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: PermissionBeanBO.java 
 * @Prject: fohowe-urm-app
 * @Package: com.hisun.lemon.urm.sys.bo 
 * @Description: 凤凰高科项目
 * @author: tian   
 * @date: 2018年1月12日 上午11:09:50 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.sys.bo;

import java.util.List;

/** 
 * @ClassName: PermissionBeanBO 
 * @Description: TODO
 * @author: tian
 * @date: 2018年1月12日 上午11:09:50  
 */
public class PermissionBeanBO {
	private String region;//0:分公司,1：区域,2总公司
	private String bonusType;
    private String areaCode;
    private String companyCode;
    private String agentNo;
    private String memberNo;
    private String userType;
    
    private List<String> managerCompanys; //管理的分公司
    
    public List<String> getManagerCompanys() {
		return managerCompanys;
	}
	public void setManagerCompanys(List<String> managerCompanys) {
		this.managerCompanys = managerCompanys;
	}
	public String getRegion() {
		return region;
	}
	public void setRegion(String region) {
		this.region = region;
	}
	public String getCompanyCode() {
        return companyCode;
    }
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
    public String getAgentNo() {
        return agentNo;
    }
    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }
    public String getMemberNo() {
        return memberNo;
    }
    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }
    public String getBonusType() {
        return bonusType;
    }
    public void setBonusType(String bonusType) {
        this.bonusType = bonusType;
    }
    public String getAreaCode() {
        return areaCode;
    }
    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }
    public String getUserType() {
        return userType;
    }
    public void setUserType(String userType) {
        this.userType = userType;
    }
}
