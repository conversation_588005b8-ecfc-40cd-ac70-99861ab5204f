package com.hisun.lemon.urm.excel.pd;



import java.io.IOException;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.pd.PdQmanifestPageRspDTO;
import com.hisun.lemon.urm.dto.pd.PdQmanifestRspDTO;
import com.hisun.lemon.urm.dto.pd.PdmanifestOrderItemsRspDTO;
import com.hisun.lemon.urm.enums.pd.QManiStatus;
import com.hisun.lemon.urm.service.pd.IPdQManifestService;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils.ExcelType;


public class QMainfestGoodExcelExporter2 {
	public DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	
	public void export(IPdQManifestService qManifestService ,Object obj, HttpServletResponse response) {
		String fileName="成品发运单管理-导出数据";
		try {
            ExcelHelperUtils.setResponseHead(fileName, response, ExcelType.XLSX);
            addCell(qManifestService,response.getOutputStream(), obj);
        } catch (IOException e) {
            e.printStackTrace();
            LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	private void addCell(IPdQManifestService qManifestService, ServletOutputStream outputStream, Object obj) throws IOException {
		XSSFWorkbook workbook = null;
        workbook = new XSSFWorkbook();
        
        PdQmanifestPageRspDTO  vo=(PdQmanifestPageRspDTO) obj;
		List<PdQmanifestRspDTO> dataList=vo.getQmanifestRspList();
        
		int rowNums = 0;
        String sheetName = "成品发运单管理";
        XSSFSheet sheet = workbook.createSheet(sheetName);
        sheet.setColumnWidth(0, 20*256);
        sheet.setColumnWidth(1, 20*256);
        sheet.setColumnWidth(2, 20*256);
        sheet.setColumnWidth(3, 20*256);
        sheet.setColumnWidth(4, 20*256);
        sheet.setColumnWidth(5, 20*256);
        sheet.setColumnWidth(6, 20*256);
        sheet.setColumnWidth(7, 20*256);
        XSSFCellStyle columnTopStyle = this.getColumnTopStyle(workbook);// 获取列头样式对象
        XSSFCellStyle columnCenterStyle = this.getColumnCenterStyle(workbook);// 获取中间对齐样式对象
        XSSFCellStyle columnRightStyle = this.getColumnRightStyle(workbook);// 获取右边对齐样式对象
        
        for(PdQmanifestRspDTO o:dataList) {
        	BigDecimal totalPrice=BigDecimal.ZERO;
        	BigDecimal totalPv=BigDecimal.ZERO;
        	
        	XSSFRow row = sheet.createRow(rowNums);
        	sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums + 1, 0, 7));
        	XSSFCell cell = row.createCell(0);
        	cell.setCellStyle(columnTopStyle);
        	cell.setCellValue("凤凰集团    成品分货单");
        	cell = row.createCell(1);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnTopStyle);
        	rowNums++;
        	row = sheet.createRow(rowNums);
        	cell = row.createCell(0);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(1);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnCenterStyle);
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
            XSSFCell cell0 = row.createCell(0);
            XSSFCell cell1 = row.createCell(1);
            XSSFCell cell2 = row.createCell(2);
            XSSFCell cell3 = row.createCell(3);
            XSSFCell cell4 = row.createCell(4);
            XSSFCell cell5 = row.createCell(5);
            XSSFCell cell6 = row.createCell(6);
            XSSFCell cell7 = row.createCell(7);
        	cell0.setCellStyle(columnRightStyle);
        	cell0.setCellValue("调出仓：");
        	cell1.setCellStyle(columnCenterStyle);
        	cell1.setCellValue(o.getFromstore());
        	
        	cell2.setCellStyle(columnRightStyle);
        	cell2.setCellValue("调入仓：");
        	cell3.setCellStyle(columnCenterStyle);
        	cell3.setCellValue(o.getReqstore());
        	
        	cell4.setCellStyle(columnRightStyle);
        	cell4.setCellValue("出库单号：");
        	cell5.setCellStyle(columnCenterStyle);
        	cell5.setCellValue(o.getReceiptno());
        	
        	cell6.setCellStyle(columnRightStyle);
        	cell6.setCellValue("调拨制单人：");
        	cell7.setCellStyle(columnCenterStyle);
        	cell7.setCellValue(o.getSender());
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
            cell0 = row.createCell(0);
            cell1 = row.createCell(1);
            cell2 = row.createCell(2);
            cell3 = row.createCell(3);
            cell4 = row.createCell(4);
            cell5 = row.createCell(5);
            cell6 = row.createCell(6);
            cell7 = row.createCell(7);
        	cell0.setCellStyle(columnRightStyle);
        	cell0.setCellValue("入库单日期：");
        	cell1.setCellStyle(columnCenterStyle);
        	cell1.setCellValue(o.getInHouseDate()==null?"":o.getInHouseDate().format(ymdhms));
        	
        	cell2.setCellStyle(columnRightStyle);
        	cell2.setCellValue("发货日期：");
        	cell3.setCellStyle(columnCenterStyle);
        	cell3.setCellValue(o.getSenddate()==null?"":o.getSenddate().format(ymdhms));
        	
        	cell4.setCellStyle(columnRightStyle);
        	cell4.setCellValue("收货日期：");
        	cell5.setCellStyle(columnCenterStyle);
        	cell5.setCellValue(o.getRecdate()==null?"":o.getRecdate().format(ymdhms));
        	
        	cell6.setCellStyle(columnRightStyle);
        	cell6.setCellValue("调拨发货人：");
        	cell7.setCellStyle(columnCenterStyle);
        	cell7.setCellValue(o.getSender());
        	rowNums++;
        	   	
        	row = sheet.createRow(rowNums);
            cell0 = row.createCell(0);
            cell1 = row.createCell(1);
            cell2 = row.createCell(2);
            cell3 = row.createCell(3);
            cell4 = row.createCell(4);
            cell5 = row.createCell(5);
            cell6 = row.createCell(6);
            cell7 = row.createCell(7);
        	cell0.setCellStyle(columnRightStyle);
        	cell0.setCellValue("审核：");
        	cell1.setCellStyle(columnCenterStyle);
        	Map statusKV = EnumsUtils.EnumToMap(QManiStatus.class);
        	cell1.setCellValue(o.getOrderstatus()==null?"":statusKV.get(o.getOrderstatus().toString())+"");
        	
        	cell2.setCellStyle(columnRightStyle);
        	cell2.setCellValue("是否发货：");
        	cell3.setCellStyle(columnCenterStyle);
        	cell3.setCellValue(o.getSendstatus()==0?"":"分公司审核");
        	
        	cell4.setCellStyle(columnRightStyle);
        	cell4.setCellValue("是否收货：");
        	cell5.setCellStyle(columnCenterStyle);
        	cell5.setCellValue(o.getRecstatus()==0?"":"分公司审核");
        	
        	cell6.setCellStyle(columnRightStyle);
        	cell6.setCellValue("发运单号：");
        	cell7.setCellStyle(columnCenterStyle);
        	cell7.setCellValue(o.getAllotno());
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
        	cell0 = row.createCell(0);
        	cell0.setCellStyle(columnRightStyle);
        	cell0.setCellValue("备注：");
        	sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 1, 7));
        	cell7.setCellStyle(columnCenterStyle);
        	cell7.setCellValue(o.getAllotno());
        	cell = row.createCell(1);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnCenterStyle);
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
        	sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 0, 7));
        	cell = row.createCell(0);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(1);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnCenterStyle);
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
        	sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums+1, 0, 7));
            cell = row.createCell(0);
        	cell.setCellStyle(columnCenterStyle);
        	cell.setCellValue("成品分货单明细");
        	cell = row.createCell(1);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnCenterStyle);
        	rowNums++;
        	row = sheet.createRow(rowNums);
        	cell = row.createCell(0);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(1);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnCenterStyle);
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
    		cell0 = row.createCell(0);
            cell1 = row.createCell(1);
            cell2 = row.createCell(2);
            cell3 = row.createCell(3);
            cell4 = row.createCell(4);
            cell5 = row.createCell(5);
            cell6 = row.createCell(6);
            cell7 = row.createCell(7);
            cell0.setCellStyle(columnCenterStyle);
            cell1.setCellStyle(columnCenterStyle);
            cell2.setCellStyle(columnCenterStyle);
            cell3.setCellStyle(columnCenterStyle);
            cell4.setCellStyle(columnCenterStyle);
            cell5.setCellStyle(columnCenterStyle);
            cell6.setCellStyle(columnCenterStyle);
            cell7.setCellStyle(columnCenterStyle);
            cell0.setCellValue("商品编号");
            cell1.setCellValue("商品名称");
            cell2.setCellValue("单价");
            cell3.setCellValue("分值");
            cell4.setCellValue("数量");
            cell5.setCellValue("单品金额小计");
            cell6.setCellValue("单品分值小计");
            cell7.setCellValue("币种");
            rowNums++;
            
            List<PdmanifestOrderItemsRspDTO> rspDTO = qManifestService.queryGoods(o.getId());
        	for(PdmanifestOrderItemsRspDTO rsp:rspDTO) {
        		row = sheet.createRow(rowNums);
        		cell0 = row.createCell(0);
                cell1 = row.createCell(1);
                cell2 = row.createCell(2);
                cell3 = row.createCell(3);
                cell4 = row.createCell(4);
                cell5 = row.createCell(5);
                cell6 = row.createCell(6);
                cell7 = row.createCell(7);
                cell0.setCellStyle(columnCenterStyle);
                cell1.setCellStyle(columnCenterStyle);
                cell2.setCellStyle(columnCenterStyle);
                cell3.setCellStyle(columnCenterStyle);
                cell4.setCellStyle(columnCenterStyle);
                cell5.setCellStyle(columnCenterStyle);
                cell6.setCellStyle(columnCenterStyle);
                cell7.setCellStyle(columnCenterStyle);
                cell0.setCellValue(rsp.getGoodsCode());
                cell1.setCellValue(rsp.getGoodsName());
                cell2.setCellValue(rsp.getStandardPrice()+"");
                cell3.setCellValue(rsp.getStandardFv()+"");
                cell4.setCellValue(rsp.getSetQty());
                cell5.setCellValue(rsp.getStandardPrice().multiply(BigDecimal.valueOf(rsp.getSetQty()))+"");
                cell6.setCellValue(rsp.getStandardFv().multiply(BigDecimal.valueOf(rsp.getSetQty()))+"");
                cell7.setCellValue("");
                totalPrice=totalPrice.add(rsp.getStandardPrice().multiply(BigDecimal.valueOf(rsp.getSetQty())));
                totalPv=totalPv.add(rsp.getStandardFv().multiply(BigDecimal.valueOf(rsp.getSetQty())));
                rowNums++;
        	}
        	
        	row = sheet.createRow(rowNums);
        	cell = row.createCell(0);
        	cell.setCellStyle(columnRightStyle);
        	cell.setCellValue("合计：");
            cell = row.createCell(1);
            cell.setCellStyle(columnCenterStyle);
            cell.setCellValue("");
            cell = row.createCell(2);
            cell.setCellStyle(columnCenterStyle);
            cell.setCellValue("");
            cell = row.createCell(3);
            cell.setCellStyle(columnCenterStyle);
            cell.setCellValue("");
            cell = row.createCell(4);
            cell.setCellStyle(columnCenterStyle);
            cell.setCellValue("");
            cell = row.createCell(5);
            cell.setCellStyle(columnCenterStyle);
            cell.setCellValue(totalPrice+"");
            cell = row.createCell(6);
            cell.setCellStyle(columnCenterStyle);
            cell.setCellValue(totalPv+"");
            cell = row.createCell(7);
            cell.setCellStyle(columnCenterStyle);
            cell.setCellValue("");
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
        	sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums, 0, 7));
        	cell = row.createCell(0);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(1);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnCenterStyle);
        	
        	rowNums++;
        	
        	row = sheet.createRow(rowNums);
        	sheet.addMergedRegion(new CellRangeAddress(rowNums, rowNums + 1, 0, 7));
        	cell = row.createCell(0);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(1);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnTopStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnTopStyle);
        	rowNums++;
        	row = sheet.createRow(rowNums);
        	cell = row.createCell(0);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(1);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(2);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(3);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(4);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(5);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(6);
        	cell.setCellStyle(columnCenterStyle);
        	cell = row.createCell(7);
        	cell.setCellStyle(columnCenterStyle);
        	rowNums++;
        }
        workbook.write(outputStream);
	}

	
	public XSSFCellStyle getColumnTopStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        font.setFontName("Courier New");
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        return style;
    }

    public XSSFCellStyle getColumnCenterStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        return style;
    }

    public XSSFCellStyle getColumnRightStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        return style;
    }

    public XSSFCellStyle getColumnRedStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setColor(XSSFFont.COLOR_RED);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
}
