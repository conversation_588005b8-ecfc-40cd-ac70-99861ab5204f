/*
 * @ClassName IMiRightLevelupLogDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.urm.dto.mi.member.RightUpgradeBean;
import com.hisun.lemon.urm.dto.mi.member.RightUpgradeReqDTO;
import com.hisun.lemon.urm.entity.MiRightLevelupLogDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IMiRightLevelupLogDao extends BaseDao<MiRightLevelupLogDO> {

	int getTotalCount(RightUpgradeReqDTO dto);

	List<RightUpgradeBean> getListByPageBreak(RightUpgradeReqDTO dto);

	void delByRightNoAndLevel(@Param("rightNo")String rightNo,@Param("levelType") String levelType);

	void delByMemberNoAndLevel(@Param("memberNo")String memberNo, @Param("levelType")String levelType,@Param("upType") String upType);
}