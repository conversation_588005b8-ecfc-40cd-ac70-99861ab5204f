package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.sys.SysConfigScaleReqDTO;
import com.hisun.lemon.urm.entity.sys.SysConfigScaleDO;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ISysConfigScaleDao extends BaseDao<SysConfigScaleDO> {


    int deleteByPrimaryKey(Long id);

    int insert(SysConfigScaleDO row);

    int insertSelective(SysConfigScaleDO row);

    SysConfigScaleDO selectByPrimaryKey(Long id);
    List<SysConfigScaleDO>  selectByKey(SysConfigScaleReqDTO reqDTO);
    SysConfigScaleDO  selectByCompany(SysConfigScaleReqDTO reqDTO);
    SysConfigScaleDO  selectByAcBalnace(@Param("companyCode") String companyCode,@Param("configKeyId") Integer configKeyId);
    int batchInsert(@Param("list") List<SysConfigScaleDO> list);

    int updateByPrimaryKeySelective(SysConfigScaleDO row);

    int updateByPrimaryKey(SysConfigScaleDO row);
}