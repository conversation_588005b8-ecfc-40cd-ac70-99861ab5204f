package com.hisun.lemon.urm.entity.sys;

import java.time.LocalDateTime;
import com.hisun.lemon.framework.data.BaseDO;

public class SysUserGrantDO extends BaseDO {
	
	/**
	 * 主键
	 */
	private Long id;
	/**
	 * 用户编号
	 */
    private String userCode;
    /**
     * 授权编号 (组织机构编码：奖金制度 、分公司、代办处编码)
     */
    private String organtCode;
    /**
     * 授权机构类型（1 奖金制度 2 分公司  3 代办处）
     */
    private Integer organType;
    /**
     * 操作员编号
     */
    private String oprUsrCode;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getOrgantCode() {
        return organtCode;
    }

    public void setOrgantCode(String organtCode) {
        this.organtCode = organtCode;
    }

    public Integer getOrganType() {
        return organType;
    }

    public void setOrganType(Integer organType) {
        this.organType = organType;
    }

    public String getOprUsrCode() {
        return oprUsrCode;
    }

    public void setOprUsrCode(String oprUsrCode) {
        this.oprUsrCode = oprUsrCode;
    }

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public LocalDateTime getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(LocalDateTime modifyTime) {
		this.modifyTime = modifyTime;
	}

}