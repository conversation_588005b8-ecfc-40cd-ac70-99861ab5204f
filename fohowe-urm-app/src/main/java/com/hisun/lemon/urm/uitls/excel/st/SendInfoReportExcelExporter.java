package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.dto.pd.ReportRspDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class SendInfoReportExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "出库销售出库汇总报表";
        ReportRspDTO vo = (ReportRspDTO) obj;
        String[] colNames = null;
        if (JudgeUtils.isEmpty(vo.getColumns())) {
            colNames = new String[1];
        } else {
            colNames = new String[vo.getColumns().size()];
        }
        colNames[0] = "产品编号";
        int colNums = 1;
        for (int i = 0; i < vo.getColumns().size(); i++) {
            if (JudgeUtils.equalsAny(vo.getColumns().get(i), "GOODS_CODE")) {
                continue;
            }
            colNames[colNums] = JudgeUtils.isNull(vo.getColumns().get(i)) ? "/" : vo.getColumns().get(i) ;
            colNums++;
        }
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        ReportRspDTO vo = (ReportRspDTO) obj;
        List<Map<String, Object>> dataList = vo.getList();
        for (Map<String, Object> o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);

            // 出库->订货分公司 o.get("COMPANY_CODE").toString() + "->" + o.get("ORDER_COMPANY_CODE").toString()
            row.createCell(0).setCellValue(o.get("GOODS_CODE").toString());

            int colNums = 1;
            for (int i = 0; i < vo.getColumns().size(); i++) {
                if (JudgeUtils.equalsAny(vo.getColumns().get(i), "GOODS_CODE")) {//ORDER_COMPANY_CODE, "COMPANY_CODE"
                    continue;
                }
                String cellValue = vo.getColumns().get(i);
                row.createCell(colNums).setCellValue(o.get(cellValue).toString());
                colNums++;
            }
        }

    }

    public static SendInfoReportExcelExporter builder() {
        return new SendInfoReportExcelExporter();
    }
}
