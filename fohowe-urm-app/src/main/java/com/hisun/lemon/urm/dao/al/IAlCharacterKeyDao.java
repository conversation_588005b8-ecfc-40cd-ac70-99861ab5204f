/*
 * @ClassName IAlCharacterKeyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-25 17:47:04
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlCharacterKeyDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlCharacterKeyDao extends BaseDao<AlCharacterKeyDO> {

    /** 
     * @Title: getByKey 
     * @Description: 根据key值获取ID
     * @param characterKey
     * @return
     * @return: AlCharacterKeyDO
     */
    AlCharacterKeyDO getByKey(@Param("characterKey") String characterKey);

    /**
     * 查询字符键值列表
     * 
     * @param characterKey
     * @param keyDesc
     * @param keyWords
     * @return
     */
    public List<AlCharacterKeyDO> getRecordList(@Param("characterKey") String characterKey, @Param("keyDesc") String keyDesc,
            @Param("keyWords") String keyWords);

    /** 
     * @Title: deleteById 
     * @Description: 删除
     * @param idList
     * @return
     * @return: int
     */
    int delete(@Param("id") Long id);

}