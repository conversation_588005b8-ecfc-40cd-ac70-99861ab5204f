/*
 * @ClassName PoInvoiceDO
 * @Description 
 * @version 1.0
 * @Date 2018-02-03 11:29:20
 */
package com.hisun.lemon.urm.entity.pd;

import java.time.LocalDate;

import com.hisun.lemon.framework.data.BaseDO;

public class PoInvoiceDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Integer id;
    /**
     * @Fields invoiceNo 发票编号
     */
    private String invoiceNo;
    /**
     * @Fields orderNo 订单编号
     */
    private String orderNo;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields companyName 公司名称
     */
    private String companyName;
    /**
     * @Fields companyRegNo 公司注册号
     */
    private String companyRegNo;
    /**
     * @Fields address 地址
     */
    private String address;
    /**
     * @Fields zip 邮编
     */
    private String zip;
    /**
     * @Fields city 城市
     */
    private String city;
    /**
     * @Fields country 国家
     */
    private String country;
    /**
     * @Fields taxNo 增值税号
     */
    private String taxNo;
    /**
     * 
     */
    private String email;
    /**
     * @Fields createCode 创建编号
     */
    private String createCode;
    /**
     * @Fields delFlag 是否删除(0-否 1-是）
     */
    private String delFlag;
    private LocalDate orderDate;
    private LocalDate payDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyRegNo() {
        return companyRegNo;
    }

    public void setCompanyRegNo(String companyRegNo) {
        this.companyRegNo = companyRegNo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public LocalDate getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDate orderDate) {
        this.orderDate = orderDate;
    }

    public LocalDate getPayDate() {
        return payDate;
    }

    public void setPayDate(LocalDate payDate) {
        this.payDate = payDate;
    }

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
    
}