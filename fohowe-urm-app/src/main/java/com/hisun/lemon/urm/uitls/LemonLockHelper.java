package com.hisun.lemon.urm.uitls;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hisun.lemon.common.Callback;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.Validate;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.lock.UnableToAquireLockException;
import com.hisun.lemon.urm.common.MsgCdLC;
@Component
public class LemonLockHelper {
    private static final Logger logger = LoggerFactory.getLogger(LemonLockHelper.class);
    
    @Autowired
    private DistributedLocker distributedLocker;
    
    public <T> T lock(Callback<T> callback, LockedInfo locked) {
        Validate.notEmpty(locked.getLockName());
        try {
          return  distributedLocker.lock(locked.getLockName(), locked.getLeaseTime(), locked.getWaitTime(), 
            		callback);
        } catch (UnableToAquireLockException e) {
            if(logger.isWarnEnabled()) {
                logger.warn("Method  has not aquired lock with lock name {}, lease time {}, wait time {}", 
                		locked.getLockName(), locked.getLeaseTime(), locked.getWaitTime());
            }
            LemonException.throwBusinessException(MsgCdLC.OPERATION_BUSY_FAIL.getMsgCd());
            
        } catch (LemonException e) {
                throw e;
        } catch (Throwable e) {
                LemonException.throwLemonException(e);
        }
		return null;
        
    }
  
}
