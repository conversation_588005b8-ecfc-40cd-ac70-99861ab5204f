/*
 * @ClassName FiAcExchangeDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class FiAcExchangeDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * 编号
     */
    private String exchangeNo;
    /**
     * @Fields acType 账户类型，fb=fb ， f$＝f$，fv＝fv, f0=f000，pv=活跃pv，b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，s1=全球分红，s2=凤凰大使分红
     */
    private String acType;
    /**
     * @Fields companyCode 公司编号 company_code
     */
    private String companyCode;
    /**
     * @Fields userCode 用户编号 user_code
     */
    private String userCode;
    /**
     * @Fields money 兑换金额 
     */
    private BigDecimal money;
    /**
     * @Fields handlingFee 手续费 handling_fee
     */
    private BigDecimal handlingFee;
    /**
     * @Fields status 状态，0:新建 1:审核
     */
    private String status;
    /**
     * @Fields creatorCode 创建人编号 creator_code
     */
    private String creatorCode;
    /**
     * @Fields checkerCode 确认人编号 checker_code
     */
    private String checkerCode;
    /**
     * @Fields checkTime 确认时间 check_time
     */
    private LocalDateTime checkTime;
    /**
     * @Fields memo 备注(会员) 
     */
    private String memo;
    /**
     * @Fields remark 摘要(公司) 
     */
    private String remark;
    /**
     * @Fields periodWeek 兑换申请期数
     */
    private String periodWeek;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 兑换类型
     */
    private Integer exType;
    
    private BigDecimal moneyFv; 
    
	private BigDecimal moneyH0;
	
	/**
     * 打折或者加价金额
     */
    private BigDecimal attrValue;
    
    /**
     * 活动规则编号
     */
    private Long eventAttrId;
    /**
     * 促销状态（1=参与、0=未参与、2=不可参与）
     */
    private Integer promotionStatus;
	
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public BigDecimal getHandlingFee() {
        return handlingFee;
    }

    public void setHandlingFee(BigDecimal handlingFee) {
        this.handlingFee = handlingFee;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public Integer getExType() {
		return exType;
	}

	public void setExType(Integer exType) {
		this.exType = exType;
	}

	public BigDecimal getMoneyFv() {
		return moneyFv;
	}

	public void setMoneyFv(BigDecimal moneyFv) {
		this.moneyFv = moneyFv;
	}

	public BigDecimal getMoneyH0() {
		return moneyH0;
	}

	public void setMoneyH0(BigDecimal moneyH0) {
		this.moneyH0 = moneyH0;
	}

	public String getExchangeNo() {
		return exchangeNo;
	}

	public void setExchangeNo(String exchangeNo) {
		this.exchangeNo = exchangeNo;
	}

	public BigDecimal getAttrValue() {
		return attrValue;
	}

	public void setAttrValue(BigDecimal attrValue) {
		this.attrValue = attrValue;
	}

	public Long getEventAttrId() {
		return eventAttrId;
	}

	public void setEventAttrId(Long eventAttrId) {
		this.eventAttrId = eventAttrId;
	}

	public Integer getPromotionStatus() {
		return promotionStatus;
	}

	public void setPromotionStatus(Integer promotionStatus) {
		this.promotionStatus = promotionStatus;
	}
	
}