/*
 * @ClassName ISysManagerDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-09 20:39:39
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysManagerDO;

import feign.Param;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ISysManagerDao extends BaseDao<SysManagerDO> {

    /** 
     * @Title: findByDepartment 
     * @Description: TODO
     * @param departmentId
     * @return
     * @return: List<T>
     */
    List<SysManagerDO> findByDepartment(@Param("departmentId") String departmentId);
}