/*
 * @ClassName IFiAcApplDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.appl.AcApplDTO;
import com.hisun.lemon.urm.dto.fi.appl.AcApplQueryBean;
import com.hisun.lemon.urm.entity.FiAcApplDO;

@Mapper
public interface IFiAcApplDao extends BaseDao<FiAcApplDO> {

	List<AcApplQueryBean> getListByCondition(AcApplDTO acApplDTO);
	
	AcApplQueryBean getListByConditionTotalF$(AcApplDTO acApplDTO);

	int getTotalCount(AcApplDTO acApplDTO);

	int deleteById(@Param("ids") String[] ids);
	
	String getMaxDateByOrderNo(@Param("applNo") String applNo);
	
	int updateFinStatus(@Param("id") Long id,@Param("finStatus") int finStatus,@Param("localFinMoney") BigDecimal localFinMoney);
}