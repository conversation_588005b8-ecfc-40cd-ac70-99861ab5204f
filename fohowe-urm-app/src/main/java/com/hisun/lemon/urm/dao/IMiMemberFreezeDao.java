/*
 * @ClassName IMiMemberFreezeDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.agent.AgentMemFreezeDTO;
import com.hisun.lemon.urm.dto.mi.agent.AgentMemFreezeQueryBean;
import com.hisun.lemon.urm.entity.MiMemberFreezeDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IMiMemberFreezeDao extends BaseDao<MiMemberFreezeDO> {

	List<AgentMemFreezeQueryBean> getListByCondition(AgentMemFreezeDTO agentMemFreezeDTO);

	int getTotalCount(AgentMemFreezeDTO agentMemFreezeDTO);
	/**
	 * 判断是否已经存在该用户的冻结记录
	 * @param memFreezeDO
	 * @return
	 */
	int isCanFreeze(MiMemberFreezeDO memFreezeDO);
}