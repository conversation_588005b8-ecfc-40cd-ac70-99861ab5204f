/*
 * @ClassName IStWarehomeAgentDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-07 16:52:56
 */
package com.hisun.lemon.urm.dao.st;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StWarehomeAgentDO;

import feign.Param;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IStWarehomeAgentDao extends BaseDao<StWarehomeAgentDO> {

    /** 
     * @Title: deleteByCompany 
     * @Description: TODO
     * @param companyId
     * @return
     * @return: int
     */
    int deleteByCompany(@Param("companyId") String companyId);

    /** 
     * @Title: getByCompany 
     * @Description: TODO
     * @param companyId
     * @return
     * @return: StWarehomeAgentDO
     */
    StWarehomeAgentDO getByCompany(@Param("companyId") String companyId);

    /** 
     * @Title: getByAgent 
     * @Description: TODO
     * @param agentNO
     * @return
     * @return: StWarehomeAgentDO
     */
    List<StWarehomeAgentDO> getByAgent(@Param("agentNO") String agentNO);
}