package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.OrderType;
import com.hisun.lemon.fohowe.common.enums.RecType;
import com.hisun.lemon.fohowe.common.enums.YesNoStatus;
import com.hisun.lemon.urm.dto.pd.SendInfoBeanDTO;
import com.hisun.lemon.urm.dto.pd.SendInfoQueryRspDTO;
import com.hisun.lemon.urm.enums.pd.OrderStatus;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class SendInfoQueryExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "库存统计列表";
        String[] colNames = new String[] { "分公司", "出库单号", "提货申请单号", "单据类型", "收货方式", "总金额", "支付F$", "支付FB","支付HV", "经销商编号",
                "代办处", "收货人姓名", "创建时间", "创建人", "发货时间", "发货人", "创建状态", "发货状态", "收货状态","物流费"};
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        SendInfoQueryRspDTO vo = (SendInfoQueryRspDTO) obj;
        if (JudgeUtils.isEmpty(vo.getSendInfoPage().getList())) {
            return;
        }
        List<SendInfoBeanDTO> dataList = vo.getSendInfoPage().getList();
        for (SendInfoBeanDTO o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);
            row.createCell(0).setCellValue(o.getCompanyCode());// 分公司
            row.createCell(1).setCellValue(o.getReceiptNo());// 出库单号
            row.createCell(2).setCellValue(o.getBiOrderNo());// 提货申请单号
            row.createCell(3).setCellValue(JudgeUtils.isNull(o.getOrderType()) ? ""
                    : OrderType.getByCode(Integer.valueOf(o.getOrderType())).getName());// 单据类型
            row.createCell(4).setCellValue(JudgeUtils.isNull(o.getRecType()) ? ""
                    : RecType.getByCode(Integer.valueOf(o.getRecType())).getName());// 收货方式
            if (JudgeUtils.isNotNull(o.getOrderType()) &&(o.getOrderType().equals(OrderType.goodsTurnOver.getCode().toString())||o.getOrderType().equals(OrderType.marketFundOrder.getCode().toString()))) {
            	 row.createCell(5).setCellValue(JudgeUtils.isNull(o.getPayAmount()) ? "" : o.getPayAmount().toString());// 6/101
			}else{
				row.createCell(5).setCellValue(JudgeUtils.isNull(o.getTotalAmount()) ? "" : o.getTotalAmount().toString());// 总金额
			}
           
            if (o.getOrderType().equals(OrderType.goodsTurnOver.getCode().toString())) {
            	row.createCell(6).setCellValue(JudgeUtils.isNull(o.getPayAmount()) ? "" : o.getPayAmount().toString());// 支付F$
			}else{
				row.createCell(6).setCellValue(JudgeUtils.isNull(o.getPayCash()) ? "" : o.getPayCash().toString());// 支付F$
			}
            row.createCell(7).setCellValue(JudgeUtils.isNull(o.getPayFb()) ? "" : o.getPayFb().toString());// 支付FB
            row.createCell(8).setCellValue(JudgeUtils.isNull(o.getTotalHV()) ? "" : o.getTotalHV().toString());// 支付HV
            
            row.createCell(9).setCellValue(JudgeUtils.isNull(o.getMemberNo()) ? "" : o.getMemberNo());// 经销商编号
            row.createCell(10).setCellValue(JudgeUtils.isNull(o.getAgentNo()) ? "" : o.getAgentNo());// 代办处
            row.createCell(11).setCellValue(JudgeUtils.isNull(o.getRecName()) ? "" : o.getRecName());// 收货人姓名
            row.createCell(12).setCellValue(JudgeUtils.isNull(o.getOrderDate()) ? "" : o.getOrderDate().format(ymdhms));// 创建时间
            row.createCell(13).setCellValue(JudgeUtils.isNull(o.getOrderor()) ? "" : o.getOrderor());// 创建人
            row.createCell(14).setCellValue(JudgeUtils.isNull(o.getSendDate()) ? "" : o.getSendDate().format(ymdhms));// 发货时间
            row.createCell(15).setCellValue(JudgeUtils.isNull(o.getSender()) ? "" : o.getSender());// 发货人
            row.createCell(16).setCellValue(
                    JudgeUtils.isNull(o.getOrderStatus()) ? "" : OrderStatus.getByCode(o.getOrderStatus()).getName());// 创建状态
            row.createCell(17).setCellValue(
                    JudgeUtils.isNull(o.getSendStatus()) ? "" : JudgeUtils.equals(o.getSendStatus(), "2")?"部分发货":YesNoStatus.getByCode(o.getSendStatus()).getName());// 发货状态
            row.createCell(18).setCellValue(
                    JudgeUtils.isNull(o.getRecStatus()) ? "" : JudgeUtils.equals(o.getSendStatus(), "2")?"部分收货":YesNoStatus.getByCode(o.getRecStatus()).getName());// 收货状态
            row.createCell(19).setCellValue(JudgeUtils.isNull(o.getSendFee()) ? "" : o.getSendFee().toString());
        }
    }

    public static SendInfoQueryExcelExporter builder() {
        return new SendInfoQueryExcelExporter();
    }
}
