/*
 * @ClassName ImpMiMemberDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class ImpMiMemberDO extends BaseDO {
    /**
     * @Fields lotNo 批次
     */
    private Long lotNo;
    /**
     * @Fields serialNo 序号
     */
    private Long serialNo;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields memberName 经销商姓名
     */
    private String memberName;
    /**
     * @Fields recommendNo 推荐人编号
     */
    private String recommendNo;
    /**
     * @Fields recommendName 推荐人姓名
     */
    private String recommendName;
    /**
     * @Fields linkNo 接点人编号
     */
    private String linkNo;
    /**
     * @Fields linkName 接点人姓名
     */
    private String linkName;
    /**
     * @Fields idCardNo 身份证号码
     */
    private String idCardNo;
    /**
     * @Fields accountBank 开户行
     */
    private String accountBank;
    /**
     * @Fields accountName 开户名
     */
    private String accountName;
    /**
     * @Fields accountCode 银行帐号
     */
    private String accountCode;
    /**
     * @Fields mobile 手机号码
     */
    private String mobile;
    /**
     * @Fields memo 备注
     */
    private String memo;
    /**
     * @Fields errMsg 错误信息
     */
    private String errMsg;
    /**
     * @Fields sRegNo 注册编号
     */
    private String sRegNo;
    /**
     * @Fields sRegRecommend 注册时推荐人
     */
    private String sRegRecommend;
    /**
     * @Fields sRecommendReg 推荐人注册编号
     */
    private String sRecommendReg;
    /**
     * @Fields sLinkReg 接点人注册编号
     */
    private String sLinkReg;
    /**
     * @Fields sNetNo 入网编号
     */
    private String sNetNo;
    /**
     * @Fields sNetLink 入网时接点人
     */
    private String sNetLink;
    /**
     * @Fields sRecommendNet 推荐人入网编号
     */
    private String sRecommendNet;
    /**
     * @Fields sLinkNet 接点人入网编号
     */
    private String sLinkNet;
    /**
     * @Fields recommendNew 推荐人为新人
     */
    private String recommendNew;
    /**
     * @Fields linkNew 接点人为新人
     */
    private String linkNew;
    /**
     * @Fields preSeqNo 预置顺序号
     */
    private Long preSeqNo;

    public Long getLotNo() {
        return lotNo;
    }

    public void setLotNo(Long lotNo) {
        this.lotNo = lotNo;
    }

    public Long getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Long serialNo) {
        this.serialNo = serialNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getRecommendNo() {
        return recommendNo;
    }

    public void setRecommendNo(String recommendNo) {
        this.recommendNo = recommendNo;
    }

    public String getRecommendName() {
        return recommendName;
    }

    public void setRecommendName(String recommendName) {
        this.recommendName = recommendName;
    }

    public String getLinkNo() {
        return linkNo;
    }

    public void setLinkNo(String linkNo) {
        this.linkNo = linkNo;
    }

    public String getLinkName() {
        return linkName;
    }

    public void setLinkName(String linkName) {
        this.linkName = linkName;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getAccountBank() {
        return accountBank;
    }

    public void setAccountBank(String accountBank) {
        this.accountBank = accountBank;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getsRegNo() {
        return sRegNo;
    }

    public void setsRegNo(String sRegNo) {
        this.sRegNo = sRegNo;
    }

    public String getsRegRecommend() {
        return sRegRecommend;
    }

    public void setsRegRecommend(String sRegRecommend) {
        this.sRegRecommend = sRegRecommend;
    }

    public String getsRecommendReg() {
        return sRecommendReg;
    }

    public void setsRecommendReg(String sRecommendReg) {
        this.sRecommendReg = sRecommendReg;
    }

    public String getsLinkReg() {
        return sLinkReg;
    }

    public void setsLinkReg(String sLinkReg) {
        this.sLinkReg = sLinkReg;
    }

    public String getsNetNo() {
        return sNetNo;
    }

    public void setsNetNo(String sNetNo) {
        this.sNetNo = sNetNo;
    }

    public String getsNetLink() {
        return sNetLink;
    }

    public void setsNetLink(String sNetLink) {
        this.sNetLink = sNetLink;
    }

    public String getsRecommendNet() {
        return sRecommendNet;
    }

    public void setsRecommendNet(String sRecommendNet) {
        this.sRecommendNet = sRecommendNet;
    }

    public String getsLinkNet() {
        return sLinkNet;
    }

    public void setsLinkNet(String sLinkNet) {
        this.sLinkNet = sLinkNet;
    }

    public String getRecommendNew() {
        return recommendNew;
    }

    public void setRecommendNew(String recommendNew) {
        this.recommendNew = recommendNew;
    }

    public String getLinkNew() {
        return linkNew;
    }

    public void setLinkNew(String linkNew) {
        this.linkNew = linkNew;
    }

    public Long getPreSeqNo() {
        return preSeqNo;
    }

    public void setPreSeqNo(Long preSeqNo) {
        this.preSeqNo = preSeqNo;
    }
}