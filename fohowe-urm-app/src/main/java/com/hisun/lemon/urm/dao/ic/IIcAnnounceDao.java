/*
 * @ClassName IIcAnnounceDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 13:00:36
 */
package com.hisun.lemon.urm.dao.ic;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.ic.IcAnnounceDO;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IIcAnnounceDao extends BaseDao<IcAnnounceDO> {

    /**
     * 
     * @Title: findAnnounceDesc 
     * @Description: 按照审核时间倒序排列查询list
     * @param userType
     * @param companyCode
     * @param title
     * @param langId 
     * @param title 
     * @param userCode 
     * @return
     * @return: List<IcAnnounceDO>
     */
    public List<IcAnnounceDO> findAnnounceDesc(@Param("userType") String userType,
            @Param("companyCode") String companyCode, @Param("langId") long langId,
            @Param("targetTerminal") String targetTerminal, @Param("type") String type, @Param("title") String title,
            @Param("userCode") String userCode,@Param("groupId") String groupId);

    /**
     * 审批公告更新
     * 
     * @param announceId
     * @param checkerCode
     * @param checkedTime
     * @return
     */
    public int updateByChecked(@Param("announcelist") List<String> announcelist,
            @Param("checkerCode") String checkerCode, @Param("checkedTime") LocalDateTime checkedTime);

    /**
     * 删除多条公告
     * 
     * @param announcelist
     * @return
     */
    public int deleteById(@Param("announcelist") List<String> announcelist);
    
    public int replaceUrlPlw();

}