/*
 * @ClassName ISysCompanyGroupDao
 * @Description 
 * @version 1.0
 * @Date 2019-02-12 17:01:51
 */
package com.hisun.lemon.urm.dao.sys;


import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysCompanyGroupDo;

@Mapper
public interface ISysCompanyGroupDao extends BaseDao<SysCompanyGroupDo> {
	
	SysCompanyGroupDo query(String companyCode);
	
	SysCompanyGroupDo finds(SysCompanyGroupDo companyGroupDo);
}