/*
 * @ClassName ISysListValueDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:46
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysListValueDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysListValueDao extends BaseDao<SysListValueDO> {

    /** 
     * @Title: findByKeyId 
     * @Description: 根据字典项keyId查询字典项值
     * @param keyId
     * @param listCode 
     * @param valueCode 
     * @param companyCode 
     * @return
     * @return: List<SysListValueDO>
     */
    public List<SysListValueDO> findByKeyId(@Param("keyId") long keyId, @Param("listCode") String listCode,
            @Param("valueCode") String valueCode, @Param("companyCode") String companyCode);

    /** 
     * @Title: getByKeyId 
     * @Description: 根据keyId查询是否有记录
     * @param keyId
     * @return
     * @return: SysListValueDO
     */
    public SysListValueDO getByKeyId(@Param("keyId") long keyId);
    
    public SysListValueDO getByCode(@Param("valueCode") String valueCode);

    /** 
     * @Title: delete 
     * @Description: 删除字典项值
     * @param valueId
     * @return
     * @return: int
     */
    public int delete(@Param("valueId") long valueId);

    /**
     * @Title: findAlterReason 
     * @Description: 查询库存调整原因列表
     * @param stockAlterType
     * @param listCode
     * @param valueCode
     * @param langCode 
     * @return
     * @return: List<SysListValueDO>
     */
    public List<SysListValueDO> findAlterReason(@Param("stockAlterType") int stockAlterType,
            @Param("listCode") String listCode, @Param("valueCode") String valueCode,
            @Param("langCode") String langCode,@Param("companyCode")String companyCode);

    /** 
     * @Title: findByLangCode 
     * @Description: TODO
     * @param keyId
     * @param listCode
     * @param valueCode
     * @param companyCode
     * @param langCode
     * @return
     * @return: List<SysListValueDO>
     */
    public List<SysListValueDO> findByLangCode(@Param("keyId") long keyId, @Param("listCode") String listCode,
            @Param("valueCode") String valueCode, @Param("companyCode") String companyCode,
            @Param("langCode") String langCode);
}