/*
 * @ClassName MiMemberDO
 * @Description 
 * @version 1.0
 * @Date 2021-9-3 9:22:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import com.hisun.lemon.urm.common.DateConstant;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

public class MiMemberPlusDO extends BaseDO{
	/**
     * @Fields id 
     */
	private Long id;
	/**
     * @Fields memberNo 经销商编号
     */
	private String memberNo;
	/**
     * @Fields memberName 经销商姓名
     */
	private String memberName;
	/**
     * @Fields agentNo 代办处编号
     */
	private String agentNo;
	/**
     * @Fields companyCode 分公司
     */
	private String companyCode;
	/**
     * @Fields promVip 是否VIP
     */
	private String promVip;
	/**
     * @Fields startWeek 资格开始期次-YYMMDD
     */
	private BigDecimal startWeek;
	/**
     * @Fields stageEndWeek 阶段奖翻倍结束期次-YYMMDD
     */
	private BigDecimal stageEndWeek;
	/**
     * @Fields PlusEndWeek PLUS精英推荐奖结束期次-YYMMDD
     */
	private BigDecimal PlusEndWeek;
	/**
     * @Fields promSales 促销资格
     */
	private String promSales;
	/**
     * @Fields promType 资格归类
     */
	private String promType;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}
	
	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	
	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getPromVip() {
		return promVip;
	}

	public void setPromVip(String promVip) {
		this.promVip = promVip;
	}

	public BigDecimal getStartWeek() {
		return startWeek;
	}

	public void setStartWeek(BigDecimal startWeek) {
		this.startWeek = startWeek;
	}

	public BigDecimal getStageEndWeek() {
		return stageEndWeek;
	}

	public void setStageEndWeek(BigDecimal stageEndWeek) {
		this.stageEndWeek = stageEndWeek;
	}

	public BigDecimal getPlusEndWeek() {
		return PlusEndWeek;
	}

	public void setPlusEndWeek(BigDecimal plusEndWeek) {
		PlusEndWeek = plusEndWeek;
	}

	public String getPromSales() {
		return promSales;
	}

	public void setPromSales(String promSales) {
		this.promSales = promSales;
	}

	public String getPromType() {
		return promType;
	}

	public void setPromType(String promType) {
		this.promType = promType;
	}
}