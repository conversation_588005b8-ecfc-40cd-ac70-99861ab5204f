/*
 * @ClassName FiAcParAdjustDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiAcParAdjustDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields acType 账户类型
     */
    private String acType;
    /**
     * @Fields adjustType 调整类型：a01=充值，a09=转账
     */
    private String adjustType;
    /**
     * @Fields outCompanyCode 出账公司
     */
    private String outCompanyCode;
    /**
     * @Fields outUserCode 出账用户
     */
    private String outUserCode;
    /**
     * @Fields outAmount 出账金额
     */
    private BigDecimal outAmount;
    /**
     * @Fields inCompanyCode 入账公司
     */
    private String inCompanyCode;
    /**
     * @Fields inUserCode 入账用户
     */
    private String inUserCode;
    /**
     * @Fields inAmount 入账金额
     */
    private BigDecimal inAmount;
    /**
     * @Fields status 状态：0=新单，1=审核，3=删除
     */
    private String status;
    /**
     * @Fields memo 摘要
     */
    private String memo;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createCode 创建人
     */
    private String createCode;
    /**
     * @Fields checkCode 审核人
     */
    private String checkCode;
    /**
     * @Fields checkTime 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * @Fields cancelCode 取消人
     */
    private String cancelCode;
    /**
     * @Fields cancelTime 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields wWeek 期数
     */
    private String wWeek;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAcType() {
        return acType;
    }

    public void setAcType(String acType) {
        this.acType = acType;
    }

    public String getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(String adjustType) {
        this.adjustType = adjustType;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getOutUserCode() {
        return outUserCode;
    }

    public void setOutUserCode(String outUserCode) {
        this.outUserCode = outUserCode;
    }

    public BigDecimal getOutAmount() {
        return outAmount;
    }

    public void setOutAmount(BigDecimal outAmount) {
        this.outAmount = outAmount;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getInUserCode() {
        return inUserCode;
    }

    public void setInUserCode(String inUserCode) {
        this.inUserCode = inUserCode;
    }

    public BigDecimal getInAmount() {
        return inAmount;
    }

    public void setInAmount(BigDecimal inAmount) {
        this.inAmount = inAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getwWeek() {
        return wWeek;
    }

    public void setwWeek(String wWeek) {
        this.wWeek = wWeek;
    }
}