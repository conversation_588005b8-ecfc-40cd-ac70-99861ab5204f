/*
 * @ClassName PdQmanifestDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-08 10:23:01
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PdQmanifestDO extends BaseDO {
    /**
     * @Fields id 00.ID ID
     */
    private Integer id;
    /**
     * @Fields receiptno 调拨单号 通过fn_sys_getno('pdpq',0)获得
     */
    private String receiptno;
    /**
     * @Fields fromstore 调出仓
     */
    private String fromstore;
    /**
     * @Fields tostore 调入仓
     */
    private String tostore;
    /**
     * @Fields orderor 创建人
     */
    private String orderor;
    /**
     * @Fields sender 发货人
     */
    private String sender;
    /**
     * @Fields receiver 收货人
     */
    private String receiver;
    /**
     * @Fields orderdate 创建日期
     */
    private LocalDateTime orderdate;
    /**
     * @Fields senddate 发货日期
     */
    private LocalDateTime senddate;
    /**
     * @Fields recdate 收货日期
     */
    private LocalDateTime recdate;
    /**
     * @Fields orderstatus 创建状态(bit)
            1=审核
            2=总部审核 3删除 4=财务审核
     */
    private Integer orderstatus;
    /**
     * @Fields sendstatus 发货状态1=审核
     */
    private Integer sendstatus;
    /**
     * @Fields recstatus 收货状态1=审核
     */
    private Integer recstatus;
    /**
     * @Fields ordchecker 创建审核人
     */
    private String ordchecker;
    /**
     * @Fields sendchecker 发货审核人
     */
    private String sendchecker;
    /**
     * @Fields recchecker 收货审核人
     */
    private String recchecker;
    /**
     * @Fields ordchkdate 请货审核日期
     */
    private LocalDateTime ordchkdate;
    /**
     * @Fields sendchkdate 发货审核日期
     */
    private LocalDateTime sendchkdate;
    /**
     * @Fields recchkdate 收货审核日期
     */
    private LocalDateTime recchkdate;
    /**
     * @Fields taxAmt 关税
     */
    private Integer taxAmt;
    /**
     * @Fields dcAmt 国际运输费用
     */
    private Integer dcAmt;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields totCost 总F$
     */
    private Integer totCost;
    /**
     * @Fields totPv 总PV
     */
    private Integer totPv;
    /**
     * @Fields canceldate 删除时间
     */
    private LocalDateTime canceldate;
    /**
     * @Fields canceler 删除人
     */
    private String canceler;
    /**
     * @Fields receiptType 调拨类型，0=调拨管理，1=申请调拨(向上级仓)
     */
    private Integer receiptType;
    /**
     * @Fields reqstore 申请仓
     */
    private String reqstore;
    /**
     * @Fields sendNo 发运单号
     */
    private String sendNo;
    /**
     * @Fields sendMemo 发运单信息
     */
    private String sendMemo;
    /**
     * @Fields memo 备注
     */
    private String memo;
    /**
     * @Fields isShip 是否通过分货单生成  0 =否  1=是
     */
    private Integer isShip;
    /**
     * @Fields shipno 成品分货单号
     */
    private String shipno;
    /**
     * @Fields toplace 目的地
     */
    private String toplace;
    /**
     * @Fields fromplace 发运地 ：1:珠海御品堂  2:天津和治友德 3:其他
     */
    private Integer fromplace;
    /**
     * @Fields transtype 运输方式 1：海运 2：陆运 3：空运 4：空运 5：个人自带 6：快递
     */
    private Integer transtype;
    /**
     * @Fields paytype 运输付款方式 1：运费国内支付，清关费用目的地支付 2：货到付款
     */
    private Integer paytype;
    /**
     * @Fields prearrivedate 预计达到日期
     */
    private LocalDateTime prearrivedate;
    /**
     * @Fields inhousedate 入库日期
     */
    private LocalDateTime inhousedate;
    /**
     * @Fields totalcase 总件数
     */
    private String totalcase;
    /**
     * @Fields actweight 实际重量
     */
    private String actweight;
    /**
     * @Fields actvolume 实际体积
     */
    private String actvolume;
    /**
     * @Fields transfee 运费结算价格
     */
    private String transfee;
    /**
     * @Fields supperno 由供应商调拨（手工填）
     */
    private String supperno;
    /**
     * @Fields allotno 调拨单号（无实际意义，手工填的，和上面调拨单号不一样）
     */
    private String allotno;
    /**
     * @Fields islad 是否提单 1 ：是
     */
    private String islad;
    /**
     * @Fields isestate 地产证  1：是
     */
    private String isestate;
    /**
     * @Fields istrait 品质证 1：是
     */
    private String istrait;
    /**
     * @Fields ishealth 卫生证 1：是
     */
    private String ishealth;
    /**
     * @Fields isinv 发票（英/俄） 1:是
     */
    private String isinv;
    /**
     * @Fields ispackcase 装箱单（英/俄）1:是
     */
    private String ispackcase;
    /**
     * @Fields isconfirmation 销售确认书 1:是
     */
    private String isconfirmation;
    /**
     * @Fields isother 其他 1：是
     */
    private String isother;
    /**
     * @Fields oldreceiptno 原收货单号 =PD_MANIFEST的RECEIPTNO(发货勾选原单用)
     */
    private String oldreceiptno;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getFromstore() {
        return fromstore;
    }

    public void setFromstore(String fromstore) {
        this.fromstore = fromstore;
    }

    public String getTostore() {
        return tostore;
    }

    public void setTostore(String tostore) {
        this.tostore = tostore;
    }

    public String getOrderor() {
        return orderor;
    }

    public void setOrderor(String orderor) {
        this.orderor = orderor;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public LocalDateTime getOrderdate() {
        return orderdate;
    }

    public void setOrderdate(LocalDateTime orderdate) {
        this.orderdate = orderdate;
    }

    public LocalDateTime getSenddate() {
        return senddate;
    }

    public void setSenddate(LocalDateTime senddate) {
        this.senddate = senddate;
    }

    public LocalDateTime getRecdate() {
        return recdate;
    }

    public void setRecdate(LocalDateTime recdate) {
        this.recdate = recdate;
    }

    public Integer getOrderstatus() {
        return orderstatus;
    }

    public void setOrderstatus(Integer orderstatus) {
        this.orderstatus = orderstatus;
    }

    public Integer getSendstatus() {
        return sendstatus;
    }

    public void setSendstatus(Integer sendstatus) {
        this.sendstatus = sendstatus;
    }

    public Integer getRecstatus() {
        return recstatus;
    }

    public void setRecstatus(Integer recstatus) {
        this.recstatus = recstatus;
    }

    public String getOrdchecker() {
        return ordchecker;
    }

    public void setOrdchecker(String ordchecker) {
        this.ordchecker = ordchecker;
    }

    public String getSendchecker() {
        return sendchecker;
    }

    public void setSendchecker(String sendchecker) {
        this.sendchecker = sendchecker;
    }

    public String getRecchecker() {
        return recchecker;
    }

    public void setRecchecker(String recchecker) {
        this.recchecker = recchecker;
    }

    public LocalDateTime getOrdchkdate() {
        return ordchkdate;
    }

    public void setOrdchkdate(LocalDateTime ordchkdate) {
        this.ordchkdate = ordchkdate;
    }

    public LocalDateTime getSendchkdate() {
        return sendchkdate;
    }

    public void setSendchkdate(LocalDateTime sendchkdate) {
        this.sendchkdate = sendchkdate;
    }

    public LocalDateTime getRecchkdate() {
        return recchkdate;
    }

    public void setRecchkdate(LocalDateTime recchkdate) {
        this.recchkdate = recchkdate;
    }

    public Integer getTaxAmt() {
        return taxAmt;
    }

    public void setTaxAmt(Integer taxAmt) {
        this.taxAmt = taxAmt;
    }

    public Integer getDcAmt() {
        return dcAmt;
    }

    public void setDcAmt(Integer dcAmt) {
        this.dcAmt = dcAmt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getTotCost() {
        return totCost;
    }

    public void setTotCost(Integer totCost) {
        this.totCost = totCost;
    }

    public Integer getTotPv() {
        return totPv;
    }

    public void setTotPv(Integer totPv) {
        this.totPv = totPv;
    }

    public LocalDateTime getCanceldate() {
        return canceldate;
    }

    public void setCanceldate(LocalDateTime canceldate) {
        this.canceldate = canceldate;
    }

    public String getCanceler() {
        return canceler;
    }

    public void setCanceler(String canceler) {
        this.canceler = canceler;
    }

    public Integer getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(Integer receiptType) {
        this.receiptType = receiptType;
    }

    public String getReqstore() {
        return reqstore;
    }

    public void setReqstore(String reqstore) {
        this.reqstore = reqstore;
    }

    public String getSendNo() {
        return sendNo;
    }

    public void setSendNo(String sendNo) {
        this.sendNo = sendNo;
    }

    public String getSendMemo() {
        return sendMemo;
    }

    public void setSendMemo(String sendMemo) {
        this.sendMemo = sendMemo;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getIsShip() {
        return isShip;
    }

    public void setIsShip(Integer isShip) {
        this.isShip = isShip;
    }

    public String getShipno() {
        return shipno;
    }

    public void setShipno(String shipno) {
        this.shipno = shipno;
    }

    public String getToplace() {
        return toplace;
    }

    public void setToplace(String toplace) {
        this.toplace = toplace;
    }

    public Integer getFromplace() {
        return fromplace;
    }

    public void setFromplace(Integer fromplace) {
        this.fromplace = fromplace;
    }

    public Integer getTranstype() {
        return transtype;
    }

    public void setTranstype(Integer transtype) {
        this.transtype = transtype;
    }

    public Integer getPaytype() {
        return paytype;
    }

    public void setPaytype(Integer paytype) {
        this.paytype = paytype;
    }

    public LocalDateTime getPrearrivedate() {
        return prearrivedate;
    }

    public void setPrearrivedate(LocalDateTime prearrivedate) {
        this.prearrivedate = prearrivedate;
    }

    public LocalDateTime getInhousedate() {
        return inhousedate;
    }

    public void setInhousedate(LocalDateTime inhousedate) {
        this.inhousedate = inhousedate;
    }

    public String getTotalcase() {
        return totalcase;
    }

    public void setTotalcase(String totalcase) {
        this.totalcase = totalcase;
    }

    public String getActweight() {
        return actweight;
    }

    public void setActweight(String actweight) {
        this.actweight = actweight;
    }

    public String getActvolume() {
        return actvolume;
    }

    public void setActvolume(String actvolume) {
        this.actvolume = actvolume;
    }

    public String getTransfee() {
        return transfee;
    }

    public void setTransfee(String transfee) {
        this.transfee = transfee;
    }

    public String getSupperno() {
        return supperno;
    }

    public void setSupperno(String supperno) {
        this.supperno = supperno;
    }

    public String getAllotno() {
        return allotno;
    }

    public void setAllotno(String allotno) {
        this.allotno = allotno;
    }

    public String getIslad() {
        return islad;
    }

    public void setIslad(String islad) {
        this.islad = islad;
    }

    public String getIsestate() {
        return isestate;
    }

    public void setIsestate(String isestate) {
        this.isestate = isestate;
    }

    public String getIstrait() {
        return istrait;
    }

    public void setIstrait(String istrait) {
        this.istrait = istrait;
    }

    public String getIshealth() {
        return ishealth;
    }

    public void setIshealth(String ishealth) {
        this.ishealth = ishealth;
    }

    public String getIsinv() {
        return isinv;
    }

    public void setIsinv(String isinv) {
        this.isinv = isinv;
    }

    public String getIspackcase() {
        return ispackcase;
    }

    public void setIspackcase(String ispackcase) {
        this.ispackcase = ispackcase;
    }

    public String getIsconfirmation() {
        return isconfirmation;
    }

    public void setIsconfirmation(String isconfirmation) {
        this.isconfirmation = isconfirmation;
    }

    public String getIsother() {
        return isother;
    }

    public void setIsother(String isother) {
        this.isother = isother;
    }

    public String getOldreceiptno() {
        return oldreceiptno;
    }

    public void setOldreceiptno(String oldreceiptno) {
        this.oldreceiptno = oldreceiptno;
    }
}