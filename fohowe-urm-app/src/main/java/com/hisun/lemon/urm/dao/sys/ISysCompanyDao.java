/*
 * @ClassName ISysCompanyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:05:49
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysCompanyDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysCompanyDao extends BaseDao<SysCompanyDO> {

    /** 
     * @Title: getCompanyList 
     * @Description: 查询公司／区域／物流中心列表
     * @param typeFlag
     * @param bonusTypeList 
     * @param areaList 
     * @param companyCode 
     * @param typeFlag2 
     * @return
     * @return: List<SysCompanyDO> 
     */
    public List<SysCompanyDO> getCompanyList(@Param("typeFlag") String typeFlag, @Param("bonusType") List<String> bonusTypeList,
            @Param("areaList") List<String> areaList, @Param("companyCodes") String companyCodes);
    
    public List<SysCompanyDO> getCompanyLists(@Param("typeFlag") String typeFlag, @Param("bonusType") List<String> bonusTypeList,
    		@Param("areaList") List<String> areaList, @Param("companyCodes") String companyCodes,@Param("companyCode") String companyCode);
    /**
     * 
     * @Title: getByCompanyCode 
     * @Description: 根据公司编码查询记录是否存在
     * @param companyCode
     * @return
     * @return: SysCompanyDO
     */
    public SysCompanyDO getByCompanyCode(@Param("companyCode") String companyCode, @Param("typeFlag") String typeFlag);

    /**
     * 
     * @Title: getByCompanyCode 
     * @Description: 根据公司编码查询记录是否存在
     * @param companyCode
     * @return
     * @return: SysCompanyDO
     */
    public SysCompanyDO getByCompanyCode(@Param("companyCode") String companyCode);

    /** 
     * @Title: findOrganizerList 
     * @Description: 查询公司组织列表
     * @param isDepatrment
     * @return
     * @return: List<SysCompanyDO>
     */
    public List<SysCompanyDO> findOrganizerList(@Param("isDepatrment") String isDepatrment);

    /** 
     * @Title: findLangExists 
     * @Description: 检查该公司是否存在该默认语言
     * @param langCode
     * @return
     * @return: SysCompanyDO
     */
    public SysCompanyDO findLangExists(@Param("langCode") String langCode);

    /** 
     * @Title: getByCompanyList 
     * @param companyList
     * @param typeFlagList 
     * @return
     * @return: List<SysCompanyDO>
     */
    public List<SysCompanyDO> getByCompanyList(@Param("companyList") List<String> companyList,
            @Param("typeFlagList") List<String> typeFlagList);
    
    //获取业务大区分公司列表。比如欧盟区分公司
    public List<SysCompanyDO> getCompanysByArea(@Param("areaType") String areaType);
     
    
}