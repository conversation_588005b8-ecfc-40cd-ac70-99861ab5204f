/*
 * @ClassName PdManifestItemDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-13 18:27:13
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class PdManifestItemDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields manifestId 单据头序号,PD_MANIFEST.ID
     */
    private Long manifestId;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    /**
     * @Fields prodDate 生产日期:若为null由tu_Manifest在收货审核时填入
     */
    private LocalDateTime prodDate;
    /**
     * @Fields inQty 进货数量:非超量收货时<=OrderQty
     */
    private Integer inQty;
    /**
     * @Fields oldQty 收货数量:=进货量+赠品,对退货单，OldQty=InQty
     */
    private Integer oldQty;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields state 
     */
    private String state;
    /**
     * @Fields setQty 已经发货数量
     */
    private Integer setQty;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getManifestId() {
        return manifestId;
    }

    public void setManifestId(Long manifestId) {
        this.manifestId = manifestId;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public LocalDateTime getProdDate() {
        return prodDate;
    }

    public void setProdDate(LocalDateTime prodDate) {
        this.prodDate = prodDate;
    }

    public Integer getInQty() {
        return inQty;
    }

    public void setInQty(Integer inQty) {
        this.inQty = inQty;
    }

    public Integer getOldQty() {
        return oldQty;
    }

    public void setOldQty(Integer oldQty) {
        this.oldQty = oldQty;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getSetQty() {
        return setQty;
    }

    public void setSetQty(Integer setQty) {
        this.setQty = setQty;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}