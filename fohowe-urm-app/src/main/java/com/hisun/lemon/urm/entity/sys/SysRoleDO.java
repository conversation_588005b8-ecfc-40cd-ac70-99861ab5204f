/*
 * @ClassName SysRoleDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-16 11:08:14
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysRoleDO extends BaseDO {
    /**
     * @Fields roleId 角色ID  ROLE_ID
     */
    private String roleId;
    /**
     * @Fields roleName 角色名称 ROLE_NAME
     */
    private String roleName;
    /**
     * @Fields roleDes 角色描述 ROLE_DES
     */
    private String roleDes;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleDes() {
        return roleDes;
    }

    public void setRoleDes(String roleDes) {
        this.roleDes = roleDes;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}