package com.hisun.lemon.urm.uitls;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.fohowe.common.enums.RightDisplayWayEnums;
import com.hisun.lemon.fohowe.common.enums.RightNodeLevel;
import com.hisun.lemon.fohowe.common.enums.RightPromtWayEnums;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.mi.member.MemRightAddReq;
import com.hisun.lemon.urm.entity.MiMemberRightDO;
/**
 * pv分配
 * <AUTHOR>
 * @date 2017-11-17 17:19
 */
public class PvCaculate {
	 private static final Logger logger = LoggerFactory.getLogger(PvCaculate.class);
	 /**
	  * 仅用于购买新经营权时的pv分配
	  * 此方法没有对pv与个数关系判断，请自行判断pv是否合格再调用
	 * @param pvLowLimit
	 * @param pvHigLimit
	 * @param req
	 * @param list
	 * @return 
	 * <AUTHOR>
	 * @param specialPromt  1+5 180促销模式接线与其他不同，他们的顶点是已经存在的主经营权，  true表示为该种模式，需要特殊处理
	  */
	public static List<MiMemberRightDO> distributePvForAddRight(BigDecimal pvLowLimit, BigDecimal pvHigLimit, MemRightAddReq req,
			List<MiMemberRightDO> list, boolean specialPromt) {
		if (logger.isDebugEnabled()) {
			logger.debug(ReflectionToStringBuilder.toString(req, ToStringStyle.MULTI_LINE_STYLE));
		}
		RightDisplayWayEnums displayEnums=RightDisplayWayEnums.getEnumsByValue(req.getDisplayType());
		
		RightPromtWayEnums promtEnums=RightPromtWayEnums.getEnumsByValue(req.getIsPromType());
		if(specialPromt) {  
			req.setBuyNum(req.getBuyNum()-1);
		}
		
		BigDecimal buyNum= new BigDecimal(req.getBuyNum());
		BigDecimal addPv= req.getAddPv();
		
		BigDecimal gap=pvHigLimit.subtract(pvLowLimit);//PV最高限制-PV最低选择
		BigDecimal lowerPV=pvLowLimit.multiply(buyNum);//PV最低选择乘以购买数
		BigDecimal s=addPv.subtract(lowerPV);//总pv-PV最低选择乘以购买数
		BigDecimal s2=s.divide(gap, 0, RoundingMode.DOWN);//计算有多少个合格点
		
		int fullNum=s2.intValue();//合格点数
		
		BigDecimal remainPv=addPv.subtract(lowerPV).subtract(gap.multiply(new BigDecimal(fullNum)));//剩余PV
		
		
		switch(displayEnums) 
		{ 
			case STRAIGHT: 
				/*
				 * 填充规则，先处理第一个，然后最后一个，然后倒数第二个  ，最后再从上到下
				 * 
				*/
				if(req.getBuyNum()==1) { //just one ,单独处理
					MiMemberRightDO d=list.get(0);
					d.setPvLimit(pvHigLimit); 
					d.setIsPromType(req.getIsPromType());
					d.setRegPv(addPv); 
					if(fullNum>=1) {
						d.setPv(pvHigLimit);
						d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
						d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
					}else {
						d.setPv(addPv);
						d.setLevelType(RightNodeLevel.PRE.getCode()+"");
					}
					
					
					break;
				}
		
				//优先填充
				List<MiMemberRightDO> newListStraigt=new ArrayList<MiMemberRightDO>();
				int one=0; 
				int two=list.size()-2; 
				int last=list.size()-1; 
				
				newListStraigt.add(list.get(one));
				
				if(last>one) {   
					newListStraigt.add(list.get(last));
				}
				if(one<two&&two<last) {   
					newListStraigt.add(list.get(two));
				}
				for(MiMemberRightDO d :newListStraigt) {
					if(fullNum>0) {
						fullNum--;
						d.setRegPv(pvHigLimit); 
						d.setPv(pvHigLimit);
						d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
						d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
					
					}else {
						if(remainPv.compareTo(BigDecimal.ZERO)==1) {
							d.setRegPv(pvLowLimit.add(remainPv)); 
							d.setPv(pvLowLimit.add(remainPv));
							remainPv=BigDecimal.ZERO;
						}else {
							d.setRegPv(pvLowLimit); 
							d.setPv(pvLowLimit);
						}
							
						d.setLevelType(RightNodeLevel.PRE.getCode()+"");
						
					}
					d.setPvLimit(pvHigLimit);
					d.setIsPromType(req.getIsPromType());
					
					
				}
				//处理其他的  和其他规则一样
				for(int i=0;i<list.size();i++) {
					if(i==one||i==two||i==last) {
						continue;
					}
					
					MiMemberRightDO d=list.get(i);
					if(fullNum>0) {
						fullNum--;
						
						d.setRegPv(pvHigLimit);
						d.setPv(pvHigLimit);
						d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
						d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
					
					}else {
						if(remainPv.compareTo(BigDecimal.ZERO)==1) {
							d.setRegPv(pvLowLimit.add(remainPv)); 
							d.setPv(pvLowLimit.add(remainPv));
							remainPv=BigDecimal.ZERO;
						}else {
							d.setRegPv(pvLowLimit); 
							d.setPv(pvLowLimit);
						}
							
						d.setLevelType(RightNodeLevel.PRE.getCode()+"");
						
					}
					d.setPvLimit(pvHigLimit);
					d.setIsPromType(req.getIsPromType());
				}
				
				
				break; 
			case DOUBLE_STRAIGHT: 
				logger.info("双直线");
				/*
				 * 填充规则，先处理第一个，再左下角，再右下角  最后从上到下
				 * 
				*/
				if(req.getBuyNum()==1) { //just one 
					MiMemberRightDO d=list.get(0);
					d.setPvLimit(pvHigLimit); 
					d.setIsPromType(req.getIsPromType());
					d.setRegPv(addPv); 
					if(fullNum>=1) {
						d.setPv(pvHigLimit);
						d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
						d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
						
					}else {
						d.setPv(addPv);
						d.setLevelType(RightNodeLevel.PRE.getCode()+"");
					}
					
					
					break;
				}
				List<MiMemberRightDO> newList=new ArrayList<MiMemberRightDO>();
				int n=list.size()/2; 
				int leftLast=(n-1)*2+1;
				int rightLast=0;
				if(!specialPromt) {
					newList.add(list.get(0));
				}
				
				newList.add(list.get(leftLast));
				
				if(list.size()%2 == 1) {
					rightLast=(n-1)*2+2;
					newList.add(list.get(rightLast));
				}else if(list.size()!=2){ 
					rightLast=(n-2)*2+2;
					newList.add(list.get(rightLast));
				}
				//优先填充
				for(MiMemberRightDO d :newList) {
					if(fullNum>0) {
						fullNum--;
						d.setRegPv(pvHigLimit); 
						d.setPv(pvHigLimit);
						d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
						d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
					
					}else {
						if(remainPv.compareTo(BigDecimal.ZERO)==1) {
							d.setRegPv(pvLowLimit.add(remainPv)); 
							d.setPv(pvLowLimit.add(remainPv));
							remainPv=BigDecimal.ZERO;
						}else {
							d.setRegPv(pvLowLimit); 
							d.setPv(pvLowLimit);
						}
							
						d.setLevelType(RightNodeLevel.PRE.getCode()+"");
						
					}
					d.setPvLimit(pvHigLimit);
					d.setIsPromType(req.getIsPromType());
					
					
				}
				//剩余的填充
				for(int i=0;i<list.size();i++) {
					
					if(i==0||i==leftLast||i==rightLast) {
						continue;
					}
					
					MiMemberRightDO d=list.get(i);
					if(fullNum>0) {
						fullNum--;
						
						d.setRegPv(pvHigLimit); 
						d.setPv(pvHigLimit);
						d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
						d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
					
					}else {
						if(remainPv.compareTo(BigDecimal.ZERO)==1) {
							d.setRegPv(pvLowLimit.add(remainPv)); 
							d.setPv(pvLowLimit.add(remainPv));
							remainPv=BigDecimal.ZERO;
						}else {
							d.setRegPv(pvLowLimit); 
							d.setPv(pvLowLimit);
						}
							
						d.setLevelType(RightNodeLevel.PRE.getCode()+"");
						
					}
					d.setPvLimit(pvHigLimit); 
					d.setIsPromType(req.getIsPromType());
				}
				
				break;  
			case TRIANGLE: //三角型
				logger.info("三角型");
				/*
				 * THREE_FOUR_PROMT_B 的填充规则为，先补满三个角
				 * 其他填充规则为， 从上到下
				 */
				if(promtEnums.equals(RightPromtWayEnums.THREE_FOUR_PROMT_B)) {
					//3+4  3个满的 ，其他为不满
					List<MiMemberRightDO> promtBList=new ArrayList<MiMemberRightDO>();
					int bfirst=0; 
					int bLeftLast=3;
					int bRightLast=6;
					promtBList.add(list.get(bfirst));
					promtBList.add(list.get(bLeftLast));
					promtBList.add(list.get(bRightLast));
					
					//优先填充
					for(MiMemberRightDO d :promtBList) {
					
						d.setRegPv(pvHigLimit); 
						d.setPv(pvHigLimit);
						d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
						d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
						d.setPvLimit(pvHigLimit);
						d.setIsPromType(req.getIsPromType());
					
					}
					
					fullNum=fullNum-3;
							
					//剩余的填充
					for(int i=0;i<list.size();i++) {
						
						if(i==bfirst||i==bLeftLast||i==bRightLast) {
							continue;
						}
						
						MiMemberRightDO d=list.get(i);
						if(fullNum>0) {
							fullNum--;
							
							d.setRegPv(pvHigLimit); 
							d.setPv(pvHigLimit);
							d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
							d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
						
						}else {
							if(remainPv.compareTo(BigDecimal.ZERO)==1) {
								d.setRegPv(pvLowLimit.add(remainPv)); 
								d.setPv(pvLowLimit.add(remainPv));
								remainPv=BigDecimal.ZERO;
							}else {
								d.setRegPv(pvLowLimit); 
								d.setPv(pvLowLimit);
							}
								
							d.setLevelType(RightNodeLevel.PRE.getCode()+"");
							
						}
						d.setPvLimit(pvHigLimit); 
						d.setIsPromType(req.getIsPromType());
					}
					
					
					
				}else {
					for(int i=0;i<list.size();i++) {
						MiMemberRightDO d=list.get(i);
						if(specialPromt&&i==0) {
							continue;
						}
						
						if(fullNum>0) {
							fullNum--;
							d.setRegPv(pvHigLimit); 
							d.setPv(pvHigLimit);
							d.setLevelType(RightNodeLevel.TYPICAL.getCode()+"");
							d.setActiveDate(DateTimeUtils.getCurrentLocalDateTime());
						
						}else {
							if(remainPv.compareTo(BigDecimal.ZERO)==1) {
								d.setRegPv(pvLowLimit.add(remainPv));
								d.setPv(pvLowLimit.add(remainPv));
								remainPv=BigDecimal.ZERO;
							}else {
								d.setRegPv(pvLowLimit); 
								d.setPv(pvLowLimit);
							}
								
							d.setLevelType(RightNodeLevel.PRE.getCode()+"");
							
						}
						d.setPvLimit(pvHigLimit); 
						d.setIsPromType(req.getIsPromType());
						
						
					}
				}
				
				
				break;  
			default: 
				LemonException.throwBusinessException(MsgCdLC.ENUM_TYPE_ERROR.getMsgCd()); 
		} 
		logger.info("req.getIsPvLimit():"+req.getIsPvLimit());
		if (req.getIsPvLimit() != null && req.getIsPvLimit().intValue()==1) {
			BigDecimal rightPvTotal = BigDecimal.ZERO;
			if(list.size()>0) {
				for (int i = 0; i<list.size();i++) {
					MiMemberRightDO miMemberRightDO = (MiMemberRightDO) list.get(i);
					rightPvTotal=rightPvTotal.add(miMemberRightDO.getPv());
				}
				logger.info("rightPvTotal:"+rightPvTotal);
				if (rightPvTotal.compareTo(BigDecimal.ZERO)==1
						&& addPv.compareTo(rightPvTotal)==1) {
					BigDecimal remPv=addPv.subtract(rightPvTotal);
					MiMemberRightDO miMemberRightDO=list.get(list.size()-1);
					if(miMemberRightDO != null) {
						logger.info("remPv:"+remPv);
						logger.info("getPv:"+miMemberRightDO.getPv());
						BigDecimal pv=miMemberRightDO.getPv().add(remPv);
						miMemberRightDO.setPv(pv);
						list.set(list.size()-1, miMemberRightDO);
						logger.info("getPv:"+miMemberRightDO.getPv());
					}
				}
			}
		}
		 
		return list;
	}
	
	
	
}
