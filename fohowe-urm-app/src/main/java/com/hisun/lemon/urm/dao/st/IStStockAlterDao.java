/*
 * @ClassName IStStockAlterDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-12 17:43:36
 */
package com.hisun.lemon.urm.dao.st;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StStockAlterDO;
import com.hisun.lemon.urm.entity.st.StStockAlterDetailDO;
import com.hisun.lemon.urm.service.pd.bo.StockAlterReportBO;

@Mapper
public interface IStStockAlterDao extends BaseDao<StStockAlterDO> {

    /** 
     * @Title: getPageList 
     * @Description: 分页查询
     * @param areaCode
     * @param companyList
     * @param receiptNo
     * @param reason
     * @param status
     * @param createBeginDate
     * @param createEndDate
     * @param checkBeginDate
     * @param checkEndDate
     * @return
     * @return: List<T>
     */
    List<StStockAlterDO> getPageList(@Param("areaCode") String areaCode, @Param("companyList") List<String> companyList,
            @Param("receiptNo") String receiptNo, @Param("reason") String reason, @Param("status") String status,
            @Param("createBeginDate") LocalDateTime createBeginDate,@Param("createEndDate") LocalDateTime createEndDate,
            @Param("checkBeginDate") LocalDateTime checkBeginDate,@Param("checkEndDate") LocalDateTime checkEndDate,
            @Param("confirmBeginDate") LocalDateTime confirmBeginDate,@Param("confirmEndDate") LocalDateTime confirmEndDate);
    /**
     * 库存调整明细查询
     * @param areaCode
     * @param companyList
     * @param receiptNo
     * @param reason
     * @param status
     * @param createBeginDate
     * @param createEndDate
     * @param checkBeginDate
     * @param checkEndDate
     * @return
     */
    List<StStockAlterDetailDO> getPageDetalList(@Param("areaCode") String areaCode, @Param("companyList") List<String> companyList,
    		@Param("receiptNo") String receiptNo, @Param("reason") String reason, @Param("status") String status,
    		@Param("createBeginDate") LocalDateTime createBeginDate,@Param("createEndDate") LocalDateTime createEndDate, 
    		@Param("checkBeginDate") LocalDateTime checkBeginDate,@Param("checkEndDate") LocalDateTime checkEndDate,
    		@Param("confirmBeginDate") LocalDateTime confirmBeginDate,@Param("confirmEndDate") LocalDateTime confirmEndDate);

    /** 
     * @Title: getByReceiptNo 
     * @Description: 根据单据编号查询订单记录
     * @param receiptNo
     * @return
     * @return: StStockAlterDO
     */
    StStockAlterDO getByReceiptNo(@Param("receiptNo") String receiptNo);

    StStockAlterDO get(@Param("id") Long id);

    /** 
     * @Title: delete 
     * @param id
     * @return
     * @return: int
     */
    int delete(@Param("id") long id);

    /** 
     * @Title: getAlterAllReport 
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> getAlterAllReport(StockAlterReportBO reportBO);

    /**
     * @Title: getAlterAreaReport 
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> getAlterAreaReport(StockAlterReportBO reportBO);

    /**
     * @Title: getAlterCompanyReport 
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> getAlterCompanyReport(StockAlterReportBO reportBO);

    /** 
     * @Title: getVAlterAllReport 
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> getVAlterAllReport(StockAlterReportBO reportBO);

    /**
     * @Title: getVAlterAreaReport 
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> getVAlterAreaReport(StockAlterReportBO reportBO);

    /**
     * @Title: getVAlterCompanyReport 
     * @param reportBO
     * @return
     * @return: List<Map<String,Object>>
     */
    List<Map<String, Object>> getVAlterCompanyReport(StockAlterReportBO reportBO);

}