package com.hisun.lemon.urm.uitls;



import org.apache.commons.lang3.StringUtils;

import com.hisun.lemon.common.exception.LemonException;


/**
 * 索引编码生成器
 * <AUTHOR>
 * @date 2017-11-16 14:15
 */
public class TwoLengthCodeGenerator {
	
	
	public static String  transIntToCode(int num){
		int i=num/36;
		int j=num%36;
		
		String result="00";
		char[] c=result.toCharArray();
		char first=c[0];
		char last=c[1];
		for(int m=1;m<=i;m++) {
			if('0'<=first&&first<='9') {
				first++;
				if(first>'9') {
					first='A';
					
				}
			}else if(65<=first&&first<=90) {
				first++;
				if(first>90) {
					LemonException.throwLemonException("数值转code超限i:"+num);
				}
				
			}
			
		}
		for(int m=1;m<=j;m++) {
			if('0'<=last&&last<='9') {
				last++;
				if(last>'9') {
					last='A';
					
				}
			}else if(65<=last&&last<=90) {
				last++;
				if(first>90) {
					LemonException.throwLemonException("数值转code超限j:"+num);
				}
				
			}
			
		}
		c[0]=first;
		c[1]=last;
		
		return String.valueOf(c);
	}

	public static String  getNextCode(String begin){
		
		if(StringUtils.isBlank(begin)) {
			begin="00";
			return begin;
		}
		
		
		char[] c=begin.toCharArray();
		char lastChar=c[1];
		
		if('0'<=lastChar&&lastChar<='9') {
			lastChar++;
			if(lastChar>'9') {
				lastChar='A';
				
			}
			c[1]=lastChar;
			return String.valueOf(c);
		}
		
		if(65<=lastChar&&lastChar<=90) {
			lastChar++;
			if(lastChar>90) {
				c=getNewBeginCharArr(c,1);
				
			}else {
				c[1]=lastChar;
			}
			
			return String.valueOf(c);
			
		}
		
		return null;
		
	}

private static char[] getNewBeginCharArr(char[] c,int index) {
	
	char firstChar=c[0]; 
	if('0'<=firstChar&&firstChar<='9') {
		firstChar++;
		if(firstChar>'9') {
			firstChar='A';
		}
		
	}else if(65<=firstChar&&firstChar<=90) {
		firstChar++;
		if(firstChar>90) {
			LemonException.throwLemonException("获取新数组超限1:"+String.valueOf(c));
		}
	}else {	
		LemonException.throwLemonException("获取新数组超限2:"+String.valueOf(c));
	}
	c[0]=firstChar;
	c[1]='0';
	
	return c;
}

public static String getMaxCode() {
	// TODO Auto-generated method stub
	return "ZZ";
}

	
	

}

