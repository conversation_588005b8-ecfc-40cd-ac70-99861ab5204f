/*
 * @ClassName IcFileDownloadDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-20 09:55:33
 */
package com.hisun.lemon.urm.entity.ic;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class IcFileDownloadDO extends BaseDO {
    /**
     * @Fields dfileId 文件ID
     */
    private long dfileId;
    /**
     * @Fields filePath 文件路径
     */
    private String filePath;
    /**
     * @Fields fileName 文件名
     */
    private String fileName;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    
    private String imgUrl;

    public long getDfileId() {
        return dfileId;
    }

    public void setDfileId(long dfileId) {
        this.dfileId = dfileId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
}