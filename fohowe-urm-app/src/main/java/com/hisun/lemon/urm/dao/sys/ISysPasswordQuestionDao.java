package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysPasswordQuestionDO;

@Mapper
public interface ISysPasswordQuestionDao extends BaseDao<SysPasswordQuestionDO> {

	public List<SysPasswordQuestionDO> finds(@Param("questionNo") String questionNo,@Param("isValid") Integer isValid);
	
	public int deleteById(@Param("id") Long id);
	
}
