/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: EntityGranExcel.java 
 * @Prject: fohowe-web-ec
 * @Package: com.hisun.lemon.fohowe.ec.excel 
 * @author: liubao   
 * @date: 2017年12月6日 上午10:46:28 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.excel.st;

import java.io.OutputStream;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.GenerateMethod;
import com.hisun.lemon.fohowe.common.enums.OrderType;
import com.hisun.lemon.fohowe.common.enums.YesNoStatus;
import com.hisun.lemon.fohowe.common.excel.ExcelNoHeaderExportFactorys;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.entity.st.StStockStraceDO;

/**
 * @ClassName: StockStraceGrantExcel 
 * @author: tian
 * @date: 2018年2月26日 下午3:20:43 
 * @param <T>
 */
public class StockStraceGrantExcel<T extends StStockStraceDO> extends ExcelNoHeaderExportFactorys<T> {
    public DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public void export2(Object obj, String fileName,String[] colNames,HttpServletResponse response) {
		try {
			response.reset();
	        response.setHeader("Content-Disposition",
	                "attachment; filename=" + new String(fileName.getBytes("utf-8"), "ISO-8859-1"));
	        response.setContentType("application/vnd.ms-excel; charset=utf-8");
            response.setHeader("Pragma", "No-cache");//设置头
            response.setHeader("Cache-Control", "no-cache");//设置头
            response.setDateHeader("Expires", 0);//设置日期头
            List<List<String>> head = new ArrayList<List<String>>();
            for (int i = 0; i < colNames.length; i++) {
            	List<String> head0 = new ArrayList<String>();
            	head0.add(colNames[i]);
            	head.add(head0);
    		}
			WriteWorkbook workBook = new WriteWorkbook();
			workBook.setExcelType(ExcelTypeEnum.XLSX);
			//OutputStream outputStream = new FileOutputStream("/app/deploy/data/excel/urm/stock/"+new String(fileName.getBytes("utf-8"), "ISO-8859-1"));
			OutputStream outputStream = response.getOutputStream(); 
			workBook.setOutputStream(outputStream);
			workBook.setNeedHead(true);
			WriteSheet sheet= new WriteSheet();
			sheet.setSheetNo(0);
			sheet.setSheetName(fileName); 
			sheet.setHead(head);
			ExcelWriter write = new ExcelWriter(workBook);
			List<List<Object>>dataList = addData(obj);
			write.write(dataList, sheet); 
			write.finish();
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
    @Override
    public void addCell(Sheet sheet, CellStyle style, List<T> dataList) throws Exception {
        if (dataList != null && dataList.size() > 0) {
            for (int i = 0; i < dataList.size(); i++) {
            	StStockStraceDO stockStraceDTO = dataList.get(i);
                // 创建所需的行数
                Row row = sheet.createRow(i + 1);
                Cell cell = null;

                // 分公司/仓库编号
                cell = row.createCell(0, CellType.STRING);
                cell.setCellValue(stockStraceDTO.getCompanyCode());
                cell.setCellStyle(style);

                //商品编码
                cell = row.createCell(1, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(stockStraceDTO.getGoodsCode()) ? "" : stockStraceDTO.getGoodsCode());
                cell.setCellStyle(style);

                //原库存
                cell = row.createCell(2, CellType.NUMERIC);
                cell.setCellValue(stockStraceDTO.getQuantityS());
                cell.setCellStyle(style);
                //库存变化量
                cell = row.createCell(3, CellType.NUMERIC);
                cell.setCellValue(stockStraceDTO.getQuantityC());
                cell.setCellStyle(style);
                //新库存
                cell = row.createCell(4, CellType.NUMERIC);
                cell.setCellValue(stockStraceDTO.getQuantityR());
                cell.setCellStyle(style);

                //原可用库存
                cell = row.createCell(5, CellType.NUMERIC);
                cell.setCellValue(stockStraceDTO.getValidQtyS());
                cell.setCellStyle(style);
                //可用库存变化量
                cell = row.createCell(6, CellType.NUMERIC);
                cell.setCellValue(stockStraceDTO.getValidQtyC());
                cell.setCellStyle(style);
                //新可用库存
                cell = row.createCell(7, CellType.NUMERIC);
                cell.setCellValue(stockStraceDTO.getValidQtyR());
                cell.setCellStyle(style);

                //价格
                cell = row.createCell(8, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockStraceDTO.getGoodsPrice()) ? ""
                        : stockStraceDTO.getGoodsPrice().toString());
                cell.setCellStyle(style);

                //动作说明
                cell = row.createCell(9, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(stockStraceDTO.getAction()) ? "" : stockStraceDTO.getAction());
                cell.setCellStyle(style);

                //订单编号
                cell = row.createCell(10, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockStraceDTO.getOrderNo()) ? "" : stockStraceDTO.getOrderNo());
                cell.setCellStyle(style);

                //代办处编号
                cell = row.createCell(11, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockStraceDTO.getAgentNo()) ? "" : stockStraceDTO.getAgentNo());
                cell.setCellStyle(style);
              
                //经销商编号
                cell = row.createCell(12, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockStraceDTO.getMemberNo()) ? "" : stockStraceDTO.getMemberNo());
                cell.setCellStyle(style);
             
                //经销商姓名
                cell = row.createCell(13, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockStraceDTO.getMemberName()) ? "" : stockStraceDTO.getMemberName());
                cell.setCellStyle(style);
                
                //订单类型
                cell = row.createCell(14, CellType.STRING);
                OrderType orderType = JudgeUtils.isNull(stockStraceDTO.getOrderType()) ? null
                		: OrderType.getByCode(Integer.valueOf(stockStraceDTO.getOrderType()));
                cell.setCellValue(JudgeUtils.isNull(orderType) ? ""
                        : orderType.getName());
                cell.setCellStyle(style);

                //发生时间
                cell = row.createCell(15, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockStraceDTO.getCreateTime()) ? ""
                        : stockStraceDTO.getCreateTime().format(ymdhms));
                cell.setCellStyle(style);

                //报单平台（经销商报单、代办处报单、网上商城报单）
                cell = row.createCell(16, CellType.STRING);
                
                GenerateMethod generateMethod = JudgeUtils.isNull(stockStraceDTO.getGenerateMethod()) ? null
                        : GenerateMethod.getByCode(Integer.valueOf(stockStraceDTO.getGenerateMethod()));
                cell.setCellValue(JudgeUtils.isNull(generateMethod) ? ""
                        : generateMethod.getName());
                cell.setCellStyle(style);

                //是否赠品单
                cell = row.createCell(17, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(YesNoStatus.getByCode(stockStraceDTO.getIsGive()))
                        ? stockStraceDTO.getIsGive().toString()
                        : YesNoStatus.getByCode(stockStraceDTO.getIsGive()).getName());
                cell.setCellStyle(style);
            }
        }
    }
    public List<List<Object>> addData(Object obj) throws Exception {
		List<List<Object>> list = new ArrayList<List<Object>>();
		List<StStockStraceDO> dataList = (List<StStockStraceDO>)obj;
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			StStockStraceDO stockStraceDTO = (StStockStraceDO)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(stockStraceDTO.getCompanyCode());
            //商品编码
			data.add(JudgeUtils.isNull(stockStraceDTO.getGoodsCode()) ? "" : stockStraceDTO.getGoodsCode());
            //商品归类
            String codeName = stockStraceDTO.getCodeName();
            Map<String, String> goodsKindMap = new HashMap<>();
            goodsKindMap.put("b1", "下架");
            goodsKindMap.put("b2", "主打产品");
            goodsKindMap.put("b3", "促销赠品");
            goodsKindMap.put("b4", "配件耗材");
            goodsKindMap.put("b5", "新产品");
            goodsKindMap.put("b6", "报单套餐");
            data.add(JudgeUtils.isNull(codeName) ? "" : goodsKindMap.getOrDefault(codeName, codeName));
            //原库存
			data.add(stockStraceDTO.getQuantityS());
            //库存变化量
			data.add(stockStraceDTO.getQuantityC());
            //新库存
			data.add(stockStraceDTO.getQuantityR());
            //原可用库存
			data.add(stockStraceDTO.getValidQtyS());
            //可用库存变化量
			data.add(stockStraceDTO.getValidQtyC());
            //新可用库存
			data.add(stockStraceDTO.getValidQtyR());
            //价格
			data.add(JudgeUtils.isNull(stockStraceDTO.getGoodsPrice()) ? ""
                    : stockStraceDTO.getGoodsPrice().toString());
            //动作说明
			data.add(JudgeUtils.isNull(stockStraceDTO.getAction()) ? "" : stockStraceDTO.getAction());
            //订单编号
			data.add(JudgeUtils.isNull(stockStraceDTO.getOrderNo()) ? "" : stockStraceDTO.getOrderNo());
            //代办处编号
			data.add(JudgeUtils.isNull(stockStraceDTO.getAgentNo()) ? "" : stockStraceDTO.getAgentNo());
            //经销商编号
			data.add(JudgeUtils.isNull(stockStraceDTO.getMemberNo()) ? "" : stockStraceDTO.getMemberNo());
            //经销商姓名
			data.add(JudgeUtils.isNull(stockStraceDTO.getMemberName()) ? "" : stockStraceDTO.getMemberName());
            //订单类型
            OrderType orderType = JudgeUtils.isNull(stockStraceDTO.getOrderType()) ? null
            		: OrderType.getByCode(Integer.valueOf(stockStraceDTO.getOrderType()));
            data.add(JudgeUtils.isNull(orderType) ? "": orderType.getName());
            //发生时间
            data.add(JudgeUtils.isNull(stockStraceDTO.getCreateTime()) ? "": stockStraceDTO.getCreateTime().format(ymdhms));
            //报单平台（经销商报单、代办处报单、网上商城报单）
            GenerateMethod generateMethod = JudgeUtils.isNull(stockStraceDTO.getGenerateMethod()) ? null
                    : GenerateMethod.getByCode(Integer.valueOf(stockStraceDTO.getGenerateMethod()));
            data.add(JudgeUtils.isNull(generateMethod) ? "": generateMethod.getName());
            //是否赠品单
            if(JudgeUtils.isNull(stockStraceDTO.getIsGive())) stockStraceDTO.setIsGive("0");
            YesNoStatus isGive =JudgeUtils.isNull(YesNoStatus.getByCode(stockStraceDTO.getIsGive()))? null: YesNoStatus.getByCode(stockStraceDTO.getIsGive());
            data.add(JudgeUtils.isNull(isGive) ? "0":isGive.getName());
            //是否套餐
            if(JudgeUtils.isNull(stockStraceDTO.getIsPkg())) stockStraceDTO.setIsPkg("0");
            YesNoStatus isPkg =JudgeUtils.isNull(YesNoStatus.getByCode(stockStraceDTO.getIsPkg()))? null: YesNoStatus.getByCode(stockStraceDTO.getIsPkg());
            data.add(JudgeUtils.isNull(isPkg) ? "套餐编号":isPkg.getName());
            
			list.add(data);
		}
		return list;
	}
}
