package com.hisun.lemon.urm.uitls.excel.fi;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsMeetingBean;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsMeetingVO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;

public class AcMeetingExcelExporter extends URMExcelExportFactorys {
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		try {
			String fileName="会议";
			String[] colNames = new String[] { "会议编号", "会议名称", "区域编号", "参加的分公司", "城市/代办处", "一级会议类型", "会议类型", "预计开始时间",
					"预计结束时间", "计划人数","代金券", "预计收费币种", "预计参与经理", "预计主讲嘉宾",  "预计主讲老师", "预计参会老师", "预计主讲翻译", "预计参与翻译", "预计成交数量",
					"实际一级会议类型", "实际会议类型", "举办时间", "举办时间", "实际人数", "实际代金券", "实际收费币种", "实际参与经理", "实际主讲嘉宾", "实际主讲老师", "实际参会老师",
					"实际翻译", "实际参与翻译", "实际成交数量", "实际定金", "状态", "备注", "会议总结", "创建人帐号", "建立时间", "录单员确认标记",
					"确认一级会议类型", "确认会议类型", "确认举办时间", "确认举办时间", "确认人数", "确认代金券", "确认收费币种",
					"确认参与经理","确认主讲嘉宾", "确认主讲老师", "确认参会老师", "确认主翻译", "确认参与翻译", "确认成交数量", "确认定金" };
			List<List<Object>> data = addData(obj);
			super.exportActually2(data,fileName,colNames, response);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	public List<List<Object>> addData(Object obj) throws Exception {
		TicketsMeetingVO vo=(TicketsMeetingVO) obj;
		List<TicketsMeetingBean> dataList= vo.getDataList();
		List<List<Object>> list = new ArrayList<List<Object>>();
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			TicketsMeetingBean o = (TicketsMeetingBean)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(o.getMeetingNo() == null ? "" : o.getMeetingNo());
			data.add(o.getMeetingTitle() == null ? "" : o.getMeetingTitle());
			data.add(o.getAreaCode() == null ? "" : o.getAreaCode());
			data.add(o.getCompanyCode() == null ? "" : o.getCompanyCode());
			data.add(o.getCity() == null ? "" : o.getCity());
			data.add(o.getPlanFirstType() == null ? "" : o.getPlanFirstType());
			data.add(o.getPlanMeetingType() == null ? "" : o.getPlanMeetingType());
			data.add(o.getMeetingDateStart() == null ? "" : o.getMeetingDateStart().format(yms));
			data.add(o.getMeetingDateEnd() == null ? "" : o.getMeetingDateEnd().format(yms));
			data.add(o.getPlanNum() == null ? 0 : o.getPlanNum());
			data.add(o.getPlanAmount() == null ? 0 : o.getPlanAmount().doubleValue());
			data.add(o.getPlanCurrency() == null ? "" : o.getPlanCurrency());
			data.add(o.getPlanManager() == null ? "" : o.getPlanManager());
			data.add(o.getPlanGuests() == null ? "" : o.getPlanGuests());
			data.add(o.getPlanLecturer() == null ? "" : o.getPlanLecturerName());
			data.add(o.getPlanTeacher() == null ? "" : o.getPlanTeacher());
			data.add(o.getPlanTranslator() == null ? "" : o.getPlanTranslatorName());
			data.add(o.getPlanTransPart() == null ? "" : o.getPlanTransPart());
			data.add(o.getPlanDealQty() == null ? 0 : o.getPlanDealQty());
			data.add(o.getActalFirstType() == null ? "" : o.getActalFirstType());
			data.add(o.getActalMeetingType() == null ? "" : o.getActalMeetingType());
			data.add(o.getHeldDateStart() == null ? "" : o.getHeldDateStart().format(yms));
			data.add(o.getHeldDateEnd() == null ? "" : o.getHeldDateEnd().format(yms));
			data.add(o.getActualNum() == null ? 0 : o.getActualNum());
			data.add(o.getActualAmount() == null ? 0 : o.getActualAmount().doubleValue());
			data.add(o.getActualCurrency() == null ? "" : o.getActualCurrency());
			data.add(o.getActualManager() == null ? "" : o.getActualManager());
			data.add(o.getActualGuests() == null ? "" : o.getActualGuests());
			data.add(o.getActualLecturer() == null ? "" : o.getActualLecturerName());
			data.add(o.getActualTeacher() == null ? "" : o.getActualTeacher());
			data.add(o.getActualTranslator() == null ? "" : o.getActualTranslatorName());
			data.add(o.getActualTransPart() == null ? "" : o.getActualTransPart());
			data.add(o.getDealQty() == null ? 0 : o.getDealQty());
			data.add(o.getDeposit() == null ? 0 : o.getDeposit().doubleValue());
			String statusStr="";
			if(o.getVcStatus() == 0) {
				statusStr="新建";
			}
			else if(o.getVcStatus() == 1) {
				statusStr="审核";
			}
			else if(o.getVcStatus() == 2) {
				statusStr="已确认";
			}
			else if(o.getVcStatus() == 3) {
				statusStr="完成";
			}
			data.add(statusStr);
//			data.add(o.getAuditTime() == null ? "" : o.getAuditTime().format(ymdhms));
			data.add(o.getMemo() == null ? "" : o.getMemo());
			data.add(o.getRemark() == null ? "" : o.getRemark());
			data.add(o.getCreaterCode() == null ? "" : o.getCreaterCode());
			data.add(o.getCreateTime() == null ? "" : o.getCreateTime().format(ymdhms));
//			data.add(o.getCheckeCode() == null ? "" : o.getCheckeCode());
//			data.add(o.getCheckeTime() == null ? "" : o.getCheckeTime().format(ymdhms));
//			data.add(o.getReCheckeCode() == null ? "" : o.getReCheckeCode());
//			data.add(o.getReCheckeTime() == null ? "" : o.getReCheckeTime().format(ymdhms));
			data.add(o.getReviewFirstType() == null ? "" : o.getReviewFirstType());
			data.add(o.getReviewMeetingType() == null ? "" : o.getReviewMeetingType());
			data.add(o.getReviewDateStart() == null ? "" : o.getReviewDateStart().format(yms));
			data.add(o.getReviewDateEnd() == null ? "" : o.getReviewDateEnd().format(yms));
			data.add(o.getReviewNum() == null ? 0 : o.getReviewNum());
			data.add(o.getReviewAmount() == null ? 0 : o.getReviewAmount().doubleValue());
			data.add(o.getReviewCurrency() == null ? "" : o.getReviewCurrency());
			data.add(o.getReviewManager() == null ? "" : o.getReviewManager());
			data.add(o.getReviewGuests() == null ? "" : o.getReviewGuests());
			data.add(o.getReviewLecturer() == null ? "" : o.getReviewLecturer());
			data.add(o.getReviewTeacher() == null ? "" : o.getReviewTeacher());
			data.add(o.getReviewTranslator() == null ? "" : o.getReviewTranslator());
			data.add(o.getReviewTransPart() == null ? "" : o.getReviewTransPart());
			data.add(o.getReviewDealQty() == null ? 0 : o.getReviewDealQty());
			data.add(o.getReviewDeposit() == null ? 0 : o.getReviewDeposit().doubleValue());
			list.add(data);
		}
		return list;
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		TicketsMeetingVO vo=(TicketsMeetingVO) obj;
		List<TicketsMeetingBean> dataList=vo.getDataList();
		for(TicketsMeetingBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getMeetingNo() == null ? "" : o.getMeetingNo());
			row.createCell(index++).setCellValue(o.getMeetingTitle() == null ? "" : o.getMeetingTitle());
			row.createCell(index++).setCellValue(o.getAreaCode() == null ? "" : o.getAreaCode());
			row.createCell(index++).setCellValue(o.getCompanyCode() == null ? "" : o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getCity() == null ? "" : o.getCity());
			row.createCell(index++).setCellValue(o.getPlanFirstType() == null ? "" : o.getPlanFirstType());
			row.createCell(index++).setCellValue(o.getPlanMeetingType() == null ? "" : o.getPlanMeetingType());
			row.createCell(index++).setCellValue(o.getMeetingDateStart() == null ? "" : o.getMeetingDateStart().format(yms));
			row.createCell(index++).setCellValue(o.getMeetingDateEnd() == null ? "" : o.getMeetingDateEnd().format(yms));
			row.createCell(index++).setCellValue(o.getPlanNum() == null ? 0 : o.getPlanNum());
			row.createCell(index++).setCellValue(o.getPlanAmount() == null ? 0 : o.getPlanAmount().doubleValue());
			row.createCell(index++).setCellValue(o.getPlanCurrency() == null ? "" : o.getPlanCurrency());
			row.createCell(index++).setCellValue(o.getPlanManager() == null ? "" : o.getPlanManager());
			row.createCell(index++).setCellValue(o.getPlanGuests() == null ? "" : o.getPlanGuests());
			row.createCell(index++).setCellValue(o.getPlanLecturer() == null ? "" : o.getPlanLecturerName());
			row.createCell(index++).setCellValue(o.getPlanTeacher() == null ? "" : o.getPlanTeacher());
			row.createCell(index++).setCellValue(o.getPlanTranslator() == null ? "" : o.getPlanTranslatorName());
			row.createCell(index++).setCellValue(o.getPlanTransPart() == null ? "" : o.getPlanTransPart());
			row.createCell(index++).setCellValue(o.getPlanDealQty() == null ? 0 : o.getPlanDealQty());
			row.createCell(index++).setCellValue(o.getActalFirstType() == null ? "" : o.getActalFirstType());
			row.createCell(index++).setCellValue(o.getActalMeetingType() == null ? "" : o.getActalMeetingType());
			row.createCell(index++).setCellValue(o.getHeldDateStart() == null ? "" : o.getHeldDateStart().format(yms));
			row.createCell(index++).setCellValue(o.getHeldDateEnd() == null ? "" : o.getHeldDateEnd().format(yms));
			row.createCell(index++).setCellValue(o.getActualNum() == null ? 0 : o.getActualNum());
			row.createCell(index++).setCellValue(o.getActualAmount() == null ? 0 : o.getActualAmount().doubleValue());
			row.createCell(index++).setCellValue(o.getActualCurrency() == null ? "" : o.getActualCurrency());
			row.createCell(index++).setCellValue(o.getActualManager() == null ? "" : o.getActualManager());
			row.createCell(index++).setCellValue(o.getActualGuests() == null ? "" : o.getActualGuests());
			row.createCell(index++).setCellValue(o.getActualLecturer() == null ? "" : o.getActualLecturerName());
			row.createCell(index++).setCellValue(o.getActualTeacher() == null ? "" : o.getActualTeacher());
			row.createCell(index++).setCellValue(o.getActualTranslator() == null ? "" : o.getActualTranslatorName());
			row.createCell(index++).setCellValue(o.getActualTransPart() == null ? "" : o.getActualTransPart());
			row.createCell(index++).setCellValue(o.getDealQty() == null ? 0 : o.getDealQty());
			row.createCell(index++).setCellValue(o.getDeposit() == null ? 0 : o.getDeposit().doubleValue());
			String statusStr="";
			if(o.getVcStatus() == 0) {
				statusStr="新建";
			}
			else if(o.getVcStatus() == 1) {
				statusStr="审核";
			}
			else if(o.getVcStatus() == 2) {
				statusStr="已确认";
			}
			else if(o.getVcStatus() == 3) {
				statusStr="完成";
			}
			row.createCell(index++).setCellValue(statusStr);
//			row.createCell(index++).setCellValue(o.getAuditTime() == null ? "" : o.getAuditTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getMemo() == null ? "" : o.getMemo());
			row.createCell(index++).setCellValue(o.getRemark() == null ? "" : o.getRemark());
			row.createCell(index++).setCellValue(o.getCreaterCode() == null ? "" : o.getCreaterCode());
			row.createCell(index++).setCellValue(o.getCreateTime() == null ? "" : o.getCreateTime().format(ymdhms));
//			row.createCell(index++).setCellValue(o.getCheckeCode() == null ? "" : o.getCheckeCode());
//			row.createCell(index++).setCellValue(o.getCheckeTime() == null ? "" : o.getCheckeTime().format(ymdhms));
//			row.createCell(index++).setCellValue(o.getReCheckeCode() == null ? "" : o.getReCheckeCode());
//			row.createCell(index++).setCellValue(o.getReCheckeTime() == null ? "" : o.getReCheckeTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getReviewFirstType() == null ? "" : o.getReviewFirstType());
			row.createCell(index++).setCellValue(o.getReviewMeetingType() == null ? "" : o.getReviewMeetingType());
			row.createCell(index++).setCellValue(o.getReviewDateStart() == null ? "" : o.getReviewDateStart().format(yms));
			row.createCell(index++).setCellValue(o.getReviewDateEnd() == null ? "" : o.getReviewDateEnd().format(yms));
			row.createCell(index++).setCellValue(o.getReviewNum() == null ? 0 : o.getReviewNum());
			row.createCell(index++).setCellValue(o.getReviewAmount() == null ? 0 : o.getReviewAmount().doubleValue());
			row.createCell(index++).setCellValue(o.getReviewCurrency() == null ? "" : o.getReviewCurrency());
			row.createCell(index++).setCellValue(o.getReviewManager() == null ? "" : o.getReviewManager());
			row.createCell(index++).setCellValue(o.getReviewGuests() == null ? "" : o.getReviewGuests());
			row.createCell(index++).setCellValue(o.getReviewLecturer() == null ? "" : o.getReviewLecturer());
			row.createCell(index++).setCellValue(o.getReviewTeacher() == null ? "" : o.getReviewTeacher());
			row.createCell(index++).setCellValue(o.getReviewTranslator() == null ? "" : o.getReviewTranslator());
			row.createCell(index++).setCellValue(o.getReviewTransPart() == null ? "" : o.getReviewTransPart());
			row.createCell(index++).setCellValue(o.getReviewDealQty() == null ? 0 : o.getReviewDealQty());
			row.createCell(index++).setCellValue(o.getReviewDeposit() == null ? 0 : o.getReviewDeposit().doubleValue());
			
		}
		
	}
	public static AcMeetingExcelExporter builder() {
		return new AcMeetingExcelExporter();
	}
}
