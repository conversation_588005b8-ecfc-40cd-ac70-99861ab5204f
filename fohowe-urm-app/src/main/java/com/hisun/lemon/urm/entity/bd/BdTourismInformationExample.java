package com.hisun.lemon.urm.entity.bd;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import lombok.Setter;
public class BdTourismInformationExample {
        /**
     * 自己创建的参数，对应添加limit的条件，可以实现分页功能
     */
    protected int offset;

    /**
     * -- SETTER --
     *  分页功能查询调用
     *
     * @param limit 从第几条开始获取数据
     */
    @Setter
    protected int limit;

    /**
     * 分页功能查询调用
     * @param count 起始条件从0开始计算
     * @param offset 可以展现多少条数据
     */
    public void setLimit( int count,int offset) {
        this.offset = offset;
        this.limit = count;
    }

    /**
     * 非0和空时会重置自增计算，从最后一个开始计算
     */
    protected int AUTO_INCREMENT;

    public void setAUTO_INCREMENT(int AUTO_INCREMENT) {
        this.AUTO_INCREMENT = AUTO_INCREMENT;
    }

    protected String orderByClause;


    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BdTourismInformationExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andVcStatusIsNull() {
            addCriterion("vc_status is null");
            return (Criteria) this;
        }

        public Criteria andVcStatusIsNotNull() {
            addCriterion("vc_status is not null");
            return (Criteria) this;
        }

        public Criteria andVcStatusEqualTo(Integer value) {
            addCriterion("vc_status =", value, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusNotEqualTo(Integer value) {
            addCriterion("vc_status <>", value, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusGreaterThan(Integer value) {
            addCriterion("vc_status >", value, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("vc_status >=", value, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusLessThan(Integer value) {
            addCriterion("vc_status <", value, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusLessThanOrEqualTo(Integer value) {
            addCriterion("vc_status <=", value, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusIn(List<Integer> values) {
            addCriterion("vc_status in", values, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusNotIn(List<Integer> values) {
            addCriterion("vc_status not in", values, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusBetween(Integer value1, Integer value2) {
            addCriterion("vc_status between", value1, value2, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andVcStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("vc_status not between", value1, value2, "vcStatus");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeIsNull() {
            addCriterion("fi_checke_code is null");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeIsNotNull() {
            addCriterion("fi_checke_code is not null");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeEqualTo(Integer value) {
            addCriterion("fi_checke_code =", value, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeNotEqualTo(Integer value) {
            addCriterion("fi_checke_code <>", value, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeGreaterThan(Integer value) {
            addCriterion("fi_checke_code >", value, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("fi_checke_code >=", value, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeLessThan(Integer value) {
            addCriterion("fi_checke_code <", value, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeLessThanOrEqualTo(Integer value) {
            addCriterion("fi_checke_code <=", value, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeIn(List<Integer> values) {
            addCriterion("fi_checke_code in", values, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeNotIn(List<Integer> values) {
            addCriterion("fi_checke_code not in", values, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeBetween(Integer value1, Integer value2) {
            addCriterion("fi_checke_code between", value1, value2, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andFiCheckeCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("fi_checke_code not between", value1, value2, "fiCheckeCode");
            return (Criteria) this;
        }

        public Criteria andMemberNoIsNull() {
            addCriterion("member_no is null");
            return (Criteria) this;
        }

        public Criteria andMemberNoIsNotNull() {
            addCriterion("member_no is not null");
            return (Criteria) this;
        }

        public Criteria andMemberNoEqualTo(String value) {
            addCriterion("member_no =", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoNotEqualTo(String value) {
            addCriterion("member_no <>", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoGreaterThan(String value) {
            addCriterion("member_no >", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoGreaterThanOrEqualTo(String value) {
            addCriterion("member_no >=", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoLessThan(String value) {
            addCriterion("member_no <", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoLessThanOrEqualTo(String value) {
            addCriterion("member_no <=", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoLike(String value) {
            addCriterion("member_no like", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoNotLike(String value) {
            addCriterion("member_no not like", value, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoIn(List<String> values) {
            addCriterion("member_no in", values, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoNotIn(List<String> values) {
            addCriterion("member_no not in", values, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoBetween(String value1, String value2) {
            addCriterion("member_no between", value1, value2, "memberNo");
            return (Criteria) this;
        }

        public Criteria andMemberNoNotBetween(String value1, String value2) {
            addCriterion("member_no not between", value1, value2, "memberNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoIsNull() {
            addCriterion("agent_no is null");
            return (Criteria) this;
        }

        public Criteria andAgentNoIsNotNull() {
            addCriterion("agent_no is not null");
            return (Criteria) this;
        }

        public Criteria andAgentNoEqualTo(String value) {
            addCriterion("agent_no =", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoNotEqualTo(String value) {
            addCriterion("agent_no <>", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoGreaterThan(String value) {
            addCriterion("agent_no >", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoGreaterThanOrEqualTo(String value) {
            addCriterion("agent_no >=", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoLessThan(String value) {
            addCriterion("agent_no <", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoLessThanOrEqualTo(String value) {
            addCriterion("agent_no <=", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoLike(String value) {
            addCriterion("agent_no like", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoNotLike(String value) {
            addCriterion("agent_no not like", value, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoIn(List<String> values) {
            addCriterion("agent_no in", values, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoNotIn(List<String> values) {
            addCriterion("agent_no not in", values, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoBetween(String value1, String value2) {
            addCriterion("agent_no between", value1, value2, "agentNo");
            return (Criteria) this;
        }

        public Criteria andAgentNoNotBetween(String value1, String value2) {
            addCriterion("agent_no not between", value1, value2, "agentNo");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andMemberTypeIsNull() {
            addCriterion("member_type is null");
            return (Criteria) this;
        }

        public Criteria andMemberTypeIsNotNull() {
            addCriterion("member_type is not null");
            return (Criteria) this;
        }

        public Criteria andMemberTypeEqualTo(String value) {
            addCriterion("member_type =", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeNotEqualTo(String value) {
            addCriterion("member_type <>", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeGreaterThan(String value) {
            addCriterion("member_type >", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeGreaterThanOrEqualTo(String value) {
            addCriterion("member_type >=", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeLessThan(String value) {
            addCriterion("member_type <", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeLessThanOrEqualTo(String value) {
            addCriterion("member_type <=", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeLike(String value) {
            addCriterion("member_type like", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeNotLike(String value) {
            addCriterion("member_type not like", value, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeIn(List<String> values) {
            addCriterion("member_type in", values, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeNotIn(List<String> values) {
            addCriterion("member_type not in", values, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeBetween(String value1, String value2) {
            addCriterion("member_type between", value1, value2, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberTypeNotBetween(String value1, String value2) {
            addCriterion("member_type not between", value1, value2, "memberType");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameIsNull() {
            addCriterion("member_last_name is null");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameIsNotNull() {
            addCriterion("member_last_name is not null");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameEqualTo(String value) {
            addCriterion("member_last_name =", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameNotEqualTo(String value) {
            addCriterion("member_last_name <>", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameGreaterThan(String value) {
            addCriterion("member_last_name >", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("member_last_name >=", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameLessThan(String value) {
            addCriterion("member_last_name <", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameLessThanOrEqualTo(String value) {
            addCriterion("member_last_name <=", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameLike(String value) {
            addCriterion("member_last_name like", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameNotLike(String value) {
            addCriterion("member_last_name not like", value, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameIn(List<String> values) {
            addCriterion("member_last_name in", values, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameNotIn(List<String> values) {
            addCriterion("member_last_name not in", values, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameBetween(String value1, String value2) {
            addCriterion("member_last_name between", value1, value2, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMemberLastNameNotBetween(String value1, String value2) {
            addCriterion("member_last_name not between", value1, value2, "memberLastName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameIsNull() {
            addCriterion("menber_first_name is null");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameIsNotNull() {
            addCriterion("menber_first_name is not null");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameEqualTo(String value) {
            addCriterion("menber_first_name =", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameNotEqualTo(String value) {
            addCriterion("menber_first_name <>", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameGreaterThan(String value) {
            addCriterion("menber_first_name >", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("menber_first_name >=", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameLessThan(String value) {
            addCriterion("menber_first_name <", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameLessThanOrEqualTo(String value) {
            addCriterion("menber_first_name <=", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameLike(String value) {
            addCriterion("menber_first_name like", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameNotLike(String value) {
            addCriterion("menber_first_name not like", value, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameIn(List<String> values) {
            addCriterion("menber_first_name in", values, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameNotIn(List<String> values) {
            addCriterion("menber_first_name not in", values, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameBetween(String value1, String value2) {
            addCriterion("menber_first_name between", value1, value2, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andMenberFirstNameNotBetween(String value1, String value2) {
            addCriterion("menber_first_name not between", value1, value2, "menberFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameIsNull() {
            addCriterion("passports_last_name is null");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameIsNotNull() {
            addCriterion("passports_last_name is not null");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameEqualTo(String value) {
            addCriterion("passports_last_name =", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameNotEqualTo(String value) {
            addCriterion("passports_last_name <>", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameGreaterThan(String value) {
            addCriterion("passports_last_name >", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("passports_last_name >=", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameLessThan(String value) {
            addCriterion("passports_last_name <", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameLessThanOrEqualTo(String value) {
            addCriterion("passports_last_name <=", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameLike(String value) {
            addCriterion("passports_last_name like", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameNotLike(String value) {
            addCriterion("passports_last_name not like", value, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameIn(List<String> values) {
            addCriterion("passports_last_name in", values, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameNotIn(List<String> values) {
            addCriterion("passports_last_name not in", values, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameBetween(String value1, String value2) {
            addCriterion("passports_last_name between", value1, value2, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsLastNameNotBetween(String value1, String value2) {
            addCriterion("passports_last_name not between", value1, value2, "passportsLastName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameIsNull() {
            addCriterion("passports_first_name is null");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameIsNotNull() {
            addCriterion("passports_first_name is not null");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameEqualTo(String value) {
            addCriterion("passports_first_name =", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameNotEqualTo(String value) {
            addCriterion("passports_first_name <>", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameGreaterThan(String value) {
            addCriterion("passports_first_name >", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("passports_first_name >=", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameLessThan(String value) {
            addCriterion("passports_first_name <", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameLessThanOrEqualTo(String value) {
            addCriterion("passports_first_name <=", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameLike(String value) {
            addCriterion("passports_first_name like", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameNotLike(String value) {
            addCriterion("passports_first_name not like", value, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameIn(List<String> values) {
            addCriterion("passports_first_name in", values, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameNotIn(List<String> values) {
            addCriterion("passports_first_name not in", values, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameBetween(String value1, String value2) {
            addCriterion("passports_first_name between", value1, value2, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andPassportsFirstNameNotBetween(String value1, String value2) {
            addCriterion("passports_first_name not between", value1, value2, "passportsFirstName");
            return (Criteria) this;
        }

        public Criteria andSexIsNull() {
            addCriterion("sex is null");
            return (Criteria) this;
        }

        public Criteria andSexIsNotNull() {
            addCriterion("sex is not null");
            return (Criteria) this;
        }

        public Criteria andSexEqualTo(String value) {
            addCriterion("sex =", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotEqualTo(String value) {
            addCriterion("sex <>", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThan(String value) {
            addCriterion("sex >", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThanOrEqualTo(String value) {
            addCriterion("sex >=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThan(String value) {
            addCriterion("sex <", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThanOrEqualTo(String value) {
            addCriterion("sex <=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLike(String value) {
            addCriterion("sex like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotLike(String value) {
            addCriterion("sex not like", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexIn(List<String> values) {
            addCriterion("sex in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotIn(List<String> values) {
            addCriterion("sex not in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexBetween(String value1, String value2) {
            addCriterion("sex between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotBetween(String value1, String value2) {
            addCriterion("sex not between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityIsNull() {
            addCriterion("menber_nationality is null");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityIsNotNull() {
            addCriterion("menber_nationality is not null");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityEqualTo(String value) {
            addCriterion("menber_nationality =", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityNotEqualTo(String value) {
            addCriterion("menber_nationality <>", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityGreaterThan(String value) {
            addCriterion("menber_nationality >", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityGreaterThanOrEqualTo(String value) {
            addCriterion("menber_nationality >=", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityLessThan(String value) {
            addCriterion("menber_nationality <", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityLessThanOrEqualTo(String value) {
            addCriterion("menber_nationality <=", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityLike(String value) {
            addCriterion("menber_nationality like", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityNotLike(String value) {
            addCriterion("menber_nationality not like", value, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityIn(List<String> values) {
            addCriterion("menber_nationality in", values, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityNotIn(List<String> values) {
            addCriterion("menber_nationality not in", values, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityBetween(String value1, String value2) {
            addCriterion("menber_nationality between", value1, value2, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberNationalityNotBetween(String value1, String value2) {
            addCriterion("menber_nationality not between", value1, value2, "menberNationality");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayIsNull() {
            addCriterion("menber_birthday is null");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayIsNotNull() {
            addCriterion("menber_birthday is not null");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayEqualTo(String value) {
            addCriterion("menber_birthday =", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayNotEqualTo(String value) {
            addCriterion("menber_birthday <>", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayGreaterThan(String value) {
            addCriterion("menber_birthday >", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayGreaterThanOrEqualTo(String value) {
            addCriterion("menber_birthday >=", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayLessThan(String value) {
            addCriterion("menber_birthday <", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayLessThanOrEqualTo(String value) {
            addCriterion("menber_birthday <=", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayLike(String value) {
            addCriterion("menber_birthday like", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayNotLike(String value) {
            addCriterion("menber_birthday not like", value, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayIn(List<String> values) {
            addCriterion("menber_birthday in", values, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayNotIn(List<String> values) {
            addCriterion("menber_birthday not in", values, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayBetween(String value1, String value2) {
            addCriterion("menber_birthday between", value1, value2, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andMenberBirthdayNotBetween(String value1, String value2) {
            addCriterion("menber_birthday not between", value1, value2, "menberBirthday");
            return (Criteria) this;
        }

        public Criteria andPassportsNoIsNull() {
            addCriterion("passports_no is null");
            return (Criteria) this;
        }

        public Criteria andPassportsNoIsNotNull() {
            addCriterion("passports_no is not null");
            return (Criteria) this;
        }

        public Criteria andPassportsNoEqualTo(String value) {
            addCriterion("passports_no =", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoNotEqualTo(String value) {
            addCriterion("passports_no <>", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoGreaterThan(String value) {
            addCriterion("passports_no >", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoGreaterThanOrEqualTo(String value) {
            addCriterion("passports_no >=", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoLessThan(String value) {
            addCriterion("passports_no <", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoLessThanOrEqualTo(String value) {
            addCriterion("passports_no <=", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoLike(String value) {
            addCriterion("passports_no like", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoNotLike(String value) {
            addCriterion("passports_no not like", value, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoIn(List<String> values) {
            addCriterion("passports_no in", values, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoNotIn(List<String> values) {
            addCriterion("passports_no not in", values, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoBetween(String value1, String value2) {
            addCriterion("passports_no between", value1, value2, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsNoNotBetween(String value1, String value2) {
            addCriterion("passports_no not between", value1, value2, "passportsNo");
            return (Criteria) this;
        }

        public Criteria andPassportsDataIsNull() {
            addCriterion("passports_data is null");
            return (Criteria) this;
        }

        public Criteria andPassportsDataIsNotNull() {
            addCriterion("passports_data is not null");
            return (Criteria) this;
        }

        public Criteria andPassportsDataEqualTo(Date value) {
            addCriterionForJDBCDate("passports_data =", value, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataNotEqualTo(Date value) {
            addCriterionForJDBCDate("passports_data <>", value, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataGreaterThan(Date value) {
            addCriterionForJDBCDate("passports_data >", value, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("passports_data >=", value, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataLessThan(Date value) {
            addCriterionForJDBCDate("passports_data <", value, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("passports_data <=", value, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataIn(List<Date> values) {
            addCriterionForJDBCDate("passports_data in", values, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataNotIn(List<Date> values) {
            addCriterionForJDBCDate("passports_data not in", values, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("passports_data between", value1, value2, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsDataNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("passports_data not between", value1, value2, "passportsData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataIsNull() {
            addCriterion("passports_effective_data is null");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataIsNotNull() {
            addCriterion("passports_effective_data is not null");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataEqualTo(Date value) {
            addCriterionForJDBCDate("passports_effective_data =", value, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataNotEqualTo(Date value) {
            addCriterionForJDBCDate("passports_effective_data <>", value, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataGreaterThan(Date value) {
            addCriterionForJDBCDate("passports_effective_data >", value, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("passports_effective_data >=", value, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataLessThan(Date value) {
            addCriterionForJDBCDate("passports_effective_data <", value, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("passports_effective_data <=", value, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataIn(List<Date> values) {
            addCriterionForJDBCDate("passports_effective_data in", values, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataNotIn(List<Date> values) {
            addCriterionForJDBCDate("passports_effective_data not in", values, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("passports_effective_data between", value1, value2, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andPassportsEffectiveDataNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("passports_effective_data not between", value1, value2, "passportsEffectiveData");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusIsNull() {
            addCriterion("fi_vc_status is null");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusIsNotNull() {
            addCriterion("fi_vc_status is not null");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusEqualTo(Integer value) {
            addCriterion("fi_vc_status =", value, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusNotEqualTo(Integer value) {
            addCriterion("fi_vc_status <>", value, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusGreaterThan(Integer value) {
            addCriterion("fi_vc_status >", value, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("fi_vc_status >=", value, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusLessThan(Integer value) {
            addCriterion("fi_vc_status <", value, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusLessThanOrEqualTo(Integer value) {
            addCriterion("fi_vc_status <=", value, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusIn(List<Integer> values) {
            addCriterion("fi_vc_status in", values, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusNotIn(List<Integer> values) {
            addCriterion("fi_vc_status not in", values, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusBetween(Integer value1, Integer value2) {
            addCriterion("fi_vc_status between", value1, value2, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiVcStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("fi_vc_status not between", value1, value2, "fiVcStatus");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerIsNull() {
            addCriterion("fi_husband_peer is null");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerIsNotNull() {
            addCriterion("fi_husband_peer is not null");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerEqualTo(Integer value) {
            addCriterion("fi_husband_peer =", value, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerNotEqualTo(Integer value) {
            addCriterion("fi_husband_peer <>", value, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerGreaterThan(Integer value) {
            addCriterion("fi_husband_peer >", value, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerGreaterThanOrEqualTo(Integer value) {
            addCriterion("fi_husband_peer >=", value, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerLessThan(Integer value) {
            addCriterion("fi_husband_peer <", value, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerLessThanOrEqualTo(Integer value) {
            addCriterion("fi_husband_peer <=", value, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerIn(List<Integer> values) {
            addCriterion("fi_husband_peer in", values, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerNotIn(List<Integer> values) {
            addCriterion("fi_husband_peer not in", values, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerBetween(Integer value1, Integer value2) {
            addCriterion("fi_husband_peer between", value1, value2, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andFiHusbandPeerNotBetween(Integer value1, Integer value2) {
            addCriterion("fi_husband_peer not between", value1, value2, "fiHusbandPeer");
            return (Criteria) this;
        }

        public Criteria andWifePassportIsNull() {
            addCriterion("wife_passport is null");
            return (Criteria) this;
        }

        public Criteria andWifePassportIsNotNull() {
            addCriterion("wife_passport is not null");
            return (Criteria) this;
        }

        public Criteria andWifePassportEqualTo(String value) {
            addCriterion("wife_passport =", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportNotEqualTo(String value) {
            addCriterion("wife_passport <>", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportGreaterThan(String value) {
            addCriterion("wife_passport >", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportGreaterThanOrEqualTo(String value) {
            addCriterion("wife_passport >=", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportLessThan(String value) {
            addCriterion("wife_passport <", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportLessThanOrEqualTo(String value) {
            addCriterion("wife_passport <=", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportLike(String value) {
            addCriterion("wife_passport like", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportNotLike(String value) {
            addCriterion("wife_passport not like", value, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportIn(List<String> values) {
            addCriterion("wife_passport in", values, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportNotIn(List<String> values) {
            addCriterion("wife_passport not in", values, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportBetween(String value1, String value2) {
            addCriterion("wife_passport between", value1, value2, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andWifePassportNotBetween(String value1, String value2) {
            addCriterion("wife_passport not between", value1, value2, "wifePassport");
            return (Criteria) this;
        }

        public Criteria andFiChildrenIsNull() {
            addCriterion("fi_children is null");
            return (Criteria) this;
        }

        public Criteria andFiChildrenIsNotNull() {
            addCriterion("fi_children is not null");
            return (Criteria) this;
        }

        public Criteria andFiChildrenEqualTo(Integer value) {
            addCriterion("fi_children =", value, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenNotEqualTo(Integer value) {
            addCriterion("fi_children <>", value, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenGreaterThan(Integer value) {
            addCriterion("fi_children >", value, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenGreaterThanOrEqualTo(Integer value) {
            addCriterion("fi_children >=", value, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenLessThan(Integer value) {
            addCriterion("fi_children <", value, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenLessThanOrEqualTo(Integer value) {
            addCriterion("fi_children <=", value, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenIn(List<Integer> values) {
            addCriterion("fi_children in", values, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenNotIn(List<Integer> values) {
            addCriterion("fi_children not in", values, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenBetween(Integer value1, Integer value2) {
            addCriterion("fi_children between", value1, value2, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andFiChildrenNotBetween(Integer value1, Integer value2) {
            addCriterion("fi_children not between", value1, value2, "fiChildren");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoIsNull() {
            addCriterion("children_passport_no is null");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoIsNotNull() {
            addCriterion("children_passport_no is not null");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoEqualTo(String value) {
            addCriterion("children_passport_no =", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoNotEqualTo(String value) {
            addCriterion("children_passport_no <>", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoGreaterThan(String value) {
            addCriterion("children_passport_no >", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoGreaterThanOrEqualTo(String value) {
            addCriterion("children_passport_no >=", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoLessThan(String value) {
            addCriterion("children_passport_no <", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoLessThanOrEqualTo(String value) {
            addCriterion("children_passport_no <=", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoLike(String value) {
            addCriterion("children_passport_no like", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoNotLike(String value) {
            addCriterion("children_passport_no not like", value, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoIn(List<String> values) {
            addCriterion("children_passport_no in", values, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoNotIn(List<String> values) {
            addCriterion("children_passport_no not in", values, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoBetween(String value1, String value2) {
            addCriterion("children_passport_no between", value1, value2, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andChildrenPassportNoNotBetween(String value1, String value2) {
            addCriterion("children_passport_no not between", value1, value2, "childrenPassportNo");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathIsNull() {
            addCriterion("prssport_pic_path is null");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathIsNotNull() {
            addCriterion("prssport_pic_path is not null");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathEqualTo(String value) {
            addCriterion("prssport_pic_path =", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathNotEqualTo(String value) {
            addCriterion("prssport_pic_path <>", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathGreaterThan(String value) {
            addCriterion("prssport_pic_path >", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathGreaterThanOrEqualTo(String value) {
            addCriterion("prssport_pic_path >=", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathLessThan(String value) {
            addCriterion("prssport_pic_path <", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathLessThanOrEqualTo(String value) {
            addCriterion("prssport_pic_path <=", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathLike(String value) {
            addCriterion("prssport_pic_path like", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathNotLike(String value) {
            addCriterion("prssport_pic_path not like", value, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathIn(List<String> values) {
            addCriterion("prssport_pic_path in", values, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathNotIn(List<String> values) {
            addCriterion("prssport_pic_path not in", values, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathBetween(String value1, String value2) {
            addCriterion("prssport_pic_path between", value1, value2, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andPrssportPicPathNotBetween(String value1, String value2) {
            addCriterion("prssport_pic_path not between", value1, value2, "prssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathIsNull() {
            addCriterion("visa_prssport_pic_path is null");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathIsNotNull() {
            addCriterion("visa_prssport_pic_path is not null");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathEqualTo(String value) {
            addCriterion("visa_prssport_pic_path =", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathNotEqualTo(String value) {
            addCriterion("visa_prssport_pic_path <>", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathGreaterThan(String value) {
            addCriterion("visa_prssport_pic_path >", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathGreaterThanOrEqualTo(String value) {
            addCriterion("visa_prssport_pic_path >=", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathLessThan(String value) {
            addCriterion("visa_prssport_pic_path <", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathLessThanOrEqualTo(String value) {
            addCriterion("visa_prssport_pic_path <=", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathLike(String value) {
            addCriterion("visa_prssport_pic_path like", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathNotLike(String value) {
            addCriterion("visa_prssport_pic_path not like", value, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathIn(List<String> values) {
            addCriterion("visa_prssport_pic_path in", values, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathNotIn(List<String> values) {
            addCriterion("visa_prssport_pic_path not in", values, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathBetween(String value1, String value2) {
            addCriterion("visa_prssport_pic_path between", value1, value2, "visaPrssportPicPath");
            return (Criteria) this;
        }

        public Criteria andVisaPrssportPicPathNotBetween(String value1, String value2) {
            addCriterion("visa_prssport_pic_path not between", value1, value2, "visaPrssportPicPath");
            return (Criteria) this;
        }

    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
