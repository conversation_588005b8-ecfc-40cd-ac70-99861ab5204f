package com.hisun.lemon.urm.uitls.excel.fi;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.entity.ticket.TicketsExportVO;
import com.hisun.lemon.urm.entity.ticket.TicketsVoucherDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;

public class AcVoucherExcelExporter extends URMExcelExportFactorys {
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		try {
			String fileName="门票";
			String[] colNames=new String[] {"代金卷编号","面值","老师","公司编码","开始期次","结束期次","状态","备注"
					,"订单编号","订单备注","经销商编号","订单期次","创建人","创建时间","订单分公司","订单代办处"};
			List<List<Object>> data= addData(obj);
			super.exportActually2(data,fileName,colNames, response);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
	}
	
	public List<List<Object>> addData(Object obj) throws Exception {
		TicketsExportVO vo=(TicketsExportVO) obj;
		List<TicketsVoucherDO> dataList=vo.getDataList();
		List<List<Object>> list = new ArrayList<List<Object>>();
		if(dataList==null) return list;
		for (int j = 0; j < dataList.size(); j++) {
			TicketsVoucherDO o = (TicketsVoucherDO)dataList.get(j);
			List<Object> data = new ArrayList<Object>();
			data.add(o.getTicketsNo());
			data.add(o.getAmount() == null ? 0 : o.getAmount().doubleValue());
			data.add(o.getTeacher());
			data.add(o.getCompanyCode());
			data.add(o.getStartWeek());
			data.add(o.getEndWeek());
			String statusStr="";
			if(o.getVcStatus() != null) {
				statusStr=vo.getStatusMap().get(o.getVcStatus()+"");
			}
			data.add(statusStr);
			data.add(o.getMemo());
			data.add(o.getOrderNo());
			data.add(o.getRemark());
			data.add(o.getMemberNo());
			data.add(o.getPeriodWeek());
			data.add(o.getCreaterCode());
			data.add(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			data.add(o.getOrderCompany());
			data.add(o.getOrderAgent());
			list.add(data);
		}
		return list;
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		TicketsExportVO vo=(TicketsExportVO) obj;
		List<TicketsVoucherDO> dataList=vo.getDataList();
		for(TicketsVoucherDO o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getTicketsNo());
			row.createCell(index++).setCellValue(o.getAmount() == null ? 0 : o.getAmount().doubleValue());
			row.createCell(index++).setCellValue(o.getTeacher());
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getStartWeek());
			row.createCell(index++).setCellValue(o.getEndWeek());
			String statusStr="";
			if(o.getVcStatus() != null) {
				statusStr=vo.getStatusMap().get(o.getVcStatus()+"");
			}
			row.createCell(index++).setCellValue(statusStr);
			row.createCell(index++).setCellValue(o.getMemo());
			row.createCell(index++).setCellValue(o.getOrderNo());
			row.createCell(index++).setCellValue(o.getRemark());
			row.createCell(index++).setCellValue(o.getMemberNo());
			row.createCell(index++).setCellValue(o.getPeriodWeek());
			row.createCell(index++).setCellValue(o.getCreaterCode());
			row.createCell(index++).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
		}
		
	}
	public static AcVoucherExcelExporter builder() {
		return new AcVoucherExcelExporter();
	}
}
