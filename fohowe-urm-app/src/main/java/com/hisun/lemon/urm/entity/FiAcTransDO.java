/*
 * @ClassName FiAcTransDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiAcTransDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields acType 账户类型，f$＝f$，fv＝fv, f0=f000，pv=活跃pv，b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，s1=全球分红，s2=凤凰大使分红
     */
    private String acType;
    /**
     * @Fields outCompanyCode 付款方公司编号 out_company_code
     */
    private String outCompanyCode;
    /**
     * @Fields outUserCode 付款方用户编号 out_user_code
     */
    private String outUserCode;
    /**
     * @Fields outCurrency 付款方货币 out_currency
     */
    private String outCurrency;
    /**
     * @Fields outRate 付款方汇率(美元) out_rate
     */
    private BigDecimal outRate;
    /**
     * @Fields outMoney 付款金额 out_money
     */
    private BigDecimal outMoney;
    /**
     * @Fields handlingFee 手续费 handling_fee
     */
    private BigDecimal handlingFee;
    /**
     * @Fields inCompanyCode 收款方公司编号 in_company_code
     */
    private String inCompanyCode;
    /**
     * @Fields inUserCode 收款方用户编号 in_user_code
     */
    private String inUserCode;
    /**
     * @Fields inCurrency 收款方货币 in_currency
     */
    private String inCurrency;
    /**
     * @Fields inRate 收款方汇率(美元) in_rate
     */
    private BigDecimal inRate;
    /**
     * @Fields inMoney 收款金额 in_money
     */
    private BigDecimal inMoney;
    /**
     * @Fields status 状态，0:新建 1:审核通过 3:审核退回/删除
     */
    private String status;
    /**
     * @Fields creatorCode 创建人编号 creator_code
     */
    private String creatorCode;
    /**
     * @Fields checkerCode 确认人编号 checker_code
     */
    private String checkerCode;
    /**
     * @Fields checkTime 确认时间 check_time
     */
    private LocalDateTime checkTime;
    /**
     * @Fields memo 备注(会员) 
     */
    private String memo;
    /**
     * @Fields remark 摘要(公司) 
     */
    private String remark;
    /**
     * @Fields transType 转账类型: 1会员向代办处申购    2会员向代办处申领  3会员向会员转账  4任意
     */
    private String transType;
    /**
     * @Fields cancelCode 取消人
     */
    private String cancelCode;
    /**
     * @Fields cancelTime 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields periodWeek 转账申请期数
     */
    private String periodWeek;
    /**
     * @Fields transNo 转账单号
     */
    private String transNo;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields outAgentNo 转出代办处
     */
    private String outAgentNo;
    /**
     * @Fields inAgentNo 转入代办处
     */
    private String inAgentNo;
    /**
     * @Fields agentMemo 代办处备注
     */
    private String agentMemo;
    private String outUserName;
    private String inUserName;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getOutUserCode() {
        return outUserCode;
    }

    public void setOutUserCode(String outUserCode) {
        this.outUserCode = outUserCode;
    }

    public String getOutCurrency() {
        return outCurrency;
    }

    public void setOutCurrency(String outCurrency) {
        this.outCurrency = outCurrency;
    }

    public BigDecimal getOutRate() {
        return outRate;
    }

    public void setOutRate(BigDecimal outRate) {
        this.outRate = outRate;
    }

    public BigDecimal getOutMoney() {
        return outMoney;
    }

    public void setOutMoney(BigDecimal outMoney) {
        this.outMoney = outMoney;
    }

    public BigDecimal getHandlingFee() {
        return handlingFee;
    }

    public void setHandlingFee(BigDecimal handlingFee) {
        this.handlingFee = handlingFee;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getInUserCode() {
        return inUserCode;
    }

    public void setInUserCode(String inUserCode) {
        this.inUserCode = inUserCode;
    }

    public String getInCurrency() {
        return inCurrency;
    }

    public void setInCurrency(String inCurrency) {
        this.inCurrency = inCurrency;
    }

    public BigDecimal getInRate() {
        return inRate;
    }

    public void setInRate(BigDecimal inRate) {
        this.inRate = inRate;
    }

    public BigDecimal getInMoney() {
        return inMoney;
    }

    public void setInMoney(BigDecimal inMoney) {
        this.inMoney = inMoney;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getOutAgentNo() {
		return outAgentNo;
	}

	public void setOutAgentNo(String outAgentNo) {
		this.outAgentNo = outAgentNo;
	}

	public String getInAgentNo() {
		return inAgentNo;
	}

	public void setInAgentNo(String inAgentNo) {
		this.inAgentNo = inAgentNo;
	}

	public String getAgentMemo() {
		return agentMemo;
	}

	public void setAgentMemo(String agentMemo) {
		this.agentMemo = agentMemo;
	}

	public String getOutUserName() {
		return outUserName;
	}

	public void setOutUserName(String outUserName) {
		this.outUserName = outUserName;
	}

	public String getInUserName() {
		return inUserName;
	}

	public void setInUserName(String inUserName) {
		this.inUserName = inUserName;
	}
    
}