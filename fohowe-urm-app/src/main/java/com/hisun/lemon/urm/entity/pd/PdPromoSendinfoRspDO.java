package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDateTime;

public class PdPromoSendinfoRspDO extends BaseDO{

    private String salesCompanyCode;

    private String companyCode;

    private String receiptno;

    private String memberNo;

    private String agentNo;

    private Integer wWeek;

    private Integer sendStatus;
    
    private Integer  orderStatus;

    private String activeName;

    private String name;

    private LocalDateTime sendTime;

    private Integer id;

    private String orderor;
    /**
     * @Fields sender 发货人
     */
    private String sender;
    /**
     * @Fields orderdate 创建日期
     */
    private LocalDateTime orderdate;
    /**
     * @Fields faCheckdate 财务审核人
     */
    private String faChecker;
    /**
     * @Fields faCheckdate 财务审核日期
     */
    private LocalDateTime faCheckdate;
    
    /**
     * @Fields recName 收货人姓名
     */
    private String recName;
    /**
     * @Fields recAddr 收货地址
     */
    private String recAddr;
    /**
     * @Fields recPhone 收货联系电话
     */
    private String recPhone;
    /**
     * @Fields requestNo 提货申请单号
     */
    private String requestNo;
    /**
     * @Fields dcsendNo 物流单号
     */
    private String dcsendNo;
    
    public String getFaChecker() {
		return faChecker;
	}

	public void setFaChecker(String faChecker) {
		this.faChecker = faChecker;
	}

	public LocalDateTime getFaCheckdate() {
		return faCheckdate;
	}

	public void setFaCheckdate(LocalDateTime faCheckdate) {
		this.faCheckdate = faCheckdate;
	}

	public Integer getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(Integer orderStatus) {
		this.orderStatus = orderStatus;
	}

	public String getSalesCompanyCode() {
        return salesCompanyCode;
    }

    public void setSalesCompanyCode(String salesCompanyCode) {
        this.salesCompanyCode = salesCompanyCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public Integer getwWeek() {
        return wWeek;
    }

    public void setwWeek(Integer wWeek) {
        this.wWeek = wWeek;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getActiveName() {
        return activeName;
    }

    public void setActiveName(String activeName) {
        this.activeName = activeName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderor() {
        return orderor;
    }

    public void setOrderor(String orderor) {
        this.orderor = orderor;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public LocalDateTime getOrderdate() {
        return orderdate;
    }

    public void setOrderdate(LocalDateTime orderdate) {
        this.orderdate = orderdate;
    }

    public String getRecName() {
        return recName;
    }

    public void setRecName(String recName) {
        this.recName = recName;
    }

    public String getRecAddr() {
        return recAddr;
    }

    public void setRecAddr(String recAddr) {
        this.recAddr = recAddr;
    }

    public String getRecPhone() {
        return recPhone;
    }

    public void setRecPhone(String recPhone) {
        this.recPhone = recPhone;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getDcsendNo() {
        return dcsendNo;
    }

    public void setDcsendNo(String dcsendNo) {
        this.dcsendNo = dcsendNo;
    }
}
