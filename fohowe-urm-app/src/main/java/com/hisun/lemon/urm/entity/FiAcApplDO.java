/*
 * @ClassName FiAcApplDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FiAcApplDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields applNo 申请编号 appl_no 通过fn_sys_getno('appl',0)获得
     */
    private String applNo;
    /**
     * @Fields userCode 用户编号 user_code
     */
    private String userCode;
    /**
     * @Fields applType 1:提现 
     */
    private String applType;
    /**
     * @Fields amount 申请金额 amount
     */
    private BigDecimal amount;
    /**
     * @Fields createrCode 申请人编号 creater_code
     */
    private String createrCode;
    /**
     * @Fields createrName 申请人名称 creater_name
     */
    private String createrName;
    /**
     * @Fields status 状态，0:新建 1:已审核(分公司审核，总部审核状态都是1)  3:发放成功   6:分公司审核退回  9:总部审核退回 
     */
    private String status;
    /**
     * @Fields checkerCode 审核人编号 checker_code(分公司)
     */
    private String checkerCode;
    /**
     * @Fields checkerName 审核人名称 checker_name
     */
    private String checkerName;
    /**
     * @Fields checkTime 审核时间 check_time
     */
    private LocalDateTime checkTime;
    /**
     * @Fields operNo 发放计划编号 oper_no
     */
    private String operNo;
    /**
     * @Fields companyCode 所属分公司
     */
    private String companyCode;
    /**
     * @Fields falseReason 失败原因
     */
    private String falseReason;
    /**
     * @Fields extApplyNo 外部单据编号
     */
    private String extApplyNo;
    /**
     * @Fields acType 账户类型，fb=fb ，f$＝f$，fv＝fv, f0=f000，pv=活跃pv，b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，s1=全球分红，s2=凤凰大使分红
     */
    private String acType;
    /**
     * @Fields openBank 开户银行
     */
    private String openBank;
    /**
     * @Fields bCity 开户银行所在城市
     */
    private String bCity;
    /**
     * @Fields bName 银行户名
     */
    private String bName;
    /**
     * @Fields bNum 银行账号
     */
    private String bNum;
    /**
     * @Fields sendAmt 实发金额
     */
    private BigDecimal sendAmt;
    /**
     * @Fields fees 手续费
     */
    private BigDecimal fees;
    /**
     * @Fields memo 备注(会员) 
     */
    private String memo;
    /**
     * @Fields remark 摘要(公司) 
     */
    private String remark;
    /**
     * @Fields reCheckerCode 二次审核人编号 checker_code
     */
    private String reCheckerCode;
    /**
     * @Fields reCheckerName 二次审核人名称 checker_name
     */
    private String reCheckerName;
    /**
     * @Fields reCheckTime 二次审核时间 check_time
     */
    private LocalDateTime reCheckTime;
    /**
     * @Fields fiCheckStatus 财务确认状态 0:未确认;1:部分确认;2:已确认
     */
    private String fiCheckStatus;
    /**
     * @Fields fiCheckerCode 财务确认人编号 checker_code
     */
    private String fiCheckerCode;
    /**
     * @Fields fiCheckerName 财务确认人名称 checker_name
     */
    private String fiCheckerName;
    /**
     * @Fields fiCheckTime 财务确认时间 check_time
     */
    private LocalDateTime fiCheckTime;
    /**
     * @Fields localCurrency 本地货币代码 local_currency
     */
    private String localCurrency;
    /**
     * @Fields localMoney 本地货币金额 local_money
     */
    private BigDecimal localMoney;
    /**
     * @Fields rate 汇率 rate
     */
    private BigDecimal rate;
    /**
     * @Fields senderCode 发放人
     */
    private String senderCode;
    /**
     * @Fields sendTime 发放时间
     */
    private LocalDateTime sendTime;
    /**
     * @Fields senderName 发放人姓名
     */
    private String senderName;
    /**
     * @Fields recAmount 已财务确认金额
     */
    private BigDecimal recAmount;
    /**
     * @Fields periodWeek 提现申请期数
     */
    private String periodWeek;
    /**
     * @Fields cancelApplNo 提现取消单号
     */
    private String cancelApplNo;
    /**
     * @Fields cancelCode 作废人
     */
    private String cancelCode;
    /**
     * @Fields cancelTime 作废时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields cancelTime 申请时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields malltype 波兰商城类别 1：自然人  2：个体户
     */
    private String malltype;
    /**
     * @Fields taxmoney 波兰商城的税金
     */
    private String taxmoney;
    /**
     * eas确认状态
     */
	private String easStatus;
	
	private Integer finStatus;
	
	private Integer finNum;
	
	private BigDecimal localFinMoney;
    private String userType;
    private String configType;

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApplNo() {
        return applNo;
    }

    public void setApplNo(String applNo) {
        this.applNo = applNo;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getApplType() {
        return applType;
    }

    public void setApplType(String applType) {
        this.applType = applType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCheckerCode() {
        return checkerCode;
    }

    public void setCheckerCode(String checkerCode) {
        this.checkerCode = checkerCode;
    }

    public String getCheckerName() {
        return checkerName;
    }

    public void setCheckerName(String checkerName) {
        this.checkerName = checkerName;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getOperNo() {
        return operNo;
    }

    public void setOperNo(String operNo) {
        this.operNo = operNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getFalseReason() {
        return falseReason;
    }

    public void setFalseReason(String falseReason) {
        this.falseReason = falseReason;
    }

    public String getExtApplyNo() {
        return extApplyNo;
    }

    public void setExtApplyNo(String extApplyNo) {
        this.extApplyNo = extApplyNo;
    }

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getOpenBank() {
        return openBank;
    }

    public void setOpenBank(String openBank) {
        this.openBank = openBank;
    }

    public String getbCity() {
        return bCity;
    }

    public void setbCity(String bCity) {
        this.bCity = bCity;
    }

    public String getbName() {
        return bName;
    }

    public void setbName(String bName) {
        this.bName = bName;
    }

    public String getbNum() {
        return bNum;
    }

    public void setbNum(String bNum) {
        this.bNum = bNum;
    }

    public BigDecimal getSendAmt() {
        return sendAmt;
    }

    public void setSendAmt(BigDecimal sendAmt) {
        this.sendAmt = sendAmt;
    }

    public BigDecimal getFees() {
        return fees;
    }

    public void setFees(BigDecimal fees) {
        this.fees = fees;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getReCheckerCode() {
        return reCheckerCode;
    }

    public void setReCheckerCode(String reCheckerCode) {
        this.reCheckerCode = reCheckerCode;
    }

    public String getReCheckerName() {
        return reCheckerName;
    }

    public void setReCheckerName(String reCheckerName) {
        this.reCheckerName = reCheckerName;
    }

    public LocalDateTime getReCheckTime() {
        return reCheckTime;
    }

    public void setReCheckTime(LocalDateTime reCheckTime) {
        this.reCheckTime = reCheckTime;
    }

    public String getFiCheckStatus() {
        return fiCheckStatus;
    }

    public void setFiCheckStatus(String fiCheckStatus) {
        this.fiCheckStatus = fiCheckStatus;
    }

    public String getFiCheckerCode() {
        return fiCheckerCode;
    }

    public void setFiCheckerCode(String fiCheckerCode) {
        this.fiCheckerCode = fiCheckerCode;
    }

    public String getFiCheckerName() {
        return fiCheckerName;
    }

    public void setFiCheckerName(String fiCheckerName) {
        this.fiCheckerName = fiCheckerName;
    }

    public LocalDateTime getFiCheckTime() {
        return fiCheckTime;
    }

    public void setFiCheckTime(LocalDateTime fiCheckTime) {
        this.fiCheckTime = fiCheckTime;
    }

    public String getLocalCurrency() {
        return localCurrency;
    }

    public void setLocalCurrency(String localCurrency) {
        this.localCurrency = localCurrency;
    }

    public BigDecimal getLocalMoney() {
        return localMoney;
    }

    public void setLocalMoney(BigDecimal localMoney) {
        this.localMoney = localMoney;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getSenderCode() {
        return senderCode;
    }

    public void setSenderCode(String senderCode) {
        this.senderCode = senderCode;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public BigDecimal getRecAmount() {
        return recAmount;
    }

    public void setRecAmount(BigDecimal recAmount) {
        this.recAmount = recAmount;
    }

    public String getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(String periodWeek) {
        this.periodWeek = periodWeek;
    }

    public String getCancelApplNo() {
        return cancelApplNo;
    }

    public void setCancelApplNo(String cancelApplNo) {
        this.cancelApplNo = cancelApplNo;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getMalltype() {
        return malltype;
    }

    public void setMalltype(String malltype) {
        this.malltype = malltype;
    }

    public String getTaxmoney() {
        return taxmoney;
    }

    public void setTaxmoney(String taxmoney) {
        this.taxmoney = taxmoney;
    }

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public String getEasStatus() {
		return easStatus;
	}

	public void setEasStatus(String easStatus) {
		this.easStatus = easStatus;
	}

	public Integer getFinStatus() {
		return finStatus;
	}

	public void setFinStatus(Integer finStatus) {
		this.finStatus = finStatus;
	}

	public Integer getFinNum() {
		return finNum;
	}

	public void setFinNum(Integer finNum) {
		this.finNum = finNum;
	}

	public BigDecimal getLocalFinMoney() {
		return localFinMoney;
	}

	public void setLocalFinMoney(BigDecimal localFinMoney) {
		this.localFinMoney = localFinMoney;
	}

	@Override
	public String toString() {
		return "FiAcApplDO [id=" + id + ", applNo=" + applNo + ", userCode=" + userCode + ", applType=" + applType
				+ ", amount=" + amount + ", createrCode=" + createrCode + ", createrName=" + createrName + ", status="
				+ status + ", checkerCode=" + checkerCode + ", checkerName=" + checkerName + ", checkTime=" + checkTime
				+ ", operNo=" + operNo + ", companyCode=" + companyCode + ", falseReason=" + falseReason
				+ ", extApplyNo=" + extApplyNo + ", acType=" + acType + ", openBank=" + openBank + ", bCity=" + bCity
				+ ", bName=" + bName + ", bNum=" + bNum + ", sendAmt=" + sendAmt + ", fees=" + fees + ", memo=" + memo
				+ ", remark=" + remark + ", reCheckerCode=" + reCheckerCode + ", reCheckerName=" + reCheckerName
				+ ", reCheckTime=" + reCheckTime + ", fiCheckStatus=" + fiCheckStatus + ", fiCheckerCode="
				+ fiCheckerCode + ", fiCheckerName=" + fiCheckerName + ", fiCheckTime=" + fiCheckTime
				+ ", localCurrency=" + localCurrency + ", localMoney=" + localMoney + ", rate=" + rate + ", senderCode="
				+ senderCode + ", sendTime=" + sendTime + ", senderName=" + senderName + ", recAmount=" + recAmount
				+ ", periodWeek=" + periodWeek + ", cancelApplNo=" + cancelApplNo + ", cancelCode=" + cancelCode
				+ ", cancelTime=" + cancelTime + ", createTime=" + createTime + ", agentNo=" + agentNo + ", malltype="
				+ malltype + ", taxmoney=" + taxmoney + ", easStatus=" + easStatus + ", finStatus=" + finStatus
				+ ", finNum=" + finNum + ", localFinMoney=" + localFinMoney + "]";
	}
	
}