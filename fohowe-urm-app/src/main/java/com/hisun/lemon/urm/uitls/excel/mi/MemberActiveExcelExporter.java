package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.mi.member.ActiveBean;
import com.hisun.lemon.urm.dto.mi.member.ActiveVO;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class MemberActiveExcelExporter extends  URMExcelExportFactory{
    private ILanguageService languageService;
	public MemberActiveExcelExporter(ILanguageService languageService) {
		// TODO Auto-generated constructor stub
		this.languageService=languageService;
	}
	public MemberActiveExcelExporter() {
		// TODO Auto-generated constructor stub
	}
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="活跃期数查询";
		
		String[] colNames=new String[] {
				"分公司","代办处编号","经销商编号","经销商姓名","经销商级别",
				"活跃期数","当前期数","剩余期数",
				"注册日期"
				};
		
		String multiLangFileName="menu.dataactivesearch";
		String[] multiLangColNames=new String[] {
				"column.company","column.agencyNo","column.memNo","column.memName","column.memType",
				"common.huo_yue_qi_shu","column.currPeroid","column.restPeriod",
				"column.registerDate"
		};
		
		Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
				
		if(result!=null) {
			colNames=(String[]) result.get("titles");
			fileName=(String) result.get("fileName");
		}
		
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		ActiveVO vo=(ActiveVO) obj;
		List<ActiveBean> dataList=vo.getDataList();
		Map<String,String> cardTypeKV=vo.getCardType();
		String currentWeek=vo.getCurrentWeek();
		for(ActiveBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getCompanyCode());
			row.createCell(1).setCellValue(o.getAgentNo());
			row.createCell(2).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(3).setCellValue(o.getMemberName());
			row.createCell(4).setCellValue(cardTypeKV.get(o.getCardType()));
			
			row.createCell(5).setCellValue(o.getActiveWeek());
			row.createCell(6).setCellValue(currentWeek);
			row.createCell(7).setCellValue(o.getRemainWeek()==null?"":o.getRemainWeek()+"");
			row.createCell(8).setCellValue(o.getRegDate()==null?"":o.getRegDate().format(ymdhms));
		
		}
		
	}
	public static MemberActiveExcelExporter builder() {
		return new MemberActiveExcelExporter();
	}
}
