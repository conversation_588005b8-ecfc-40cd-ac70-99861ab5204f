
package com.hisun.lemon.urm.uitls.excel;

import java.io.InputStream;
import java.io.OutputStream;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.common.MIBaseQueryInfo;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.dto.al.CharacterQueryListRspDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryReqDTO;
import com.hisun.lemon.urm.dto.al.CharacterQueryRspDTO;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils.ExcelType;

/**
 * URMExcelExportFactory
 * <AUTHOR>
 * @date 2017-12-26 15:20
 */
public class URMExcelExportFactory {
	public DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	public DateTimeFormatter yms = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	public void export(Object obj, HttpServletResponse response) {
		
	}
	public void export2(Object obj,HttpServletResponse response) {
	       
	}
	public void exportActually(String fileName,String[] colNames,Object obj, HttpServletResponse response) {
		
		try {
			ExcelHelperUtils.setResponseHead(fileName,response,ExcelType.XLSX);
			this.exportExcel(response.getOutputStream(), obj, fileName, colNames);
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
		
	}
	
	public void exportActually2(List<List<Object>> data,String fileName,String[] colNames,HttpServletResponse response) {
		try {
			response.reset();
			response.setHeader("Content-disposition", "attachment; filename=" +fileName+ ".xlsx");
            response.setContentType("application/msexcel;charset=UTF-8");//设置类型
            response.setHeader("Pragma", "No-cache");//设置头
            response.setHeader("Cache-Control", "no-cache");//设置头
            response.setDateHeader("Expires", 0);//设置日期头
            List<List<String>> head = new ArrayList<List<String>>();
            for (int i = 0; i < colNames.length; i++) {
            	List<String> head0 = new ArrayList<String>();
            	head0.add(colNames[i]);
            	head.add(head0);
    		}
			WriteWorkbook workBook = new WriteWorkbook();
			workBook.setExcelType(ExcelTypeEnum.XLSX);
			workBook.setOutputStream(response.getOutputStream());
			workBook.setNeedHead(true);
			WriteSheet sheet= new WriteSheet();
			sheet.setSheetNo(0);
			sheet.setSheetName(fileName); 
			sheet.setHead(head);
			ExcelWriter write = new ExcelWriter(workBook);
			write.write(data, sheet); 
			write.finish();
		} catch (Exception e) {
			e.printStackTrace();
			LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
		}
		
	}

	public void exportExcel(OutputStream  oss, Object obj, String sheetName, String[] rowName) throws Exception {
		XSSFWorkbook workbook = null;
		try {
			workbook = new XSSFWorkbook();
			XSSFSheet sheet = workbook.createSheet(sheetName);
//			XSSFRow rowm = sheet.createRow(0);
//			XSSFCell cellTiltle = rowm.createCell(0);
			XSSFCellStyle columnTopStyle = this.getColumnTopStyle(workbook);// 获取列头样式对象

			XSSFCellStyle style = this.getStyle(workbook);

//			sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, (rowName.length - 1)));
//			cellTiltle.setCellStyle(columnTopStyle);
//			cellTiltle.setCellValue(sheetName);

			int columnNum = rowName.length;
			XSSFRow rowRowName = sheet.createRow(0);

			for (int n = 0; n < columnNum; n++) {
				XSSFCell cellRowName = rowRowName.createCell(n);
				cellRowName.setCellType(CellType.STRING);//(XSSFCell.CELL_TYPE_STRING);
				XSSFRichTextString text = new XSSFRichTextString(rowName[n]);
				cellRowName.setCellValue(text);
				cellRowName.setCellStyle(columnTopStyle);
				sheet.setColumnWidth(n, rowName[n].getBytes().length*2*256);
			}
			int beginRow=1;
//			int beginRow=3;
			addCell(sheet, style, obj,beginRow);
			workbook.write(oss);
		} catch (Exception e) {
			throw e;
		}
	}

	public Object excelImport(InputStream input) throws Exception {
		try {
			XSSFWorkbook hssfWorkbook = new XSSFWorkbook(input);
			XSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
			if (hssfSheet == null) {
				return null;
			}
			Object obj = parseExcel(hssfSheet);
			return obj;
		} catch (Exception e) {
			throw e;
		}
	}

	public Object parseExcel(XSSFSheet st) throws LemonException {
		return null;
	}
	
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
	}
	public List<List<Object>> addData(Object obj) throws Exception {
		return null;
	}

	public XSSFCellStyle getColumnTopStyle(XSSFWorkbook workbook) {

		XSSFFont font = workbook.createFont();
		font.setFontHeightInPoints((short) 11);
		font.setBold(true);
		font.setFontName("Courier New");
		XSSFCellStyle style = workbook.createCellStyle();
		style.setFont(font);
		style.setWrapText(false);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);

		return style;
	}
	

	public XSSFCellStyle getStyle(XSSFWorkbook workbook) {
		XSSFFont font = workbook.createFont();
		font.setFontName("Courier New");
		XSSFCellStyle style = workbook.createCellStyle();
		style.setFont(font);
		style.setWrapText(false);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);

		return style;
	}

	public void setMaxRecords(MIBaseQueryInfo baseInfo) {
		baseInfo.setPageSize(ExcelHelperUtils.MAX_RECORDS);
	}
	public void setMaxRecords30000(MIBaseQueryInfo baseInfo) {
		baseInfo.setPageSize(ExcelHelperUtils.MAX_RECORDS2);
	}
	
	public void setMaxRecords2(MIBaseQueryInfo baseInfo) {
		baseInfo.setPageSize(10000);
	}
	public void setMaxRecords3(MIBaseQueryInfo baseInfo) {
		baseInfo.setPageSize(ExcelHelperUtils.MAX_RECORDS3);
	}
	
	/**
	 * 多语言处理
	* @param titleKeyArr
	* @param languageClient
	* @return 
	* <AUTHOR>
	 */
	public Map<String,Object>  multiLanguageDeal(String multiLangFileNameKey,String[] titleKeyArr, ILanguageService languageService) {
       
	    String titleKeyStr= Stream.of(titleKeyArr).collect(Collectors.joining(","))+","+multiLangFileNameKey;
        
        String langCode = LemonUtils.getLocale().getLanguage() + "-" + LemonUtils.getLocale().getCountry();
       
        CharacterQueryReqDTO reqDTO = new CharacterQueryReqDTO();
        reqDTO.setCharacterKey(titleKeyStr);
        reqDTO.setLangCode(langCode);
        
        CharacterQueryListRspDTO resultList =  languageService.languageQueryList(reqDTO);
        if (resultList==null||resultList.getCharValueList()==null||resultList.getCharValueList().size()==0) {
        	return  null;
        }
        List<CharacterQueryRspDTO> valueList = resultList.getCharValueList();
        Map<String, String> map = valueList.stream().collect(
                Collectors.toMap(CharacterQueryRspDTO::getCharacterKey, CharacterQueryRspDTO::getCharacterValue));

        List<String> arr= Stream.of(titleKeyArr).map(s->{
        	String v=map.get(s);
			return StringUtils.isBlank(v)?s:v;
			
		}).collect(Collectors.toCollection(ArrayList::new));
       /* map.forEach((k,v)->
        	str
        	System.out.println("Item : " + k + " Count : " + v)
        		
        		);*/
        String fileName=map.get(multiLangFileNameKey);
        fileName=StringUtils.isBlank(fileName)?multiLangFileNameKey:fileName;
        
        Map<String,Object> result=new HashMap<>();
        result.put("fileName", fileName);
        result.put("titles", arr.stream().toArray(String[]::new));
		return   result;
		
	}
	

	
}
