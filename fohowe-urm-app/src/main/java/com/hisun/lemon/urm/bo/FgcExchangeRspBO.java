package com.hisun.lemon.urm.bo;

import java.math.BigDecimal;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.page.PageInfo;
import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.entity.FiFgcExchangeDO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ClientValidated
@ApiModel(value = "FgcExchangeRspBO", description = "兑换查询结果集对象")
public class FgcExchangeRspBO extends GenericDTO<NoBody> {

    @ApiModelProperty(value = "分页结果集")
    PageInfo<FiFgcExchangeDO> exchangeInfo;


    @ApiModelProperty(value = "总金额")
    private BigDecimal countAmount;


    @ApiModelProperty(value = "总数")
    private BigDecimal countNumber;

    public PageInfo<FiFgcExchangeDO> getExchangeInfo() {
        return exchangeInfo;
    }

    public void setExchangeInfo(PageInfo<FiFgcExchangeDO> exchangeInfo) {
        this.exchangeInfo = exchangeInfo;
    }

    public BigDecimal getCountAmount() {
        return countAmount;
    }

    public void setCountAmount(BigDecimal countAmount) {
        this.countAmount = countAmount;
    }

    public BigDecimal getCountNumber() {
        return countNumber;
    }

    public void setCountNumber(BigDecimal countNumber) {
        this.countNumber = countNumber;
    }
}
