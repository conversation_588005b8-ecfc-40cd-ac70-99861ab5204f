package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.fohowe.common.enums.CardTypes;
import com.hisun.lemon.fohowe.common.enums.PromQualEnums;
import com.hisun.lemon.urm.dto.mi.member.SubMember;
import com.hisun.lemon.urm.dto.mi.member.SubMemberDO;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class SubMemberExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="下属经销商查询";
		String[] colNames=new String[] {
				"经销商编号","经销商姓名","代办处编号",//"推荐人编号",
				"代数","经销商级别","注册时间","促销资格"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		SubMemberDO vo=(SubMemberDO) obj;
		List<SubMember> dataList=vo.getMembers();
		Map<Object, String> cardtypeKV=EnumsUtils.EnumToMap(CardTypes.class);
		Map<Object, String> promQualLevel = EnumsUtils.EnumToMap(PromQualEnums.class);
		for(SubMember o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(index++).setCellValue(o.getMemberName());
			row.createCell(index++).setCellValue(o.getAgentNo());
//			row.createCell(index++).setCellValue(o.getRecommendNo()==null?"":o.getRecommendNo().trim());
			row.createCell(index++).setCellValue(o.getLayer());
			row.createCell(index++).setCellValue(cardtypeKV.get(o.getCardType()));
			row.createCell(index++).setCellValue(o.getRegisterDate()==null?"":o.getRegisterDate().format(ymdhms));
			String promQual = o.getPromQual() + "";
			row.createCell(index++).setCellValue(promQualLevel.get(promQual));
		}
		
	}
	public static SubMemberExcelExporter builder() {
		return new SubMemberExcelExporter();
	}
}
