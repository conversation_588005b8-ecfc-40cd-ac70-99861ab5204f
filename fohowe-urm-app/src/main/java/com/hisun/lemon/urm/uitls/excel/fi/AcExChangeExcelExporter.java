package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.urm.dto.fi.exchange.AcExChangeVO;
import com.hisun.lemon.urm.dto.fi.exchange.AcExchangeBean;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcExChangeExcelExporter extends  URMExcelExportFactorys{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="兑换";
		String[] colNames=new String[] {"期次","编号","公司编码","公司名称","代办处编码","用户编码","F$","积分","F000","时间","备注"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		AcExChangeVO vo=(AcExChangeVO) obj;
		List<AcExchangeBean> dataList=vo.getDataList();
		for(AcExchangeBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getPeriodWeek());
			row.createCell(index++).setCellValue(o.getExchangeNo());
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getCompanyName());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getUserCode());
			row.createCell(index++).setCellValue(o.getMoney().doubleValue());
			if(o.getExType() != null && o.getExType()==1) {
				row.createCell(index++).setCellValue(o.getMoneyFv()==null?o.getMoney().doubleValue():o.getMoneyFv().doubleValue());
				row.createCell(index++).setCellValue(o.getMoneyH0()==null?o.getMoney().doubleValue():o.getMoneyH0().doubleValue());
			}else {
				row.createCell(index++).setCellValue(o.getMoney().doubleValue());
				row.createCell(index++).setCellValue(o.getMoney().doubleValue());
			}
			row.createCell(index++).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getMemo());
		}
		
	}
	public static AcExChangeExcelExporter builder() {
		return new AcExChangeExcelExporter();
	}
}
