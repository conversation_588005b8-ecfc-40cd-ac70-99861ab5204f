package com.hisun.lemon.urm.uitls;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.urm.dto.mi.member.MemMainTree;
import com.hisun.lemon.urm.dto.mi.member.MemRecTree;
import com.hisun.lemon.urm.dto.mi.member.NetWorkTO;
import com.hisun.lemon.urm.dto.mi.member.RightIndivShortBean;
import com.hisun.lemon.urm.dto.mi.member.RightNetworkBean;

public class BuildTree {
	private static final Logger logger = LoggerFactory.getLogger(BuildTree.class);
	  
    public static MemRecTree buildTreeDown(List<MemRecTree> nodes,NetWorkTO to) throws Exception {

    	String topNodeId=to.getMemNo();
        if(nodes == null||StringUtils.isBlank(to.getMemNo())){
        	throw new Exception("nodes is null");
        }
        List<MemRecTree> topNodes = new ArrayList<MemRecTree>();

        for (MemRecTree children : nodes) {

            String memId = children.getMemNo();
            if (topNodeId.equals(memId)) {
                topNodes.add(children);
                continue;
            }
            String pid = children.getParentMemNo();
            for (MemRecTree parent : nodes) {
                String id = parent.getMemNo();
                if (id != null && id.equals(pid)) {
                    parent.getChildren().add(children);
                    continue;
                }
            }

        }

        MemRecTree root = new MemRecTree();
        if (topNodes.size() == 1) {
            root = topNodes.get(0);
        } else {
           throw new Exception("Node error, expected to return 1, actual return:"+topNodes.size());
        }

        return root;
    }

    
    public static MemRecTree buildTreeUp(List<MemRecTree> nodes,NetWorkTO to) throws Exception {

        if(nodes == null||StringUtils.isBlank(to.getMemNo())){
        	throw new Exception("nodes is null");
        }
        List<MemRecTree> topNodes = new ArrayList<MemRecTree>();

        for (MemRecTree children : nodes) {
     
            String pid = children.getParentMemNo();
            for (MemRecTree parent : nodes) {
                String id = parent.getMemNo();
                if (id != null && id.equals(pid)) {
                	children.setHasParent(true);
                    parent.getChildren().add(children);
                    continue;
                }
            }

        }
        
        for (MemRecTree node : nodes) {
        	if(!node.isHasParent()) {
        		topNodes.add(node);
        		break;
        	}
        }
        
        

        MemRecTree root = null;
        if (topNodes.size() == 1) {
            root = topNodes.get(0);
        } else {
           throw new Exception("Node error, expected to return 1, actual return:"+topNodes.size());
        }

        return root;
    }


	public static RightNetworkBean buildRightTreeUp(List<RightNetworkBean> nodes, NetWorkTO to) throws Exception {
		 if(nodes == null){
	        	throw new Exception("nodes is null");
	        }
	        List<RightNetworkBean> topNodes = new ArrayList<RightNetworkBean>();

	        
	        for (RightNetworkBean children : nodes) {
	        	
	            String childRightNo = children.getRightNo();
	            String childParentNo = children.getParentNo();
	            
	          //  String id = children.getParentNo();
	            for (RightNetworkBean parent : nodes) {
	                String pid = parent.getRightNo();
	                if (pid != null && pid.equals(childParentNo)) {
	                	children.setHasParent(true);
	                	if(childRightNo.equals(parent.getLeftMem())) {
	                		parent.getChildren().add(0,children);
	                	}else {
	                		parent.getChildren().add(children);
	                	}
	                    continue;
	                }
	            }

	        }
	        
	        for (RightNetworkBean node : nodes) {
	        	if(!node.isHasParent()) {
	        		topNodes.add(node);
	        		break;
	        	}
	        }

	        RightNetworkBean root = null;
	        if (topNodes.size() == 1) {
	            root = topNodes.get(0);
	        } else {
	           throw new Exception("Node error, expected to return 1, actual return:"+topNodes.size());
	        }

	        return root;
	}


	public static RightNetworkBean buildRightTreeDown(List<RightNetworkBean> nodes, NetWorkTO to) throws Exception {
		String topNodeId=to.getMemNo();
        if(nodes == null||StringUtils.isBlank(to.getMemNo())){
        	throw new Exception("nodes is null");
        }
        List<RightNetworkBean> topNodes = new ArrayList<RightNetworkBean>();

        for (RightNetworkBean children : nodes) {


            String childRightNo = children.getRightNo();
            String childParentNo = children.getParentNo();
            
            if (topNodeId.equals(childRightNo)) {
                topNodes.add(children);
                continue;
            }
            
            for (RightNetworkBean parent : nodes) {
                String pid = parent.getRightNo();
            	if (childParentNo != null && childParentNo.equals(pid)) {
            		if(childRightNo.equals(parent.getLeftMem())) {
                		parent.getChildren().add(0,children);
                	}else {
                		parent.getChildren().add(children);
                	}
                }
                continue;
            }

        }

        RightNetworkBean root = new RightNetworkBean();
        if (topNodes.size() == 1) {
            root = topNodes.get(0);
        } else {
           throw new Exception("Node error, expected to return 1, actual return:"+topNodes.size());
        }

        return root;
	}


	public static RightIndivShortBean buildRightIndivNetworkShortTreeDown(List<RightIndivShortBean> nodes, NetWorkTO to) {
		String topNodeId=to.getRightNo();//top rightNo
        if(nodes == null||StringUtils.isBlank(to.getMemNo())){
        	LemonException.throwLemonException("nodes is null");
        }
        List<RightIndivShortBean> topNodes = new ArrayList<RightIndivShortBean>();

        for (RightIndivShortBean children : nodes) {

            String childRightNo = children.getRightNo();
            String childParentNo = children.getParentNo();
            
            if (topNodeId.equals(childRightNo)) {
                topNodes.add(children);
                continue;
            }
            
            if(!children.getMemberNo().equals(to.getMemNo())) {
            	
            	
            	if(StringUtils.isNotBlank(children.getLeftMem())&&StringUtils.isNotBlank(children.getRightMem())) {
            		//full  hidden 
            		children.setHidden(true);
            		
            		boolean leftFlag=hasNodeOfTargetMem(children.getLeftMem(),to,nodes,1);
            		boolean rightFlag=hasNodeOfTargetMem(children.getRightMem(),to,nodes,1);
            		if(leftFlag&&rightFlag) {
            			
                		//hang it to its parent
                		for (RightIndivShortBean parent : nodes) {
                            String parentRightNo = parent.getRightNo();
                        	if (childParentNo != null && childParentNo.equals(parentRightNo)) {
                        		if(childRightNo.equals(parent.getLeftMem())) {
                            		parent.getChildren().add(0,children);
                            	}else {
                            		parent.getChildren().add(children);
                            	}
                        		break;
                            }
                        }
            		}else if(leftFlag||rightFlag) {//only one is true
            			passRightAndSubmitSonsSelective(nodes,children,leftFlag,rightFlag);
            			
            		}//else ignore
            		
            	
            		
            		
            		
            	}else if(StringUtils.isNotBlank(children.getLeftMem())||StringUtils.isNotBlank(children.getRightMem())) {
            		//one ,  pass and submit children
            		passRightAndSubmitSons(nodes,children);
            	}else {
            		//none , pass
            		continue;
            	}
            	
            }else {
            	
                for (RightIndivShortBean parent : nodes) {
                    String parentRightNo = parent.getRightNo();
                	if (childParentNo != null && childParentNo.equals(parentRightNo)) {
                		if(childRightNo.equals(parent.getLeftMem())) {
                    		parent.getChildren().add(0,children);
                    	}else {
                    		parent.getChildren().add(children);
                    	}
                		 break;
                    }
                   
                }
            	
            }
            

        }

        RightIndivShortBean root = new RightIndivShortBean();
        if (topNodes.size() == 1) {
            root = topNodes.get(0);
        } else {
        	LemonException.throwLemonException("Node error, expected to return 1, actual return:"+topNodes.size());
        }

        return root;
	}

	private static void passRightAndSubmitSonsSelective(List<RightIndivShortBean> nodes, RightIndivShortBean passNode,
			boolean leftFlag, boolean rightFlag) {
		
		String passNodeOfChildNo=null;
		if(leftFlag) {
			// submit left children
			passNodeOfChildNo=passNode.getLeftMem();
		}
		if(rightFlag) {
			// submit right children
			passNodeOfChildNo=passNode.getRightMem();
		}
		
		for (RightIndivShortBean n : nodes) {
			if(n.getRightNo().equals(passNodeOfChildNo)) { //search child itself
				boolean isSearchedChild=false;
				if(passNode.getChildren()!=null&&passNode.getChildren().size()!=0) {
					//it shows children had been foreached,but it has two children !!! not sure they all have been foreached
					
					for(RightIndivShortBean s:passNode.getChildren()) {
						if(s.getRightNo().equals(passNodeOfChildNo)) {
							//submit children
							RightIndivShortBean p=getParent(passNode.getParentNo(),nodes);
							
							if(p.getLeftMem().equals(passNode.getRightNo())) {
								p.getChildren().add(0,n);
							}else {
								p.getChildren().add(n);
							}
							isSearchedChild=true;
						}
					}
					
					
				}
				if(passNode.getChildren()==null||passNode.getChildren().size()==0||!isSearchedChild) {
					//it shows children has not been foreach,and it will be auto foreach
					// so prepare the params: set the child of 'pass' node  to upper node
					
					RightIndivShortBean p=getParent(passNode.getParentNo(),nodes);
					if(p!=null) {
						n.setParentNo(p.getRightNo());
						if(p.getLeftMem().equals(passNode.getRightNo())) {
							p.setLeftMem(n.getRightNo());
						}else {
							p.setRightMem(n.getRightNo());
						}
					}
				}
				
				break;
				
			}
		}
		
		
	}


	private static RightIndivShortBean getParent(String parentNo,List<RightIndivShortBean> nodes) {
		for (RightIndivShortBean n : nodes) {
			if(n.getRightNo().equals(parentNo)) {
				return n;  
			}
		}
		return null;
	}


	private static boolean  hasNodeOfTargetMem(String rightNo, NetWorkTO to,
			List<RightIndivShortBean> nodes,int type ) {
		for(RightIndivShortBean n:nodes) {
			if(type==1) {  
				if(n.getRightNo().equals(rightNo)) { //search itself
					if(n.getMemberNo().equals(to.getMemNo())) {
						return true;
					}else {
						if(hasNodeOfTargetMem(n.getRightNo(),to,nodes,2)) {
							return true;
						}
					}
				}
			}else {  
				if(n.getParentNo().equals(rightNo)) {  //search sub children  
					if(n.getMemberNo().equals(to.getMemNo())) {
						return true;
					}else {
						if(hasNodeOfTargetMem(n.getRightNo(),to,nodes,2)) {
							return true;
						}
					}
				}
			}
			
			
		}
		return false;
	}



	private static void passRightAndSubmitSons(List<RightIndivShortBean> nodes, RightIndivShortBean pass) {
		for (RightIndivShortBean n : nodes) {
			if(n.getRightNo().equals(pass.getParentNo())) {
				RightIndivShortBean children =null;
				if(pass.getChildren()!=null&&pass.getChildren().size()!=0) {
					//it has only one children ; it shows children had been foreached
					children=pass.getChildren().get(0);
					//submit children
					if(n.getLeftMem().equals(pass.getRightNo())) {
						n.getChildren().add(0,children);
					}else {
						n.getChildren().add(children);
					}
					
				}else {
					//otherwise,it shows children has not been foreach,and it will be auto foreach
					// so prepare the params: set the child of 'pass' node  to upper node
					String leftMem=pass.getLeftMem()==null?"":pass.getLeftMem(); 
					String rightMem=pass.getRightMem()==null?"":pass.getRightMem();
					String childrenRightNo=leftMem+rightMem;
					children =getChild(childrenRightNo,nodes); //it has only one child
					if(children!=null) {// if it has not children then pass it 
						children.setParentNo(n.getRightNo());
						
						if(n.getLeftMem().equals(pass.getRightNo())) {
							n.setLeftMem(children.getRightNo());
						}else {
							n.setRightMem(children.getRightNo());
						}
					}
					
					
					
				}
				
				break;
				
			}
		}
		
	}

	/**
	 * 只适用于单个Child情况
	* @param childrenRightNo
	* @param nodes
	* @return 
	* <AUTHOR>
	 */
	private static RightIndivShortBean getChild(String childrenRightNo, List<RightIndivShortBean> nodes) {
		for (RightIndivShortBean n : nodes) {
			if(n.getRightNo().equals(childrenRightNo)) {
				return n;  
			}
		}
		return null;
	}


	public static MemMainTree buildMemberMainDown(List<MemMainTree> nodes, NetWorkTO to, String type) {
		String topNodeId=to.getRightNo();
        if(nodes == null||StringUtils.isBlank(topNodeId)){
        	LemonException.throwLemonException("nodes is null");
            
        }
        MemMainTree topNodes =null;

        for (MemMainTree children : nodes) {

           // String rightNo = children.getRightNo();

            String childRightNo = children.getRightNo();
            String childParentNo = children.getParentMemNo();//actual this is linkNo
            
            if (topNodeId.equals(childRightNo)) {
            	topNodes=children;
                continue;
            }
            int level=Integer.parseInt(children.getLevelKey());
            int typeIn=Integer.parseInt(type);
            
            if(level<typeIn) {
            	children.setHidden(true);
            }
            
            if(!"1".equals(children.getPrimRight())) {
            	children.setHidden(true);
            }
            
            
            for (MemMainTree parent : nodes) {
                String pid = parent.getRightNo();
            	if (childParentNo != null && childParentNo.equals(pid)) {
            		if(childRightNo.equals(parent.getLeftMem())) {
                		parent.getChildren().add(0,children);
                	}else {
                		parent.getChildren().add(children);
                	}
                }
                continue;
            }

        }
     
        if (topNodes == null) {
        
           	LemonException.throwLemonException("Node error, expected to return 1, actual return null");
        }

        return topNodes;
	}


}