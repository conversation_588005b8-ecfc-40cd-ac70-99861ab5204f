/*
 * @ClassName IMiMemberActiveDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-17 20:53:27
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.ActiveBean;
import com.hisun.lemon.urm.dto.mi.member.ActiveDTO;
import com.hisun.lemon.urm.entity.MiMemberActiveDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IMiMemberActiveDao extends BaseDao<MiMemberActiveDO> {
	
	int getTotalCount(ActiveDTO dto);

	List<ActiveBean> getListByPageBreak(ActiveDTO dto);
}