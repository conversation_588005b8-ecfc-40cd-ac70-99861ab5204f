/*
 * @ClassName sysUserPwdLogDO
 * @Description 
 * @version 1.0
 * @Date 2018-02-08 18:10:52
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class sysUserPwdLogDO extends BaseDO {
    /**
     * @Fields logId 日志ID LOG_ID
     */
    private Long logId;
    /**
     * @Fields userCode 用户编号 USER_CODE
     */
    private String userCode;
    /**
     * @Fields pwdType 1:一级密码 2:二级密码
     */
    private String pwdType;
    /**
     * @Fields editorCode 变更人编号 EDITOR_CODE
     */
    private String editorCode;
    /**
     * @Fields editTime 变更时间 EDIT_TIME
     */
    private LocalDateTime editTime;
    
    private String companyCode;
    private String companyName;
    private String agentNo;
    private String agentName;
    
    private String userType;
    private String userName;

    
    public Long getLogId() {
		return logId;
	}

	public void setLogId(Long logId) {
		this.logId = logId;
	}

	public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getPwdType() {
        return pwdType;
    }

    public void setPwdType(String pwdType) {
        this.pwdType = pwdType;
    }

    public String getEditorCode() {
        return editorCode;
    }

    public void setEditorCode(String editorCode) {
        this.editorCode = editorCode;
    }

    public LocalDateTime getEditTime() {
        return editTime;
    }

    public void setEditTime(LocalDateTime editTime) {
        this.editTime = editTime;
    }

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
    
}