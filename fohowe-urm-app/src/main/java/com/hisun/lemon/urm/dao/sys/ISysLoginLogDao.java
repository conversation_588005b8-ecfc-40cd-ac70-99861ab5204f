/*
 * @ClassName ISysLoginLogDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-28 14:48:29
 */
package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import com.hisun.lemon.urm.dto.sys.UserLoginDTO;
import com.hisun.lemon.urm.dto.sys.UserQueryReqDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysLoginLogDO;

@Mapper
public interface ISysLoginLogDao extends BaseDao<SysLoginLogDO> {
	List<SysLoginLogDO> getLoginLogs(@Param("userCode") String userCode);
	
	int insertLoginLogtoHis(@Param("userCode") String userCode);

	List<UserLoginDTO> getLoginUser(UserQueryReqDTO reqDTO);
}