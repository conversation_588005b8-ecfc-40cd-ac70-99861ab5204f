/*
 * @ClassName FiAcBalanceDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;

public class FiAcBalanceDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields companyCode 分公司编号
     */
    private String companyCode;
    /**
     * @Fields acType 账户类型，fb=fb ，f$＝f$，fv＝fv, f0=f000，pv=活跃pv，lv=活跃额度，
            b1=旅游基金，b2=名车基金，b3=游艇基金，b4=住宅基金，b5=国内旅游基金
            s1=全球分红，s2=凤凰大使分红 mv=中国代办处购货(给经销报单扣)
            nv=中国重消额度
     */
    private String acType;
    /**
     * @Fields userCode 经销商/代办处编号
     */
    private String userCode;
    /**
     * @Fields balance 余额 balance
     */
    private BigDecimal balance;
    /**
     * @Fields validBalance 可用余额 balance
     */
    private BigDecimal validBalance;
    /**
     * @Fields status 状态（bit），1＝暂停发放
     */
    private String status;
    /**
     * @Fields oweAmt 借款
     */
    private BigDecimal oweAmt;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

	public String getAcType() {
		return acType;
	}

	public void setAcType(String acType) {
		this.acType = acType;
	}

	public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getValidBalance() {
        return validBalance;
    }

    public void setValidBalance(BigDecimal validBalance) {
        this.validBalance = validBalance;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getOweAmt() {
        return oweAmt;
    }

    public void setOweAmt(BigDecimal oweAmt) {
        this.oweAmt = oweAmt;
    } 
    
}