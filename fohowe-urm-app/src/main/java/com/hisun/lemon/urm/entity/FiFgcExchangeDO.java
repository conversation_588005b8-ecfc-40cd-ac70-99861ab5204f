/*
 * @ClassName FiFgcExchangeDO
 * @Description 
 * @version 1.0
 * @Date 2019-10-29 15:06:31
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;


public class FiFgcExchangeDO extends BaseDO {
    /**
     * @Fields id 兑换单头
     */
    private String id;
    /**
     * @Fields memberNo 经销商编号
     */
    private String memberNo;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields companyCode 公司编码
     */
    private String companyCode;
    /**
     * @Fields name 经销商姓名
     */
    private String name;
    /**
     * @Fields goldPrive 黄金单价
     */
    private BigDecimal goldPrive;
    /**
     * @Fields usaRate 兑美元汇率
     */
    private BigDecimal usaRate;
    /**
     * @Fields fRate 兑F$汇率
     */
    private BigDecimal fRate;
    /**
     * @Fields payAmount 需支付的F$
     */
    private BigDecimal payAmount;
    /**
     * @Fields number 兑换数量
     */
    private BigDecimal number;
    /**
     * @Fields periodWeek 购买期数
     */
    private Integer periodWeek;
    /**
     * @Fields exchangeType 兑换标示 1:购入 2:出售
     */
    private Integer exchangeType;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields createCode 创建人
     */
    private String createCode;
    /**
     * @Fields updateCode 更新人
     */
    private String updateCode;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;


    private String bonusType;

    private String areaCode;
    /*查询条件*/
    private String[] bonusTypes;

    private String[] areaCodes;

    private String[] companyCodes;

    private Integer startWeek;

    private Integer endWeek;

    private LocalDateTime startTime;

    private LocalDateTime endTime;
    
    private String cardType;
    
    private BigDecimal preorderAmount;
    
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getGoldPrive() {
        return goldPrive;
    }

    public void setGoldPrive(BigDecimal goldPrive) {
        this.goldPrive = goldPrive;
    }

    public BigDecimal getUsaRate() {
        return usaRate;
    }

    public void setUsaRate(BigDecimal usaRate) {
        this.usaRate = usaRate;
    }

    public BigDecimal getfRate() {
        return fRate;
    }

    public void setfRate(BigDecimal fRate) {
        this.fRate = fRate;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public Integer getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(Integer periodWeek) {
        this.periodWeek = periodWeek;
    }

    public Integer getExchangeType() {
        return exchangeType;
    }

    public void setExchangeType(Integer exchangeType) {
        this.exchangeType = exchangeType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public String getUpdateCode() {
        return updateCode;
    }

    public void setUpdateCode(String updateCode) {
        this.updateCode = updateCode;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(Integer startWeek) {
        this.startWeek = startWeek;
    }

    public Integer getEndWeek() {
        return endWeek;
    }

    public void setEndWeek(Integer endWeek) {
        this.endWeek = endWeek;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String[] getBonusTypes() {
        return bonusTypes;
    }

    public void setBonusTypes(String[] bonusTypes) {
        this.bonusTypes = bonusTypes;
    }

    public String[] getAreaCodes() {
        return areaCodes;
    }

    public void setAreaCodes(String[] areaCodes) {
        this.areaCodes = areaCodes;
    }

    public String[] getCompanyCodes() {
        return companyCodes;
    }

    public void setCompanyCodes(String[] companyCodes) {
        this.companyCodes = companyCodes;
    }

    public String getBonusType() {
        return bonusType;
    }

    public void setBonusType(String bonusType) {
        this.bonusType = bonusType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public BigDecimal getPreorderAmount() {
		return preorderAmount;
	}

	public void setPreorderAmount(BigDecimal preorderAmount) {
		this.preorderAmount = preorderAmount;
	}
}