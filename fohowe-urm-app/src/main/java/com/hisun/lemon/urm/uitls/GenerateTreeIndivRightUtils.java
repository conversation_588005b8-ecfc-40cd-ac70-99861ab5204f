package com.hisun.lemon.urm.uitls;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.urm.dto.mi.member.NetWorkTO;
import com.hisun.lemon.urm.dto.mi.member.RightIndivShortBean;

public class GenerateTreeIndivRightUtils {
	
	public RightIndivShortBean generateRightIndivNetworkShortTree(List<RightIndivShortBean> nodes, NetWorkTO to, Integer totalLay) {
		
		RightIndivShortBean top=null;
		String topNodeNO =to.getRightNo();
		for(RightIndivShortBean d:nodes) {

            String childRightNo = d.getRightNo();
            
		   if (childRightNo.equals(topNodeNO)) {
			   top=d;
                break;
            }
			
		}
		List<RightIndivShortBean> topNode=new ArrayList<>();
		topNode.add(top);
		Map<Integer, List<RightIndivShortBean>> map = new HashMap<>();
		map.put(1,topNode );
		
		int i=1;
		while(true) {
			List<RightIndivShortBean> tempNodes=new ArrayList<>();
			List<RightIndivShortBean> layerNodes =map.get(i);
			if(layerNodes==null) {
				break;
			}
			for(RightIndivShortBean parent:layerNodes) {
				List<RightIndivShortBean> list= searchSubNodes(parent,nodes); 
				if(list!=null&&list.size()!=0) {
					tempNodes.addAll(list);
				}
	            
			}
			i++;
			if(tempNodes!=null&&tempNodes.size()!=0) {
				map.put(i,tempNodes );
			}
			if(i>=totalLay) {
				break;
			}
			
		}
		if(map.get(1)!=null) {
			return map.get(1).get(0);
		}
		return null;
		
	
	}
	

	private  List<RightIndivShortBean> searchSubNodes(RightIndivShortBean parent, List<RightIndivShortBean> nodes) {
        
        RightIndivShortBean left=getLeftOrRightMem(parent,nodes,"0");
        RightIndivShortBean right=getLeftOrRightMem(parent,nodes,"1");
        List<RightIndivShortBean> tempNodes=null;
        if(left!=null||right!=null) {
        	tempNodes=new ArrayList<>();
        	if(left!=null) {
        		parent.getChildren().add(left);
        		tempNodes.add(left);
        	}
        	if(right!=null) {
        		parent.getChildren().add(right);
        		tempNodes.add(right);
        	}
        }
        
        return tempNodes;
		
	}

	private  RightIndivShortBean getLeftOrRightMem(RightIndivShortBean parent, List<RightIndivShortBean> nodes,String leftOrRight) {
		String subrightNo = null;
        String sublinkIndex = null;
        
        if("0".equals(leftOrRight)) {
        	subrightNo = parent.getLeftMem();
        	sublinkIndex = parent.getLinkIndex()+"0";
        }else {
        	subrightNo = parent.getRightMem();
        	sublinkIndex = parent.getLinkIndex()+"1";
        }
        //优先通过登记的左右区查询，如果未登记，而却有直接子节点，最终会有问题，因为getSubMemByLinkIndex是建立在上述条件之上的
        RightIndivShortBean right=getSubMemByRightNO(subrightNo,nodes);
        if(right==null){
        	right=getSubMemByLinkIndex(sublinkIndex,nodes);
        	/*if(right!=null) {
        		right.setRightNo(parent.getRightNo());
        	}*/
        }
		return right;
	}
	

	private  RightIndivShortBean getSubMemByRightNO(String subRightNo, List<RightIndivShortBean> nodes) {
		
		if(subRightNo!=null&&!"VITURL".equals(subRightNo)) {  //不要是虚构节点
			for (RightIndivShortBean sub : nodes) {
	        	if (sub.getRightNo().equals(subRightNo)) {
	        		return sub;
	            }
	        }
		}
		
		return null;
	}
	private  RightIndivShortBean getSubMemByLinkIndex(String linkIndex, List<RightIndivShortBean> nodes) {
		//when going this method ,it means it has not direct subchildren
		String sub2leftLinkIndex = linkIndex+"0";
		String sub2rightLinkIndex = linkIndex+"1";
		boolean sub2leftFlag=false;
		boolean sub2rightFlag=false;
		
		List<RightIndivShortBean> oneSideNodes=new ArrayList<>();
		
		for (RightIndivShortBean sub : nodes) {
			if(sub.getLinkIndex().startsWith(linkIndex)) {
				oneSideNodes.add(sub);
				if(sub.getLinkIndex().startsWith(sub2leftLinkIndex)) {
					sub2leftFlag=true;
				}
				if(sub.getLinkIndex().startsWith(sub2rightLinkIndex)) {
					sub2rightFlag=true;
				}
				if(sub.getLinkIndex().equals(linkIndex)) {
					return sub;
				}
			}
			//特殊处理
//			if(linkIndex.equals("0000000000000000101000001000000000000000000000000000000000000001100000000000011000000111000011")) {
//				sub2leftFlag = false;
//			}
        	
        }
		String decideFlag=null;
		
		if(sub2leftFlag||sub2rightFlag) {
			if(sub2leftFlag&&sub2rightFlag) {
				decideFlag="FULL";
			}else {
				decideFlag="SINGLE";
			}
		}
		
		if(decideFlag==null) {
			return null;
		}
		if("FULL".equals(decideFlag)) {
			RightIndivShortBean newDO=new RightIndivShortBean();
			newDO.setLinkIndex(linkIndex);
			newDO.setLeftMem("VITURL");
			newDO.setRightMem("VITURL");  
			newDO.setHidden(true);
			return newDO;
		}else if("SINGLE".equals(decideFlag)) {
			if(oneSideNodes.size()==1) {
				return oneSideNodes.get(0);
			}
			Collections.sort(oneSideNodes, (o1, o2) -> o1.getLinkLayer()-o2.getLinkLayer());
			boolean flag=chargeNodesOneSide(oneSideNodes);
			if(flag) {
				return oneSideNodes.get(0);
			}else {
				String tempIndex=sub2leftLinkIndex;
				if(sub2rightFlag) {
					 tempIndex=sub2rightLinkIndex;
				}
			return	getSubMemByLinkIndex(tempIndex, oneSideNodes) ;
			}
			
		}
		
		return null;
	}


	private boolean chargeNodesOneSide(List<RightIndivShortBean> oneSideNodes) {
		
		RightIndivShortBean top=oneSideNodes.get(0);
		
		String topIndex=top.getLinkIndex();
		for (RightIndivShortBean sub : oneSideNodes) {
			if(!sub.getLinkIndex().startsWith(topIndex)) {
				return false;
			}
        	
        }
		
		return true;
	}



}
