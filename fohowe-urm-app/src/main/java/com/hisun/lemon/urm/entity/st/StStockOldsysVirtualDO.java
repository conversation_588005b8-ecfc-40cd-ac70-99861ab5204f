package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.*;

public class StStockOldsysVirtualDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields companyCode 公司id
     */
    private String companyCode;
    /**
     * @Fields goodsCode 代办处编号
     */
    private String goodsCode;

    private BigDecimal byQty;
    
    private BigDecimal bsQty;
    
    private BigDecimal virtualQty;
    
    private String Remark;
    
    /**
     * @Column(length=10 ,scale=2)
     */
 
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }
   
    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public BigDecimal getByQty() {
        return byQty;
    }

    public void setByQty(BigDecimal byQty) {
        this.byQty = byQty;
    }
    
    public BigDecimal getBsQty() {
        return bsQty;
    }

    public void setBsQty(BigDecimal bsQty) {
        this.bsQty = bsQty;
    }

    public BigDecimal getVirtualQty() {
        return virtualQty;
    }

    public void setVirtualQty(BigDecimal virtualQty) {
        this.virtualQty = virtualQty;
    }
    
    public String getRemark() {
        return Remark;
    }
    public void setRemark(String Remark) {
        this.Remark = Remark;
    }
}