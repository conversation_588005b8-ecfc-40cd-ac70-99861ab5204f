package com.hisun.lemon.urm.uitls.excel.fi;



import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.fi.change.AcChangeQueryBean;
import com.hisun.lemon.urm.dto.fi.change.AcChangeVO;
import com.hisun.lemon.urm.enums.fi.ChangeTypeEnums;
import com.hisun.lemon.urm.enums.fi.StatusEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class AcChangeExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="账户补扣";
		String[] colNames=new String[] {"所属分公司","代办处","用户编号","账户类型","状态","交易类别","存入","取出",
				"创建时间","审核时间","备注"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AcChangeVO vo=(AcChangeVO) obj;
		List<AcChangeQueryBean> dataList=vo.getDataList();
		Map<Object, String> statusKV=EnumsUtils.EnumToMap(StatusEnums.class);
		Map<String,String> orderTypeKV=vo.getOrderTypeKV();
		Map<Object, String> changeTypeKV=EnumsUtils.EnumToMap(ChangeTypeEnums.class);
		Map<String,String> acTypeKV=vo.getAcTypeKV();
		for(AcChangeQueryBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(o.getCompanyCode());
			row.createCell(1).setCellValue(o.getAgentNo());
			row.createCell(2).setCellValue(o.getUserCode());
			row.createCell(3).setCellValue(acTypeKV.get(o.getAcType()));
			row.createCell(4).setCellValue(statusKV.get(o.getStatus()));
			row.createCell(5).setCellValue(orderTypeKV.get(o.getOrderType()));
			if(o.getChangeType().equals(ChangeTypeEnums.IN.getCode())) {
				row.createCell(6).setCellValue(o.getMoney().doubleValue());
			}else {
				row.createCell(6).setCellValue(BigDecimal.ZERO.doubleValue());
			}
			if(o.getChangeType().equals(ChangeTypeEnums.OUT.getCode())) {
				row.createCell(7).setCellValue(o.getMoney().doubleValue());
			}else {
				row.createCell(7).setCellValue(BigDecimal.ZERO.doubleValue());
			}
			row.createCell(8).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(9).setCellValue(o.getCheckTime()==null?"":o.getCheckTime().format(ymdhms));
			row.createCell(10).setCellValue(o.getMemo());
		}
		
	}
	public static AcChangeExcelExporter builder() {
		return new AcChangeExcelExporter();
	}
}
