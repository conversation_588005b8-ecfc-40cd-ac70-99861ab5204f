package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.fohowe.common.enums.OrderTypeEnums;
import com.hisun.lemon.urm.dto.fi.balance.AccountStateBean;
import com.hisun.lemon.urm.dto.fi.balance.AcountStateVO;
import com.hisun.lemon.urm.enums.SummaryTyepEnums;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;


public class AcBalanceAcountExcelExporter extends  URMExcelExportFactory{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="账户统计报表";
		AcountStateVO vo=(AcountStateVO) obj;
		String[] colNames=null;
		if(OrderTypeEnums.SUPPLY.getCode().equals(vo.getOrderType())) {
			if(SummaryTyepEnums.HEAD.getCode().equals(vo.getType())) {
				colNames=new String[] {"期数","购F000F$"};
			}else if(SummaryTyepEnums.AREA.getCode().equals(vo.getType())) {
				colNames=new String[] {"期数","区域","购F000F$"};
			}else if(SummaryTyepEnums.COMPANY.getCode().equals(vo.getType())) {
				colNames=new String[] {"期数","区域","分公司","购F000F$"};
			}else if(SummaryTyepEnums.AGENT.getCode().equals(vo.getType())) {
				colNames=new String[] {"期数","区域","分公司","代办处","购F000F$"};
			}else {
				colNames=new String[] {"期数","区域","分公司","代办处","用户编号","购F000F$"};
			}
			fileName="F$兑换报表";
		}else if(OrderTypeEnums.F000.getCode().equals(vo.getOrderType())) {
			if(SummaryTyepEnums.HEAD.getCode().equals(vo.getType())) {
				colNames=new String[] {"存入","取出","结余金额"};
			}else if(SummaryTyepEnums.AREA.getCode().equals(vo.getType())) {
				colNames=new String[] {"区域","存入","取出","结余金额"};
			}else if(SummaryTyepEnums.COMPANY.getCode().equals(vo.getType())) {
				colNames=new String[] {"区域","分公司","存入","取出","结余金额"};
			}else if(SummaryTyepEnums.AGENT.getCode().equals(vo.getType())) {
				colNames=new String[] {"区域","分公司","代办处","存入","取出","结余金额"};
			}else {
				colNames=new String[] {"区域","分公司","代办处","用户编号","存入","取出","结余金额"};
			}
			fileName="F000换货抵扣报表";
		}else {
			if(SummaryTyepEnums.HEAD.getCode().equals(vo.getType())) {
				colNames=new String[] {"账户类型","存入","取出","结余金额"};
			}else if(SummaryTyepEnums.AREA.getCode().equals(vo.getType())) {
				colNames=new String[] {"区域","账户类型","存入","取出","结余金额"};
			}else if(SummaryTyepEnums.COMPANY.getCode().equals(vo.getType())) {
				colNames=new String[] {"区域","分公司","账户类型","存入","取出","结余金额"};
			}else if(SummaryTyepEnums.AGENT.getCode().equals(vo.getType())) {
				colNames=new String[] {"区域","分公司","代办处","账户类型","存入","取出","结余金额"};
			}else {
				colNames=new String[] {"区域","分公司","代办处","用户编号","账户类型","存入","取出","结余金额"};
			}
		}
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		AcountStateVO vo=(AcountStateVO) obj;
		List<AccountStateBean> dataList=vo.getAcountStateBeanList();
		Map<String,String> acTypeKV = vo.getAcTypeKV();
		for(AccountStateBean o:dataList) {
			XSSFRow row = sheet.createRow(beginRow++);
			if(OrderTypeEnums.SUPPLY.getCode().equals(vo.getOrderType())) {
				int i = 0;
				row.createCell(i++).setCellValue(vo.getThePeriod());
				if(o.getAreaCode()!="" && o.getAreaCode()!=null) {
					row.createCell(i++).setCellValue(o.getAreaCode());
				}
				if(o.getCompanyCode()!="" && o.getCompanyCode()!=null) {
					row.createCell(i++).setCellValue(o.getCompanyCode());
				}
				if(o.getAgentNo()!="" && o.getAgentNo()!=null) {
					row.createCell(i++).setCellValue(o.getAgentNo());
				}
				if(o.getMemberNo()!="" && o.getMemberNo()!=null) {
					row.createCell(i++).setCellValue(o.getMemberNo());
				}
				row.createCell(i++).setCellValue(o.getOutMoney()+"");
			}else if(OrderTypeEnums.F000.getCode().equals(vo.getOrderType())) {
				int i = 0;
				if(o.getAreaCode()!="" && o.getAreaCode()!=null) {
					row.createCell(i++).setCellValue(o.getAreaCode());
				}
				if(o.getCompanyCode()!="" && o.getCompanyCode()!=null) {
					row.createCell(i++).setCellValue(o.getCompanyCode());
				}
				if(o.getAgentNo()!="" && o.getAgentNo()!=null) {
					row.createCell(i++).setCellValue(o.getAgentNo());
				}
				if(o.getMemberNo()!="" && o.getMemberNo()!=null) {
					row.createCell(i++).setCellValue(o.getMemberNo());
				}
				row.createCell(i++).setCellValue(o.getInMoney()+"");
				row.createCell(i++).setCellValue(o.getOutMoney()+"");
				row.createCell(i++).setCellValue(o.getOutFactMoney()+"");
			}else {
				int i = 0;
				if(o.getAreaCode()!="" && o.getAreaCode()!=null) {
					row.createCell(i++).setCellValue(o.getAreaCode());
				}
				if(o.getCompanyCode()!="" && o.getCompanyCode()!=null) {
					row.createCell(i++).setCellValue(o.getCompanyCode());
				}
				if(o.getAgentNo()!="" && o.getAgentNo()!=null) {
					row.createCell(i++).setCellValue(o.getAgentNo());
				}
				if(o.getMemberNo()!="" && o.getMemberNo()!=null) {
					row.createCell(i++).setCellValue(o.getMemberNo());
				}
				row.createCell(i++).setCellValue(acTypeKV.get(o.getAcType()));
				row.createCell(i++).setCellValue(o.getInMoney()+"");
				row.createCell(i++).setCellValue(o.getOutMoney()+"");
				row.createCell(i++).setCellValue(o.getOutFactMoney()+"");
			}
		}
		
	}
	public static AcBalanceAcountExcelExporter builder() {
		return new AcBalanceAcountExcelExporter();
	}
}
