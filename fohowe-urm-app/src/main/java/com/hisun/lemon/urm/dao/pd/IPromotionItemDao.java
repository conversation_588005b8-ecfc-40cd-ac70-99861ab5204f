package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.pd.PromoItemDTO;
import com.hisun.lemon.urm.entity.pd.PromotionItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IPromotionItemDao extends BaseDao<PromotionItemDO> {


    int insertBatch(List<PromotionItemDO> goodsDTOS);

    int deleteGoods(@Param("orderNo") String orderNo, @Param("operCode") String opercode);

    List<PromotionItemDO> queryByOrderNo(List<String> orderList);

    List<PromotionItemDO> queryDetailById(Integer id);

	//PromotionItemDO findByNo(@Param("orderNo") String orderNo);
}
