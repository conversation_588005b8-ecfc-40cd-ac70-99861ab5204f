/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: EntityGranExcel.java 
 * @Prject: fohowe-web-ec
 * @Package: com.hisun.lemon.fohowe.ec.excel 
 * @author: liubao   
 * @date: 2017年12月6日 上午10:46:28 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.excel.st;

import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.dto.st.StockHistoryBeanDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

/**
 * @ClassName: StockHistoryGrantExcel 
 * @author: tian
 * @date: 2018年2月26日 下午3:50:18 
 * @param <T>
 */
public class StockHistoryGrantExcel extends URMExcelExportFactory {

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style,Object obj,int beginRow) throws Exception {
    	 List<StockHistoryBeanDTO> dataList = (List<StockHistoryBeanDTO>) obj;
        if (dataList != null && dataList.size() > 0) {
            for (int i = 0; i < dataList.size(); i++) {
            	//获取文件头信息
            	//Row row1 = sheet.getRow(2);
            	//判断文件头一共几列
            	//int n =row1.getLastCellNum();
            	int n = 6;
            	//创建列标志
            	int j = 0;
            	  
                StockHistoryBeanDTO stockDTO = dataList.get(i);
                // 创建所需的行数
                Row row = sheet.createRow(i +beginRow);
                Cell cell = null;

                if(6==n) {
                	//分公司
                    cell = row.createCell(j, CellType.STRING);
                    cell.setCellValue(JudgeUtils.isNull(stockDTO.getCompanyCodeStr()) ? "" : stockDTO.getCompanyCodeStr());
                    cell.setCellStyle(style);
                    j++;
                    //区域
                    cell = row.createCell(j, CellType.STRING);
                    cell.setCellValue(JudgeUtils.isNull(stockDTO.getAreaCodeStr()) ? "" : stockDTO.getAreaCodeStr());
                    cell.setCellStyle(style);
                    j++;
                }
                
                if(5==n) {
                	//区域
                    cell = row.createCell(j, CellType.STRING);
                    cell.setCellValue(JudgeUtils.isNull(stockDTO.getAreaCodeStr()) ? "" : stockDTO.getAreaCodeStr());
                    cell.setCellStyle(style);
                    j++;
                }
                
                //商品编码
                cell = row.createCell(j, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockDTO.getGoodsCode()) ? "" : stockDTO.getGoodsCode());
                cell.setCellStyle(style);
                j++;

                //名称
                cell = row.createCell(j, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(stockDTO.getGoodsName()) ? "" : stockDTO.getGoodsName());
                cell.setCellStyle(style);
                j++;
                //价格
               /* cell = row.createCell(2, CellType.NUMERIC);
                cell.setCellValue(JudgeUtils.isNull(stockDTO.getPrice()) ? "" : stockDTO.getPrice() + "");
                cell.setCellStyle(style);*/

                //可用库存
                cell = row.createCell(j, CellType.NUMERIC);
                cell.setCellValue(stockDTO.getValidQty());
                cell.setCellStyle(style);
                j++;
                
                //实际库存
                cell = row.createCell(j, CellType.NUMERIC);
                cell.setCellValue(stockDTO.getQuantity());
                cell.setCellStyle(style);
                j++;
                //虚拟库存库存
                cell = row.createCell(j, CellType.NUMERIC);
                cell.setCellValue(stockDTO.getVirtualQty());
                cell.setCellStyle(style);

            }
        }
    }

}
