/*
 * @ClassName BdUpdatePointDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-12 11:31:28
 */
package com.hisun.lemon.urm.entity.bd;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class BdUpdatePointDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields wWeek 根据ID序号设置
     */
    private String wWeek;
    /**
     * @Fields ruleType 类型,0=左区业绩，1=右区业绩
     */
    private String ruleType;
    /**
     * @Fields rightNo 经营权编号
     */
    private String rightNo;
    /**
     * @Fields points 业绩调整,+增加，-减少
     */
    private BigDecimal points;
    /**
     * @Fields status 状态，1=审核，2=取消，3=锁定
     */
    private String status;
    /**
     * @Fields creatorCode 建单人
     */
    private String creatorCode;
    /**
     * @Fields checkCode 审核人
     */
    private String checkCode;
    /**
     * @Fields checkTime 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * @Fields cancelCode 取消人
     */
    private String cancelCode;
    /**
     * @Fields cancelTime 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * @Fields remark 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getwWeek() {
        return wWeek;
    }

    public void setwWeek(String wWeek) {
        this.wWeek = wWeek;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getRightNo() {
        return rightNo;
    }

    public void setRightNo(String rightNo) {
        this.rightNo = rightNo;
    }

    public BigDecimal getPoints() {
        return points;
    }

    public void setPoints(BigDecimal points) {
        this.points = points;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getCancelCode() {
        return cancelCode;
    }

    public void setCancelCode(String cancelCode) {
        this.cancelCode = cancelCode;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}