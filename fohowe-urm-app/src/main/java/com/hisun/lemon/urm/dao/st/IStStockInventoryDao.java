/*
 * @ClassName IStStockRequestDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-13 21:08:15
 */
package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StStockInventoryDO;

@Mapper
public interface IStStockInventoryDao extends BaseDao<StStockInventoryDO> {

	int search(StStockInventoryDO requestDO);
	int callInsertInventoryDetail(@Param("wWeek") Integer wWeek);
	List<StStockInventoryDO> searchList(
			@Param("bonusType") String bonusType,
			@Param("areaCode") String areaCode,
			@Param("startWeek") Integer startWeek,
			@Param("endWeek") Integer endWeek,
    		@Param("companyList") List<String> companyList, 
    		@Param("goodsCodeList") List<String> goodsCodeList);
	int deleteByInsert(String requestId);
}