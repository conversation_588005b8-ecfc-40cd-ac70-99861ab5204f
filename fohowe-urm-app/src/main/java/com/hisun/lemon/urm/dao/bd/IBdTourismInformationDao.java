package com.hisun.lemon.urm.dao.bd;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.bd.BdTourismPromQueryDTO;
import com.hisun.lemon.urm.dto.bd.BdToursimInformationReqDTO;
import com.hisun.lemon.urm.entity.bd.BdTourismInformationBO;
import com.hisun.lemon.urm.entity.bd.BdTourismInformationDO;
import com.hisun.lemon.urm.entity.bd.BdTourismInformationExample;
@Mapper
public interface IBdTourismInformationDao extends BaseDao<BdTourismInformationDO> {
     /**
     * 新加重置递增函数
     * @param example
     */
    void reidnumber(BdTourismInformationExample example);
    long countByExample(BdTourismInformationExample example);

    int deleteByExample(BdTourismInformationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BdTourismInformationDO row);

    int insertSelective(BdTourismInformationDO row);

    List<BdTourismInformationDO> selectByExample(BdTourismInformationExample example);

    List<BdTourismInformationDO> selectByMemberNo(BdToursimInformationReqDTO reqDTO);

    BdTourismInformationDO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") BdTourismInformationDO row, @Param("example") BdTourismInformationExample example);

    int updateByExample(@Param("row") BdTourismInformationDO row, @Param("example") BdTourismInformationExample example);

    int updateByPrimaryKeySelective(BdTourismInformationDO row);

    int updateByPrimaryKey(BdTourismInformationDO row);
    int updateInfoMemNo();
    int updateInfoMemName();
    
	List<BdTourismInformationBO> queryInformation(BdTourismPromQueryDTO query);
}