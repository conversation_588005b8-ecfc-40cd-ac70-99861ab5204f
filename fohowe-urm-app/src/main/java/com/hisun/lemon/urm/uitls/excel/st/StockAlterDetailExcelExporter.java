package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.bns.dto.common.PageRspDTO;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.entity.st.StStockAlterDetailDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class StockAlterDetailExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "库存调整明细表";
        String[] colNames = new String[] {"分公司编号","调整单编号","产品编码","调整数量","调整类型","调整原因","创建时间","总部审核时间","确认时间","备注","调整单状态" };
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        PageRspDTO<StStockAlterDetailDO> vo = (PageRspDTO<StStockAlterDetailDO>) obj;
        List<StStockAlterDetailDO> dataList = vo.getRetultList();
        for (StStockAlterDetailDO o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);
            // 分公司编号
            row.createCell(0).setCellValue(o.getCompanyCode());
            // 调整单编号
            row.createCell(1).setCellValue(o.getReceiptNo() + "");
            // 产品编码
            row.createCell(2).setCellValue(o.getGoodsCode());
            // 调整数量
            row.createCell(3).setCellValue(o.getAlterQty());
            // 调整类型
            row.createCell(4).setCellValue(o.getReceiptType());
            // 调整原因
            row.createCell(5).setCellValue(o.getReceiptReason());
           // 创建时间
            row.createCell(6).setCellValue(JudgeUtils.isNull(o.getOperDate()) ? "" : o.getOperDate().format(ymdhms)); 
           // 总部审核时间
            row.createCell(7).setCellValue(JudgeUtils.isNull(o.getCheckDate()) ? "" : o.getCheckDate().format(ymdhms)); 
            // 确认时间
            row.createCell(8).setCellValue(JudgeUtils.isNull(o.getConfirmCheckDate()) ? "" : o.getConfirmCheckDate().format(ymdhms)); 
            // 备注
            row.createCell(9).setCellValue(o.getRemark());
            // 调整单状态
            row.createCell(10).setCellValue(o.getStatus());
           
        }

    }

    public static StockAlterDetailExcelExporter builder() {
        return new StockAlterDetailExcelExporter();
    }
}
