package com.hisun.lemon.urm.entity.pd;

import java.math.BigDecimal;

public class PdManifestItemsRspDO {

    private int id;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    /**
     * @Fields standardPrice 标准F$
     */
    private BigDecimal standardPrice;
    /**
     * @Fields standardFv 标准FV
     */
    private BigDecimal standardFv;
    /**
     * @Fields inQty 进货数量:非超量收货时<=OrderQty
     */
    private Integer inQty;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields setQty 已经发货数量
     */
    private Integer setQty;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public BigDecimal getStandardPrice() {
        return standardPrice;
    }

    public void setStandardPrice(BigDecimal standardPrice) {
        this.standardPrice = standardPrice;
    }

    public BigDecimal getStandardFv() {
        return standardFv;
    }

    public void setStandardFv(BigDecimal standardFv) {
        this.standardFv = standardFv;
    }

    public Integer getInQty() {
        return inQty;
    }

    public void setInQty(Integer inQty) {
        this.inQty = inQty;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSetQty() {
        return setQty;
    }

    public void setSetQty(Integer setQty) {
        this.setQty = setQty;
    }
}
