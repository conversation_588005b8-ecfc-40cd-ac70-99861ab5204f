package com.hisun.lemon.urm.dao.st;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StLogisticsCompany;

@Mapper
public interface IStLogisticsCompanyDao extends BaseDao<StLogisticsCompany> {
    int deleteByPrimaryKey(Integer id);

    int insert(StLogisticsCompany record);

    int insertSelective(StLogisticsCompany record);

    StLogisticsCompany selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(StLogisticsCompany record);

    int updateByPrimaryKey(StLogisticsCompany record);

	void deleteByLogisticsNo(String logisticsNo);
}