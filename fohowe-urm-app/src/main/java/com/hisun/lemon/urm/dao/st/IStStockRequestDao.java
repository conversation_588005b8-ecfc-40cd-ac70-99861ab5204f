/*
 * @ClassName IStStockRequestDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-13 21:08:15
 */
package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StStockRequestDO;

@Mapper
public interface IStStockRequestDao extends BaseDao<StStockRequestDO> {

	int search(StStockRequestDO requestDO);
	List<StStockRequestDO> searchList(StStockRequestDO requestDO);
	int delete(String requestId);
	int deleteByInsert(String requestId);
}