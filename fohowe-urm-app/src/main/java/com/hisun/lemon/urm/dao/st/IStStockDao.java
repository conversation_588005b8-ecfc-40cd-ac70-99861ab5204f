/*
 * @ClassName IStStockDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-08 14:49:31
 */
package com.hisun.lemon.urm.dao.st;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.st.StockModifyReqDTO;
import com.hisun.lemon.urm.entity.st.GoodsPkgDO;
import com.hisun.lemon.urm.entity.st.StStockCountDO;
import com.hisun.lemon.urm.entity.st.StStockDO;

@Mapper
public interface IStStockDao extends BaseDao<StStockDO> {

    /** 
     * @Title: getPageList 
     * @Description: 库存分页查询
     * @param areaCode
     * @param companyList
     * @param goodsList
     * @param compareStockQty
     * @param stockQty
     * @param compareValidQty
     * @param validQty
     * @param kindId
     * @return
     * @return: List<T>
     */
    List<StStockDO> getPageList(@Param("areaCode") String areaCode, @Param("companyList") List<String> companyList,
            @Param("goodsList") List<String> goodsList, @Param("compareStockQty") String compareStockQty,
            @Param("stockQty") int stockQty, @Param("compareValidQty") String compareValidQty,
            @Param("validQty") int validQty, @Param("kindId") int kindId,@Param("isDel") Integer isDel,
            @Param("codeName") String codeName,@Param("excludeB1") boolean excludeB1);

    /** 
     * @Title: getByGood 
     * @Description: 根据分公司编号和商品编号查询库存（商品主表）
     * @param companyCode
     * @param goodsCode
     * @param vendorNo 
     * @param delFlag 
     * @return
     * @return: StStockDO
     */
    StStockDO getByGood(@Param("companyCode") String companyCode, @Param("goodsCode") String goodsCode,
            @Param("delFlag") String delFlag);
    
    /** 
     * @Title: getByGood 
     * @Description: 根据分公司编号和商品编号查询库存(商品分公司表)
     * @param companyCode
     * @param goodsCode
     * @param vendorNo 
     * @param delFlag 
     * @return
     * @return: StStockDO
     */
    StStockDO getByGoods(@Param("companyCode") String companyCode, @Param("goodsCode") String goodsCode,
            @Param("delFlag") String delFlag);
    
    /** 
     * @Title: getCompanyList 
     * @Description: 查询分公司列表
     * @param goodsCodeList
     * @param companyCode 
     * @return
     * @return: List<StStockCountDO>
     */
    List<StStockCountDO> getCompanyList(@Param("goodsCodeList") List<String> goodsCodeList,@Param("companyCode")  String companyCode);

    /** 
     * @Title: getStockCount 
     * @Description: 查询商品各分公司库存量
     * @param goodsCodeList
     * @param company 
     * @return
     * @return: List<StStockCountDO>
     */
    List<StStockCountDO> getStockCount(@Param("goodsCodeList") List<String> goodsCodeList,@Param("companyList")  List<String> companyList);

    /** 
     * @Title: getGood 
     * @param goodsCode
     * @param delFlag
     * @return
     * @return: StStockDO
     */
    StStockDO getGood(@Param("goodsCode") String goodsCode, @Param("delFlag") String delFlag);

    /** 
     * @Title: getByGoodVendor 
     * @Description: TODO
     * @param companyCode
     * @param goodsCode
     * @param vendorNo
     * @param delFlag
     * @return
     * @return: StStockDO
     */
    StStockDO getByGoodVendor(@Param("companyCode") String companyCode, @Param("goodsCode") String goodsCode,
            @Param("vendorNo") String vendorNo, @Param("delFlag") String delFlag);
    
    public int checkStockQty(StockModifyReqDTO reqDTO);
    public int checkGoodsPkg(StockModifyReqDTO reqDTO);
    
    public List<GoodsPkgDO> selectPkgList(@Param("goodsCode") String goodsCode);
    public String selectGoodsName(@Param("goodsCode") String goodsCode);
}