/*
 * @ClassName IPromotionDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-26 15:29:10
 */
package com.hisun.lemon.urm.dao.pd;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.pd.PromotionRspDTO;
import com.hisun.lemon.urm.entity.pd.PromotionDO;
import com.hisun.lemon.urm.entity.pd.PromotionIntDO;
import com.hisun.lemon.urm.entity.pd.PromotionQueryDO;

@Mapper
public interface IPromotionDao extends BaseDao<PromotionDO> {

    List<PromotionDO> promoPageQuery(PromotionQueryDO promoQueryDO);
    
    List<PromotionRspDTO> promoPageQuerys(PromotionQueryDO promoQueryDO);

    int insertBatch(List<PromotionIntDO> promoBeanList);

    int batchCheckPromo(@Param("idList") List<Integer> idList, @Param("checkTime") LocalDateTime checkTime
            , @Param("checkCode") String checkCode);

    int deletePromotion(@Param("id") Integer id, @Param("operCode") String operCode);

    List<PromotionDO> getPromoByIds(List<Integer> idList);

    int updateComboByIds(List<PromotionDO> promotionDOS);

    int doSplit(Integer id);
}