package com.hisun.lemon.urm.uitls.excel.fi;



import java.math.BigDecimal;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.urm.entity.fi.FiFgcReportDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcFgcSumExcelExporter extends URMExcelExportFactorys{
	/**
	 * fgc汇总导出
	 * @param fileName
	 * @param colNames
	 * @param obj
	 * @param response
	 */
	public void export(String fileName,Object obj, HttpServletResponse response) { 
		String[] colNames = { "期次", "分公司", "代办处", "经销商", "期初汇率", "期初（个）", "本期购入（个）", "购入价", "购入总额", "本期出售", "卖出价",
				"卖出总额", "可用数额（个）", "可用余额", "定存转入（个）", "定存转入金额", "定存转出（个）", "利息（个）", "定存转出金额", "转入十年定存（个）", "转入十年定存金额",
				 "十年定存取消（个）", "十年定存取消金额","实际可用数量（个）", "期末汇率", "实际可用余额" };
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		List<FiFgcReportDO> dataList=(List<FiFgcReportDO>)obj;
		for(FiFgcReportDO fgc:dataList) {
			Row row = sheet.createRow(beginRow++);
			//期次 按范围选择
			String startWeek=fgc.getStartWeek();
			String endWeek=fgc.getEndWeek();
			//分公司
			String companyCode=fgc.getCompanyCode();
			//代办处
			String agentNo=fgc.getAgentNo();
			//经销商
			String memberNo=fgc.getMemberNo();
			//期初汇率 所选期次startWeek第一天汇率
			BigDecimal initialRate=fgc.getInitialRate();
			//期初（个）所选期次之前的FGC余额
			BigDecimal initialFgcNum=fgc.getInitialFgcNum();
			//本期购入（个）所选期购买的FGC数量
			BigDecimal buyFgcNum=fgc.getBuyFgcNum();
			//购入价 平均值=汇总购买总金额/(本期购入（个）)
			BigDecimal buyFgcPrice=fgc.getBuyFgcPrice();
			//购入总额 所选期购买的总额
			BigDecimal buyFgcAmount=fgc.getBuyFgcAmount();
			//本期出售  所选期出售的FGC数量
			BigDecimal sellFgcNum=fgc.getSellFgcNum();
			//卖出价 平均值=汇总购买总金额/(本期出售)
			BigDecimal sellFgcPrice=fgc.getSellFgcPrice();
			//卖出总额  所选期出售的总额
			BigDecimal sellFgcAmount=fgc.getSellFgcAmount();
			//可用数额（个） 期初（个）+本期购入（个）-出售	
			BigDecimal avaFgcNum=fgc.getAvaFgcNum();
			//可用余额  可用数额（个）*卖出价
			BigDecimal avaFgcAmount=fgc.getAvaFgcAmount();
			//定存转入（个） 所选期定存中的数量 
			BigDecimal depInFgcNum=fgc.getDepInFgcNum();
			//定存转入金额  汇总所选期定存中的总金额
			BigDecimal depInFgcAmount=fgc.getDepInFgcAmount();
			//定存转出（个）本期定存到期总FGC个数+定存取消个数
			BigDecimal depOutFgcNum=fgc.getDepOutFgcNum();
			//利息（个） 汇总所选期定存到期利息个数
			BigDecimal depOutFgcInterest=fgc.getDepOutFgcInterest();
			//定存转出金额  定存转出（个）*卖出价
			BigDecimal depOutFgcAmount=fgc.getDepOutFgcAmount();
			//转入十年定存（个）汇总所选期十年定存的数量
			BigDecimal depTenFgcNum=fgc.getDepTenFgcNum();
			//转入十年定存金额  汇总所选期十年定存中的总金额
			BigDecimal depTenFgcAmount=fgc.getDepTenFgcAmount();
			//转入十年定存（个）汇总所选期十年定存的数量
			BigDecimal depTenCancelFgcNum=fgc.getDepTenCancelFgcNum();
			//转入十年定存金额  汇总所选期十年定存中的总金额
			BigDecimal depTenCancelFgcAmount=fgc.getDepTenCancelFgcAmount();
			//实际可用数量（个）可用数额（个）-定存转入（个）+定存转出（个）
			BigDecimal actFgcNum=fgc.getActFgcNum();
			//期末汇率 所选期次最后一天汇率
			BigDecimal closingRate=fgc.getClosingRate();
			//实际可用余额   实际可用数量（个）*卖出价
			BigDecimal actFgcAmount=fgc.getActFgcAmount();
		    int index=0;
			row.createCell(index++).setCellValue(startWeek+"-"+endWeek);
			row.createCell(index++).setCellValue(companyCode);
			row.createCell(index++).setCellValue(agentNo);
			row.createCell(index++).setCellValue(memberNo);
			row.createCell(index++).setCellValue(initialRate.doubleValue());
			row.createCell(index++).setCellValue(initialFgcNum.doubleValue());
			row.createCell(index++).setCellValue(buyFgcNum.doubleValue());
			row.createCell(index++).setCellValue(buyFgcPrice.doubleValue());
			row.createCell(index++).setCellValue(buyFgcAmount.doubleValue());
			row.createCell(index++).setCellValue(sellFgcNum.doubleValue());
			row.createCell(index++).setCellValue(sellFgcPrice.doubleValue());
			row.createCell(index++).setCellValue(sellFgcAmount.doubleValue());
			row.createCell(index++).setCellValue(avaFgcNum.doubleValue());
			row.createCell(index++).setCellValue(avaFgcAmount.doubleValue());
			row.createCell(index++).setCellValue(depInFgcNum.doubleValue());
			row.createCell(index++).setCellValue(depInFgcAmount.doubleValue());
			row.createCell(index++).setCellValue(depOutFgcNum.doubleValue());
			row.createCell(index++).setCellValue(depOutFgcInterest.doubleValue());
			row.createCell(index++).setCellValue(depOutFgcAmount.doubleValue());
			row.createCell(index++).setCellValue(depTenFgcNum.doubleValue());
			row.createCell(index++).setCellValue(depTenFgcAmount.doubleValue());
			row.createCell(index++).setCellValue(depTenCancelFgcNum.doubleValue());
			row.createCell(index++).setCellValue(depTenCancelFgcAmount.doubleValue());
			row.createCell(index++).setCellValue(actFgcNum.doubleValue());
			row.createCell(index++).setCellValue(closingRate.doubleValue());
			row.createCell(index++).setCellValue(actFgcAmount.doubleValue());
		}
		
	}
	public static AcFgcSumExcelExporter builder() {
		return new AcFgcSumExcelExporter();
	}
}
