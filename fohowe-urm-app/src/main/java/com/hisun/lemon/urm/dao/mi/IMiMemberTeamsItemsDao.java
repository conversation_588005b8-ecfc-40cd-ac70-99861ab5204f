package com.hisun.lemon.urm.dao.mi;

import java.util.List;

import com.hisun.lemon.urm.entity.mi.MemberOfOrderAmount;
import com.hisun.lemon.urm.entity.mi.MiMemberTeams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.mi.MiMemberTeamsItems;

@Mapper
public interface IMiMemberTeamsItemsDao extends BaseDao<MiMemberTeamsItems> {
    int deleteByPrimaryKey(Integer id);
    
    int deleteByTeamNo(String teamNo);

    int insertSelective(MiMemberTeamsItems row);

    MiMemberTeamsItems selectByPrimaryKey(Integer id);
    
    MiMemberTeamsItems selectByMemberNo(@Param("memberNo")String memberNo,@Param("teamType")Integer teamType);
    
    List<MiMemberTeamsItems> getItemsByType(@Param("teamType")Integer teamType);
    List<MiMemberTeamsItems> getItemsByTeamNo(@Param("teamNo")String teamNo);
    List<MiMemberTeamsItems> selectByTeamNumByFifteenTeamNo();
    void updateBySLFMember();
    List<MemberOfOrderAmount> selectBySLFMember();
    List<MemberOfOrderAmount> selectByFDMember();
    void batchUpdatePF(List<MemberOfOrderAmount> list);
    void batchDeleteTeamItem(@Param("list") List list);

    int updateByPrimaryKeySelective(MiMemberTeamsItems row);
    int batchUpdateTeam(@Param("list") List<MiMemberTeams> list);

}