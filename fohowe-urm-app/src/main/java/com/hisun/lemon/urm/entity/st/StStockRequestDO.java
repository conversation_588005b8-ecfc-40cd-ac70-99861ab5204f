/*
 * @ClassName FiAcBalanceRequestDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-13 21:08:15
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class StStockRequestDO extends BaseDO {
    /**
     * @Fields requestId 请求ID
     */
    private String requestId;
    /**
     * @Fields result 请求结果
     */
    private String result;
    /**
     * @Fields createDate 处理时间
     */
    private LocalDateTime createDate;
    /**
     * 扣减结果
     */
    private String changeResult;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 动作类型
     */
    private String actionType;
    
    private String companyCode;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

	public String getChangeResult() {
		return changeResult;
	}

	public void setChangeResult(String changeResult) {
		this.changeResult = changeResult;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	
}