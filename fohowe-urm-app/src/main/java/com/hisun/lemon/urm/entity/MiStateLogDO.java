/*
 * @ClassName MiStateLogDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class MiStateLogDO extends BaseDO {
    /**
     * @Fields logId 
     */
    private Long logId;
    /**
     * @Fields companyCode 所属公司 COMPANY_CODE
     */
    private String companyCode;
    /**
     * @Fields memberNo 会员编号 MEMBER_NO
     */
    private String memberNo;
    /**
     * @Fields statusType 01:限制状态 02:冻结状态 03:注销状态
     */
    private String statusType;
    /**
     * @Fields newStatus 变更后状态 NEW_STATUS
     */
    private String newStatus;
    /**
     * @Fields editorCode 变更人编号 EDITOR_CODE
     */
    private String editorCode;
    /**
     * @Fields editTime 变更时间 EDIT_TIME
     */
    private LocalDateTime editTime;
    /**
     * @Fields editReason 01:订单审核 02:状态变更申请单
     */
    private String editReason;
    /**
     * @Fields receiptCode 相关单据编号 RECEIPT_CODE
     */
    private String receiptCode;

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getStatusType() {
        return statusType;
    }

    public void setStatusType(String statusType) {
        this.statusType = statusType;
    }

    public String getNewStatus() {
        return newStatus;
    }

    public void setNewStatus(String newStatus) {
        this.newStatus = newStatus;
    }

    public String getEditorCode() {
        return editorCode;
    }

    public void setEditorCode(String editorCode) {
        this.editorCode = editorCode;
    }

    public LocalDateTime getEditTime() {
        return editTime;
    }

    public void setEditTime(LocalDateTime editTime) {
        this.editTime = editTime;
    }

    public String getEditReason() {
        return editReason;
    }

    public void setEditReason(String editReason) {
        this.editReason = editReason;
    }

    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }
}