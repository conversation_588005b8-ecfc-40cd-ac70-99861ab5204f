package com.hisun.lemon.urm.uitls.excel.mi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.hisun.lemon.urm.dto.mi.member.MemRightVO;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.fohowe.common.enums.PromQualEnums;
import com.hisun.lemon.fohowe.common.enums.RightNodeLevel;
import com.hisun.lemon.urm.dto.mi.member.MemRightBean;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class RightExcelExporter1 extends  URMExcelExportFactorys{
	private static final Logger logger = LoggerFactory.getLogger(RightExcelExporter1.class);
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="经营权资料";
		//String[] colNames=new String[] {"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","",""};
		String[] colNames=new String[] {
				"所属分公司","代办处编号","所属经销商","经销商姓名","经营权编号",
				"经营权级别","是否主经营权",//"接点权编号","左区编号","右区编号",
				"所处阶段","已完成循环","已支付FV","需要FV","注册时间","PLUS/VIP"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		MemRightVO vo=(MemRightVO) obj;
		List<MemRightBean> dataList=vo.getDataList();
		Map<Object, String> levelNameKV=EnumsUtils.EnumToMap(RightNodeLevel.class);
		Map<Object, String> promQualLevel = EnumsUtils.EnumToMap(PromQualEnums.class);
//		logger.info("promQualLevel的大小"+promQualLevel.size());
//		logger.info(promQualLevel.get("2")+promQualLevel.toString());
		for(MemRightBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(index++).setCellValue(o.getMemberName());
			row.createCell(index++).setCellValue(o.getRightNo()==null?"":o.getRightNo().trim());
			
			row.createCell(index++).setCellValue(levelNameKV.get(Integer.parseInt(o.getLevelType())));
//			System.out.println(levelNameKV.get(Integer.parseInt(o.getLevelType())));
			row.createCell(index++).setCellValue(o.getPrimRight().equals("1")==true?"是":"否");
//			row.createCell(index++).setCellValue(o.getLinkNo()==null?"":o.getLinkNo().trim());
//			row.createCell(index++).setCellValue(o.getLeftMem()); 
//			row.createCell(index++).setCellValue(o.getRightMem());
			row.createCell(index++).setCellValue(o.getStage());
			row.createCell(index++).setCellValue(o.getCicles());
			row.createCell(index++).setCellValue(o.getPv().toString());
			row.createCell(index++).setCellValue(o.getPvLimit().toString());
			
//			String promQual = promQualLevel.get(o.getPromQual()+"");
//			logger.info("促销值为"+o.getPromQual());
//			logger.info("促销资格为"+promQual);
//			logger.info(PromQualEnums.getEnumsByValue(o.getPromQual()+"").getName());
			row.createCell(index++).setCellValue(o.getCreateTime()==null?"":o.getCreateTime().format(ymdhms));
			row.createCell(index++).setCellValue(promQualLevel.get(o.getPromQual()+""));
		}
		
	}
	public static RightExcelExporter1 builder() {
		return new RightExcelExporter1();
	}
}
