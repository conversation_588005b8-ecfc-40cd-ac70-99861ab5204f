/*
 * @ClassName ISysListKeyDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:46
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysListKeyDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysList<PERSON>ey<PERSON><PERSON> extends BaseDao<SysListKeyDO> {

    /** 
     * @Title: pageFind 
     * @Description: 分页模糊查询
     * @param listCode
     * @param listName
     * @return
     * @return: List<T>
     */
    public List<SysListKeyDO> pageFind(@Param("listCode") String listCode, @Param("listName") String listName);

    /** 
     * @Title: get 
     * @Description: 查询list的key值
     * @param keyId
     * @return
     * @return: SysListKeyDO
     */
    public SysListKeyDO get(@Param("keyId") long keyId);

    /** 
     * @Title: delete 
     * @Description: 删除记录
     * @param keyId
     * @return
     * @return: int
     */
    public int delete(@Param("keyId") long keyId);

    /** 
     * @Title: getByCode 
     * @Description: 根据listCode查询记录
     * @param keycode
     * @return
     * @return: SysListKeyDO
     */
    public SysListKeyDO getByCode(@Param("listCode") String listCode);
}