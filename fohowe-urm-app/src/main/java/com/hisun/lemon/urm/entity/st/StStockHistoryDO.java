/*
 * @ClassName StStockDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-08 14:49:31
 */
package com.hisun.lemon.urm.entity.st;

import java.math.BigDecimal;

public class StStockHistoryDO {
	private String bonusType;
	private String companyCode;
	private String areaCode;
	private String companyCodeStr;
	private String areaCodeStr;
    private String goodsCode;
    private String goodsName;
    private int validQty;
    private int quantity;
    private BigDecimal price;
    private Integer virtualQty;
    
    public String getBonusType() {
		return bonusType;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	public String getCompanyCodeStr() {
		return companyCodeStr;
	}

	public void setCompanyCodeStr(String companyCodeStr) {
		this.companyCodeStr = companyCodeStr;
	}

	public String getAreaCodeStr() {
		return areaCodeStr;
	}

	public void setAreaCodeStr(String areaCodeStr) {
		this.areaCodeStr = areaCodeStr;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public int getValidQty() {
        return validQty;
    }

    public void setValidQty(int validQty) {
        this.validQty = validQty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

	public Integer getVirtualQty() {
		return virtualQty;
	}

	public void setVirtualQty(Integer virtualQty) {
		this.virtualQty = virtualQty;
	}

}