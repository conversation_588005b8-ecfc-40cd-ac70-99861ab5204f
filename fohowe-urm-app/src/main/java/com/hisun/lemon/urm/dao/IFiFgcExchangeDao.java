/*
 * @ClassName IFiFgcExchangeDao
 * @Description 
 * @version 1.0
 * @Date 2019-10-29 15:06:31
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.bo.FgcExchangeRspBO;
import com.hisun.lemon.urm.entity.FiFgcExchangeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IFiFgcExchangeDao extends BaseDao<FiFgcExchangeDO> {

    List<FiFgcExchangeDO> finds(FiFgcExchangeDO fiFgcExchangeDO);

    FgcExchangeRspBO findTotalExchange(FiFgcExchangeDO fiFgcExchangeDO);

}