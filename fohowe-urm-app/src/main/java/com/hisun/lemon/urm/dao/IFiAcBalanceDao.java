/*
 * @ClassName IFiAcBalanceDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceBean;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceDTO;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceQueryBean;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceQueryBean2;
import com.hisun.lemon.urm.dto.fi.balance.AcBalanceRateAdjust;
import com.hisun.lemon.urm.dto.fi.balance.AccountStateBean;
import com.hisun.lemon.urm.dto.fi.balance.AcountStateReqDTO;
import com.hisun.lemon.urm.dto.fi.balance.FIBaseInfo;
import com.hisun.lemon.urm.entity.FiAcBalanceDO;

@Mapper
public interface IFiAcBalanceDao extends BaseDao<FiAcBalanceDO> {

	List<AcBalanceQueryBean> getListByCondition(AcBalanceDTO acBalanceDTO);
	List<AcBalanceQueryBean2> getListByCondition2(AcBalanceDTO acBalanceDTO);

	AcBalanceBean getAcBalance(AcBalanceDTO acBalanceDTO);

	FiAcBalanceDO getAcBalanceAddLock(AcBalanceDTO acBalanceDTO);
    /**
     * 查询总页数
     * @param acBalanceDTO
     * @return
     */
	int getTotalCount(AcBalanceDTO acBalanceDTO);
	int getTotalCountOne(AcBalanceDTO acBalanceDTO);
	int getTotalCountTwo(AcBalanceDTO acBalanceDTO);
	int getTotalCountThree(AcBalanceDTO acBalanceDTO);
	int getTotalCountFour(AcBalanceDTO acBalanceDTO);
	int getTotalCountFive(AcBalanceDTO acBalanceDTO);
	int getTotalCountSix(AcBalanceDTO acBalanceDTO);
	int getTotalCountSeven(AcBalanceDTO acBalanceDTO);
	
	int getTotalCount2(AcBalanceDTO acBalanceDTO);
	/**
	 * 查询账户基本信息
	 * @param userCode
	 * @return
	 */
	FIBaseInfo searchBaseInfo(String userCode);
	/**
	 * 根据用户编号删除用户账户信息
	 * @param userCode
	 */
	void deleteByUserCode(String userCode);
    /**
     * 汇率调整查询需要变动金额的代办处
     * @param currencyCode
     * @return
     */
	List<AcBalanceBean> agentRateAdjust(String currencyCode);
	/**
	 * 汇率调整查询需要变动金额的经销商
	 * @param agentNo
	 * @return
	 */
	List<AcBalanceBean> memberRateAdjust(String agentNo);
	/**
	 * 通过所属代办处更新账户所属分公司
	 * @param agentNo
	 * @param companyCode
	 */
	void updateCompanyCodeByAgent(@Param("agentNo") String agentNo,@Param("companyCode") String companyCode);
	/**
	 * 根据经销商所属分公司更新账户所属分公司
	 * @param memberNo
	 * @param companyCode 
	 */
	void updateCompanyCodeByMember(@Param("memberNo") String memberNo,@Param("companyCode") String companyCode);
    /**
     * 代办处下属经销商账户所属分公司更新
     * @param toAgentNo
     * @param companyCode
     * @param memberNo 
     */
	void updateCompanyCodeByAgentLink(@Param("memberNo") String memberNo,@Param("toAgentNo") String toAgentNo,@Param("companyCode") String companyCode);
    
	List<AccountStateBean> pageQueryRecordByHead(AcountStateReqDTO acountStateReqDTO);

	List<AccountStateBean> pageQueryRecordByCompany(AcountStateReqDTO acountStateReqDTO);

	List<AccountStateBean> pageQueryRecordByArea(AcountStateReqDTO acountStateReqDTO);

	List<AccountStateBean> pageQueryRecordByAgent(AcountStateReqDTO acountStateReqDTO);

	List<AccountStateBean> pageQueryRecordByMember(AcountStateReqDTO acountStateReqDTO);

	/**
	 * 根据货币编号查询需要汇率调整的账户
	 * @param currencyCode
	 * @return
	 */
	List<AcBalanceRateAdjust> agentAndMemberRateAdjust(String currencyCode);
	/**
	 * 统计各账户总和
	 * @param acBalanceDTO
	 * @return
	 */
	AcBalanceQueryBean2 sumBalance(AcBalanceDTO acBalanceDTO);
	
	Double queryClobalShare(String memNo);
	
	Double queryAmbassadorShare(String memNo);
	
	List<FiAcBalanceDO> getBalanceByUserCode(@Param("userCode") String userCode);
	FiAcBalanceDO getBalanceByShare1(@Param("userCode") String userCode);
	FiAcBalanceDO getBalanceByShare2(@Param("userCode") String userCode);

	List<AcBalanceQueryBean> getSumByCondition(AcBalanceDTO acBalanceDTO);
}