/*
 * @ClassName IFiAcChangeDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.change.AcChangeDTO;
import com.hisun.lemon.urm.dto.fi.change.AcChangeQueryBean;
import com.hisun.lemon.urm.entity.FiAcChangeDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IFiAcChangeDao extends BaseDao<FiAcChangeDO> {

	List<AcChangeQueryBean> getListByCondition(AcChangeDTO acChangeDTO);
    /**
     * 查询总页数
     * @param acChangeDTO
     * @return
     */
	int getTotalCount(AcChangeDTO acChangeDTO);
}