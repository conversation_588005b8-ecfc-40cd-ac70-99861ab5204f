/*
 * @ClassName IPoInvoiceDao
 * @Description 
 * @version 1.0
 * @Date 2018-02-03 11:29:21
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PoInvoiceDO;
import com.hisun.lemon.urm.service.pd.bo.InvoiceItemQueryBO;

import feign.Param;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IPoInvoiceDao extends BaseDao<PoInvoiceDO> {

    /** 
     * @Title: getByOrderNo 
     * @param orderNo
     * @return
     * @return: PoInvoiceDO
     */
    PoInvoiceDO getByOrderNo(@Param("orderNo") String orderNo);
    
    List<InvoiceItemQueryBO> getInvoiceItem(@Param("orderNo") String orderNo);
}