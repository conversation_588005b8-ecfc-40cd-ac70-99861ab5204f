package com.hisun.lemon.urm.uitls.excel.mi;



import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.fohowe.common.enums.CardTypes;
import com.hisun.lemon.fohowe.common.enums.HealthyTypeEnums;
import com.hisun.lemon.fohowe.common.enums.KnowWayTypeEnums;
import com.hisun.lemon.fohowe.common.enums.PaperTypeEnums;
import com.hisun.lemon.fohowe.common.enums.RightDisplayWayEnums;
import com.hisun.lemon.fohowe.common.enums.Sex;
import com.hisun.lemon.urm.dto.mi.member.MemberQueryBean;
import com.hisun.lemon.urm.dto.mi.member.MemberVO;
import com.hisun.lemon.urm.enums.mi.MemStatusEnums;
import com.hisun.lemon.urm.enums.mi.MemTypeEnums;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class MemberExcelExporter1 extends  URMExcelExportFactorys{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="经销商基本信息";
		String[] colNames=new String[] {
				"经销商编号","所属分公司","代办处编号","经销商姓名","经销商级别",
				"注册时间","加入时间","加入期数","用户状态","活跃期数","昵称","性别","生日","证件类型","证件号","详细地址",
				"邮政编码","开户银行","户名","账号","办公电话","移动电话","传真","健康状态","知晓方式","种类","注册时布局",
				//"推荐人代办处","推荐人分公司",
				"经营权数量","促销资格","邮箱"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {
		MemberVO vo=(MemberVO) obj;
		List<MemberQueryBean> dataList=vo.getDataList();
		Map<Object, String> memStatusKV=EnumsUtils.EnumToMap(MemStatusEnums.class);
		Map<Object, String> sexKV=EnumsUtils.EnumToMap(Sex.class);
		Map<Object, String> paperTypeKV = EnumsUtils.EnumToMap(PaperTypeEnums.class);
		Map<Object, String> healthyKV = EnumsUtils.EnumToMap(HealthyTypeEnums.class);
		Map<Object, String> knowWayKV = EnumsUtils.EnumToMap(KnowWayTypeEnums.class);
		Map<Object, String> disTypeKV = EnumsUtils.EnumToMap(RightDisplayWayEnums.class);
		Map<Object, String> memTypeKV = EnumsUtils.EnumToMap(MemTypeEnums.class);
		Map<Object, String> carTypeKV = EnumsUtils.EnumToMap(CardTypes.class);
		for(MemberQueryBean o:dataList) {
			Row row = sheet.createRow(beginRow++);
			int index=0;
			row.createCell(index++).setCellValue(o.getMemberNo()==null?"":o.getMemberNo().trim());
			row.createCell(index++).setCellValue(o.getCompanyCode());
			row.createCell(index++).setCellValue(o.getAgentNo());
			row.createCell(index++).setCellValue(o.getMemberName());
			row.createCell(index++).setCellValue(carTypeKV.get(o.getCardType()));
			row.createCell(index++).setCellValue(o.getRegisterDate()==null?"":o.getRegisterDate().format(ymdhms));
			row.createCell(index++).setCellValue(o.getActiveTime()==null?"":o.getActiveTime().format(ymdhms));
			row.createCell(index++).setCellValue(o.getPeriodWeek());
			row.createCell(index++).setCellValue(memStatusKV.get(o.getStatus()));
			row.createCell(index++).setCellValue(o.getActiveWeek());
			row.createCell(index++).setCellValue(o.getPetName());
			row.createCell(index++).setCellValue(sexKV.get(o.getSex()));
//			SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");sdf.format(o.getBirthday())
			row.createCell(index++).setCellValue(o.getBirthday()==null?"":o.getBirthday().format(yms));
			row.createCell(index++).setCellValue(paperTypeKV.get(o.getPaperType()));
			row.createCell(index++).setCellValue(o.getPaperNo());
			row.createCell(index++).setCellValue(o.getStoreAddr());
			row.createCell(index++).setCellValue(o.getStorePost());
			row.createCell(index++).setCellValue(o.getAccountBank());
			row.createCell(index++).setCellValue(o.getAccountName());
			row.createCell(index++).setCellValue(o.getAccountCode());
			row.createCell(index++).setCellValue(o.getOfficeTel());
			row.createCell(index++).setCellValue(o.getMobile());
			row.createCell(index++).setCellValue(o.getFax());
			row.createCell(index++).setCellValue(healthyKV.get(o.getHealthy()));
			row.createCell(index++).setCellValue(knowWayKV.get(o.getKnowWay()));
			row.createCell(index++).setCellValue(memTypeKV.get(o.getMemType()));
			row.createCell(index++).setCellValue(disTypeKV.get(o.getDisType()));
//			row.createCell(index++).setCellValue(o.getRecAgentNo());
//			row.createCell(index++).setCellValue(o.getRecCompanyCode());
			row.createCell(index++).setCellValue(o.getRightNumber());
			row.createCell(index++).setCellValue(o.getPromQual()==1?"VIP":"无");
			row.createCell(index++).setCellValue(o.getEmail());
		}
		
	}
	public static MemberExcelExporter1 builder() {
		return new MemberExcelExporter1();
	}
}
