/*
 * @ClassName IFiAcExchangeDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.turnover.AcTurnOverBean;
import com.hisun.lemon.urm.dto.fi.turnover.AcTurnOverDTO;
import com.hisun.lemon.urm.entity.FiAcTurnOverDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IFiAcTurnOverDao extends BaseDao<FiAcTurnOverDO> {

	int getTotalCount(AcTurnOverDTO acTurnOverDTO);

	List<AcTurnOverBean> getListByCondition(AcTurnOverDTO acTurnOverDTO);

}