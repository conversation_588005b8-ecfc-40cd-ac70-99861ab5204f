package com.hisun.lemon.urm.entity.sys;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hisun.lemon.framework.data.BaseDO;

public class SysAttachmentDO extends BaseDO {
	 /**ID*/
    private Long id;
    /**分公司编号*/
    private String companyCode;
    /**代办处编号 / 供应商ID*/
    private String agentNo;
    /**文件名*/
    private String fileName;
    /**保存路径*/
    private String fileUrl;
    /**文件大小*/
    private Integer fileSize;
    /**文件描述*/
    private String fileDesc;
    /**后缀名*/
    private String suffixName;
    /**原文件名*/
    private String originalName;
    /**文件类型 0-文件 1-图片*/
    private Integer fileType;
    /**上传人编号*/
    private String creatorCode;
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
	/**上传时间*/
    private LocalDateTime createTime;
    /**下载次数*/
    private Integer downlaodTimes;
    /**已删除，1是，0否*/
    private Integer isTrash;
    /**单据编号*/
    private Long orderId;
    /**附件类型
     * 单据类型:0 公司文件，1 代办处文件，2 库存相关，3 帐户相关，4 订单附件，5基金附件，6 旅游附件,501 教育
     * */
    private Integer orderType;
    
    private String imgUrl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Integer getFileSize() {
        return fileSize;
    }

    public void setFileSize(Integer fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileDesc() {
        return fileDesc;
    }

    public void setFileDesc(String fileDesc) {
        this.fileDesc = fileDesc;
    }

    public String getSuffixName() {
        return suffixName;
    }

    public void setSuffixName(String suffixName) {
        this.suffixName = suffixName;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getDownlaodTimes() {
        return downlaodTimes;
    }

    public void setDownlaodTimes(Integer downlaodTimes) {
        this.downlaodTimes = downlaodTimes;
    }

    public Integer getIsTrash() {
        return isTrash;
    }

    public void setIsTrash(Integer isTrash) {
        this.isTrash = isTrash;
    }

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
	
}