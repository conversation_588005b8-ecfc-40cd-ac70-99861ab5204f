/*
 * @ClassName SysListValueDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-13 10:32:46
 */
package com.hisun.lemon.urm.entity.sys;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class SysListValueDO extends BaseDO {
    /**
     * @Fields valueId 值ID VALUE_ID
     */
    private long valueId;
    /**
     * @Fields keyId 列表ID KEY_ID
     */
    private long keyId;
    /**
     * @Fields valueCode 值编码 VALUE_CODE
     */
    private String valueCode;
    /**
     * @Fields valueTitle 存储格式为字符编码键值
     */
    private String valueTitle;
    /**
     * @Fields exCompanyCode 保存不适合此值的公司编码,多个公司编码以逗号分开,如CN,US,VN
     */
    private String exCompanyCode;
    /**
     * @Fields orderNo 顺序号 ORDER_NO
     */
    private Integer orderNo;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields remark 
     */
    private String remark;
    

    public long getValueId() {
        return valueId;
    }

    public void setValueId(long valueId) {
        this.valueId = valueId;
    }

    public long getKeyId() {
        return keyId;
    }

    public void setKeyId(long keyId) {
        this.keyId = keyId;
    }

    public String getValueCode() {
        return valueCode;
    }

    public void setValueCode(String valueCode) {
        this.valueCode = valueCode;
    }

    public String getValueTitle() {
        return valueTitle;
    }

    public void setValueTitle(String valueTitle) {
        this.valueTitle = valueTitle;
    }

    public String getExCompanyCode() {
        return exCompanyCode;
    }

    public void setExCompanyCode(String exCompanyCode) {
        this.exCompanyCode = exCompanyCode;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
    
}