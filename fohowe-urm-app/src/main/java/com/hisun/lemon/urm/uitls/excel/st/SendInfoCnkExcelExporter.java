package com.hisun.lemon.urm.uitls.excel.st;

import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.enums.PayMethod;
import com.hisun.lemon.urm.common.MsgCdLC;
import com.hisun.lemon.urm.service.pd.bo.SendInfoCntQueryBO;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils;
import com.hisun.lemon.urm.uitls.excel.ExcelHelperUtils.ExcelType;

public class SendInfoCnkExcelExporter {
    public DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public void export(List<SendInfoCntQueryBO> dataList, HttpServletResponse response) {
        String fileName = "分公司发货跨境发货单";
        try {
            ExcelHelperUtils.setResponseHead(fileName, response, ExcelType.XLSX);
            addCell(response.getOutputStream(), dataList);
        } catch (IOException e) {
            e.printStackTrace();
            LemonException.throwBusinessException(MsgCdLC.EXPORT_ERROR.getMsgCd());
        }
    }

    public void addCell(OutputStream oss, List<SendInfoCntQueryBO> dataList) throws IOException {
        XSSFWorkbook workbook = null;
        workbook = new XSSFWorkbook();

        int rowNums = 0;
        String sheetName = "分公司跨境发货单";
        XSSFSheet sheet = workbook.createSheet(sheetName);
        XSSFCellStyle columnCenterStyle = this.getColumnCenterStyle(workbook);// 获取中间对齐样式对象

        String[] rowName = new String[] { "Payer Company", "Payer Code", "Warehouse Name", "Reference No.",
                "Recipient Name", "Recipient Address Line 1", "Recipient Address Line 2",
                "Recipient Doorplate Recipient City", "Recipient State Recipient Country Code", "Recipient Zip Code",
                "Recipient Tel", "SKU", "中文名称", "创建日期", "支付日期", "Quantity", "Shipping Method", "Insured Value (USD)",
                "Declared Value (USD)", "Description", "Recipient Company Name", "Recipient Email Returnable",
                "Recipient's Tax ID", "Remote Area Surcharge", "Shipping Worth (USD)", "eBay Artikelnummer",
                "Transaction ID", "Qualified Product", "eBay buyer's ID", "Related eBay account", "Payment Method" };
        int columnNum = rowName.length;
        XSSFRow rowRowName = sheet.createRow(rowNums);

        for (int n = 0; n < columnNum; n++) {
            XSSFCell cellRowName = rowRowName.createCell(n);
            cellRowName.setCellType(CellType.STRING);
            XSSFRichTextString text = new XSSFRichTextString(rowName[n]);
            cellRowName.setCellValue(text);
            cellRowName.setCellStyle(columnCenterStyle);
            sheet.setColumnWidth(n, rowName[n].getBytes().length * 2 * 256);
        }

        for (SendInfoCntQueryBO o : dataList) {
            rowNums++;// 行数增1
            XSSFRow row = sheet.createRow(rowNums);

            row.createCell(0).setCellValue(JudgeUtils.isNull(o.getOrderCompanyCode()) ? "" : o.getOrderCompanyCode());
            row.createCell(1).setCellValue(JudgeUtils.isNull(o.getPayerCode()) ? "" : o.getPayerCode());
            row.createCell(2).setCellValue(JudgeUtils.isNull(o.getCompanyCode()) ? "" : o.getCompanyCode());
            row.createCell(3).setCellValue(JudgeUtils.isNull(o.getBiOrderNo()) ? "" : o.getBiOrderNo());
            row.createCell(4).setCellValue(JudgeUtils.isNull(o.getRecName()) ? "" : o.getRecName());
            row.createCell(5).setCellValue(JudgeUtils.isNull(o.getRecAddr()) ? "" : o.getRecAddr());
            row.createCell(6).setCellValue("");
            row.createCell(7).setCellValue("");
            row.createCell(8).setCellValue("");
            row.createCell(9).setCellValue("");
            row.createCell(10).setCellValue(JudgeUtils.isNull(o.getRecPhone()) ? "" : o.getRecPhone());
            row.createCell(11).setCellValue(JudgeUtils.isNull(o.getGoodsCode()) ? "" : o.getGoodsCode());
            row.createCell(12).setCellValue(JudgeUtils.isNull(o.getGoodsName()) ? "" : o.getGoodsName());
            row.createCell(13).setCellValue(JudgeUtils.isNull(o.getOrderDate()) ? "" : o.getOrderDate().format(ymdhms));
            row.createCell(14).setCellValue(JudgeUtils.isNull(o.getPayDate()) ? "" : o.getPayDate().format(ymdhms));
            row.createCell(15).setCellValue(JudgeUtils.isNull(o.getQuantity()) ? "" : o.getQuantity() + "");
            row.createCell(16).setCellValue("");
            row.createCell(17).setCellValue("");
            row.createCell(18).setCellValue("");
            row.createCell(19).setCellValue("");
            row.createCell(20).setCellValue("");
            row.createCell(21).setCellValue("");
            row.createCell(22).setCellValue("");
            row.createCell(23).setCellValue("");
            row.createCell(24).setCellValue("");
            row.createCell(25).setCellValue("");
            row.createCell(26).setCellValue("");
            row.createCell(27).setCellValue("");
            row.createCell(28).setCellValue("");
            row.createCell(29).setCellValue("");
            row.createCell(30).setCellValue(JudgeUtils.isNull(o.getPayMethod()) ? ""
                    : PayMethod.getByCode(Integer.valueOf(o.getPayMethod())).getName());
        }
        workbook.write(oss);
    }

    public XSSFCellStyle getColumnCenterStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    public XSSFCellStyle getColumnRedStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setColor(XSSFFont.COLOR_RED);
        XSSFCellStyle style = workbook.createCellStyle();
        // 设置底边框;
        style.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色;
        style.setBottomBorderColor(new XSSFColor(Color.BLACK));
        // 设置左边框;
        style.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色;
        style.setLeftBorderColor(new XSSFColor(Color.BLACK));
        // 设置右边框;
        style.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色;
        style.setRightBorderColor(new XSSFColor(Color.BLACK));
        // 设置顶边框;
        style.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色;
        style.setTopBorderColor(new XSSFColor(Color.BLACK));
        style.setFont(font);
        style.setWrapText(false);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }
}
