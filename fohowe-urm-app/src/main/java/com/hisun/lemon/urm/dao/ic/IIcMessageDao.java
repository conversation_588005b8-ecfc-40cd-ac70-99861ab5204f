/*
 * @ClassName IIcMessageDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 13:00:36
 */
package com.hisun.lemon.urm.dao.ic;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.ic.IcMessageDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IIcMessageDao extends BaseDao<IcMessageDO> {

    public List<IcMessageDO> findMessageList(@Param("firstName") String firstName, @Param("lastName") String lastName,
            @Param("phoneNumber") String phoneNumber, @Param("status") String status);
}