/*
 * @ClassName StStockStraceDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-08 14:49:31
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDateTime;

public class StStockStraceDO extends BaseDO {
    /**
     * @Fields straceId 主键id
     */
    private Long straceId;
    /**
     * @Fields companyCode 分公司/仓库编号
     */
    private String companyCode;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    /**
     * @Fields quantityS 原库存
     */
    private Integer quantityS;
    /**
     * @Fields quantityC 变化量
     */
    private Integer quantityC;
    /**
     * @Fields quantityR 新库存
     */
    private Integer quantityR;
    /**
     * @Fields onWayS 原订货在途
     */
    private Integer onWayS;
    /**
     * @Fields onWayC 变化量
     */
    private Integer onWayC;
    /**
     * @Fields onWayR 新订货在途
     */
    private Integer onWayR;
    /**
     * @Fields donWayS 原调拨在途
     */
    private Integer donWayS;
    /**
     * @Fields donWayC 变化量 
     */
    private Integer donWayC;
    /**
     * @Fields donWayR 新调拨在途
     */
    private Integer donWayR;
    /**
     * @Fields validQtyS 原可用
     */
    private Integer validQtyS;
    /**
     * @Fields validQtyC 变化量 
     */
    private Integer validQtyC;
    /**
     * @Fields validQtyR 新可用
     */
    private Integer validQtyR;
    /**
     * @Fields actionSource 动作来源
     */
    private String actionSource;
    /**
     * @Fields actionType 动作类型
     */
    private String actionType;
    /**
     * @Fields action 动作说明
     */
    private String action;
    /**
     * @Fields orderType 订单类型
     */
    private String orderType;
    /**
     * @Fields orderNo 订单号
     */
    private String orderNo;
    /**
     * @Fields usrNo 操作人
     */
    private String usrNo;
    /**
     * @Fields requestQtyS 采购申请量
     */
    private Integer requestQtyS;
    /**
     * @Fields requestQtyC 变化量
     */
    private Integer requestQtyC;
    /**
     * @Fields requestQtyR 新采购申请量
     */
    private Integer requestQtyR;
    /**
     * @Fields outWayQtyS 原出货在途
     */
    private Integer outWayQtyS;
    /**
     * @Fields outWayQtyC 变化量
     */
    private Integer outWayQtyC;
    /**
     * @Fields outWayQtyR 新出货在途
     */
    private Integer outWayQtyR;
    /**
     * @Fields alterQtyS 原报损库存
     */
    private Integer alterQtyS;
    /**
     * @Fields alterQtyC 变化量
     */
    private Integer alterQtyC;
    /**
     * @Fields alterQtyR 新报损库存
     */
    private Integer alterQtyR;
    /**
     * @Fields saleWayQtyS 原销售在途
     */
    private Integer saleWayQtyS;
    /**
     * @Fields saleWayQtyC 变化量
     */
    private Integer saleWayQtyC;
    /**
     * @Fields saleWayQtyR 新销售在途
     */
    private Integer saleWayQtyR;
    /**
     * @Fields waitQtyS 原待发货
     */
    private Integer waitQtyS;
    /**
     * @Fields waitQtyC 变化量
     */
    private Integer waitQtyC;
    /**
     * @Fields waitQtyR 新待发货
     */
    private Integer waitQtyR;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * @Fields waitQtyS 原待发货
     */
    private Integer saleWaitS;
    /**
     * @Fields waitQtyC 变化量
     */
    private Integer saleWaitC;
    /**
     * @Fields waitQtyR 新待发货
     */
    private Integer saleWaitR;
    
    private String generateMethod;
    private String isGive;
    
    /**
     * @Fields tmSmp 
     */
    private String agentNo;
    /**
     * @Fields 价格 
     */
    private String  goodsPrice;
    private String memberName;
    private String memberNo;
    
    private String isPkg;

    private String codeName;

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getGoodsPrice() {
		return goodsPrice;
	}

	public void setGoodsPrice(String goodsPrice) {
		this.goodsPrice = goodsPrice;
	}

	public String getAgentNo() {
		return agentNo;
	}

	public void setAgentNo(String agentNo) {
		this.agentNo = agentNo;
	}

	public Long getStraceId() {
        return straceId;
    }

    public void setStraceId(Long straceId) {
        this.straceId = straceId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public Integer getQuantityS() {
        return quantityS;
    }

    public void setQuantityS(Integer quantityS) {
        this.quantityS = quantityS;
    }

    public Integer getQuantityC() {
        return quantityC;
    }

    public void setQuantityC(Integer quantityC) {
        this.quantityC = quantityC;
    }

    public Integer getQuantityR() {
        return quantityR;
    }

    public void setQuantityR(Integer quantityR) {
        this.quantityR = quantityR;
    }

    public Integer getOnWayS() {
        return onWayS;
    }

    public void setOnWayS(Integer onWayS) {
        this.onWayS = onWayS;
    }

    public Integer getOnWayC() {
        return onWayC;
    }

    public void setOnWayC(Integer onWayC) {
        this.onWayC = onWayC;
    }

    public Integer getOnWayR() {
        return onWayR;
    }

    public void setOnWayR(Integer onWayR) {
        this.onWayR = onWayR;
    }

    public Integer getDonWayS() {
        return donWayS;
    }

    public void setDonWayS(Integer donWayS) {
        this.donWayS = donWayS;
    }

    public Integer getDonWayC() {
        return donWayC;
    }

    public void setDonWayC(Integer donWayC) {
        this.donWayC = donWayC;
    }

    public Integer getDonWayR() {
        return donWayR;
    }

    public void setDonWayR(Integer donWayR) {
        this.donWayR = donWayR;
    }

    public Integer getValidQtyS() {
        return validQtyS;
    }

    public void setValidQtyS(Integer validQtyS) {
        this.validQtyS = validQtyS;
    }

    public Integer getValidQtyC() {
        return validQtyC;
    }

    public void setValidQtyC(Integer validQtyC) {
        this.validQtyC = validQtyC;
    }

    public Integer getValidQtyR() {
        return validQtyR;
    }

    public void setValidQtyR(Integer validQtyR) {
        this.validQtyR = validQtyR;
    }

    public String getActionSource() {
        return actionSource;
    }

    public void setActionSource(String actionSource) {
        this.actionSource = actionSource;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getUsrNo() {
        return usrNo;
    }

    public void setUsrNo(String usrNo) {
        this.usrNo = usrNo;
    }

    public Integer getRequestQtyS() {
        return requestQtyS;
    }

    public void setRequestQtyS(Integer requestQtyS) {
        this.requestQtyS = requestQtyS;
    }

    public Integer getRequestQtyC() {
        return requestQtyC;
    }

    public void setRequestQtyC(Integer requestQtyC) {
        this.requestQtyC = requestQtyC;
    }

    public Integer getRequestQtyR() {
        return requestQtyR;
    }

    public void setRequestQtyR(Integer requestQtyR) {
        this.requestQtyR = requestQtyR;
    }

    public Integer getOutWayQtyS() {
        return outWayQtyS;
    }

    public void setOutWayQtyS(Integer outWayQtyS) {
        this.outWayQtyS = outWayQtyS;
    }

    public Integer getOutWayQtyC() {
        return outWayQtyC;
    }

    public void setOutWayQtyC(Integer outWayQtyC) {
        this.outWayQtyC = outWayQtyC;
    }

    public Integer getOutWayQtyR() {
        return outWayQtyR;
    }

    public void setOutWayQtyR(Integer outWayQtyR) {
        this.outWayQtyR = outWayQtyR;
    }

    public Integer getAlterQtyS() {
        return alterQtyS;
    }

    public void setAlterQtyS(Integer alterQtyS) {
        this.alterQtyS = alterQtyS;
    }

    public Integer getAlterQtyC() {
        return alterQtyC;
    }

    public void setAlterQtyC(Integer alterQtyC) {
        this.alterQtyC = alterQtyC;
    }

    public Integer getAlterQtyR() {
        return alterQtyR;
    }

    public void setAlterQtyR(Integer alterQtyR) {
        this.alterQtyR = alterQtyR;
    }

    public Integer getSaleWayQtyS() {
        return saleWayQtyS;
    }

    public void setSaleWayQtyS(Integer saleWayQtyS) {
        this.saleWayQtyS = saleWayQtyS;
    }

    public Integer getSaleWayQtyC() {
        return saleWayQtyC;
    }

    public void setSaleWayQtyC(Integer saleWayQtyC) {
        this.saleWayQtyC = saleWayQtyC;
    }

    public Integer getSaleWayQtyR() {
        return saleWayQtyR;
    }

    public void setSaleWayQtyR(Integer saleWayQtyR) {
        this.saleWayQtyR = saleWayQtyR;
    }

    public Integer getWaitQtyS() {
        return waitQtyS;
    }

    public void setWaitQtyS(Integer waitQtyS) {
        this.waitQtyS = waitQtyS;
    }

    public Integer getWaitQtyC() {
        return waitQtyC;
    }

    public void setWaitQtyC(Integer waitQtyC) {
        this.waitQtyC = waitQtyC;
    }

    public Integer getWaitQtyR() {
        return waitQtyR;
    }

    public void setWaitQtyR(Integer waitQtyR) {
        this.waitQtyR = waitQtyR;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getGenerateMethod() {
        return generateMethod;
    }

    public void setGenerateMethod(String generateMethod) {
        this.generateMethod = generateMethod;
    }

    public String getIsGive() {
        return isGive;
    }

    public void setIsGive(String isGive) {
        this.isGive = isGive;
    }

	public Integer getSaleWaitS() {
		return saleWaitS;
	}

	public void setSaleWaitS(Integer saleWaitS) {
		this.saleWaitS = saleWaitS;
	}

	public Integer getSaleWaitC() {
		return saleWaitC;
	}

	public void setSaleWaitC(Integer saleWaitC) {
		this.saleWaitC = saleWaitC;
	}

	public Integer getSaleWaitR() {
		return saleWaitR;
	}

	public void setSaleWaitR(Integer saleWaitR) {
		this.saleWaitR = saleWaitR;
	}

	public String getIsPkg() {
		return isPkg;
	}

	public void setIsPkg(String isPkg) {
		this.isPkg = isPkg;
	}
}