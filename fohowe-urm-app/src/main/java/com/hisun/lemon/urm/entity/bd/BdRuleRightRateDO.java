/*
 * @ClassName BdRuleRightRateDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-15 15:06:12
 */
package com.hisun.lemon.urm.entity.bd;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class BdRuleRightRateDO extends BaseDO {
    /**
     * @Fields id 
     */
    private String id;
    /**
     * @Fields sourceType 权的奖金制度,1=欧洲奖金制度，2=非洲奖金制度
     */
    private String sourceType;
    /**
     * @Fields targetType 接点的奖金制度,1=欧洲奖金制度，2=非洲奖金制度
     */
    private String targetType;
    /**
     * @Fields point 计为新增业绩
     */
    private BigDecimal point;
    /**
     * @Fields operCode 最后修改人
     */
    private String operCode;
    /**
     * @Fields operDate 最后修改时间
     */
    private LocalDateTime operDate;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public BigDecimal getPoint() {
        return point;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public String getOperCode() {
        return operCode;
    }

    public void setOperCode(String operCode) {
        this.operCode = operCode;
    }

    public LocalDateTime getOperDate() {
        return operDate;
    }

    public void setOperDate(LocalDateTime operDate) {
        this.operDate = operDate;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}