/*
 * @ClassName IStGoodAlterDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-15 11:11:00
 */
package com.hisun.lemon.urm.dao.st;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.st.StGoodAlterDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IStGoodAlterDao extends BaseDao<StGoodAlterDO> {

    /** 
     * @Title: getByAlterId 
     * @Description: 根据调整单id查询调整商品list
     * @param valueOf
     * @return
     * @return: List<StGoodAlterDO>
     */
    List<StGoodAlterDO> getByAlterId(@Param("stockAlterId") Long stockAlterId);

}