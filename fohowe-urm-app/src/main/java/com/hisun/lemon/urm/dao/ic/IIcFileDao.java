/*
 * @ClassName IIcFileDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-20 09:33:39
 */
package com.hisun.lemon.urm.dao.ic;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.ic.IcFileDO;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IIcFileDao extends BaseDao<IcFileDO> {

    /**
     * 多条删除
     * 
     * @param fileList
     * @return
     */
    public int deleteById(@Param("fileList") List<String> fileList);

    /**
     * 文件列表查询
     * @param langId 
     * @param manCompanyCode
     * @param fileDesc
     * @param fileDesc 
     * @param beginDate
     * @param endDate
     * @param isMember 
     * @param isAgent 
     * @return
     */
    public List<IcFileDO> findFileList(@Param("langId") long langId, @Param("manCompanyCode") String[] manCompanyCode,
            @Param("fileDesc") String fileDesc, @Param("beginDate") LocalDateTime beginDate,
            @Param("endDate") LocalDateTime endDate,@Param("agentValid") String agentValid, @Param("memberValid") String memberValid);

    /** 
     * @Title: getByDfileId 
     * @Description: 根据下载文件编号查询原记录
     * @param dfileId
     * @return
     * @return: IcFileDO
     */
    public IcFileDO getByDfileId(@Param("dfileId") String dfileId);
}