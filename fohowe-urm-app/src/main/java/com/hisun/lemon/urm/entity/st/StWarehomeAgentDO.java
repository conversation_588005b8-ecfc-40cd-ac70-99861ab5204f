/*
 * @ClassName StWarehomeAgentDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-07 16:52:56
 */
package com.hisun.lemon.urm.entity.st;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class StWarehomeAgentDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields companyId 公司id
     */
    private String companyId;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    private String companyCode;
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}