/*
 * @ClassName PdQmanifestRecitemsDO
 * @Description 
 * @version 1.0
 * @Date 2018-01-08 10:23:01
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;

public class PdQmanifestRecitemsDO extends BaseDO {
    /**
     * @Fields id 01.ID ID
     */
    private Integer id;
    /**
     * @Fields receiptno 02.调拨单号 ReceiptNO
     */
    private String receiptno;
    /**
     * @Fields goodscode 商品代码
     */
    private String goodscode;
    /**
     * @Fields recQty 收货数量
     */
    private Integer recQty;
    /**
     * @Fields orderNo 收货单号
     */
    private String orderNo;
    /**
     * @Fields createrCode 收货人
     */
    private String createrCode;
    /**
     * @Fields fromstore 调出仓
     */
    private String fromstore;
    /**
     * @Fields tostore 调入仓
     */
    private String tostore;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReceiptno() {
        return receiptno;
    }

    public void setReceiptno(String receiptno) {
        this.receiptno = receiptno;
    }

    public String getGoodscode() {
        return goodscode;
    }

    public void setGoodscode(String goodscode) {
        this.goodscode = goodscode;
    }

    public Integer getRecQty() {
        return recQty;
    }

    public void setRecQty(Integer recQty) {
        this.recQty = recQty;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getFromstore() {
        return fromstore;
    }

    public void setFromstore(String fromstore) {
        this.fromstore = fromstore;
    }

    public String getTostore() {
        return tostore;
    }

    public void setTostore(String tostore) {
        this.tostore = tostore;
    }
}