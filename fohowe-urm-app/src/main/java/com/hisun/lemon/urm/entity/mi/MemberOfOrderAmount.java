package com.hisun.lemon.urm.entity.mi;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;

public class MemberOfOrderAmount extends BaseDO {
    Integer periodWeek;
    BigDecimal totalAmount;
    String memberNo;
    Integer passeFlag;
    Integer itemStatus;

    public Integer getPeriodWeek() {
        return periodWeek;
    }

    public void setPeriodWeek(Integer periodWeek) {
        this.periodWeek = periodWeek;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public Integer getPasseFlag() {
        return passeFlag;
    }

    public void setPasseFlag(Integer passeFlag) {
        this.passeFlag = passeFlag;
    }

    public Integer getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(Integer itemStatus) {
        this.itemStatus = itemStatus;
    }
}
