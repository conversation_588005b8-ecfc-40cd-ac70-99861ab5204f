/*
 * @ClassName EasNetwork
 * @Description 
 * @version 1.0
 * @Date 2018-08-24 11:42:20
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;


public class EasNetwork extends BaseDO {
    /**
     * @Fields id id
     */
    private Long id;
    /**
     * @Fields businessId 业务id
     */
    private String businessId;
    /**
     * @Fields isSuccess 请求结果是否成功；是否成功 -1 失败 成功1
     */
    private String isSuccess;
    /**
     * @Fields message 提示信息
     */
    private String message;
    /**
     * @Fields totalCount 传递总行数
     */
    private String totalCount;
    /**
     * @Fields sucessCount 失败行数
     */
    private String sucessCount;
    /**
     * @Fields type 1：申购、2 申领、3 经销商基金发放 4 分红发放
     */
    private Integer type;
    /**
     * @Fields count 成功计数量
     */
    private Integer count;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields createCode 创建人
     */
    private String createCode;
    /**
     * @Fields modifyTime 修改时间
     */
    private LocalDateTime modifyTime;
    /**
     * 通知序号
     */
    private String businessSn;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(String isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(String totalCount) {
        this.totalCount = totalCount;
    }

    public String getSucessCount() {
        return sucessCount;
    }

    public void setSucessCount(String sucessCount) {
        this.sucessCount = sucessCount;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }
    
	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
	
	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getBusinessSn() {
		return businessSn;
	}

	public void setBusinessSn(String businessSn) {
		this.businessSn = businessSn;
	}

	@Override
	public String toString() {
		return "EasNetwork [id=" + id + ", businessId=" + businessId + ", businessSn=" + businessSn + ", isSuccess=" + isSuccess + ", message="
				+ message + ", totalCount=" + totalCount + ", sucessCount=" + sucessCount + ", type=" + type
				+ ", count=" + count + ", createTime=" + createTime + ", createCode=" + createCode + ", modifyTime="
				+ modifyTime + "]";
	}
	
	
}