package com.hisun.lemon.urm.uitls.excel.fi;



import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import com.hisun.lemon.fohowe.common.enums.AcTypeEnums;
import com.hisun.lemon.fohowe.common.enums.BonusType;
import com.hisun.lemon.urm.dto.fi.balance.AcHistoryBalanceItemDO;
import com.hisun.lemon.urm.uitls.EnumsUtils;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactorys;


public class AcHistoryBalanceExcelExporter extends  URMExcelExportFactorys{
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="历史账户余额";
		String[] colNames=new String[] {"奖金制度","所属分公司","代办处","用户编号","账户类型","可用余额"};
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(Sheet sheet, CellStyle style, Object obj,int beginRow) throws Exception {

		List<AcHistoryBalanceItemDO> vo = (List<AcHistoryBalanceItemDO>)obj;
		Map<Object, String> AcType = EnumsUtils.EnumToMap(AcTypeEnums.class);
		Map<Object, String> bonusType = EnumsUtils.EnumToMap(BonusType.class);
		for(AcHistoryBalanceItemDO o:vo) {
			Row row = sheet.createRow(beginRow++);
			
			row.createCell(0).setCellValue(bonusType.get(o.getBonusType()));
			row.createCell(1).setCellValue(o.getCompanyCode());
			row.createCell(2).setCellValue(o.getAgentNo());
			row.createCell(3).setCellValue(o.getUserCode());
			row.createCell(4).setCellValue((AcType.get(o.getAcType())));
			row.createCell(5).setCellValue(o.getBalance()==null?new Integer(0):o.getBalance().doubleValue());
			
		}
		
	}
	public static AcHistoryBalanceExcelExporter builder() {
		return new AcHistoryBalanceExcelExporter();
	}
}
