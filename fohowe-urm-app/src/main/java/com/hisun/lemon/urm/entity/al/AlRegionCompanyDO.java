/*
 * @ClassName AlRegionCompanyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-07 16:43:35
 */
package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AlRegionCompanyDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private String id;
    /**
     * @Fields regionId 地区ID
     */
    private String regionId;
    /**
     * @Fields companyCode 公司编号
     */
    private String companyCode;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 地区编码
     */
    private String regionCode;
    /**
     * 地区名称
     */
    private String regionName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
}