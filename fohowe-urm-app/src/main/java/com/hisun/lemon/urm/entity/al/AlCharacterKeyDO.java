/*
 * @ClassName AlCharacterKeyDO
 * @Description 
 * @version 1.0
 * @Date 2017-11-27 10:16:06
 */
package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class AlCharacterKeyDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields characterKey 字符编码键值
     */
    private String characterKey;
    /**
     * @Fields keyDesc 字符编码键值说明
     */
    private String keyDesc;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    private String modifyType;
    
    private LocalDateTime createTime;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCharacterKey() {
        return characterKey;
    }

    public void setCharacterKey(String characterKey) {
        this.characterKey = characterKey;
    }

    public String getKeyDesc() {
        return keyDesc;
    }

    public void setKeyDesc(String keyDesc) {
        this.keyDesc = keyDesc;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getModifyType() {
        return modifyType;
    }

    public void setModifyType(String modifyType) {
        this.modifyType = modifyType;
    }

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}
    
}