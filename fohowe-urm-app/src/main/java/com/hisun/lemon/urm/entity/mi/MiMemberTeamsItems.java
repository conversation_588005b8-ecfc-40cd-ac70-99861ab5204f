package com.hisun.lemon.urm.entity.mi;

import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class MiMemberTeamsItems extends BaseDO {
    private Integer id;

    private String teamNo;

    private String memberNo;

    private String memberName;

    private String cardType;

    private Integer itemStatus;

    private Integer passeFlag;
    
    private Integer fendou;
    
    private Integer leader;
    
    private Integer teamType;
    
    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTeamNo() {
        return teamNo;
    }

    public void setTeamNo(String teamNo) {
        this.teamNo = teamNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public Integer getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(Integer itemStatus) {
        this.itemStatus = itemStatus;
    }

    public Integer getPasseFlag() {
        return passeFlag;
    }

    public void setPasseFlag(Integer passeFlag) {
        this.passeFlag = passeFlag;
    }

	public Integer getFendou() {
		return fendou;
	}

	public void setFendou(Integer fendou) {
		this.fendou = fendou;
	}

	public Integer getLeader() {
		return leader;
	}

	public void setLeader(Integer leader) {
		this.leader = leader;
	}

	public LocalDateTime getCreateTime() {
		return createTime;
	}

	public void setCreateTime(LocalDateTime createTime) {
		this.createTime = createTime;
	}

	public Integer getTeamType() {
		return teamType;
	}

	public void setTeamType(Integer teamType) {
		this.teamType = teamType;
	}
    
}