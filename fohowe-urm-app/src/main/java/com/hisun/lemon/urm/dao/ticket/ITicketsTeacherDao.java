package com.hisun.lemon.urm.dao.ticket;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.urm.dto.fi.tickets.TicketsManager;
import com.hisun.lemon.urm.dto.fi.tickets.TicketsTeacherBean;
import com.hisun.lemon.urm.entity.ticket.TicketsTeacherDO;

@Mapper
public interface ITicketsTeacherDao {

	List<TicketsTeacherBean> getListByCondition(TicketsTeacherBean queryDTO);
	
	int getTotalCount(TicketsTeacherBean queryDTO);
	
	List<TicketsTeacherDO> queryList(TicketsTeacherBean query);
	
	List<TicketsManager> getManagerList(TicketsTeacherBean query);

	TicketsTeacherDO selectByNo(@Param("teacherNo") String teacherNo);
	
	List<TicketsTeacherDO> selectByTeam(@Param("teamNo") String teamNo);
	
	List<TicketsTeacherDO> selectByNos(@Param("teacherArrs") String[] teacherArrs);
	
    int deleteByPrimaryKey(Long id);

    int insert(TicketsTeacherDO record);

    int insertSelective(TicketsTeacherDO record);

    TicketsTeacherDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TicketsTeacherDO record);

    int updateByPrimaryKey(TicketsTeacherDO record);
}