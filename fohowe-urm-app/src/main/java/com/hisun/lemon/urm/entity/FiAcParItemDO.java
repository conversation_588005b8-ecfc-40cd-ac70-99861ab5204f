/*
 * @ClassName FiAcParItemDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;

public class FiAcParItemDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields parValue 面值
     */
    private BigDecimal parValue;
    /**
     * @Fields changeQty 数量变化
     */
    private BigDecimal changeQty;
    /**
     * @Fields changeValidQty 可用数量变化
     */
    private BigDecimal changeValidQty;
    /**
     * @Fields qty 剩余数量
     */
    private BigDecimal qty;
    /**
     * @Fields validQty 剩余可用数量
     */
    private BigDecimal validQty;
    /**
     * @Fields changeType 变动类型：a01=充值，a09=转账
     */
    private String changeType;
    /**
     * @Fields memo 摘要
     */
    private String memo;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields companyCode 公司编号
     */
    private String companyCode;
    /**
     * @Fields acType 账户类型
     */
    private String acType;
    /**
     * @Fields userCode 用户编号
     */
    private String userCode;
    /**
     * @Fields createCode 创建人
     */
    private String createCode;
    /**
     * @Fields relateId 关联id
     */
    private Long relateId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getParValue() {
        return parValue;
    }

    public void setParValue(BigDecimal parValue) {
        this.parValue = parValue;
    }

    public BigDecimal getChangeQty() {
        return changeQty;
    }

    public void setChangeQty(BigDecimal changeQty) {
        this.changeQty = changeQty;
    }

    public BigDecimal getChangeValidQty() {
        return changeValidQty;
    }

    public void setChangeValidQty(BigDecimal changeValidQty) {
        this.changeValidQty = changeValidQty;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getValidQty() {
        return validQty;
    }

    public void setValidQty(BigDecimal validQty) {
        this.validQty = validQty;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAcType() {
        return acType;
    }

    public void setAcType(String acType) {
        this.acType = acType;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public Long getRelateId() {
        return relateId;
    }

    public void setRelateId(Long relateId) {
        this.relateId = relateId;
    }
}