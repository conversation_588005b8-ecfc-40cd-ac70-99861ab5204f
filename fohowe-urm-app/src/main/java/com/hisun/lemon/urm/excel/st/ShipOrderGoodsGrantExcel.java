/**   
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: EntityGranExcel.java 
 * @Prject: fohowe-web-ec
 * @Package: com.hisun.lemon.fohowe.ec.excel 
 * @author: liubao   
 * @date: 2017年12月6日 上午10:46:28 
 * @version: V1.0   
 */
package com.hisun.lemon.urm.excel.st;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.common.DateConstant;
import com.hisun.lemon.urm.entity.pd.PdQmanifestItemRspDO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

/**
 * @ClassName: ShipOrderGoodsGrantExcel 
 * @author: tian
 * @date: 2018年3月12日 下午3:01:31 
 * @param <T>
 */
public class ShipOrderGoodsGrantExcel extends URMExcelExportFactory {
    public DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
    	List<PdQmanifestItemRspDO> dataList = (List<PdQmanifestItemRspDO>) obj;
        if (dataList != null && dataList.size() > 0) {
            for (int i = 0; i < dataList.size(); i++) {
                PdQmanifestItemRspDO itemDO = dataList.get(i);
                // 创建所需的行数
                XSSFRow row = sheet.createRow(i + beginRow);
                XSSFCell cell = null;

                // 订单编号
                cell = row.createCell(0, CellType.STRING);
                cell.setCellValue(itemDO.getReceiptno());
                cell.setCellStyle(style);

                // 商品编号
                cell = row.createCell(1, CellType.STRING);
                cell.setCellValue(itemDO.getGoodscode());
                cell.setCellStyle(style);

                // 商品名称
                cell = row.createCell(2, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(itemDO.getGoodsName()) ? "" : itemDO.getGoodsName());
                cell.setCellStyle(style);
                
                // 供货方
                cell = row.createCell(3, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(itemDO.getFromstore()) ? "" : itemDO.getFromstore());
                cell.setCellStyle(style);
                
                // 调拨申请分公司
                cell = row.createCell(4, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(itemDO.getReqstore()) ? "" : itemDO.getReqstore());
                cell.setCellStyle(style);
                
                // 入库单日期
                cell = row.createCell(5, CellType.STRING);
                DateTimeFormatter df = DateTimeFormatter.ofPattern(DateConstant.STR1_YMDHMS);
                cell.setCellValue(itemDO.getInHouseDate()==null?"":df.format(itemDO.getInHouseDate()).toString());
                cell.setCellStyle(style);
                
                // 入库单日期
                cell = row.createCell(6, CellType.STRING);
                cell.setCellValue(itemDO.getSenddate()==null?"":df.format(itemDO.getSenddate()).toString());
                cell.setCellStyle(style);
                
                // 单价
                cell = row.createCell(7, CellType.STRING);
                cell.setCellValue(
                        JudgeUtils.isNull(itemDO.getStandardPrice()) ? "" : itemDO.getStandardPrice().toString());
                cell.setCellStyle(style);

                // 分值
                cell = row.createCell(8, CellType.NUMERIC);
                cell.setCellValue(JudgeUtils.isNull(itemDO.getStandardFv()) ? "" : itemDO.getStandardFv().toString());
                cell.setCellStyle(style);

                // 数量
                cell = row.createCell(9, CellType.NUMERIC);
                cell.setCellValue(itemDO.getSendqty());
                cell.setCellStyle(style);

                // 单品金额小计
                cell = row.createCell(10, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNullAny(itemDO.getStandardPrice(), itemDO.getSendqty()) ? ""
                        : itemDO.getStandardPrice().multiply(BigDecimal.valueOf(itemDO.getSendqty())).toString());
                cell.setCellStyle(style);

                // 单品分值小计
                cell = row.createCell(11, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNullAny(itemDO.getStandardFv(), itemDO.getSendqty()) ? ""
                        : itemDO.getStandardFv().multiply(BigDecimal.valueOf(itemDO.getSendqty())).toString());
                cell.setCellStyle(style);

                // 币种
                cell = row.createCell(12, CellType.STRING);
                cell.setCellValue(JudgeUtils.isNull(itemDO.getCurrencyName()) ? "" : itemDO.getCurrencyName());
                cell.setCellStyle(style);
            }
        }
    }

}
