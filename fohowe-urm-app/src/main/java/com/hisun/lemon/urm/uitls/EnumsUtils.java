package com.hisun.lemon.urm.uitls;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import com.hisun.lemon.urm.enums.mi.DivideOrMergeEnums;

public class EnumsUtils {

    public static Map<String, String> getListFromEnums(Enum enums) {
        Map<String, String> map = new HashMap<>();
        DivideOrMergeEnums[] array = DivideOrMergeEnums.values();
        for (DivideOrMergeEnums e : array) {
            map.put(e.getCode(), e.getName());
        }
        return map;
    }

    /** 
     * 枚举转map结合code作为map的key,name作为map的value 
     * @param enumT 
     * @param method 
     * @return enum mapcolloction 
     */
    public static <T> Map<Object, String> EnumToMap(Class<T> enumT, String... methodNames) {
        Map<Object, String> enummap = new HashMap<Object, String>();
        if (!enumT.isEnum()) {
            return enummap;
        }
        T[] enums = enumT.getEnumConstants();
        if (enums == null || enums.length <= 0) {
            return enummap;
        }
        int count = methodNames.length;
        String valueMathod = "getCode"; //默认接口value方法  
        String desMathod = "getName";//默认接口description方法  
        if (count >= 1 && !"".equals(methodNames[0])) { //扩展方法  
            valueMathod = methodNames[0];
        }
        if (count == 2 && !"".equals(methodNames[1])) {
            desMathod = methodNames[1];
        }
        for (int i = 0, len = enums.length; i < len; i++) {
            T tobj = enums[i];
            try {
                Object resultValue = getMethodValue(valueMathod, tobj); //获取value值  
                if ("".equals(resultValue)) {
                    continue;
                }
                Object resultDes = getMethodValue(desMathod, tobj); //获取description描述值  
                if ("".equals(resultDes)) { //如果描述不存在获取属性值  
                    resultDes = tobj;
                }
                enummap.put(resultValue, resultDes + "");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return enummap;
    }

    /** 
     * 根据反射，通过方法名称获取方法值，忽略大小写的 
     * @param methodName 
     * @param obj 
     * @param args 
     * @return return value 
     */
    private static <T> Object getMethodValue(String methodName, T obj, Object... args) {
        Object resut = "";
        try {
            Method[] methods = obj.getClass().getMethods(); //获取方法数组，这里只要共有的方法  
            if (methods.length <= 0) {
                return resut;
            }
            Method method = null;
            for (int i = 0, len = methods.length; i < len; i++) {
                if (methods[i].getName().equalsIgnoreCase(methodName)) { //忽略大小写取方法  
                    // isHas = true;  
                    methodName = methods[i].getName(); //如果存在，则取出正确的方法名称  
                    method = methods[i];
                    break;
                }
            }
            if (method == null) {
                return resut;
            }
            resut = method.invoke(obj, args); //方法执行  
            if (resut == null) {
                resut = "";
            }
            return resut; //返回结果  
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resut;
    }

}
