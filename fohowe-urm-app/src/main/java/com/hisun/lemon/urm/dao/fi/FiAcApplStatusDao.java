package com.hisun.lemon.urm.dao.fi;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.appl.AcApplStatusDTO;
import com.hisun.lemon.urm.dto.fi.appl.AcApplStatusRspDTO;
import com.hisun.lemon.urm.entity.fi.FiAcApplStatusDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface FiAcApplStatusDao extends BaseDao<FiAcApplStatusDO> {
    AcApplStatusRspDTO selectByStatus(AcApplStatusDTO reqDTO);
}
