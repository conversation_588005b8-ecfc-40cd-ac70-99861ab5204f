/**
 * Copyright © 2017 湖南极客融联信息技术有限公司. All rights reserved.
 * @Title: CollectionDtoUtil.java
 * @Prject: fohowe-urm-app
 * @Package: com.hisun.lemon.urm.uitls
 * @Description: 凤凰高科项目
 * @author: tian
 * @date: 2017年12月18日 上午11:21:57
 * @version: V1.0
 */
package com.hisun.lemon.urm.uitls;

import java.util.ArrayList;
import java.util.List;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.framework.page.PageInfo;

/**
 * @ClassName: CollectionDtoUtil
 * @Description: dto的工具类,用来进行bo到dto的转换
 * 1、包含分页信息的转换
 * 2、包含列表的转换
 * @author: tian
 * @date: 2017年12月18日 上午11:21:57
 */
public class CollectionDtoUtil {

    /**
     * @Title: copyFromPage
     * @param pageInfo
     * @param cls
     * @return
     * @return: PageInfo<T>
     */
    public static <T> PageInfo<T> copyFromPage(PageInfo<?> pageInfo, Class<T> cls) {
        List<T> items = new ArrayList<>();
        if (null != pageInfo.getList()) {
            for (Object bo : pageInfo.getList()) {
                T item;
                try {
                    item = cls.newInstance();
                    BeanUtils.copyProperties(item, bo);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                items.add(item);
            }
        }
        PageInfo<T> page = new PageInfo<T>(items);
        BeanUtils.copyProperties(page, pageInfo);
        page.setList(items);
        return page;
    }

    /**
     * 根据bo的list来生成dto对象。
     * @param list
     * @param cls dto对象类名
     * @param elementCls dto中item类名
     * @return
     */
    public static <T> List<T> copyFromList(List<?> list, Class<T> cls) {
        List<T> items = new ArrayList<>();
        if (null != list) {
            for (Object bo : list) {
                T dto = null;
                try {
                    dto = cls.newInstance();
                    BeanUtils.copyProperties(dto, bo);
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
                items.add(dto);
            }
        }
        return items;
    }
}
