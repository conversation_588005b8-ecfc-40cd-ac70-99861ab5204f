/*
 * @ClassName FiAcParDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;

public class FiAcParDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields companyCode 公司编号
     */
    private String companyCode;
    /**
     * @Fields acType 账户类型
     */
    private String acType;
    /**
     * @Fields userCode 用户编号
     */
    private String userCode;
    /**
     * @Fields parValue 面值
     */
    private BigDecimal parValue;
    /**
     * @Fields qty 数量
     */
    private BigDecimal qty;
    /**
     * @Fields validQty 可用数量
     */
    private BigDecimal validQty;
    /**
     * @Fields status 状态
     */
    private String status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAcType() {
        return acType;
    }

    public void setAcType(String acType) {
        this.acType = acType;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public BigDecimal getParValue() {
        return parValue;
    }

    public void setParValue(BigDecimal parValue) {
        this.parValue = parValue;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getValidQty() {
        return validQty;
    }

    public void setValidQty(BigDecimal validQty) {
        this.validQty = validQty;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}