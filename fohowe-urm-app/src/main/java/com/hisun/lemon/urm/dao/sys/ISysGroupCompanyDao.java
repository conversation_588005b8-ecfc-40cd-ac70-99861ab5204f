/*
 * @ClassName ISysGroupCompanyDao
 * @Description 
 * @version 1.0
 * @Date 2019-02-12 11:18:35
 */
package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysGroupCompanyDo;

@Mapper
public interface ISysGroupCompanyDao extends BaseDao<SysGroupCompanyDo> {
	
	public List<SysGroupCompanyDo> getGroupCompanyList(SysGroupCompanyDo groupCompanyDo);
	/**
	 * 根据分公司获取群组信息
	 * @param groupCompanyDo
	 * @return
	 */
	public SysGroupCompanyDo getGroup(SysGroupCompanyDo groupCompanyDo);
	/**
	 * 绑定群组的分公司
	 * @param groupCompanyDo
	 * @return
	 */
	public List<SysGroupCompanyDo> findIsNotGroup(SysGroupCompanyDo groupCompanyDo);
	
	public int updateGroupCompanyList(SysGroupCompanyDo groupCompanyDo);
	
	/**
	 * 初始化分公司群组关系
	 * @param groupCompanyDo
	 * @return
	 */
	public int intoGroupCompany(SysGroupCompanyDo groupCompanyDo);
}