package com.hisun.lemon.urm.excel.st;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.fohowe.common.excel.ExcelExportFactory;
import com.hisun.lemon.fohowe.ec.dto.EntityGrantExcelErrorRes;
import com.hisun.lemon.urm.common.MsgCd;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class GoodsNumExcel <T extends Map<String, BigDecimal>> extends ExcelExportFactory<T> {

    private static final Logger logger = LoggerFactory.getLogger(SendInfoGrantExcel.class);

    @Override
    public Object parseExcel(XSSFSheet sheet) throws LemonException {
        Map<String, BigDecimal> goodsNumMap = new HashMap<>();
        // 正文内容应该从第二行开始,第一、二行为表头的标题
        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            XSSFRow hssfRow = sheet.getRow(rowNum);
            if (hssfRow != null) {
                EntityGrantExcelErrorRes errorRes = new EntityGrantExcelErrorRes();
                errorRes.setIdNo("第" + rowNum + "行");

                // 代办处编号
                String agentNo = getCellValue(hssfRow.getCell(0));
                // 物流单号
                BigDecimal goodsNum = BigDecimal.valueOf(Double.valueOf(getCellValue(hssfRow.getCell(1))));
                if (JudgeUtils.isBlankAny(agentNo) || JudgeUtils.isNull(goodsNum)) {
                    LemonException.throwBusinessException(MsgCd.ID_IS_NOT_EMPTY.getMsgCd());
                }
                goodsNumMap.put(agentNo, goodsNum);
            }
        }
        return goodsNumMap;
    }

    private String getCellValue(XSSFCell cell) {
        Object obj = "";
        switch (cell.getCellTypeEnum()) {
            case STRING:
                obj = cell.getStringCellValue();
                break;
            case NUMERIC:
                obj = cell.getNumericCellValue();
                break;
            case FORMULA:
                obj = cell.getCellFormula();
                break;
            case ERROR:
                obj = cell.getErrorCellValue();
                break;
            case BOOLEAN:
                obj = cell.getBooleanCellValue();
                break;
            case BLANK:
                break;
            case _NONE:
                break;
            default:
                break;
        }
        return String.valueOf(obj).trim();
    }
}
