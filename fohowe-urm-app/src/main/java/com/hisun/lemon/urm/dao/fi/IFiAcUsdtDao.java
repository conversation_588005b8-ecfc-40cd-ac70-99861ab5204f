package com.hisun.lemon.urm.dao.fi;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.fi.fgc.AcUsdtDTO;
import com.hisun.lemon.urm.entity.fi.FiAcUsdtDO;

@Mapper
public interface IFiAcUsdtDao extends BaseDao<FiAcUsdtDO>  {
	// 根据关键字删除
    int deleteByPrimaryKey(Long id);
    int insert(FiAcUsdtDO record);
   //根据关键字查询
    FiAcUsdtDO selectByPrimaryKey(Long id);
    // 更新对象不为空的数据
    int updateByPrimaryKeySelective(FiAcUsdtDO record);
    
    int countByQuery(AcUsdtDTO query);
    
    FiAcUsdtDO sumByQuery(AcUsdtDTO query);
    
    List<FiAcUsdtDO> queryList(AcUsdtDTO query);
    
    String getUsdtRewardsRate(String key);
    
}