package com.hisun.lemon.urm.uitls.excel.pd;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.urm.common.MsgCd;
import com.hisun.lemon.urm.entity.bd.BdTourismPromotionDO;

public class TourismPromExcelImport extends BaseService{
	private Logger logger = LoggerFactory.getLogger(TourismPromExcelImport.class);
	private static FormulaEvaluator evaluator;
	public final static Integer  MAX_ABLE_CNT=10000;
	
	
	public List<BdTourismPromotionDO> tourismBatchPreAdd(InputStream in) {
		Workbook book = null;
		//XSSFWorkbook book = null;
		try {
			//book = new XSSFWorkbook(in);
			book = WorkbookFactory.create(in);
			evaluator=book.getCreationHelper().createFormulaEvaluator();
		} catch (Exception e) {
			LemonException.throwBusinessException(MsgCd.IS_NOT_FILE.getMsgCd());
		}  
    	if (book == null) {
			LemonException.throwBusinessException(MsgCd.IS_NOT_FILE.getMsgCd());
		}
    	Sheet xssfSheet = book.getSheetAt(0);
    	int rowNum = xssfSheet.getLastRowNum();
    	List<BdTourismPromotionDO> tourismPromoList = new ArrayList<BdTourismPromotionDO>();
    	//忽略表头
    	for (int i = 0; i <= rowNum; i++) {
    		if(i==0){
				logger.debug("跳过表头");
				continue;
			}
			if(rowNum>MAX_ABLE_CNT){
				logger.debug("导入数量大于{}条，忽略后续数据",MAX_ABLE_CNT);
				LemonException.throwBusinessException(MsgCd.IMPORT_FILE_MAX_LINE.getMsgCd());
				break;
			}
    		//区域 代办处 分公司 经销商  姓名  获得来源 需补F$ 已补F$ 总金额	旅游名额 是否已出行 备注 补款期数 确认状态
    		Row row = xssfSheet.getRow(i);
    		try {
    			BdTourismPromotionDO tourismPromotionDO = new BdTourismPromotionDO();
//    			int index=0;
//    			String ids =getCellValue(row.getCell(index++));序号 
//    			ids=ids+",";
    			int index=1;
    			String areaName =getCellValue(row.getCell(index++));
    			tourismPromotionDO.setAreaName(areaName);
    			String agentNo = getCellValue(row.getCell(index++));
    			tourismPromotionDO.setAgentNo(agentNo);
    			String companyCode = getCellValue(row.getCell(index++));
    			tourismPromotionDO.setCompanyCode(companyCode);
        		String memberNo = getCellValue(row.getCell(index++));
        		tourismPromotionDO.setMemberNo(memberNo);
        		String memberName = getCellValue(row.getCell(index++));
        		tourismPromotionDO.setMemberName(memberName);
        		String sales = getCellValue(row.getCell(index++)); //获得来源
        		tourismPromotionDO.setSalesPromotion(sales);
        		String repairAmountStr = getCellValue(row.getCell(index++)) ;
        		String payAmountStr = getCellValue(row.getCell(index++)) ;
        		String totalAmountStr = getCellValue(row.getCell(index++));
        		BigDecimal repairAmount = BigDecimal.ZERO;
        		if(JudgeUtils.isNotBlank(repairAmountStr)) {
        			repairAmount=new BigDecimal(repairAmountStr) ;
        		}
        		tourismPromotionDO.setRepairAmount(repairAmount);
        		BigDecimal payAmount = BigDecimal.ZERO;
        		if(JudgeUtils.isNotBlank(payAmountStr)) {
        			payAmount=new BigDecimal(payAmountStr) ;
        		}
        		tourismPromotionDO.setPayAmount(payAmount);
        		BigDecimal totalAmount = BigDecimal.ZERO;
        		if(JudgeUtils.isNotBlank(totalAmountStr)) {
        			totalAmount=new BigDecimal(totalAmountStr) ;
        		}
        		tourismPromotionDO.setTotalAmount(totalAmount);
        		String quotaNumStr =getCellValue(row.getCell(index++));
        		Integer quotaNum = 0;
        		if(JudgeUtils.isNotBlank(quotaNumStr)) {
        			quotaNum=new Integer(quotaNumStr);
        		}
        		tourismPromotionDO.setQuotaNum(quotaNum);
        		String traveledStr = getCellValue(row.getCell(index++));
        		int isTraveled = 0;
        		if(JudgeUtils.isNotBlank(traveledStr)) {
        			if(JudgeUtils.equals(traveledStr, "是")) {
        				isTraveled = 1;
        			}
        		}
        		tourismPromotionDO.setIsTraveled(isTraveled);
        		String remark = getCellValue(row.getCell(index++));
        		tourismPromotionDO.setRemark(remark);
        		String payWeekStr = getCellValue(row.getCell(index++));
        		Integer payWeek = 0;
        		if(JudgeUtils.isNotBlank(payWeekStr)) {
        			payWeek=new Integer(payWeekStr) ;
        		}
        		tourismPromotionDO.setPayWeek(payWeek);
        		String ficheckeStatusStr = getCellValue(row.getCell(index++));//确认状态
        		/**
        		 * 【1】未确认；
					【2】已确认；
					【3】已取消；
					【4】已领取兑换产品/F$；
					【5】已转让
        		 */
        		Integer ficheckeStatus=1;
        		if(JudgeUtils.isNotBlank(ficheckeStatusStr)) {
        			if(JudgeUtils.equals(ficheckeStatusStr, "已确认")) {
        				ficheckeStatus=2;
        			}
        			if(JudgeUtils.equals(ficheckeStatusStr, "已取消")) {
        				ficheckeStatus=3;
        			}
        			if(JudgeUtils.equals(ficheckeStatusStr, "已领取兑换产品/F$")) {
        				ficheckeStatus=4;
        			}
        			if(JudgeUtils.equals(ficheckeStatusStr, "已转让")) {
        				ficheckeStatus=5;
        			}
        		}
        		tourismPromotionDO.setFicheckeStatus(ficheckeStatus);
        		tourismPromotionDO.setVcStatus(0);
        		
        		tourismPromoList.add(tourismPromotionDO);
			} catch (Exception e) {
				logger.info("多少行"+i+"报错"+"经销商"+getCellValue(row.getCell(1)));
				e.printStackTrace();
				LemonException.throwBusinessException(MsgCd.IMPORT_FILE_FAILED.getMsgCd());
			}
		}
		return tourismPromoList;
	}
	
	
	private String getCellValue(Cell cell){
		if(cell==null){
			return null;
		}
		String str = null;

		if (cell.getCellTypeEnum() == CellType.BOOLEAN){
			return String.valueOf(cell.getBooleanCellValue());
		}else if (cell.getCellTypeEnum() == CellType.NUMERIC){
			DecimalFormat df = new DecimalFormat("0");
			str=df.format(cell.getNumericCellValue());
		}else if (cell.getCellTypeEnum() == CellType.STRING){
			str=cell.getStringCellValue();
		}else if (cell.getCellTypeEnum() == CellType.FORMULA) {
			str=getCellValue(evaluator.evaluate(cell)); 
		}else{
			return null;
		}
		return str.replaceAll("[\\s\\?]", "").replace("　", "");
	}
	private static String getCellValue(CellValue cell) {
        String cellValue = null;
        switch (cell.getCellTypeEnum()) {
        case STRING:
            cellValue=cell.getStringValue();
            break;
        case NUMERIC:
        	DecimalFormat df = new DecimalFormat("0");
        	cellValue=df.format(cell.getNumberValue());
            break;
        case FORMULA:
            break;
        default:
            break;
        }
        return cellValue;
    }
}
