/*
 * @ClassName IAlRegionDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-10 11:31:42
 */
package com.hisun.lemon.urm.dao.al;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.al.AlRegionDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IAlRegionDao extends BaseDao<AlRegionDO> {

    /** 
     * @Title: findPageList 
     * @param parentRegionId
     * @param regionName
     * @param isCross
     * @return
     * @return: List<AlRegionDO>
     */
    List<AlRegionDO> findPageList(@Param("parentRegionId") String parentRegionId, @Param("regionName") String regionName,
            @Param("isCross") String isCross,@Param("regionCode") String regionCode);

    
    /** 
     * @Title: getByAreaCode 
     * @Description: 查询地区资料是否存在
     * @param areaCode
     * @return
     * @return: AlRegionDO
     */
    AlRegionDO getByAreaCode(@Param("areaCode") String areaCode);


	List<AlRegionDO> findList(@Param("parentRegionId") String parentRegionId, @Param("regionId") String regionId,
			@Param("regionName") String regionName,@Param("isCross") String isCross,
			@Param("bonusList") List<String> bonusList);
	
	
	
	
	
}