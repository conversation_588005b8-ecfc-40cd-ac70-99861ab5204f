package com.hisun.lemon.urm.dao.sys;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysPasswordAnswerDO;

@Mapper
public interface ISysPasswordAnswerDao  extends BaseDao<SysPasswordAnswerDO>  {

	 public List<SysPasswordAnswerDO> finds(@Param("userCode") String userCode);
	 
	 public SysPasswordAnswerDO findsByNo(@Param("userCode") String userCode,@Param("questionNo") String questionNo);
	 
	 public int deleteById(@Param("id") Long id);
	 
}
