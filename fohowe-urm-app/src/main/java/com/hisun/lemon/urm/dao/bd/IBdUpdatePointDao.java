/*
 * @ClassName IBdUpdatePointDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-11 17:02:49
 */
package com.hisun.lemon.urm.dao.bd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.RightPointAdjustBean;
import com.hisun.lemon.urm.dto.mi.member.RightPointAdjustQryDTO;
import com.hisun.lemon.urm.entity.bd.BdUpdatePointDO;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IBdUpdatePointDao extends BaseDao<BdUpdatePointDO> {

	int getTotalCount(RightPointAdjustQryDTO dto);

	List<RightPointAdjustBean> getListByPageBreak(RightPointAdjustQryDTO dto);


	RightPointAdjustBean getExtensionDetailById(@Param("id") long id);

	BdUpdatePointDO get(@Param("id")long id);

    void insertBatch(List<Map<String, Object>> dataList);

    void deleteAdjustList(@Param("id")Long id);
}