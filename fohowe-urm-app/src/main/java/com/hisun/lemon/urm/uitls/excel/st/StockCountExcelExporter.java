package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.dto.st.StockCountRspDTO;
import com.hisun.lemon.urm.dto.st.StockCountRspDTO.StockCountDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class StockCountExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "库存统计列表";
        StockCountRspDTO vo = (StockCountRspDTO) obj;
        String[] colNames = new String[vo.getCompanyList().size() + 2];
        colNames[0] = "产品";
        colNames[1] = "合计";
        for (int i = 0; i < vo.getCompanyList().size(); i++) {
            colNames[i + 2] = vo.getCompanyList().get(i);
        }
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
        StockCountRspDTO vo = (StockCountRspDTO) obj;
        List<StockCountDTO> dataList = vo.getGoodsList();
        for (StockCountDTO o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);

            // 产品
            row.createCell(0).setCellValue(o.getGoodsDesc());

            // 合计
            row.createCell(1).setCellValue(o.getGoodsCount());

            for (int i = 0; i < o.getStockList().size(); i++) {
                row.createCell(i + 2).setCellValue(o.getStockList().get(i).getValidQty());
            }
        }

    }

    public static StockCountExcelExporter builder() {
        return new StockCountExcelExporter();
    }
}
