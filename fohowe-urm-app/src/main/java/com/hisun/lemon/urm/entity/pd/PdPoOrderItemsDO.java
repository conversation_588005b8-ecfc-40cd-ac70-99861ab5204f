/*
 * @ClassName PdPoOrderItemsDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-25 16:28:14
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;

public class PdPoOrderItemsDO extends BaseDO {
    /**
     * @Fields id ID
     */
    private Long id;
    /**
     * @Fields orderId 单据头序号,PD_PO_ORDER.ID
     */
    private Long orderId;
    /**
     * @Fields goodsCode 商品代码
     */
    private String goodsCode;
    private String goodsName;
    /**
     * @Fields ordType 订货规格
     */
    private String ordType;
    /**
     * @Fields moq 订货单位
     */
    private Integer moq;
    /**
     * @Fields boxQty 订货箱数
     */
    private Integer boxQty;
    /**
     * @Fields prodDate 生产日期:若为null由tu_Manifest在收货审核时填入
     */
    private LocalDateTime prodDate;
    /**
     * @Fields orderQty 订货数量,当以订货箱数方式输入,应自动计算订货量
     */
    private Integer orderQty;
    /**
     * @Fields inQty 进货数量
     */
    private Integer inQty;
    /**
     * @Fields oldQty 收货数量
     */
    private Integer oldQty;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields tmSmp 
     */
    private LocalDateTime tmSmp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getOrdType() {
        return ordType;
    }

    public void setOrdType(String ordType) {
        this.ordType = ordType;
    }

    public Integer getMoq() {
        return moq;
    }

    public void setMoq(Integer moq) {
        this.moq = moq;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public LocalDateTime getProdDate() {
        return prodDate;
    }

    public void setProdDate(LocalDateTime prodDate) {
        this.prodDate = prodDate;
    }

    public Integer getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Integer orderQty) {
        this.orderQty = orderQty;
    }

    public Integer getInQty() {
        return inQty;
    }

    public void setInQty(Integer inQty) {
        this.inQty = inQty;
    }

    public Integer getOldQty() {
        return oldQty;
    }

    public void setOldQty(Integer oldQty) {
        this.oldQty = oldQty;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }
}