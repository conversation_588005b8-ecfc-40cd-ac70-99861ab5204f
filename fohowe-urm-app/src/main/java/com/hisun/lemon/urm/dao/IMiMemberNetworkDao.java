/*
 * @ClassName IMiMemberNetworkDao
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.member.MemMainTree;
import com.hisun.lemon.urm.dto.mi.member.MemRecTree;
import com.hisun.lemon.urm.dto.mi.member.NetWorkBean;
import com.hisun.lemon.urm.dto.mi.member.NetWorkDTO;
import com.hisun.lemon.urm.dto.mi.member.NetWorkTO;
import com.hisun.lemon.urm.entity.MiMemberNetworkDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IMiMemberNetworkDao extends BaseDao<MiMemberNetworkDO> {
	MiMemberNetworkDO getByMemNo(String memberNo);
	
	MiMemberNetworkDO selectByMemNo(@Param("memNo") String memNo);
	/**
	 * 推荐网络查询
	* @param network
	* @param cardTypeKey
	* @param cardTypePicKey
	* @return 
	* <AUTHOR>
	 */
	List<NetWorkBean> queryNetWorkBeanByMem(@Param("network") MiMemberNetworkDO network, @Param("cardTypeKey")String cardTypeKey, @Param("cardTypePicKey")String cardTypePicKey);
	List<MemRecTree> queryMemRecTreeByMem( @Param("networkTO") NetWorkTO reqDTO);
	
	int getTotalCount(@Param("networkTO") NetWorkTO to);
	List<MemMainTree> getMemberMainTree(@Param("networkTO") NetWorkTO to);
	/**
	 * 当该用户没有下线的时候，删除用户同时删除推荐网络信息
	 * @param memberNo
	 */
	void deleteByMemNo(String memberNo);
	/**
	 * 重算网络相关 --start
	 */
	int countWhereMemberNoIsNull();

	int countWhereRecMemberNoIsNull();

	int countWhereMemEQRecMemberNo();

	void createTempMemberNetworkTable(@Param("tableSuffix")String suffix);

	void transferDataFromMemToNetwork(@Param("tableSuffix")String suffix);
	void backupMemberNetwork(@Param("tableSuffix")String suffix);
	void renameTempMemberNetwork(@Param("tableSuffix")String suffix);


	int updateTopNodeNetwork(@Param("recmemNo") String tOP_RECOMMEND_MEMBERNO, @Param("layer") int layer, @Param("index") String index,@Param("tableSuffix")String suffix);

	List<MiMemberNetworkDO> selectListByLayerAndIndex(@Param("layer")int layer,@Param("index") String index);

	int countWhereIndexOrLayerIsNull(@Param("tableSuffix")String tableSuffix);
	List<MiMemberNetworkDO> selectListByRecommendMemNo(@Param("recmemNo") String recmemNo);

	void updateTempTable(@Param("sub")MiMemberNetworkDO sub, @Param("tableSuffix")String tableSuffix);
	/**
	 * 重算网络相关 --end
	 */

	int chargeIndexExist(@Param("newIndex") String newIndex,@Param("recommendNo") String recommendNo);

	void updateNetworkToAnotherByRecmem(@Param("recmemNo")String recmemNo, @Param("toMemberNo")String toMemberNo);

	void deleteNetworkByMemberNo(@Param("memNo") String memberNo);

	void updateByMemberNo(MiMemberNetworkDO newNetwork);

}