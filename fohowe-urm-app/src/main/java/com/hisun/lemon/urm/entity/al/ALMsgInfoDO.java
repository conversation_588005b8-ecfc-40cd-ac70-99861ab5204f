package com.hisun.lemon.urm.entity.al;

import com.hisun.lemon.framework.data.BaseDO;

public class ALMsgInfoDO extends BaseDO {
	private String msgCd;
    private String language;
    private String langName;
    private String scenario;
    private String msgInfo;
    
    public String getMsgCd() {
        return msgCd;
    }
    public void setMsgCd(String msgCd) {
        this.msgCd = msgCd;
    }
    public String getMsgInfo() {
        return msgInfo;
    }
    public void setMsgInfo(String msgInfo) {
        this.msgInfo = msgInfo;
    }
    public String getLanguage() {
        return language;
    }
    public void setLanguage(String language) {
        this.language = language;
    }
    public String getScenario() {
        return scenario;
    }
    public void setScenario(String scenario) {
        this.scenario = scenario;
    }
	public String getLangName() {
		return langName;
	}
	public void setLangName(String langName) {
		this.langName = langName;
	}
	 
}
