package com.hisun.lemon.urm.entity.st;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.hisun.lemon.framework.data.BaseDO;

public class StStockAnalysisDO extends BaseDO {

	private String regionCode;//国家编号
	private String goodsCode;//商品编号
	private List<StStockAnalysisItemDO> itemList;//期初库存
	private List<String> regionList;//国家列表
	private List<String> goodsList;//商品列表
	
	private List<Map<String,BigDecimal>> valueList;//值列表

	public List<StStockAnalysisItemDO> getItemList() {
		return itemList;
	}

	public void setItemList(List<StStockAnalysisItemDO> itemList) {
		this.itemList = itemList;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getGoodsCode() {
		return goodsCode;
	}

	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}

	public List<String> getRegionList() {
		return regionList;
	}

	public void setRegionList(List<String> regionList) {
		this.regionList = regionList;
	}

	public List<String> getGoodsList() {
		return goodsList;
	}

	public void setGoodsList(List<String> goodsList) {
		this.goodsList = goodsList;
	}

	public List<Map<String, BigDecimal>> getValueList() {
		return valueList;
	}

	public void setValueList(List<Map<String, BigDecimal>> valueList) {
		this.valueList = valueList;
	}
	
}
