/*
 * @ClassName IPdPoOrderItemsDao
 * @Description 
 * @version 1.0
 * @Date 2017-12-25 16:28:14
 */
package com.hisun.lemon.urm.dao.pd;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.pd.PdPoOrderItemsDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IPdPoOrderItemsDao extends BaseDao<PdPoOrderItemsDO> {

    /** 
     * @Title: get 
     * @param id
     * @return
     * @return: PdPoOrderItemsDO
     */
    PdPoOrderItemsDO get(@Param("id") long id);

    /** 
     * @Title: getByOrderId 
     * @param orderId
     * @param delFlag 
     * @return
     * @return: List<PdPoOrderItemsDO>
     */
    List<PdPoOrderItemsDO> getByOrderId(@Param("orderId") long orderId, @Param("delFlag") String delFlag);
    
   void deleteByOrderId(@Param("orderId") long orderId);
}