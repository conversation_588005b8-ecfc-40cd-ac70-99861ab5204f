/*
 * @ClassName MiAgentDO
 * @Description 
 * @version 1.0
 * @Date 2017-10-30 17:06:26
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import com.hisun.lemon.urm.common.DateConstant;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

public class MiAgentDO extends BaseDO {
    /**
     * @Fields id 
     */
    private Long id;
    /**
     * @Fields agentNo 编号
     */
    private String agentNo;
    /**
     * @Fields recommendNo 负责人
     */
    private String recommendNo;
    /**
     * @Fields storeId 店号？？
     */
    private String storeId;
    /**
     * @Fields companyCode 所属分公司
     */
    private String companyCode;
    /**
     * @Fields cardType 级别
     */
    private String cardType;
    /**
     * @Fields name 名称
     */
    private String name;
    /**
     * @Fields startWeek 加入期数
     */
    private String startWeek;
    /**
     * @Fields storeName 店铺名称
     */
    private String storeName;
    /**
     * @Fields regionCode 地区编号
     */
    private String regionCode;
    /**
     * @Fields storeAddr 店铺地址
     */
    private String storeAddr;
    /**
     * @Fields storePost 邮编
     */
    private String storePost;
    /**
     * @Fields homeTel 家庭电话
     */
    private String homeTel;
    /**
     * @Fields officeTel 办公电话
     */
    private String officeTel;
    /**
     * @Fields mobile 手机
     */
    private String mobile;
    /**
     * @Fields fax 传真
     */
    private String fax;
    /**
     * @Fields accountBank 开户银行
     */
    private String accountBank;
    /**
     * @Fields accountCode 银行帐号
     */
    private String accountCode;
    /**
     * @Fields accountName 开户名
     */
    private String accountName;
    /**
     * @Fields email 电子邮箱
     */
    private String email;
    /**
     * @Fields webAddr 网址
     */
    private String webAddr;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields fareArea 经营面积
     */
    private String fareArea;
    /**
     * @Fields fareBreed 经营品种
     */
    private String fareBreed;
    /**
     * @Fields petName 昵称
     */
    private String petName;
    /**
     * @Fields sex 性别
     */
    private String sex;
    /**
     * @Fields birthday 生日
     */
    @DateTimeFormat(pattern=DateConstant.STR1_YMD)
    private Date birthday;
    /**
     * @Fields paperType 证件类型
     */
    private String paperType;
    /**
     * @Fields paperNo 证件号
     */
    private String paperNo;
    /**
     * @Fields promAmt 半价购货金额
     */
    private BigDecimal promAmt;
    /**
     * @Fields fvLimit FV允许的最低额度，
     */
    private BigDecimal fvLimit;
    /**
     * @Fields fpLimit F$允许的最低额度
     */
    private BigDecimal fpLimit;
    /**
     * @Fields f0Limit F000允许的最低额度
     */
    private BigDecimal f0Limit;
    /**
     * @Fields currencyCode 币种
     */
    private String currencyCode;
    /**
     * @Fields bonusSendType 0=发放到代办处，1=发放到经销商
     */
    private String bonusSendType;
    /**
     * @Fields promTotalAmt 累计半价购货金额
     */
    private BigDecimal promTotalAmt;
    /**
     * @Fields isBonus 奖金发放到会员模式代办费计提：0 否，1 计提经销商报单 2计提代办处报单
     */
    private String isBonus;
    /**
     * @Fields balanceType 奖金发放币种:  F$,FB (涉及代办费 分红 奖金)
     */
    private String balanceType;
    /**
     * @Fields recMemberNo 推荐人
     */
    private String recMemberNo;
    
    private String attachmentURL;
    
    private String status;
    
    private String parentNo;
    
    private Integer levelType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getRecommendNo() {
        return recommendNo;
    }

    public void setRecommendNo(String recommendNo) {
        this.recommendNo = recommendNo;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(String startWeek) {
        this.startWeek = startWeek;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getStoreAddr() {
        return storeAddr;
    }

    public void setStoreAddr(String storeAddr) {
        this.storeAddr = storeAddr;
    }

    public String getStorePost() {
        return storePost;
    }

    public void setStorePost(String storePost) {
        this.storePost = storePost;
    }

    public String getHomeTel() {
        return homeTel;
    }

    public void setHomeTel(String homeTel) {
        this.homeTel = homeTel;
    }

    public String getOfficeTel() {
        return officeTel;
    }

    public void setOfficeTel(String officeTel) {
        this.officeTel = officeTel;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getAccountBank() {
        return accountBank;
    }

    public void setAccountBank(String accountBank) {
        this.accountBank = accountBank;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebAddr() {
        return webAddr;
    }

    public void setWebAddr(String webAddr) {
        this.webAddr = webAddr;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFareArea() {
        return fareArea;
    }

    public void setFareArea(String fareArea) {
        this.fareArea = fareArea;
    }

    public String getFareBreed() {
        return fareBreed;
    }

    public void setFareBreed(String fareBreed) {
        this.fareBreed = fareBreed;
    }

    public String getPetName() {
        return petName;
    }

    public void setPetName(String petName) {
        this.petName = petName;
    }

    public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public String getPaperType() {
		return paperType;
	}

	public void setPaperType(String paperType) {
		this.paperType = paperType;
	}

	public String getPaperNo() {
        return paperNo;
    }

    public void setPaperNo(String paperNo) {
        this.paperNo = paperNo;
    }

    public BigDecimal getPromAmt() {
        return promAmt;
    }

    public void setPromAmt(BigDecimal promAmt) {
        this.promAmt = promAmt;
    }

    public BigDecimal getFvLimit() {
        return fvLimit;
    }

    public void setFvLimit(BigDecimal fvLimit) {
        this.fvLimit = fvLimit;
    }

    public BigDecimal getFpLimit() {
        return fpLimit;
    }

    public void setFpLimit(BigDecimal fpLimit) {
        this.fpLimit = fpLimit;
    }

    public BigDecimal getF0Limit() {
        return f0Limit;
    }

    public void setF0Limit(BigDecimal f0Limit) {
        this.f0Limit = f0Limit;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getBonusSendType() {
        return bonusSendType;
    }

    public void setBonusSendType(String bonusSendType) {
        this.bonusSendType = bonusSendType;
    }

    public BigDecimal getPromTotalAmt() {
        return promTotalAmt;
    }

    public void setPromTotalAmt(BigDecimal promTotalAmt) {
        this.promTotalAmt = promTotalAmt;
    }

    public String getIsBonus() {
        return isBonus;
    }

    public void setIsBonus(String isBonus) {
        this.isBonus = isBonus;
    }

    public String getBalanceType() {
        return balanceType;
    }

    public void setBalanceType(String balanceType) {
        this.balanceType = balanceType;
    }

    public String getRecMemberNo() {
        return recMemberNo;
    }

    public void setRecMemberNo(String recMemberNo) {
        this.recMemberNo = recMemberNo;
    }

    public Integer getLevelType() {
        return levelType;
    }

    public void setLevelType(Integer levelType) {
        this.levelType = levelType;
    }

	public String getAttachmentURL() {
		return attachmentURL;
	}

	public void setAttachmentURL(String attachmentURL) {
		this.attachmentURL = attachmentURL;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getParentNo() {
		return parentNo;
	}

	public void setParentNo(String parentNo) {
		this.parentNo = parentNo;
	}
	
}