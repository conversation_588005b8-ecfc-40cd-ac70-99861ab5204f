package com.hisun.lemon.urm.dao.mi;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.mi.teams.MemberTeamsBean;
import com.hisun.lemon.urm.dto.mi.teams.MemberTeamsQuery;
import com.hisun.lemon.urm.dto.mi.teams.MemberTeamsVO;
import com.hisun.lemon.urm.entity.mi.MiMemberTeams;

@Mapper
public interface IMiMemberTeamsDao extends BaseDao<MiMemberTeams> {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(MiMemberTeams row);
    int updateTeamInfo(MiMemberTeams row);

    MiMemberTeams selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MiMemberTeams row);

    MemberTeamsVO getTotalSum(MemberTeamsQuery query);
    
	int getTotalCount(MemberTeamsQuery query);

	List<MiMemberTeams> getListList(MemberTeamsQuery query);
	List<MiMemberTeams> getFIfteenByTeamNo();

	List<MemberTeamsBean> getListByCondition(MemberTeamsQuery query);

	MiMemberTeams selectByMemberNo(@Param("memberNo")String memberNo,@Param("teamType")Integer teamType);
	
	MiMemberTeams selectByItemMem(@Param("memberNo")String memberNo,@Param("teamType")Integer teamType);
	
	MiMemberTeams selectByTeamNo(@Param("teamNo")String teamNo);
	
	int initTeamsItemFendou(@Param("teamType")Integer teamType);
	int updateTeamsItemFendou(@Param("teamType")Integer teamType);
	
	int initTeamsItemStatus(@Param("periodWeek") Integer periodWeek,@Param("teamType")Integer teamType);
	int updateTeamsItemStatus31(@Param("periodWeek") Integer periodWeek);
	int updateTeamsItemStatus32(@Param("periodWeek") Integer periodWeek);
	int updateTeamsItemStatus(@Param("periodWeek") Integer periodWeek);
	int updateTeamsItemStatusVstar(@Param("periodWeek") Integer periodWeek);
	int initTeamsMemStatus(@Param("periodWeek") Integer periodWeek,@Param("teamType")Integer teamType);
	int initTeamsMemStatus2();
	int updateTeamsMemStatus(@Param("periodWeek") Integer periodWeek,@Param("teamType")Integer teamType);
	int updateTeamsItemMemStatus(@Param("periodWeek") Integer periodWeek,@Param("teamType")Integer teamType);
	int updateTeamsMemStatus2(@Param("teamType")Integer teamType);
	
	int updateTeamsItemsSlf(@Param("teamType")Integer teamType);
	
	int deleteTeamsItemMem(@Param("periodWeek") Integer periodWeek);
	int getTeamsSum();
	MiMemberTeams getTeamsLast();

	int getTeamsItemMemNum(@Param("periodWeek") Integer periodWeek);
	
	int insertTeamsItemMem(@Param("periodWeek") Integer periodWeek);
	int insertTeamsItemMemFDX(@Param("periodWeek") Integer periodWeek);

	//明星队增加推荐新人相关
	int deleteStarTeamsItemMem(@Param("periodWeek") Integer periodWeek);
	int getStarTeamsItemMemNum(@Param("periodWeek") Integer periodWeek);
	int insertStarTeamsItemMem(@Param("periodWeek") Integer periodWeek);
	
	int updateTeamsMemNum();
	int updateByTeamNum();
	int selectByTeamNumByFifteen();

	int insertTeamsMem(@Param("memberNo") String memberNo,@Param("teamType") int teamType);
	
	int insertMemTeams(@Param("memberNo") String memberNo);
	int insertMemTeamsItems();
	int TeamsPeroidWeek(@Param("periodWeek") Integer periodWeek);
	int TeamStatusForOne(@Param("periodWeek") Integer periodWeek);
	int TeamStatusForTwo(@Param("periodWeek") Integer periodWeek);

	Integer isTeamMem(@Param("memberNo") String memberNo);
	
	Integer isTeamMem2(@Param("memberNo") String memberNo,@Param("teamType") int teamType);
	
	Integer isStarTeamMem(@Param("memberNo") String memberNo);
	
	Integer checkstruggle(@Param("memberNo") String memberNo);
	Integer checkstruggleSLF(@Param("memberNo") String memberNo);

	String getNextNo();
	
	String getStarNextNo();

	Integer queryOrderByTKT(@Param("memberNo") String memberNo);
	Integer queryOrderByVRP(@Param("memberNo") String memberNo);

	Integer queryOrderByFendou(@Param("memberNo") String memberNo,@Param("periodWeek") Integer periodWeek);
	Integer queryOrderByFendouEu(@Param("memberNo") String memberNo,@Param("periodWeek") Integer periodWeek);
	
	Integer queryOrderByFendou2(@Param("memberNo") String memberNo,@Param("periodWeek") Integer periodWeek);
	Integer queryOrderByFendouEu2(@Param("memberNo") String memberNo,@Param("periodWeek") Integer periodWeek);
	
	Double queryOrderAmount(@Param("memberNo") String memberNo,@Param("periodWeek") Integer periodWeek);
	Double queryOrderAmountByFD(@Param("memberNo") String memberNo,@Param("periodWeek") Integer periodWeek);

}