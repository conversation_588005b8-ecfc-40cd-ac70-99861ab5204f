package com.hisun.lemon.urm.common;


/**
 * 错误码
 * <AUTHOR>
 * @date 2018年1月2日
 * @time 下午4:40:05
 */
public enum MsgCdYJJ {
    QUERY_NO_RECORD("URM60001", "查询无记录！"),
    INSERT_RECORD_FAILED("URM60002", "新增记录失败！"),
    UPDATE_RECORD_FAILED("URM60003", "修改记录失败！"),
    DELETE_RECORD_FAILED("URM60004", "删除记录失败！"),    
    RECORD_ALREADY_EXISTS("URM60005", "记录已存在！"),
    
    SEARCH_INFO_ISNULL("URM60006","查询参数为空！"),
    
    MEMFREEZE_ALREADISEXT("URM60007","该冻结已存在并且生效中，请不要重复添加！"),
    CANNOT_BE_DELETE("URM60008","不允许删除！"),
    OVER_PROMAMT_LIMIT("URM60009","已使用特批货物额度超限！"),
    NOT_SAME_BONUS_TYPE("URM60010","不符合同奖金制度转移规则！"),
    NOT_SAME_AGENT_NO("URM60011","代办处与经销商编号前5位不符！"),
    
    IS_CANCLE_STATUS("URM60012","为已取消记录，不可以再次审核！"),
    FALSE_CHECK_CONDITION("URM60013","不符合审核条件！"),
    
    INSUFFICIENT_ACCOUNT_BALANCE("URM60014","余额不足！"),
    INSUFFICIENT_FB_BALANCE("URM600142","FB余额不足！"),
    NO_BALANCE_CHANGE_INFO("URM60015","没有账户变动信息！"),
    FAILE_ACCHANGE("URM60016","请求重复,扣减失败！"), 
    FAILE_APPL("URM60017","申领失败！"),
    NO_TRANS_CONDITION("URM60018","不符合转账条件！"),
    NO_APPL_CONDITION("URM60019","不符合申领条件！"),
    NO_ENOUGHF$_CONDITION("URM60020","申领金额大于F$！"),
    FAILE_ADDPV("URM60021","提供的加入pv错误！"),
    FAILE_HAS_SUB("URM60022","经营权下有下线！"),
    FAILE_RIGHT_NUM("URM60023","现有经营权数量与注册购买数量不符，不允许删除！"),
    FAILE_QUALIFY_INFO("URM60024","提供的回退资格单信息有误！"),
    FAILE_SEARCH_USER("URM60025","查询相关用户信息失败！"),
    FALSE_LOCALMONEY("URM60026","申购-提供的本地货币金额有误！"),
    FALSE_SENDAMENT("URM60027","申领-提供的实发金额有误！"),
    NO_NETWORK("URM60028","转账双方至少有一方未入网！"),
    BALANCE_FREEZE("URM60029","该账户已冻结！"),
    MEM_FREEZE("URM60030","该经销商已注销！"),
    NO_LONGING_INFO("URM60031","获取登录用户信息失败！"),
    NO_RIGHT_INFO("URM60032","用户无网络信息！"),
    NOT_MEMBER("URM60033","该经销商不存在！"),
    NOT_SAME_COMPANY("URM60034","不符合同公司报单规则！"),
    NOT_SUB("URM60035","不满足只能给下线报单规则！"),
    NOT_SUB_RIGHT("URM60036","不符合下属经营权规则！"),
    NO_BALANCE_INFO("URM60037","该用户没有账户信息！"),
    IS_EMPTY_OR_NULL("URM60038","余额变更失败，新旧汇率存在空或者零的情况！"),
    NO_ENOUGHFB_CONDITION("URM60039","申领金额大于FB"),
    MEMBER_IS_NOT_FENDOU("URM60040","该经销商不是奋斗经销商！"),
    MEMBER_IS_NOT_AMOUNT("URM60041","该经销商F+电商订单金额不足！"),
    
    ORD_DEL_RELATION_ERROR("URM61000","订单取消失败，请检查取消经营权是否存在下线。"),
    ORD_DEL_RELATION_ACIPUT("URM61001","订单取消失败，关联的申购单已审核！"),
    ORD_DEL_RELATION_NOENOUGHINFO("URM61002","订单取消失败，相关信息缺失！"),
    NO_RIGHT_ANNNOT_AUTHORIZE("URM61003","没有报首单，不允许授权代办处报单！"),
    
    
    REGISTER_FAIL_NO_RECOMMEND("URM61004","注册失败，推荐人不存在！"),
    REGISTER_FAIL_NOT_TYPICAL("URM61005","注册失败，推荐人未入网！"),
    REGISTER_FAIL_MEMBERNO_EXIST("URM61006","注册失败，该经销商编号已经存在！"),

    
    AFRICA_CONN_LIMIT_ERROR("URM62000","不符合东、西非奖金制度接线限制"),
    NO_AGENT_INFO("URM62101","未查询到当前登录代办处信息！"),
    NO_MONEY_AND_LOCALCURRENCY_INFO("URM62102","未正常提供金额或使用货币信息"),
    NOT_ENOUGH_STOCK("URM62103","存在商品库存不足情况，请核实后再审核"),
    REGISTER_FAIL_NOT_TAX("URM61007","美国注册，税号不能未空！"),
    EAS_ERROR("URM70001","EAS以通知！") ,
    EAS_LOGIN_ERROR("URM70002","通知金蝶系统异常！") ,
    
    ANALYSIS_ERROR_IS_CALC("URM90001","正在计算中，请稍后再试！"),

    INPUT_NO_REPEAT("URM900021","重复生成申购单！"),
    ANALYSIS_ERROR_IS_SEND("URM90003","发货单正在执行中，请稍后再试！"),


    PREORDER_AMOUNt_ISNULL("URM62001","可预购金额不能为空"),
    LOGINUSER_ERROR("URM62002","购买人和登陆用户不符"),
    NUMBER_ISNULL("URM62003","购买数量不能未空"),
    FGC_NUMBER_ERROR("URM62004","不符合fgc可预购数量"),
    MEMBER_ISNULL("URM62005","经销商编号不能未空"),
    FGC_NUMBER_ISNULL("URM62006","凤凰金币不足"),
    FGC_MIN_ERROR("URM62007","凤凰金币不足"),
    FGC_DEPOSIT_ERROR("URM62008","FGC定存时间错误"),
    FGC_DEPOSITNO_ISNULL("URM62009","定存单号未空"),
    DEPOSIT_STATUS_ISNULL("URM62010","定存状态不满足取消条件"),
    DEPOSIT_RECORD_ERROR("URM62011","定存记录不存在"),
    DEPOSIT_CANCEL_ERROR("URM62012","定存记录取消失败"),
    DEPOSIT_CANCEL_10YEAR("URM62013","十年定存只能总部取消"),
    DEPOSIT_AUDIT_CONDITION("URM62014","不满足审核条件"),
    AGENT_NOT_EXCHANGE("URM62015","代办处不能兑换F360"),
    MEMBER_IS_TEAMNO("URM62016","团队名称不能填写其它团队的编号"),
    MEMBER_IS_TEAMS("URM62017","该经销商已经加入其它团队"),
    MEMBER_TEAMS10("URM62018","团队最多能加10个初始队员"),
    MEMBER_TEAMS5("URM62019","团队最多能加5个队员"),
    MEMBER_TEAMS15("URM62020","团队最多能加15个队员"),
    FI_AC_BALANCE_ERROR("URM62021","金额数据不正确!"),
    MEMBER_TEAMS6("URM62022","团队初始队员最多6个队员(包含组长)!"),
    MEMBER_IS_INTO_TEAMNO("URM62023","该经销商已入队!"),
    MEMBER_IS_INTO_NEW_TEAMNO("URM62024","团队编号已被占用,请再次保存获取新的团队编号!"),
    ;
    
    private String msgCd;
    private String msgInfo;
    private MsgCdYJJ(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }
    public String getMsgCd() {
        return msgCd;
    }
    public String getMsgInfo() {
        return msgInfo;
    }
    
}
