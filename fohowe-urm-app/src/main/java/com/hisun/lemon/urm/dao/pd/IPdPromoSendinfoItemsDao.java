/*
 * @ClassName IPdPromoSendinfoItemsDao
 * @Description 
 * @version 1.0
 * @Date 2018-01-02 11:30:52
 */
package com.hisun.lemon.urm.dao.pd;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.dto.pd.PromoSendinfoItemDTO;
import com.hisun.lemon.urm.entity.pd.PdPromoSendDetailDO;
import com.hisun.lemon.urm.entity.pd.PdPromoSendinfoItemsDO;
import com.hisun.lemon.urm.entity.pd.PdSendInfoItemsDO;

@Mapper
public interface IPdPromoSendinfoItemsDao extends BaseDao<PdPromoSendinfoItemsDO> {

    int deleteByReciepNo(String receipno);

    int insertBatch(List<PdPromoSendinfoItemsDO> sendinfoItemList);

    int deleteByRecips(List<String> reciepList);

    int updateSendNum(String receiptno);
    
    int updateSendNums(List<PdPromoSendinfoItemsDO> sendinfoItemList);

    List<PdPromoSendinfoItemsDO> queryByReceiptno(String receiptno);

    int delete(Integer id);
    
    PdPromoSendinfoItemsDO getItem(Integer id);
    
    List<PdPromoSendinfoItemsDO> getItemList(@Param("itemIds") List<Integer> itemIds);

    List<PdPromoSendDetailDO> queryBySendId(Integer id);

    List<PdPromoSendinfoItemsDO> queryByReceiptnos(List<String> receiptnoList);
    
    List<PdSendInfoItemsDO> getByComboReceiptNo(@Param("comboReceiptNo") String comboReceiptNo,
            @Param("orderStatus") String orderStatus);
    
    List<PdSendInfoItemsDO> getByComboReceiptNos(@Param("comboReceiptNo") String comboReceiptNo,
    		@Param("orderStatus") String orderStatus, @Param("lists") List<Long> lists);
    
    List<PdSendInfoItemsDO> getByComboReceiptNoCnt(@Param("comboReceiptNo") String comboReceiptNo,
            @Param("orderStatus") String orderStatus);

	List<PromoSendinfoItemDTO> queryBySendIdList(@Param("itemIds") List<Long> itemIds);

	void UpdateSendStatusByCombo(@Param("comboReceiptNo") String comboReceiptNo);

	PdPromoSendinfoItemsDO getComboSendStatusNum(@Param("comboReceiptNo") String comboReceiptNo);

	List<PdPromoSendinfoItemsDO> getItemsListByIds(@Param("lists") List<Integer> itemId);

}