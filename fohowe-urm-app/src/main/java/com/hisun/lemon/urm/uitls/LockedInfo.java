package com.hisun.lemon.urm.uitls;

public class LockedInfo {
	private String lockName;
	public int leaseTime=300;  //default 300 seconds ,it will be unlock if successed
	public int waitTime=0;
	public LockedInfo() {
	}
	
	public LockedInfo(String lockName) {
		super();
		this.lockName = lockName;
	}

	public LockedInfo(String lockName, int leaseTime) {
		this.lockName = lockName;
		this.leaseTime = leaseTime;
	}
	
	
	public LockedInfo(String lockName, int leaseTime, int waitTime) {
		super();
		this.lockName = lockName;
		this.leaseTime = leaseTime;
		this.waitTime = waitTime;
	}
	public String getLockName() {
		return lockName;
	}
	public void setLockName(String lockName) {
		this.lockName = lockName;
	}
	public int getLeaseTime() {
		return leaseTime;
	}
	public void setLeaseTime(int leaseTime) {
		this.leaseTime = leaseTime;
	}
	public int getWaitTime() {
		return waitTime;
	}
	public void setWaitTime(int waitTime) {
		this.waitTime = waitTime;
	}
	
	
}
