package com.hisun.lemon.urm.uitls.excel.st;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.dto.st.StockAnalysisResDTO;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class StockAnalysisExcelExporter extends URMExcelExportFactory {

    @Override
    public void export(Object obj, HttpServletResponse response) {
        String fileName = "库存分析报表";
        StockAnalysisResDTO vo = (StockAnalysisResDTO) obj;
        String[] colNames = null;
        if (JudgeUtils.isEmpty(vo.getColumns())) {
            colNames = new String[1];	
        } else {
            colNames = new String[vo.getColumns().size()];
        }
        colNames[0] = "期次";
        int colNums = 1;
        for (int i = 0; i < vo.getColumns().size(); i++) {
        	 if (JudgeUtils.equalsAny(vo.getColumns().get(i), "w_week")) {
                 continue;
             }
        	colNames[colNums] = JudgeUtils.isNull(vo.getColumns().get(i)) ? "/" : vo.getColumns().get(i) ; 
            colNums++;
        }
        super.exportActually(fileName, colNames, obj, response);
    }

    @Override
    public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj, int beginRow) throws Exception {
    	StockAnalysisResDTO vo = (StockAnalysisResDTO) obj;
        List<Map<String, Object>> dataList = vo.getList();
        for (Map<String, Object> o : dataList) {
            XSSFRow row = sheet.createRow(beginRow++);

            //期次
            row.createCell(0).setCellValue(o.get("w_week").toString());

            int colNums = 1;
            for (int i = 0; i < vo.getColumns().size(); i++) {
                if (JudgeUtils.equalsAny(vo.getColumns().get(i), "w_week")) {
                    continue;
                }
                String cellValue = vo.getColumns().get(i);
                if(o.get(cellValue) != null) {
                	row.createCell(colNums).setCellValue(o.get(cellValue).toString());
                }
                colNums++;
            }
        }

    }

    public static StockAnalysisExcelExporter builder() {
        return new StockAnalysisExcelExporter();
    }
}
