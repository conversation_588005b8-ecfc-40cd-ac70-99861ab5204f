/*
 * @ClassName PromotionDO
 * @Description 
 * @version 1.0
 * @Date 2017-12-26 15:29:10
 */
package com.hisun.lemon.urm.entity.pd;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PromotionDO extends BaseDO {
    /**
     * @Fields id id标识
     */
    private Integer id;
    /**
     * @Fields wWeek 期次
     */
    private Integer wWeek;
    /**
     * @Fields companyCode 所属分公司
     */
    private String companyCode;
    /**
     * @Fields orderNo 订单编号
     */
    private String orderNo;
    /**
     * @Fields combineNo 合并单号
     */
    private String combineNo;
    /**
     * @Fields memberNo 
     */
    private String memberNo;
    /**
     * @Fields name 姓名
     */
    private String name;
    /**
     * @Fields agentNo 代办处编号
     */
    private String agentNo;
    /**
     * @Fields orderStatus 单据状态，0=新增，1=审核
     */
    private String orderStatus;
    /**
     * @Fields checkTime 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * @Fields checkCode 审核人
     */
    private String checkCode;
    /**
     * @Fields isCombo 是否合并 0 不是 1 是
     */
    private String isCombo;
    /**
     * @Fields bonusType 奖金制度
     */
    private String bonusType;
    /**
     * @Fields areaCode 区域编码
     */
    private String areaCode;
    /**
     * @Fields operCode 操作人代码
     */
    private String operCode;
    /**
     * @Fields activeCode 活跃编码
     */
    private String activeCode;
    /**
     * @Fields activeName 活跃编名称
     */
    private String activeName;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /*
     * @Fields repoCompany
     */
    private String repoCompany;
    /*
     * @Fields repoAgent
     */
    private String repoAgent;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getwWeek() {
        return wWeek;
    }

    public void setwWeek(Integer wWeek) {
        this.wWeek = wWeek;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCombineNo() {
        return combineNo;
    }

    public void setCombineNo(String combineNo) {
        this.combineNo = combineNo;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAgentNo() {
        return agentNo;
    }

    public void setAgentNo(String agentNo) {
        this.agentNo = agentNo;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getIsCombo() {
        return isCombo;
    }

    public void setIsCombo(String isCombo) {
        this.isCombo = isCombo;
    }

    public String getBonusType() {
        return bonusType;
    }

    public void setBonusType(String bonusType) {
        this.bonusType = bonusType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getOperCode() {
        return operCode;
    }

    public void setOperCode(String operCode) {
        this.operCode = operCode;
    }

    public String getActiveCode() {
        return activeCode;
    }

    public void setActiveCode(String activeCode) {
        this.activeCode = activeCode;
    }

    public String getActiveName() {
        return activeName;
    }

    public void setActiveName(String activeName) {
        this.activeName = activeName;
    }

    @Override
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getRepoCompany() {
        return repoCompany;
    }

    public void setRepoCompany(String repoCompany) {
        this.repoCompany = repoCompany;
    }

    public String getRepoAgent() {
        return repoAgent;
    }

    public void setRepoAgent(String repoAgent) {
        this.repoAgent = repoAgent;
    }
}