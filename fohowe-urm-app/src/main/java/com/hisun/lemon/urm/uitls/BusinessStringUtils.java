package com.hisun.lemon.urm.uitls;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;

@Component
public class BusinessStringUtils {
    /**
     * 将以,分隔的字符改成list
     * 
     * @param str
     * @param searchStr
     * @return
     */
    public List<String> stringToList(String str) {
        if (JudgeUtils.isNull(str) || JudgeUtils.equals(str, "")) {
            return null;
        }
        String[] strs = StringUtils.split(str, ",");
        List<String> lists = new ArrayList<>();
        for (int i = 0; i < strs.length; i++) {
            lists.add(strs[i].trim());
        }
        return lists;
    }
    public String listToString(List<String> strList){
    	return listToString(strList,",");
    }
    /**
     * 
     * @param strList
     * @param strSpace
     * @return
     */
    public String listToString(List<String> strList,String strSpace){
    	if(strList == null || strList.size()==0) {
    		return "";
    	}
    	if(JudgeUtils.isBlank(strSpace)) {
    		strSpace=",";
    	}
    	String strResult = "";
        for (int i = 0; i < strList.size(); i++)
        {
            String str = strList.get(i);
            if (i==0){
                strResult = str;
            }else {
            	strResult += strSpace+str;
            }
        }
        return strResult;
    }
    /**
     * 将以分隔的字符改成list
     * 
     * @param str
     * @param separator 分隔符
     * @return
     */
    public List<String> stringToListBySeparator(String str,String separator) {
        if (JudgeUtils.isNull(str) || JudgeUtils.equals(str, "")) {
            return null;
        }
        String[] strs = StringUtils.split(str, separator);
        List<String> lists = new ArrayList<>();
        for (int i = 0; i < strs.length; i++) {
            lists.add(strs[i].trim());
        }
        return lists;
    }
    /**
     * 将以,分隔的字符改成list
     * 
     * @param str
     * @param searchStr
     * @return
     */
    public List<Long> stringToLongList(String str) {
        if (JudgeUtils.isNull(str) || JudgeUtils.equals(str, "")) {
            return null;
        }
        String[] strs = StringUtils.split(str, ",");
        List<Long> lists = new ArrayList<>();
        for (int i = 0; i < strs.length; i++) {
            lists.add(Long.valueOf(strs[i].trim()));
        }
        return lists;
    }

    /**
     * @Title: stringToMap 
     * @param str
     * @param searchSeq
     * @return
     * @return: Map<String,String>
     */
    public Map<String, String> stringToMap(String str, String searchSeq) {
        if (JudgeUtils.isNull(str) || JudgeUtils.equals(str, "")) {
            return null;
        }
        String[] strs = StringUtils.split(str, ",");
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < strs.length; i++) {
            map.put(StringUtils.substring(strs[i], 0, StringUtils.indexOf(strs[i], searchSeq)),
                    StringUtils.substring(strs[i], StringUtils.indexOf(strs[i], searchSeq) + 1));
        }
        return map;
    }

    /**
     * @Title: stringLike 
     * @Description: 模糊查询参数设置,截取掉最后的字符symbol
     * @param str
     * @param symbol
     * @return
     * @return: String
     */
    public String stringLike(String str, String symbol) {
        if (JudgeUtils.isNull(str) || JudgeUtils.equals(str, "")) {
            return "%";
        }
        if (StringUtils.endsWith(str, symbol)) {
            str = StringUtils.substring(str, str.length() - symbol.length());
        }
        return "%" + str + "%";
    }

}
