package com.hisun.lemon.urm.uitls.excel.pd;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.hisun.lemon.urm.entity.bd.BdTourismPromotionDO;
import com.hisun.lemon.urm.service.al.ILanguageService;
import com.hisun.lemon.urm.uitls.excel.URMExcelExportFactory;

public class TourismPromExcelExporter extends URMExcelExportFactory {
	private ILanguageService languageService;

	public ILanguageService getLanguageService() {
		return languageService;
	}

	public void setLanguageService(ILanguageService languageService) {
		this.languageService = languageService;
	}
	
	public TourismPromExcelExporter(ILanguageService languageService) {
		this.languageService=languageService;
	}
	
	public TourismPromExcelExporter() {
		
	}
	
	@Override
	public void export(Object obj, HttpServletResponse response) {
		String fileName="旅游促销查询";
		String[] colNames=new String[] {
				"区域","分公司","代办处编号","经销商编号","经销商姓名","总金额","需支付","已支付",
				"获取来汇","支付时间","补交期次","旅游名额","是否已出行","支付状态",
				"确认状态","备注","名额状态","是否系统外收款","ID","旅游路线"
				};
		String multiLangFileName="menu.tourismPromotion";
		String[] multiLangColNames=new String[] {
				"column.area","column.company","column.agencyNo","column.memNo","column.memName","common.zong_jin_e","common.xu_zhi_fu","common.yi_zhi_fu",
				"fohowe-business.sales_promotion","fohowe-business.payujrn.txTm","fohowe-business.repair_Week","旅游名额","fohowe-business.is_traveled","fohowe-business.payujrn.status"
				,"column.okStatus","common.bei_zhu","名额状态","是否系统外收款","ID","旅游路线"
		};
		 
		Map<String,Object> result=super.multiLanguageDeal(multiLangFileName,multiLangColNames, languageService);
		if(result!=null) {
			colNames=(String[]) result.get("titles");
			fileName=(String) result.get("fileName");
		}
		
		super.exportActually(fileName, colNames, obj, response);
	}
	
	@Override
	public void addCell(XSSFSheet sheet, XSSFCellStyle style, Object obj,int beginRow) throws Exception {
		List<BdTourismPromotionDO> dataList=(List<BdTourismPromotionDO>)obj;
		for(BdTourismPromotionDO item:dataList) {
			if(item !=null) {
				XSSFRow row = sheet.createRow(beginRow++);
				int index=0;
				row.createCell(index++).setCellValue(item.getAreaName()==null?"":item.getAreaName());
				row.createCell(index++).setCellValue(item.getCompanyCode()==null?"":item.getCompanyCode());
				row.createCell(index++).setCellValue(item.getAgentNo()==null?"":item.getAgentNo());
				row.createCell(index++).setCellValue(item.getMemberNo()==null?"":item.getMemberNo().trim());
				row.createCell(index++).setCellValue(item.getMemberName()==null?"":item.getMemberName());
				row.createCell(index++).setCellValue(item.getTotalAmount()==null?0:item.getTotalAmount().doubleValue());
				row.createCell(index++).setCellValue(item.getRepairAmount()==null?0:item.getRepairAmount().doubleValue());
				row.createCell(index++).setCellValue(item.getPayAmount()==null?0:item.getPayAmount().doubleValue());
				row.createCell(index++).setCellValue(item.getSalesPromotion()==null?"":item.getSalesPromotion());
				row.createCell(index++).setCellValue(item.getPayTime()==null?"":item.getPayTime().format(ymdhms));
				row.createCell(index++).setCellValue(item.getPayWeek()==null?0:item.getPayWeek());
				row.createCell(index++).setCellValue(item.getQuotaNum()==null?0:item.getQuotaNum());
				row.createCell(index++).setCellValue(item.getIsTraveled()==null?0:item.getIsTraveled());
				//"支付状态","确认状态","备注"
				String payStatus="未支付";
				if(item.getVcStatus() == null || item.getVcStatus() ==0) {
					payStatus="未支付";
				}else if(item.getVcStatus() ==1) {
					payStatus="已支付";
				} else if(item.getVcStatus() ==2) {
					payStatus="已确认";
				}else if(item.getVcStatus() ==3) {
					payStatus="保留";
				}else if(item.getVcStatus() ==4) {
					payStatus="领取产品";
				}
				row.createCell(index++).setCellValue(payStatus);
				String okStatus="未确认";
				if(item.getFicheckeStatus() == null || item.getFicheckeStatus() ==1) {
					okStatus="未确认";
				}else if(item.getFicheckeStatus() ==2) {
					okStatus="已确认";
				}else if(item.getFicheckeStatus() ==3) {
					okStatus="已取消";
				}else if(item.getFicheckeStatus() ==4) {
					okStatus="已领取兑换产品/F$";
				}else if(item.getFicheckeStatus() ==5) {
					okStatus="已转让";
				}else {
					okStatus="未确认";
				}
				row.createCell(index++).setCellValue(okStatus);
				row.createCell(index++).setCellValue(item.getRemark());
				String agStatus="";
				if(item.getAgFicheckeStatus()==1){
					agStatus="参与旅游";
				} else if(item.getAgFicheckeStatus()==2){
					agStatus="保留资格";
				} else if(item.getAgFicheckeStatus()==3){
					agStatus="领取产品";
				}
				row.createCell(index++).setCellValue(agStatus);
				String isOutPayStatus="否";
				if(item.getIsOutPay()==1){
					isOutPayStatus="是";
				}else {
					isOutPayStatus="否";
				}
				row.createCell(index++).setCellValue(isOutPayStatus);
				row.createCell(index++).setCellValue(item.getId());
				row.createCell(index++).setCellValue(item.getLineStatus());
			}
		}
		
	}
	public static TourismPromExcelExporter builder() {
		return new TourismPromExcelExporter();
	}
}
