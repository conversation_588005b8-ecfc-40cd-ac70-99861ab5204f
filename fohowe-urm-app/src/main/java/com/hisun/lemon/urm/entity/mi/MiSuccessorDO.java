package com.hisun.lemon.urm.entity.mi;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

public class MiSuccessorDO extends BaseDO {
    private Long id;

    private String companyCode;

    private String agent;

    private String memberNo;

    private String memberName;

    private String startWeek;

    private String upgradeWeek;

    private Integer status;

    private String creater;

    private String isCreater;

    private String checker;

    private String isChecker;

    private String cancel;

    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime cancelTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getMemberNo() {
        return memberNo;
    }

    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getStartWeek() {
        return startWeek;
    }

    public void setStartWeek(String startWeek) {
        this.startWeek = startWeek;
    }

    public String getUpgradeWeek() {
        return upgradeWeek;
    }

    public void setUpgradeWeek(String upgradeWeek) {
        this.upgradeWeek = upgradeWeek;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getIsCreater() {
        return isCreater;
    }

    public void setIsCreater(String isCreater) {
        this.isCreater = isCreater;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getIsChecker() {
        return isChecker;
    }

    public void setIsChecker(String isChecker) {
        this.isChecker = isChecker;
    }

    public String getCancel() {
        return cancel;
    }

    public void setCancel(String cancel) {
        this.cancel = cancel;
    }

    public LocalDateTime getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelTime = cancelTime;
    }
}