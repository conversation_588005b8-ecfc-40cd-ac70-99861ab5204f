/*
 * @ClassName ISysUserRoleDao
 * @Description 
 * @version 1.0
 * @Date 2017-11-16 11:08:14
 */
package com.hisun.lemon.urm.dao.sys;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.sys.SysUserRoleDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ISysUserRoleDao extends BaseDao<SysUserRoleDO> {

    /** 
     * @Title: recordExists 
     * @Description: 查询该角色是否存在对应关系
     * @param roleId
     * @return
     * @return: SysUserRoleDO
     */
    public SysUserRoleDO recordExists(@Param("roleId") String roleId);

    /** 
     * @Title: insertAgm 
     * @param ruId
     * @param userCode
     * @param roleName
     * @return
     * @return: int
     */
    public int insertAgm(@Param("ruId") String ruId, @Param("userCode") String userCode,
            @Param("roleName") String roleName);
    public int deleteByRoleId(@Param("userCode") String userCode,@Param("roleId") String roleId);
    
    public List<SysUserRoleDO> getRoleByUser(@Param("userCode") String userCode,@Param("roleId") String roleId);
    
}